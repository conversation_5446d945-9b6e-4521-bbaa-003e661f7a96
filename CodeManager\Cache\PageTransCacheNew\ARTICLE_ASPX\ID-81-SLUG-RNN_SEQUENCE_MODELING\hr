﻿"<!DOCTYPE html>\r\n<html xmlns=\"http://www.w3.org/1999/xhtml\" lang=\"hr\">\r\n<head><link rel=\"canonical\" href=\"https://ocr.oldfish.cn/hr/Article.aspx?id=81&slug=rnn-sequence-modeling\" /><link rel=\"alternate\" href=\"https://ocr.oldfish.cn/Article.aspx?id=81&slug=rnn-sequence-modeling\" hreflang=\"x-default\" />\r\n<link rel=\"alternate\" href=\"https://ocr.oldfish.cn/Article.aspx?id=81&slug=rnn-sequence-modeling\" hreflang=\"zh\" />\r\n<link rel=\"alternate\" href=\"https://ocr.oldfish.cn/Article.aspx?id=81&slug=rnn-sequence-modeling\" hreflang=\"zh\" />\r\n<link rel=\"alternate\" href=\"https://ocr.oldfish.cn/zh-Hant/Article.aspx?id=81&slug=rnn-sequence-modeling\" hreflang=\"zh-tw\" />\r\n<link rel=\"alternate\" href=\"https://ocr.oldfish.cn/yue/Article.aspx?id=81&slug=rnn-sequence-modeling\" hreflang=\"zh-yue\" />\r\n<link rel=\"alternate\" href=\"https://ocr.oldfish.cn/en/Article.aspx?id=81&slug=rnn-sequence-modeling\" hreflang=\"en\" />\r\n<link rel=\"alternate\" href=\"https://ocr.oldfish.cn/es/Article.aspx?id=81&slug=rnn-sequence-modeling\" hreflang=\"es\" />\r\n<link rel=\"alternate\" href=\"https://ocr.oldfish.cn/hi/Article.aspx?id=81&slug=rnn-sequence-modeling\" hreflang=\"hi\" />\r\n<link rel=\"alternate\" href=\"https://ocr.oldfish.cn/ar/Article.aspx?id=81&slug=rnn-sequence-modeling\" hreflang=\"ar\" />\r\n<link rel=\"alternate\" href=\"https://ocr.oldfish.cn/pt/Article.aspx?id=81&slug=rnn-sequence-modeling\" hreflang=\"pt\" />\r\n<link rel=\"alternate\" href=\"https://ocr.oldfish.cn/bn/Article.aspx?id=81&slug=rnn-sequence-modeling\" hreflang=\"bn\" />\r\n<link rel=\"alternate\" href=\"https://ocr.oldfish.cn/ru/Article.aspx?id=81&slug=rnn-sequence-modeling\" hreflang=\"ru\" />\r\n<link rel=\"alternate\" href=\"https://ocr.oldfish.cn/ja/Article.aspx?id=81&slug=rnn-sequence-modeling\" hreflang=\"ja\" />\r\n<link rel=\"alternate\" href=\"https://ocr.oldfish.cn/pa/Article.aspx?id=81&slug=rnn-sequence-modeling\" hreflang=\"pa\" />\r\n<link rel=\"alternate\" href=\"https://ocr.oldfish.cn/de/Article.aspx?id=81&slug=rnn-sequence-modeling\" hreflang=\"de\" />\r\n<link rel=\"alternate\" href=\"https://ocr.oldfish.cn/fr/Article.aspx?id=81&slug=rnn-sequence-modeling\" hreflang=\"fr\" />\r\n<link rel=\"alternate\" href=\"https://ocr.oldfish.cn/mr/Article.aspx?id=81&slug=rnn-sequence-modeling\" hreflang=\"mr\" />\r\n<link rel=\"alternate\" href=\"https://ocr.oldfish.cn/te/Article.aspx?id=81&slug=rnn-sequence-modeling\" hreflang=\"te\" />\r\n<link rel=\"alternate\" href=\"https://ocr.oldfish.cn/vi/Article.aspx?id=81&slug=rnn-sequence-modeling\" hreflang=\"vi\" />\r\n<link rel=\"alternate\" href=\"https://ocr.oldfish.cn/ko/Article.aspx?id=81&slug=rnn-sequence-modeling\" hreflang=\"ko\" />\r\n<link rel=\"alternate\" href=\"https://ocr.oldfish.cn/ta/Article.aspx?id=81&slug=rnn-sequence-modeling\" hreflang=\"ta\" />\r\n<link rel=\"alternate\" href=\"https://ocr.oldfish.cn/ur/Article.aspx?id=81&slug=rnn-sequence-modeling\" hreflang=\"ur\" />\r\n<link rel=\"alternate\" href=\"https://ocr.oldfish.cn/tr/Article.aspx?id=81&slug=rnn-sequence-modeling\" hreflang=\"tr\" />\r\n<link rel=\"alternate\" href=\"https://ocr.oldfish.cn/it/Article.aspx?id=81&slug=rnn-sequence-modeling\" hreflang=\"it\" />\r\n<link rel=\"alternate\" href=\"https://ocr.oldfish.cn/gu/Article.aspx?id=81&slug=rnn-sequence-modeling\" hreflang=\"gu\" />\r\n<link rel=\"alternate\" href=\"https://ocr.oldfish.cn/pl/Article.aspx?id=81&slug=rnn-sequence-modeling\" hreflang=\"pl\" />\r\n<link rel=\"alternate\" href=\"https://ocr.oldfish.cn/uk/Article.aspx?id=81&slug=rnn-sequence-modeling\" hreflang=\"uk\" />\r\n<link rel=\"alternate\" href=\"https://ocr.oldfish.cn/ms/Article.aspx?id=81&slug=rnn-sequence-modeling\" hreflang=\"ms\" />\r\n<link rel=\"alternate\" href=\"https://ocr.oldfish.cn/id/Article.aspx?id=81&slug=rnn-sequence-modeling\" hreflang=\"id\" />\r\n<link rel=\"alternate\" href=\"https://ocr.oldfish.cn/ml/Article.aspx?id=81&slug=rnn-sequence-modeling\" hreflang=\"ml\" />\r\n<link rel=\"alternate\" href=\"https://ocr.oldfish.cn/kn/Article.aspx?id=81&slug=rnn-sequence-modeling\" hreflang=\"kn\" />\r\n<link rel=\"alternate\" href=\"https://ocr.oldfish.cn/fa/Article.aspx?id=81&slug=rnn-sequence-modeling\" hreflang=\"fa\" />\r\n<link rel=\"alternate\" href=\"https://ocr.oldfish.cn/nl/Article.aspx?id=81&slug=rnn-sequence-modeling\" hreflang=\"nl\" />\r\n<link rel=\"alternate\" href=\"https://ocr.oldfish.cn/th/Article.aspx?id=81&slug=rnn-sequence-modeling\" hreflang=\"th\" />\r\n<link rel=\"alternate\" href=\"https://ocr.oldfish.cn/sw/Article.aspx?id=81&slug=rnn-sequence-modeling\" hreflang=\"sw\" />\r\n<link rel=\"alternate\" href=\"https://ocr.oldfish.cn/ro/Article.aspx?id=81&slug=rnn-sequence-modeling\" hreflang=\"ro\" />\r\n<link rel=\"alternate\" href=\"https://ocr.oldfish.cn/my/Article.aspx?id=81&slug=rnn-sequence-modeling\" hreflang=\"my\" />\r\n<link rel=\"alternate\" href=\"https://ocr.oldfish.cn/or/Article.aspx?id=81&slug=rnn-sequence-modeling\" hreflang=\"or\" />\r\n<link rel=\"alternate\" href=\"https://ocr.oldfish.cn/he/Article.aspx?id=81&slug=rnn-sequence-modeling\" hreflang=\"he\" />\r\n<link rel=\"alternate\" href=\"https://ocr.oldfish.cn/am/Article.aspx?id=81&slug=rnn-sequence-modeling\" hreflang=\"am\" />\r\n<link rel=\"alternate\" href=\"https://ocr.oldfish.cn/fil/Article.aspx?id=81&slug=rnn-sequence-modeling\" hreflang=\"fil\" />\r\n<link rel=\"alternate\" href=\"https://ocr.oldfish.cn/sv/Article.aspx?id=81&slug=rnn-sequence-modeling\" hreflang=\"sv\" />\r\n<link rel=\"alternate\" href=\"https://ocr.oldfish.cn/el/Article.aspx?id=81&slug=rnn-sequence-modeling\" hreflang=\"el\" />\r\n<link rel=\"alternate\" href=\"https://ocr.oldfish.cn/cs/Article.aspx?id=81&slug=rnn-sequence-modeling\" hreflang=\"cs\" />\r\n<link rel=\"alternate\" href=\"https://ocr.oldfish.cn/hu/Article.aspx?id=81&slug=rnn-sequence-modeling\" hreflang=\"hu\" />\r\n<link rel=\"alternate\" href=\"https://ocr.oldfish.cn/be/Article.aspx?id=81&slug=rnn-sequence-modeling\" hreflang=\"be\" />\r\n<link rel=\"alternate\" href=\"https://ocr.oldfish.cn/si/Article.aspx?id=81&slug=rnn-sequence-modeling\" hreflang=\"si\" />\r\n<link rel=\"alternate\" href=\"https://ocr.oldfish.cn/ne/Article.aspx?id=81&slug=rnn-sequence-modeling\" hreflang=\"ne\" />\r\n<link rel=\"alternate\" href=\"https://ocr.oldfish.cn/km/Article.aspx?id=81&slug=rnn-sequence-modeling\" hreflang=\"km\" />\r\n<link rel=\"alternate\" href=\"https://ocr.oldfish.cn/sk/Article.aspx?id=81&slug=rnn-sequence-modeling\" hreflang=\"sk\" />\r\n<link rel=\"alternate\" href=\"https://ocr.oldfish.cn/bg/Article.aspx?id=81&slug=rnn-sequence-modeling\" hreflang=\"bg\" />\r\n<link rel=\"alternate\" href=\"https://ocr.oldfish.cn/fr-ca/Article.aspx?id=81&slug=rnn-sequence-modeling\" hreflang=\"fr-ca\" />\r\n<link rel=\"alternate\" href=\"https://ocr.oldfish.cn/ha/Article.aspx?id=81&slug=rnn-sequence-modeling\" hreflang=\"ha\" />\r\n<link rel=\"alternate\" href=\"https://ocr.oldfish.cn/yo/Article.aspx?id=81&slug=rnn-sequence-modeling\" hreflang=\"yo\" />\r\n<link rel=\"alternate\" href=\"https://ocr.oldfish.cn/ig/Article.aspx?id=81&slug=rnn-sequence-modeling\" hreflang=\"ig\" />\r\n<link rel=\"alternate\" href=\"https://ocr.oldfish.cn/ku/Article.aspx?id=81&slug=rnn-sequence-modeling\" hreflang=\"ku\" />\r\n<link rel=\"alternate\" href=\"https://ocr.oldfish.cn/rw/Article.aspx?id=81&slug=rnn-sequence-modeling\" hreflang=\"rw\" />\r\n<link rel=\"alternate\" href=\"https://ocr.oldfish.cn/ca/Article.aspx?id=81&slug=rnn-sequence-modeling\" hreflang=\"ca\" />\r\n<link rel=\"alternate\" href=\"https://ocr.oldfish.cn/da/Article.aspx?id=81&slug=rnn-sequence-modeling\" hreflang=\"da\" />\r\n<link rel=\"alternate\" href=\"https://ocr.oldfish.cn/fi/Article.aspx?id=81&slug=rnn-sequence-modeling\" hreflang=\"fi\" />\r\n<link rel=\"alternate\" href=\"https://ocr.oldfish.cn/nb/Article.aspx?id=81&slug=rnn-sequence-modeling\" hreflang=\"nb\" />\r\n<link rel=\"alternate\" href=\"https://ocr.oldfish.cn/hr/Article.aspx?id=81&slug=rnn-sequence-modeling\" hreflang=\"hr\" />\r\n<link rel=\"alternate\" href=\"https://ocr.oldfish.cn/sr-Cyrl/Article.aspx?id=81&slug=rnn-sequence-modeling\" hreflang=\"sr-cyrl\" />\r\n<link rel=\"alternate\" href=\"https://ocr.oldfish.cn/sr-Latn/Article.aspx?id=81&slug=rnn-sequence-modeling\" hreflang=\"sr-latn\" />\r\n<link rel=\"alternate\" href=\"https://ocr.oldfish.cn/sq/Article.aspx?id=81&slug=rnn-sequence-modeling\" hreflang=\"sq\" />\r\n<link rel=\"alternate\" href=\"https://ocr.oldfish.cn/so/Article.aspx?id=81&slug=rnn-sequence-modeling\" hreflang=\"so\" />\r\n<link rel=\"alternate\" href=\"https://ocr.oldfish.cn/zu/Article.aspx?id=81&slug=rnn-sequence-modeling\" hreflang=\"zu\" />\r\n<link rel=\"alternate\" href=\"https://ocr.oldfish.cn/ka/Article.aspx?id=81&slug=rnn-sequence-modeling\" hreflang=\"ka\" />\r\n<meta http-equiv=\"Content-Type\" content=\"text/html; charset=UTF-8\" /><meta http-equiv=\"X-UA-Compatible\" content=\"IE=Edge,chrome=1\" /><meta name=\"renderer\" content=\"webkit\" /><meta name=\"viewport\" content=\"width=device-width, initial-scale=1.0, maximum-scale=5.0\" /><link rel=\"icon\" href=\"/favicon.ico\" type=\"image/x-icon\" /><meta name=\"robots\" content=\"all\" /><link rel=\"preconnect\" href=\"https://scm-file-new-1301864755.cos.ap-beijing.myqcloud.com\" /><link rel=\"preconnect\" href=\"https://cdn.oldfish.cn\" /><link rel=\"dns-prefetch\" href=\"https://hm.baidu.com\" /><link rel=\"dns-prefetch\" href=\"https://cdn.oldfish.cn\" /><link rel=\"preload\" href=\"/site/css/site.css\" as=\"style\" />\r\n<script>function toArray(l){var a=[];for(var i=0;i<(l?l.length:0);i++)a.push(l[i]);return a;}function forEach(l,f){for(var i=0,n=(l?l.length:0);i<n;i++)f(l[i],i);}function hasClass(e,c){if(!e||!c)return false;return(' '+e.className+' ').indexOf(' '+c+' ')>-1;}function addClass(e,c){if(!e||!c||hasClass(e,c))return;e.className=(e.className?e.className+' ':'')+c;}function removeClass(e,c){if(!e||!c)return;var u=' '+e.className+' ';while(u.indexOf(' '+c+' ')>-1){u=u.replace(' '+c+' ',' ');}e.className=u.replace(/^\\s+|\\s+$/g,'');}function toggleClass(e,c){if(!e||!c)return;if(hasClass(e,c))removeClass(e,c);else addClass(e,c);}function matchesSelector(e,s){if(!e||e.nodeType!==1)return false;if(e.matches)return e.matches(s);var p=e.parentNode||document;var m=toArray(p.querySelectorAll(s));for(var i=0;i<m.length;i++)if(m[i]===e)return true;return false;}function closest(e,s){var n=e;while(n&&n.nodeType===1){if(matchesSelector(n,s))return n;n=n.parentNode;}return null;}function safeRemove(e){if(e&&e.parentNode)e.parentNode.removeChild(e);}function qa(s,r){return toArray((r||document).querySelectorAll(s));}function q(s,r){return(r||document).querySelector(s);}function xhrGet(u,o,e){try{var x=new XMLHttpRequest();x.open('GET',u,true);x.onreadystatechange=function(){if(x.readyState===4){if(x.status>=200&&x.status<300)o&&o(x.responseText);else e&&e(x);}};x.send(null);}catch(r){e&&e(r);}}function getScrollY(){return window.pageYOffset||document.documentElement.scrollTop||document.body.scrollTop||0;}function getOrigin(){return window.location.protocol+'//'+window.location.host;}</script>\r\n<script>var isWebP=(function(){try{return document.createElement('canvas').toDataURL('image/webp').indexOf('data:image/webp')===0;}catch(e){return false;}})();document.addEventListener('DOMContentLoaded',function(){var c=document.getElementById('user-login-container');if(c){var u=c.getAttribute('data-url');if(u){xhrGet(u,function(h){c.innerHTML=h;forEach(qa('a',c),function(l){l.style.display='inline-block';l.style.marginLeft='10px';});});}}});</script>\r\n    \n    \n\n    <meta name=\"description\" content=\"Zaronite u primjenu RNN, LSTM, GRU u OCR-u. Detaljna analiza principa modeliranja sekvenci, rješenja gradijentnih problema i prednosti dvosmjernih RNN-ova.\" />\n    <meta name=\"keywords\" content=\"RNN, LSTM, GRU, modeliranje sekvenci, gradijentno nestajanje, dvosmjerni RNN, mehanizam pažnje, CRNN, OCR, OCR prepoznavanje teksta, pretvaranje slike u tekst, OCR tehnologija\" />\n    <meta property=\"og:title\" content=\"【OCR serija dubokog učenja·4】 Rekurentne neuronske mreže i modeliranje sekvenci\" />\n    <meta property=\"og:description\" content=\"Zaronite u primjenu RNN, LSTM, GRU u OCR-u. Detaljna analiza principa modeliranja sekvenci, rješenja gradijentnih problema i prednosti dvosmjernih RNN-ova.\" />\n    <meta property=\"og:type\" content=\"article\" />\n    <meta property=\"og:url\" content=\"https://ocr.oldfish.cn/Article.aspx?id=81&slug=rnn-sequence-modeling&lang=zh-Hans&_translation_source=true\" />\n    <meta property=\"og:site_name\" content=\"OCR pomoćnik za prepoznavanje teksta\" />\n    <meta name=\"twitter:card\" content=\"summary\" />\n    <meta name=\"twitter:title\" content=\"【OCR serija dubokog učenja·4】 Rekurentne neuronske mreže i modeliranje sekvenci\" />\n    <meta name=\"twitter:description\" content=\"Zaronite u primjenu RNN, LSTM, GRU u OCR-u. Detaljna analiza principa modeliranja sekvenci, rješenja gradijentnih problema i prednosti dvosmjernih RNN-ova.\" />\n\n    \n    <script type=\"application/ld+json\">\n    {\n        \"@context\": \"https://schema.org\",\n        \"@type\": \"Article\",\n        \"headline\": \"【OCR serija dubokog učenja 4] Rekurentna neuronska mreža i modeliranje sekvenci\",\n        \"description\": \"Zaronite u primjenu RNN, LSTM, GRU u OCR-u. Detaljna analiza principa modeliranja sekvenci, rješenja gradijentnih problema i prednosti dvosmjernih RNN-ova。\",\n        \"publisher\": {\n            \"@type\": \"Organization\",\n            \"name\": \"OCR tim pomoćnika za prepoznavanje teksta\"\n        },\n        \"datePublished\": \"2025-08-19T06:32:05Z\",\n        \"dateModified\": \"2025-08-19T06:32:05Z\",\n        \"mainEntityOfPage\": {\n            \"@type\": \"WebPage\",\n            \"@id\": \"https://ocr.oldfish.cn/Article.aspx?id=81&slug=rnn-sequence-modeling&lang=zh-Hans&_translation_source=true\"\n        }\n    }\n    </script>\n    \n    <style>\n        .reading-progress { position: fixed; top: 0; left: 0; width: 0%; height: 3px; background: linear-gradient(90deg, #1764ff, #0d47a1); z-index: 9999; transition: width 0.2s ease; box-shadow: 0 1px 3px rgba(23, 100, 255, 0.3); }\n        .home-container.root { overflow: visible !important; }\n        .article-detail-container { background: #f5f7fc; min-height: calc(100vh - 60px); padding: 80px 0 60px; position: relative; overflow: visible; }\n        .article-detail-container .row { margin: 0 -12px; align-items: flex-start !important; min-height: 100vh !important; }\n        .article-detail-container .row > [class*=\"col-\"] { padding: 0 12px; box-sizing: border-box; min-width: 0; }\n        .article-detail-container .row > .col-lg-8, .article-detail-container .row > .col-xl-9 { overflow: hidden; }\n        .article-detail-container .row > .col-lg-4, .article-detail-container .row > .col-xl-3 { overflow: visible !important; height: 100% !important; min-height: 100% !important; position: static !important; display: block !important; transform: none !important; will-change: auto !important; contain: none !important; perspective: none !important; filter: none !important; }\n        .article-detail { background: #fff; border-radius: 8px; padding: 1.5rem; margin-bottom: 2rem; box-shadow: 0 2px 10px rgba(0,0,0,0.1); width: 100%; box-sizing: border-box; }\n        .article-title { font-size: 1.7rem; color: #1a1c1f; line-height: 1.3; margin-bottom: 1.5rem; font-weight: 600; }\n        .article-meta { display: flex; align-items: center; flex-wrap: nowrap; gap: 1.2rem; color: #6c757d; font-size: 0.88rem; margin-bottom: 2rem; padding: 1.2rem 1.4rem; background: linear-gradient(135deg, #f8f9fa 0%, #ffffff 100%); border-radius: 12px; border-left: 4px solid #1764ff; box-shadow: 0 2px 8px rgba(0,0,0,0.06); overflow-x: hidden; transition: all 0.3s ease; }\n        .article-meta:hover { box-shadow: 0 4px 16px rgba(0,0,0,0.1); transform: translateY(-1px); }\n        .article-meta .meta-item { display: flex; align-items: center; gap: 0.25rem; font-weight: 500; color: #495057; white-space: nowrap; flex-shrink: 1; transition: color 0.2s ease; cursor: default; }\n        .article-meta .meta-item:hover { color: #1764ff; }\n        .article-meta .meta-item:hover .meta-icon { transform: scale(1.1); filter: drop-shadow(0 2px 6px rgba(13, 71, 161, 0.25)); }\n        .meta-icon { display: inline-block; font-size: 16px; flex-shrink: 0; vertical-align: middle; transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1); margin-right: 4px; opacity: 0.9; }\n        .meta-item:hover .meta-icon { opacity: 1; }\n        .meta-item:hover { color: #1764ff; }\n        .meta-item { position: relative; }\n        .meta-item::after { content: ''; position: absolute; bottom: -2px; left: 0; width: 0; height: 2px; background: linear-gradient(90deg, #1764ff, #00d4ff); transition: width 0.3s ease; }\n        .meta-item:hover::after { width: 100%; }\n        .article-category, .read-time { display: inline-block; padding: 6px 12px; border-radius: 16px; font-size: 0.8rem; font-weight: 600; }\n        .article-category { background: linear-gradient(135deg, #1764ff, #0d47a1); color: white; text-decoration: none; text-transform: uppercase; letter-spacing: 0.5px; transition: all 0.3s ease; }\n        .article-category:hover { background: linear-gradient(135deg, #0d47a1, #1764ff); color: white; text-decoration: none; transform: translateY(-1px); box-shadow: 0 4px 12px rgba(23, 100, 255, 0.3); }\n        .read-time { background: #e3f2fd; color: #1565c0; border: 1px solid #90caf9; }\n        .article-tags { margin: 20px 0 1.5rem; }\n        .article-tags .tag { display: inline-block; background: #1764ff; color: #fff; padding: 4px 12px; border-radius: 12px; font-size: 0.8rem; margin: 0 8px 4px 0; }\n        .article-summary { background: #e3f2fd; border-left: 4px solid #1764ff; padding: 1rem 1.5rem; margin-bottom: 2rem; border-radius: 0 4px 4px 0; }\n        .article-content { line-height: 1.8; font-size: 16px; color: #333; word-wrap: break-word; }\n        .article-content h1, .article-content h2, .article-content h3, .article-content h4, .article-content h5, .article-content h6 { margin: 2rem 0 1rem; font-weight: 600; line-height: 1.25; color: #1a1c1f; }\n        .article-content h1 { font-size: 2rem; border-bottom: 2px solid #e9ecef; padding-bottom: 0.5rem; }\n        .article-content h2 { font-size: 1.5rem; border-bottom: 1px solid #e9ecef; padding-bottom: 0.3rem; }\n        .article-content h3 { font-size: 1.25rem; }\n        .article-content h4 { font-size: 1.1rem; }\n        .article-content h5, .article-content h6 { font-size: 1rem; }\n        .article-content ul, .article-content ol { margin: 1rem 0; padding-left: 2rem; }\n        .article-content ul ul, .article-content ol ol, .article-content ul ol, .article-content ol ul { margin: 0.5rem 0; }\n        .article-content blockquote { margin: 1rem 0; padding: 0.5rem 1rem; border-left: 4px solid #1764ff; background-color: #f8f9fa; color: #6c757d; font-style: italic; }\n        .article-content pre { background-color: #f8f9fa; border: 1px solid #e9ecef; border-radius: 6px; padding: 1rem; margin: 1rem 0; overflow-x: auto; font-family: 'Courier New', Courier, monospace; font-size: 0.9em; line-height: 1.4; }\n        .article-content pre code { background: none; border: none; padding: 0; color: inherit; }\n        .article-content p { margin: 1rem 0; }\n        .article-content p:first-child { margin-top: 0; }\n        .article-content p:last-child { margin-bottom: 0; }\n        .article-footer { margin-top: 4rem; padding: 2.5rem; background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%); border-radius: 12px; border: 1px solid #e9ecef; position: relative; overflow: hidden; }\n        .article-footer::before { content: ''; position: absolute; top: 0; left: 0; right: 0; height: 3px; background: linear-gradient(90deg, #1764ff, #17a2b8, #28a745); }\n        .article-share h6 { color: #495057; font-weight: 600; margin-bottom: 1.5rem; font-size: 16px; }\n        .share-buttons { display: flex; gap: 12px; flex-wrap: wrap; }\n        .share-buttons .btn { padding: 10px 20px; border-radius: 25px; font-weight: 500; font-size: 14px; border: none; transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1); box-shadow: 0 2px 8px rgba(0,0,0,0.1); display: inline-flex; align-items: center; gap: 8px; text-decoration: none; }\n        .btn-weibo { background: linear-gradient(135deg, #e6162d, #c41230); color: white; }\n        .btn-copy { background: #1764ff; color: white; }\n        .btn-print { background: linear-gradient(135deg, #17a2b8, #138496); color: white; }\n        .btn-weibo:hover { background: linear-gradient(135deg, #c41230, #a00e26); box-shadow: 0 4px 16px rgba(230, 22, 45, 0.3); }\n        .btn-copy:hover { background: #0d47a1; box-shadow: 0 4px 16px rgba(23, 100, 255, 0.3); }\n        .btn-print:hover { background: linear-gradient(135deg, #138496, #117a8b); box-shadow: 0 4px 16px rgba(23, 162, 184, 0.3); }\n        .btn-weibo:hover, .btn-copy:hover, .btn-print:hover { transform: translateY(-2px); color: white; }\n        .article-actions { display: flex; justify-content: flex-end; align-items: center; }\n        .related-articles { margin-top: 3rem; }\n        .related-article-item { background: #fff; border-radius: 8px; padding: 1.5rem; margin-bottom: 1rem; box-shadow: 0 2px 8px rgba(0,0,0,0.1); transition: transform 0.3s ease; }\n        .related-article-item:hover { transform: translateY(-2px); }\n        .sidebar-card { background: linear-gradient(135deg, rgba(255,255,255,0.9) 0%, rgba(255,255,255,0.8) 100%); backdrop-filter: blur(20px); border: 1px solid rgba(255,255,255,0.3); border-radius: 16px; padding: 1rem; margin-bottom: 0.5rem; box-shadow: 0 4px 20px rgba(0,0,0,0.08), inset 0 1px 0 rgba(255,255,255,0.2); overflow: visible; box-sizing: border-box; width: 100%; z-index: 10; }\n        .sidebar-card.fixed { box-shadow: 0 8px 32px rgba(0,0,0,0.12); }\n        .sidebar-card::before { content: ''; position: absolute; top: 0; left: 0; right: 0; height: 3px; background: linear-gradient(90deg, #00d4ff, #1764ff, #9c27b0); border-radius: 20px 20px 0 0; }\n        .sidebar-card h5 { color: #1a202c; padding-bottom: 0.8rem; border-bottom: 2px solid rgba(23, 100, 255, 0.2); font-weight: 700; font-size: 1.1rem; }\n        .sidebar { min-height: 100vh; position: relative !important; display: block !important; flex-direction: column !important; gap: 1rem; }\n        .toc-card { flex: 0 1 auto; max-height: 60vh; overflow-y: auto; }\n        .sidebar-card:not(.toc-card) { flex: 1 1 auto; overflow: visible; }\n        .recommended-item { padding: 0.75rem 0; border-bottom: 1px solid rgba(0,0,0,0.05); }\n        .recommended-item:last-child { border-bottom: none; }\n        .recommended-item h6 { margin: 0 0 0.5rem 0; font-size: 0.9rem; line-height: 1.4; }\n        .recommended-item h6 a { color: #1a1c1f; text-decoration: none; transition: color 0.3s ease; }\n        .recommended-item h6 a:hover { color: #1764ff; }\n        .recommended-item .text-muted { font-size: 0.75rem; margin: 0; }\n        .toc-list { list-style: none; padding: 0; margin: 0; max-height: none; overflow-y: visible; overflow-x: hidden; }\n        .toc-list::-webkit-scrollbar { width: 4px; }\n        .toc-list::-webkit-scrollbar-track { background: rgba(0,0,0,0.1); border-radius: 2px; }\n        .toc-list::-webkit-scrollbar-thumb { background: linear-gradient(135deg, #00d4ff, #1764ff); border-radius: 2px; }\n        .toc-list::-webkit-scrollbar-thumb:hover { background: linear-gradient(135deg, #1764ff, #0d47a1); }\n        .toc-list a { color: #4a5568; text-decoration: none; font-size: 13px; font-weight: 500; display: block; padding: 8px 12px; border-radius: 8px; transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1); position: relative; margin-bottom: 4px; border: 1px solid transparent; }\n        .toc-list a::before { content: ''; position: absolute; left: 0; top: 0; bottom: 0; width: 3px; background: linear-gradient(135deg, #00d4ff, #1764ff); border-radius: 0 12px 12px 0; transform: scaleY(0); transition: transform 0.3s ease; }\n        .toc-list a:hover { color: #1764ff; background: linear-gradient(135deg, rgba(0, 212, 255, 0.1), rgba(23, 100, 255, 0.1)); border-color: rgba(23, 100, 255, 0.2); transform: translateX(8px); box-shadow: 0 4px 12px rgba(0, 212, 255, 0.2); }\n        .toc-list a:hover::before, .toc-list a.active::before { transform: scaleY(1); }\n        .toc-list a.active { color: #1764ff; background: linear-gradient(135deg, rgba(0, 212, 255, 0.15), rgba(23, 100, 255, 0.15)); font-weight: 700; border-color: rgba(23, 100, 255, 0.3); transform: translateX(4px); box-shadow: 0 6px 16px rgba(0, 212, 255, 0.25); }\n        .toc-number { color: #1764ff; font-weight: 600; margin-right: 0.5rem; font-size: 0.9em; }\n        .toc-text { flex: 1; }\n        .toc-level-1 { font-weight: 700; font-size: 15px; }\n        .toc-level-2 { font-size: 14px; }\n        .toc-level-3 { font-size: 13px; opacity: 0.9; }\n        .toc-level-4, .toc-level-5, .toc-level-6 { font-size: 12px; opacity: 0.8; }\n        .article-content .formatted-list { margin: 1.5rem 0; padding-left: 0; list-style: none; }\n        .article-content .formatted-list.ordered { counter-reset: list-counter; }\n        .article-content .formatted-list.ordered li { counter-increment: list-counter; position: relative; margin: 1rem 0; padding: 1rem 1rem 1rem 3rem; background: linear-gradient(135deg, rgba(23, 100, 255, 0.03), rgba(0, 212, 255, 0.03)); border-left: 4px solid #1764ff; border-radius: 0 8px 8px 0; transition: all 0.3s ease; }\n        .article-content .formatted-list.ordered li::before { content: counter(list-counter); position: absolute; left: 0.8rem; top: 1rem; background: linear-gradient(135deg, #1764ff, #00d4ff); color: white; width: 1.5rem; height: 1.5rem; border-radius: 50%; display: flex; align-items: center; justify-content: center; font-weight: 600; font-size: 0.9rem; }\n        .article-content .formatted-list.ordered li:hover { background: linear-gradient(135deg, rgba(23, 100, 255, 0.06), rgba(0, 212, 255, 0.06)); transform: translateX(4px); box-shadow: 0 2px 8px rgba(23, 100, 255, 0.15); }\n        .article-content .formatted-list.unordered li { position: relative; margin: 0.8rem 0; padding: 0.8rem 1rem 0.8rem 2.5rem; background: linear-gradient(135deg, rgba(40, 167, 69, 0.03), rgba(25, 135, 84, 0.03)); border-left: 4px solid #28a745; border-radius: 0 6px 6px 0; transition: all 0.3s ease; }\n        .article-content .formatted-list.unordered li::before { content: \"•\"; position: absolute; left: 1rem; top: 0.8rem; color: #28a745; font-size: 1.2rem; font-weight: bold; }\n        .article-content .formatted-list.unordered li:hover { background: linear-gradient(135deg, rgba(40, 167, 69, 0.06), rgba(25, 135, 84, 0.06)); transform: translateX(4px); box-shadow: 0 2px 8px rgba(40, 167, 69, 0.15); }\n        .article-content .list-title { color: #1764ff; font-weight: 600; display: inline-block; margin-bottom: 0.3rem; }\n        .article-content .list-content { color: #2c3e50; line-height: 1.6; display: block; }\n        .article-content .inline-code { background: #f8f9fa; border: 1px solid #e9ecef; border-radius: 4px; padding: 2px 6px; font-family: 'Consolas', 'Monaco', 'Courier New', monospace; font-size: 0.9em; color: #e83e8c; }\n        .article-content .label-item { margin: 0.8rem 0; }\n        .article-content .label { color: #1764ff; font-weight: 600; }\n        .article-content .section-label { color: #2c3e50; font-weight: 600; margin: 1rem 0 0.5rem 0; font-size: 1.1em; border-left: 4px solid #1764ff; padding-left: 0.8rem; }\n        .article-content p, .article-content .content-paragraph { margin: 1.2rem 0; line-height: 1.8; text-align: justify; color: #2c3e50; font-size: 1rem; }\n        .article-content .content-paragraph { text-indent: 0; margin: 1rem 0; }\n        .article-content .indent-paragraph { text-indent: 2em; margin: 1.2rem 0; }\n        .article-content .text-indent-1 { text-indent: 2em; display: block; }\n        .article-content .text-indent-2 { text-indent: 4em; display: block; }\n        .article-content .text-indent-3 { text-indent: 6em; display: block; }\n        .article-content .network-config { background: linear-gradient(135deg, #f8f9fa, #e9ecef); border: 1px solid #dee2e6; border-radius: 6px; padding: 0.8rem 1rem; margin: 0.5rem 0; font-family: 'Consolas', 'Monaco', 'Courier New', monospace; font-size: 0.9rem; box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05); }\n        .article-content .config-label { color: #1764ff; font-weight: 600; }\n        .article-content .config-desc { color: #495057; }\n        .article-content .algorithm-step { background: linear-gradient(135deg, rgba(23, 100, 255, 0.05), rgba(0, 212, 255, 0.05)); border-left: 4px solid #1764ff; padding: 0.8rem 1rem; margin: 0.5rem 0; border-radius: 0 6px 6px 0; transition: all 0.3s ease; }\n        .article-content .step-number { color: #1764ff; font-weight: 700; margin-right: 0.5rem; }\n        .article-content .step-desc { color: #2c3e50; line-height: 1.5; }\n        .article-content table { width: 100%; border-collapse: collapse; margin: 1.5rem 0; background: white; border-radius: 8px; overflow: hidden; box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1); }\n        .article-content table th { background: linear-gradient(135deg, #1764ff, #00d4ff); color: white; padding: 1rem; text-align: left; font-weight: 600; }\n        .article-content table td { padding: 0.8rem 1rem; border-bottom: 1px solid #e9ecef; }\n        .article-content table tr:hover { background: rgba(23, 100, 255, 0.05); }\n        .article-content strong { color: #1764ff; font-weight: 600; }\n        .article-content em { color: #6c757d; font-style: italic; }\n        .article-content a { color: #1764ff; text-decoration: none; border-bottom: 1px solid transparent; transition: all 0.3s ease; }\n        .article-content a:hover { border-bottom-color: #1764ff; color: #0056b3; }\n        .article-content .definition-item { background: linear-gradient(135deg, rgba(40, 167, 69, 0.05), rgba(25, 135, 84, 0.05)); border-left: 4px solid #28a745; padding: 1rem; margin: 1rem 0; border-radius: 0 8px 8px 0; }\n        .article-content .definition-label { color: #28a745; font-weight: 600; display: block; margin-bottom: 0.5rem; }\n        .article-content .definition-content { color: #2c3e50; line-height: 1.6; }\n        .article-content .note-item { background: linear-gradient(135deg, rgba(255, 193, 7, 0.1), rgba(255, 193, 7, 0.05)); border: 1px solid rgba(255, 193, 7, 0.3); border-radius: 8px; padding: 1rem; margin: 1rem 0; position: relative; }\n        .article-content .note-item::before { content: \"💡\"; position: absolute; left: 1rem; top: 1rem; font-size: 1.2em; }\n        .article-content .note-label { color: #856404; font-weight: 600; margin-left: 2rem; }\n        .article-content .note-content { color: #2c3e50; line-height: 1.6; margin-left: 2rem; display: block; margin-top: 0.3rem; }\n        .article-content .math-formula { background: #f8f9fa; border: 1px solid #e9ecef; border-radius: 6px; padding: 1rem; margin: 1rem 0; text-align: center; font-family: 'Times New Roman', serif; font-size: 1.1em; color: #2c3e50; }\n        @media (min-width: 1400px) { .article-content { font-size: 17px; line-height: 1.8; } .article-detail { padding: 2.5rem; } }\n        @media (max-width: 1199px) and (min-width: 992px) { .article-detail { padding: 1.5rem; } .sidebar-card { padding: 1rem; } .toc-list a { font-size: 12px; padding: 6px 8px; } }\n        @media (max-width: 768px) {\n            .article-detail-container { padding: 60px 15px; }\n            .article-content .network-config, .article-content .algorithm-step, .article-content .definition-item, .article-content .note-item { margin: 0.5rem -1rem; border-radius: 0; }\n            .article-content .note-item::before { left: 0.5rem; }\n            .article-content .note-label, .article-content .note-content { margin-left: 1.5rem; }\n            .article-detail { padding: 1.5rem; }\n            .article-title { font-size: 1.6rem; line-height: 1.3; }\n            .article-meta { flex-wrap: wrap; gap: 1rem; padding: 1rem; font-size: 0.8rem; }\n            .article-meta .meta-item { flex-shrink: 1; }\n            .meta-icon { font-size: 14px; margin-right: 3px; }\n            .article-content { font-size: 15px; }\n            .article-content h1 { font-size: 1.6rem; }\n            .article-content h2 { font-size: 1.4rem; }\n            .article-content h3 { font-size: 1.2rem; }\n            .article-content h4 { font-size: 1.1rem; }\n            .article-content pre { padding: 0.75rem; font-size: 0.85em; overflow-x: auto; }\n            .article-content code { font-size: 0.85em; padding: 1px 4px; }\n            .sidebar { margin-top: 2rem; }\n            .sidebar-card { position: static; }\n            .toc-card { max-height: none; overflow-y: visible; }\n            .share-buttons { flex-direction: column; gap: 0.5rem; }\n            .share-buttons .btn { width: 100%; justify-content: center; }\n        }\n        @media print { body * { visibility: hidden; } .article-detail, .article-detail * { visibility: visible; } .article-detail { position: absolute; left: 0; top: 0; width: 100%; background: white; box-shadow: none; border: none; margin: 0; padding: 20px; } .article-meta { border: none; background: none; padding: 0; margin-bottom: 20px; } .article-footer, .reading-progress, .back-to-top { display: none !important; } .article-content { font-size: 12pt; line-height: 1.6; } .article-content h1, .article-content h2, .article-content h3, .article-content h4, .article-content h5, .article-content h6 { page-break-after: avoid; margin-top: 20pt; margin-bottom: 10pt; } .article-content pre, .article-content blockquote { page-break-inside: avoid; } }\n    </style>\n\n<script type=\"application/ld+json\">{\"@context\":\"https://schema.org\",\"@type\":\"BreadcrumbList\",\"itemListElement\":[{\"@type\":\"ListItem\",\"position\":1,\"name\":\"Dom\",\"item\":\"https://ocr.oldfish.cn/default.aspx\"},{\"@type\":\"ListItem\",\"position\":2,\"name\":\"Tehnički članci\",\"item\":\"https://ocr.oldfish.cn/Articles.aspx\"},{\"@type\":\"ListItem\",\"position\":3,\"name\":\"Detalji članka\",\"item\":\"https://ocr.oldfish.cn/Article.aspx?id=81&slug=rnn-sequence-modeling&lang=zh-Hans&_translation_source=true\"}]}</script>\n<link rel=\"stylesheet\" href=\"/site/css/site.css\" type=\"text/css\" /><title>【OCR serija dubokog učenja·4】 Rekurentne neuronske mreže i modeliranje sekvenci</title><meta http-equiv=\"Content-Language\" content=\"hr\" />\n</head>\r\n<body>\r\n    <div class=\"home-container root\">\r\n        <header class=\"v4_header_pc\">\r\n            <div class=\"header_pc_left\">\r\n                <a href=\"Default.aspx\" title=\"Naslovnica | AI inteligentno prepoznavanje teksta\" class=\"pc-logo-wrap ml-4 mr-5\">\r\n                    <picture>\r\n                        <source srcset=\"site/image/logo-s.png.webp\" type=\"image/webp\" />\r\n                        <img src=\"site/image/logo-s.png\" class=\"pc-logo\" style=\"height: 100%\" alt=\"OCR pomoćnik za prepoznavanje teksta Službeni logotip web stranice - AI inteligentna platforma za prepoznavanje teksta\" />\r\n                    </picture>\r\n                    <span style=\"font-size: 18px; font-weight: bold; color: #333;\">OCR pomoćnik za prepoznavanje teksta</span>\r\n                </a>\r\n                <ul class=\"top-nav\" role=\"navigation\" aria-label=\"Glavna navigacija\">\r\n                    <li><a class=\"color-default\" href=\"Default.aspx\" title=\"Početna stranica OCR pomoćnika za prepoznavanje teksta\">Dom</a></li>\r\n                    <li><a class=\"color-default\" data-sub=\"product-sub-nav\" href=\"Detail.aspx\" title=\"Uvod u funkciju OCR proizvoda\">Značajke proizvoda:</a><div class=\"triangle\"></div>\r\n                    </li>\r\n                    <li><a class=\"color-default\" data-sub=\"experience-sub-nav\" href=\"Ocr.aspx\" title=\"Doživite OCR značajke na mreži\">Online iskustvo</a><div class=\"triangle\"></div>\r\n                    </li>\r\n                    <li><a class=\"color-default\" data-sub=\"vip-sub-nav\" href=\"Version.aspx\" title=\"Usluga nadogradnje OCR članstva\">Nadogradnje članstva</a><div class=\"triangle\"></div>\r\n                    </li>\r\n                    <li><a class=\"color-default\" href=\"Download.aspx\" title=\"Besplatno preuzmite OCR pomoćnik za prepoznavanje teksta\">Besplatno preuzimanje</a></li>\r\n                    <li><a class=\"color-default\" data-sub=\"articles-sub-nav\" href=\"Articles.aspx\" title=\"OCR tehnički članci i dijeljenje znanja\">Dijeljenje tehnologije</a><div class=\"triangle\"></div>\r\n                    </li>\r\n                    <li><a class=\"color-default\" data-sub=\"support-sub-nav\" href=\"FAQ.aspx\" title=\"Pomoć i tehnička podrška za korištenje OCR-a\">Centar za pomoć</a><div class=\"triangle\"></div>\r\n                    </li>\r\n                </ul>\r\n                <div class=\"product-con\">\r\n                    <section id=\"product-sub-nav\" class=\"pls-nav-dropdown\" style=\"visibility: hidden; height: 0px; display: none;\">\r\n                        <div class=\"d-flex nav-drown-con\" style=\"height: 0px; transition: transform 0.2s ease-in-out 0s,opacity 0.2s ease-in-out 0s; opacity: 0; transform: translate(0px, -100%);\">\r\n                            <div class=\"prdocu-sub-left\">\r\n                                <div class=\"mr-auto\">\r\n                                    <picture>\r\n                                        <source class=\"p-icon\" srcset=\"site/image/icon/p_1.png.webp\" type=\"image/webp\" alt=\"\" aria-hidden=\"true\" />\r\n                                        <img class=\"p-icon\" src=\"site/image/icon/p_1.png\" alt=\"Ikona funkcije OCR proizvoda\" aria-hidden=\"true\" loading=\"lazy\" />\r\n                                    </picture>\r\n                                    <div class=\"mt-4\">\r\n                                        <h3 class=\"h6\">OCR pomoćnik za prepoznavanje teksta</h3>\r\n                                        <p class=\"color-gray fn14 mt-3 sub-left-desc\">Poboljšajte učinkovitost, smanjite troškove i stvorite vrijednost</p>\r\n                                        <p class=\"color-gray fn14 mt-3 sub-left-desc\">&nbsp;</p>\r\n                                        <p class=\"color-gray fn14 mt-3 sub-left-desc\">Inteligentno prepoznavanje, brza obrada i točan izlaz</p>\r\n                                        <p class=\"color-gray fn14 mt-3 sub-left-desc\">Od teksta do tablica, od formula do prijevoda</p>\r\n                                        <p class=\"color-gray fn14 mt-3 sub-left-desc\">Neka svaka obrada teksta bude tako jednostavna</p>\r\n                                        <a class=\"link block disa fn14 contact-business shake\" href=\"Detail.aspx\">Saznajte više o značajkama<span class=\"iconfont4 icon-xiangzuo gt\"></span></a>\r\n                                    </div>\r\n                                </div>\r\n                            </div>\r\n                            <div class=\"bg-white prdocu-sub-right\">\r\n                                <div>\r\n                                    <h2 class=\"text-base-color h6\">Značajke proizvoda:</h2>\r\n                                    <div class=\"mt-4 d-flex flex-wrap\">\r\n                                        <a href=\"Detail.aspx\" class=\"pro-item color-default\" title=\"Provjerite pojedinosti o osnovnim funkcijama OCR Assistanta\">\r\n                                            <div class=\"letter-wrap\">\r\n                                                <h3 class=\"h6\">Osnovne značajke:</h3>\r\n                                                <span class=\"color-gray fn14\">Saznajte više o osnovnim značajkama i tehničkim prednostima OCR asistenta, sa stopom prepoznavanja od 98%+</span>\r\n                                            </div>\r\n                                        </a>\r\n                                        <a href=\"Version.aspx\" class=\"pro-item color-default\" title=\"Usporedite razlike između verzija OCR pomoćnika\">\r\n                                            <div class=\"letter-wrap\">\r\n                                                <h3 class=\"h6\">Usporedba verzija</h3>\r\n                                                <span class=\"color-gray fn14\">Detaljno usporedite funkcionalne razlike besplatne verzije, osobne verzije, profesionalne verzije i ultimativne verzije</span>\r\n                                            </div>\r\n                                        </a>\r\n                                        <a href=\"FAQ.aspx\" class=\"pro-item color-default\" title=\"Pogledajte često postavljana pitanja OCR pomoćnika\">\r\n                                            <div class=\"letter-wrap\">\r\n                                                <h3 class=\"h6\">Pitanja i odgovori o proizvodu</h3>\r\n                                                <span class=\"color-gray fn14\">Brzo saznajte više o značajkama proizvoda, načinima upotrebe i detaljnim odgovorima na najčešća pitanja</span>\r\n                                            </div>\r\n                                        </a>\r\n                                        <a href=\"Download.aspx\" class=\"pro-item color-default\" title=\"Besplatno preuzmite OCR pomoćnik za prepoznavanje teksta\">\r\n                                            <div class=\"letter-wrap\">\r\n                                                <h3 class=\"h6\">Isprobaj besplatno</h3>\r\n                                                <span class=\"color-gray fn14\">Preuzmite i instalirajte OCR Assistant sada kako biste besplatno iskusili moćnu funkciju prepoznavanja teksta</span>\r\n                                            </div>\r\n                                        </a>\r\n                                        <div style=\"width: 100%; margin: 15px 0;\">\r\n                                            <h4 class=\"h6\" style=\"color: #007cfa; margin-bottom: 10px;\">Mrežno prepoznavanje OCR-a</h4>\r\n                                        </div>\r\n                                        <a href=\"Ocr.aspx?type=text_recognize\" class=\"pro-item color-default\" title=\"Doživite univerzalno prepoznavanje teksta na mreži\">\r\n                                            <div class=\"letter-wrap\">\r\n                                                <h3 class=\"h6\">Univerzalno prepoznavanje znakova</h3>\r\n                                                <span class=\"color-gray fn14\">Inteligentno izdvajanje višejezičnog teksta visoke preciznosti, podržavajući prepoznavanje ispisanih i složenih slika na više scena</span>\r\n                                            </div>\r\n                                        </a>\r\n                                        <a href=\"Ocr.aspx?type=table\" class=\"pro-item color-default\">\r\n                                            <div class=\"letter-wrap\">\r\n                                                <h3 class=\"h6\">Univerzalna identifikacija stola</h3>\r\n                                                <span class=\"color-gray fn14\">Inteligentna pretvorba slika tablica u Excel datoteke, automatska obrada složenih struktura tablica i spojenih ćelija</span>\r\n                                            </div>\r\n                                        </a>\r\n                                        <a href=\"Ocr.aspx?type=handwritten_ocr\" class=\"pro-item color-default\">\r\n                                            <div class=\"letter-wrap\">\r\n                                                <h3 class=\"h6\">Prepoznavanje rukopisa</h3>\r\n                                                <span class=\"color-gray fn14\">Inteligentno prepoznavanje kineskog i engleskog rukom pisanog sadržaja, bilješke iz učionice, medicinska dokumentacija i drugi scenariji</span>\r\n                                            </div>\r\n                                        </a>\r\n                                        <a href=\"Ocr.aspx?type=pdf2word\" class=\"pro-item color-default\">\r\n                                            <div class=\"letter-wrap\">\r\n                                                <h3 class=\"h6\">PDF u Word</h3>\r\n                                                <span class=\"color-gray fn14\">PDF dokumenti se brzo pretvaraju u Word format, savršeno čuvajući izvorni izgled i grafički izgled</span>\r\n                                            </div>\r\n                                        </a>\r\n                                    </div>\r\n                                </div>\r\n                            </div>\r\n                        </div>\r\n                    </section>\r\n                    <section id=\"experience-sub-nav\" class=\"pls-nav-dropdown solution-part\" style=\"visibility: hidden; height: 0px; display: none;\">\r\n                        <div class=\"d-flex nav-drown-con\" style=\"height: 0px; transition: transform 0.2s ease-in-out 0s,opacity 0.2s ease-in-out 0s; opacity: 0; transform: translate(0px, -100%);\">\r\n                            <div class=\"prdocu-sub-left\">\r\n                                <div class=\"mr-auto\">\r\n                                    <picture>\r\n                                        <source srcset=\"site/image/icon/p_4.png.webp\" type=\"image/webp\" />\r\n                                        <img class=\"p-icon\" src=\"site/image/icon/p_4.png\" alt=\"Ikona Online OCR Experience Center\" aria-hidden=\"true\" loading=\"lazy\" />\r\n                                    </picture>\r\n                                    <div class=\"mt-4\">\r\n                                        <h3 class=\"h6\">OCR pomoćnik za prepoznavanje teksta</h3>\r\n                                        <p class=\"color-gray fn14 mt-3 sub-left-desc\">Tekst, tablice, formule, dokumenti, prijevodi</p>\r\n                                        <p class=\"color-gray fn14 mt-3 sub-left-desc\">&nbsp;</p>\r\n                                        <p class=\"color-gray fn14 mt-3 sub-left-desc\">Ispunite sve svoje potrebe za obradom teksta u tri koraka</p>\r\n                                        <p class=\"color-gray fn14 mt-3 sub-left-desc\">Snimka zaslona → Identificirajte → aplikacije</p>\r\n                                        <p class=\"color-gray fn14 mt-3 sub-left-desc\">Povećajte radnu učinkovitost za 300%</p>\r\n                                        <a class=\"link block disa fn14 contact-business shake\" href=\"Ocr.aspx\">Probaj sada<span class=\"iconfont4 icon-xiangzuo gt\"></span></a>\r\n                                    </div>\r\n                                </div>\r\n                            </div>\r\n                            <div class=\"bg-white prdocu-sub-right\">\r\n                                <div>\r\n                                    <div class=\"mt-4 d-flex flex-wrap\">\r\n                                        <div style=\"width: 100%; margin-bottom: 15px;\">\r\n                                            <h4 class=\"h6\" style=\"color: #007cfa; margin-bottom: 10px;\">Iskustvo OCR funkcije</h4>\r\n                                        </div>\r\n                                        <a href=\"Ocr.aspx?type=index\" class=\"pro-item color-default\">\r\n                                            <div class=\"letter-wrap\">\r\n                                                <h3 class=\"h6\">Potpuna funkcionalnost</h3>\r\n                                                <span class=\"color-gray fn14\">Iskusite sve OCR pametne značajke na jednom mjestu kako biste brzo pronašli najbolje rješenje za svoje potrebe</span>\r\n                                            </div>\r\n                                        </a>\r\n                                        <a href=\"Ocr.aspx?type=text_recognize\" class=\"pro-item color-default\">\r\n                                            <div class=\"letter-wrap\">\r\n                                                <h3 class=\"h6\">Univerzalno prepoznavanje znakova</h3>\r\n                                                <span class=\"color-gray fn14\">Inteligentno izdvajanje višejezičnog teksta visoke preciznosti, podržavajući prepoznavanje ispisanih i složenih slika na više scena</span>\r\n                                            </div>\r\n                                        </a>\r\n                                        <a href=\"Ocr.aspx?type=table\" class=\"pro-item color-default\">\r\n                                            <div class=\"letter-wrap\">\r\n                                                <h3 class=\"h6\">Univerzalna identifikacija stola</h3>\r\n                                                <span class=\"color-gray fn14\">Inteligentna pretvorba slika tablica u Excel datoteke, automatska obrada složenih struktura tablica i spojenih ćelija</span>\r\n                                            </div>\r\n                                        </a>\r\n                                        <a href=\"Ocr.aspx?type=handwritten_ocr\" class=\"pro-item color-default\">\r\n                                            <div class=\"letter-wrap\">\r\n                                                <h3 class=\"h6\">Prepoznavanje rukopisa</h3>\r\n                                                <span class=\"color-gray fn14\">Inteligentno prepoznavanje kineskog i engleskog rukom pisanog sadržaja, bilješke iz učionice, medicinska dokumentacija i drugi scenariji</span>\r\n                                            </div>\r\n                                        </a>\r\n                                        <a href=\"Ocr.aspx?type=pdf2word\" class=\"pro-item color-default\">\r\n                                            <div class=\"letter-wrap\">\r\n                                                <h3 class=\"h6\">PDF u Word</h3>\r\n                                                <span class=\"color-gray fn14\">PDF dokumenti se brzo pretvaraju u Word format, savršeno čuvajući izvorni izgled i grafički izgled</span>\r\n                                            </div>\r\n                                        </a>\r\n                                        <a href=\"Ocr.aspx?type=pdf2markdown\" class=\"pro-item color-default\">\r\n                                            <div class=\"letter-wrap\">\r\n                                                <h3 class=\"h6\">PDF u Markdown</h3>\r\n                                                <span class=\"color-gray fn14\">PDF dokumenti se inteligentno pretvaraju u MD format, a blokovi koda i strukture teksta automatski se optimiziraju za obradu</span>\r\n                                            </div>\r\n                                        </a>\r\n                                        <div style=\"width: 100%; margin: 15px 0;\">\r\n                                            <h4 class=\"h6\" style=\"color: #007cfa; margin-bottom: 10px;\">Alati za obradu dokumenata</h4>\r\n                                        </div>\r\n                                        <a href=\"Ocr.aspx?type=word2pdf\" class=\"pro-item color-default\">\r\n                                            <div class=\"letter-wrap\">\r\n                                                <h3 class=\"h6\">Word u PDF</h3>\r\n                                                <span class=\"color-gray fn14\">Word dokumenti pretvaraju se u PDF jednim klikom, savršeno zadržavajući izvorni format, pogodan za arhiviranje i službeno dijeljenje dokumenata</span>\r\n                                            </div>\r\n                                        </a>\r\n                                        <a href=\"Ocr.aspx?type=word2jpg\" class=\"pro-item color-default\">\r\n                                            <div class=\"letter-wrap\">\r\n                                                <h3 class=\"h6\">Od riječi do slike</h3>\r\n                                                <span class=\"color-gray fn14\">Inteligentna pretvorba Word dokumenta u JPG sliku, podržava obradu više stranica, lako se dijeli na društvenim mrežama</span>\r\n                                            </div>\r\n                                        </a>\r\n                                        <a href=\"Ocr.aspx?type=pdf2jpg\" class=\"pro-item color-default\">\r\n                                            <div class=\"letter-wrap\">\r\n                                                <h3 class=\"h6\">PDF u sliku</h3>\r\n                                                <span class=\"color-gray fn14\">Pretvorite PDF dokumente u JPG slike u visokoj razlučivosti, podržite skupnu obradu i prilagođenu razlučivost</span>\r\n                                            </div>\r\n                                        </a>\r\n                                        <a href=\"Ocr.aspx?type=image2pdf\" class=\"pro-item color-default\">\r\n                                            <div class=\"letter-wrap\">\r\n                                                <h3 class=\"h6\">Slika u PDF</h3>\r\n                                                <span class=\"color-gray fn14\">Spojite više slika u PDF dokumente, podržite sortiranje i postavljanje stranica</span>\r\n                                            </div>\r\n                                        </a>\r\n                                        <div style=\"width: 100%; margin: 15px 0;\">\r\n                                            <h4 class=\"h6\" style=\"color: #007cfa; margin-bottom: 10px;\">Alati za razvojne programere</h4>\r\n                                        </div>\r\n                                        <a href=\"Tool.aspx?type=Json\" class=\"pro-item color-default\">\r\n                                            <div class=\"letter-wrap\">\r\n                                                <h3 class=\"h6\">JSON oblikovanje</h3>\r\n                                                <span class=\"color-gray fn14\">Inteligentno uljepšajte strukturu JSON koda, podržite kompresiju i proširenje te olakšajte razvoj i otklanjanje pogrešaka</span>\r\n                                            </div>\r\n                                        </a>\r\n                                        <a href=\"Tool.aspx?type=Regex\" class=\"pro-item color-default\">\r\n                                            <div class=\"letter-wrap\">\r\n                                                <h3 class=\"h6\">Regularni izraz</h3>\r\n                                                <span class=\"color-gray fn14\">Provjerite efekte podudaranja regularnih izraza u stvarnom vremenu uz ugrađenu biblioteku uobičajenih uzoraka</span>\r\n                                            </div>\r\n                                        </a>\r\n                                        <a href=\"Tool.aspx?type=Encode\" class=\"pro-item color-default\">\r\n                                            <div class=\"letter-wrap\">\r\n                                                <h3 class=\"h6\">Pretvorba kodiranja teksta</h3>\r\n                                                <span class=\"color-gray fn14\">Podržava pretvorbu više formata kodiranja kao što su Base64, URL i Unicode</span>\r\n                                            </div>\r\n                                        </a>\r\n                                        <a href=\"Tool.aspx?type=Diff\" class=\"pro-item color-default\">\r\n                                            <div class=\"letter-wrap\">\r\n                                                <h3 class=\"h6\">Podudaranje i spajanje teksta</h3>\r\n                                                <span class=\"color-gray fn14\">Istaknite razlike u tekstu i podržite usporedbu redak po redak i inteligentno spajanje</span>\r\n                                            </div>\r\n                                        </a>\r\n                                        <a href=\"Tool.aspx?type=Color\" class=\"pro-item color-default\">\r\n                                            <div class=\"letter-wrap\">\r\n                                                <h3 class=\"h6\">Alat za boju</h3>\r\n                                                <span class=\"color-gray fn14\">RGB/HEX pretvorba boja, mrežni alat za odabir boja, neophodan alat za front-end razvoj</span>\r\n                                            </div>\r\n                                        </a>\r\n                                        <a href=\"Tool.aspx?type=Count\" class=\"pro-item color-default\">\r\n                                            <div class=\"letter-wrap\">\r\n                                                <h3 class=\"h6\">Broj riječi</h3>\r\n                                                <span class=\"color-gray fn14\">Inteligentno brojanje znakova, vokabulara i odlomaka te automatska optimizacija rasporeda teksta</span>\r\n                                            </div>\r\n                                        </a>\r\n                                        <a href=\"Tool.aspx?type=Timespan\" class=\"pro-item color-default\">\r\n                                            <div class=\"letter-wrap\">\r\n                                                <h3 class=\"h6\">Pretvorba vremenske oznake</h3>\r\n                                                <span class=\"color-gray fn14\">Vrijeme se pretvara u i iz Unix vremenskih oznaka, a podržani su višestruki formati i postavke vremenske zone</span>\r\n                                            </div>\r\n                                        </a>\r\n                                        <a href=\"Tool.aspx?type=Calc\" class=\"pro-item color-default\">\r\n                                            <div class=\"letter-wrap\">\r\n                                                <h3 class=\"h6\">Alat za kalkulator</h3>\r\n                                                <span class=\"color-gray fn14\">Online znanstveni kalkulator s podrškom za osnovne operacije i napredne izračune matematičkih funkcija</span>\r\n                                            </div>\r\n                                        </a>\r\n                                    </div>\r\n                                </div>\r\n                            </div>\r\n                        </div>\r\n                    </section>\r\n                    <section id=\"articles-sub-nav\" class=\"pls-nav-dropdown solution-part\" style=\"visibility: hidden; height: 0px; display: none;\">\r\n                        <div class=\"d-flex nav-drown-con\" style=\"height: 0px; transition: transform 0.2s ease-in-out 0s,opacity 0.2s ease-in-out 0s; opacity: 0; transform: translate(0px, -100%);\">\r\n                            <div class=\"prdocu-sub-left\">\r\n                                <div class=\"mr-auto\">\r\n                                    <picture>\r\n                                        <source srcset=\"site/image/icon/p_4.png.webp\" type=\"image/webp\" />\r\n                                        <img class=\"p-icon\" src=\"site/image/icon/p_4.png\" alt=\"Ikona centra za zajedničko korištenje tehnologije\" aria-hidden=\"true\" loading=\"lazy\" />\r\n                                    </picture>\r\n                                    <div class=\"mt-4\">\r\n                                        <h3 class=\"h6\">Dijeljenje OCR tehnologije</h3>\r\n                                        <p class=\"color-gray fn14 mt-3 sub-left-desc\">Tehnički vodiči, slučajevi primjene, preporuke alata</p>\r\n                                        <p class=\"color-gray fn14 mt-3 sub-left-desc\">&nbsp;</p>\r\n                                        <p class=\"color-gray fn14 mt-3 sub-left-desc\">Potpuni put učenja od početnika do majstorstva</p>\r\n                                        <p class=\"color-gray fn14 mt-3 sub-left-desc\">Praktični slučajevi → tehničku analizu → primjene alata</p>\r\n                                        <p class=\"color-gray fn14 mt-3 sub-left-desc\">Osnažite svoj put do poboljšanja OCR tehnologije</p>\r\n                                        <a class=\"link block disa fn14 contact-business shake\" href=\"Articles.aspx\">Pregledajte članke<span class=\"iconfont4 icon-xiangzuo gt\"></span></a>\r\n                                    </div>\r\n                                </div>\r\n                            </div>\r\n                            <div class=\"bg-white prdocu-sub-right\">\r\n                                <div>\r\n                                    <h2 class=\"text-base-color h6\">Dijeljenje tehnologije</h2>\r\n                                    <div class=\"mt-4 d-flex flex-wrap\">\r\n                                        <a href=\"Articles.aspx\" class=\"pro-item color-default\" title=\"Pogledajte sve OCR tehničke članke\">\r\n                                            <div class=\"letter-wrap\">\r\n                                                <h3 class=\"h6\">Svi članci</h3>\r\n                                                <span class=\"color-gray fn14\">Pregledajte sve OCR tehničke članke koji pokrivaju cjelokupno znanje od osnovnog do naprednog</span>\r\n                                            </div>\r\n                                        </a>\r\n                                        \r\n                                        <a href=\"Articles.aspx?category=2\" class=\"pro-item color-default\" title=\"OCR tehnički vodiči i vodiči za početak rada\">\r\n                                            <div class=\"letter-wrap\">\r\n                                                <h3 class=\"h6\">Napredni vodič</h3>\r\n                                                <span class=\"color-gray fn14\">Od uvodnih do stručnih tehničkih vodiča za OCR, detaljnih vodiča i praktičnih vodiča</span>\r\n                                            </div>\r\n                                        </a>\r\n                                        \r\n                                        <a href=\"Articles.aspx?category=3\" class=\"pro-item color-default\" title=\"Principi, algoritmi i primjene OCR tehnologije\">\r\n                                            <div class=\"letter-wrap\">\r\n                                                <h3 class=\"h6\">Tehnološko istraživanje</h3>\r\n                                                <span class=\"color-gray fn14\">Istražite granice OCR tehnologije, od principa do aplikacija, i dubinski analizirajte temeljne algoritme</span>\r\n                                            </div>\r\n                                        </a>\r\n                                        \r\n                                        <a href=\"Articles.aspx?category=4\" class=\"pro-item color-default\" title=\"Najnoviji razvoj i razvojni trendovi u OCR industriji\">\r\n                                            <div class=\"letter-wrap\">\r\n                                                <h3 class=\"h6\">Trendovi u industriji</h3>\r\n                                                <span class=\"color-gray fn14\">Dubinski uvid u trendove razvoja OCR tehnologije, analizu tržišta, dinamiku industrije i buduće izglede</span>\r\n                                            </div>\r\n                                        </a>\r\n                                        \r\n                                        <a href=\"Articles.aspx?category=5\" class=\"pro-item color-default\" title=\"Slučajevi primjene OCR tehnologije u raznim industrijama\">\r\n                                            <div class=\"letter-wrap\">\r\n                                                <h3 class=\"h6\">Slučajevi upotrebe:</h3>\r\n                                                <span class=\"color-gray fn14\">Dijele se slučajevi primjene, rješenja i najbolje prakse OCR tehnologije u stvarnom svijetu u različitim industrijama</span>\r\n                                            </div>\r\n                                        </a>\r\n                                        \r\n                                        <a href=\"Articles.aspx?category=6\" class=\"pro-item color-default\" title=\"Stručne recenzije, usporedna analiza i preporučene smjernice za korištenje OCR softverskih alata\">\r\n                                            <div class=\"letter-wrap\">\r\n                                                <h3 class=\"h6\">Pregled alata</h3>\r\n                                                <span class=\"color-gray fn14\">Procijenite različite OCR softvere i alate za prepoznavanje teksta i pružite detaljnu usporedbu funkcija i prijedloge za odabir</span>\r\n                                            </div>\r\n                                        </a>\r\n                                        \r\n                                    </div>\r\n                                </div>\r\n                            </div>\r\n                        </div>\r\n                    </section>\r\n                    <section id=\"vip-sub-nav\" class=\"pls-nav-dropdown solution-part\" style=\"visibility: hidden; height: 0px; display: none;\">\r\n                        <div class=\"d-flex nav-drown-con\" style=\"height: 0px; transition: transform 0.2s ease-in-out 0s,opacity 0.2s ease-in-out 0s; opacity: 0; transform: translate(0px, -100%);\">\r\n                            <div class=\"prdocu-sub-left\">\r\n                                <div class=\"mr-auto\">\r\n                                    <picture>\r\n                                        <source class=\"p-icon\" srcset=\"site/image/icon/p_1.png.webp\" type=\"image/webp\" alt=\"\" aria-hidden=\"true\" />\r\n                                        <img class=\"p-icon\" src=\"site/image/icon/p_1.png\" alt=\"Ikona usluge nadogradnje članstva\" aria-hidden=\"true\" loading=\"lazy\" />\r\n                                    </picture>\r\n                                    <div class=\"mt-4\">\r\n                                        <h3 class=\"h6\">Usluga nadogradnje članstva</h3>\r\n                                        <p class=\"color-gray fn14 mt-3 sub-left-desc\">Otključajte sve premium značajke i uživajte u ekskluzivnim uslugama</p>\r\n                                        <p class=\"color-gray fn14 mt-3 sub-left-desc\">&nbsp;</p>\r\n                                        <p class=\"color-gray fn14 mt-3 sub-left-desc\">Izvanmrežno prepoznavanje, skupna obrada, neograničena upotreba</p>\r\n                                        <p class=\"color-gray fn14 mt-3 sub-left-desc\">Pro → Ultimate → Enterprise</p>\r\n                                        <p class=\"color-gray fn14 mt-3 sub-left-desc\">Ima nešto za vaše potrebe</p>\r\n                                        <a class=\"link block disa fn14 contact-business shake\" href=\"Version.aspx\">Pogledajte detalje<span class=\"iconfont4 icon-xiangzuo gt\"></span></a>\r\n                                    </div>\r\n                                </div>\r\n                            </div>\r\n                            <div class=\"bg-white prdocu-sub-right\">\r\n                                <div>\r\n                                    <h2 class=\"text-base-color h6\" style=\"margin: inherit;\">Nadogradnje članstva</h2>\r\n                                    <div class=\"mt-4 d-flex flex-wrap\">\r\n                                        <a href=\"Version.aspx\" class=\"pro-item color-default\">\r\n                                            <div class=\"letter-wrap\">\r\n                                                <h3 class=\"h6\">Privilegije članstva</h3>\r\n                                                <span class=\"color-gray fn14\">Saznajte više o razlikama između izdanja i odaberite razinu članstva koja vam najviše odgovara</span>\r\n                                            </div>\r\n                                        </a>\r\n                                        <a href=\"Upgrade.aspx\" class=\"pro-item color-default\">\r\n                                            <div class=\"letter-wrap\">\r\n                                                <h3 class=\"h6\">Nadogradite sada</h3>\r\n                                                <span class=\"color-gray fn14\">Brzo nadogradite svoje VIP članstvo kako biste otključali više premium značajki i ekskluzivnih usluga</span>\r\n                                            </div>\r\n                                        </a>\r\n                                        <a href=\"User.aspx\" class=\"pro-item color-default\">\r\n                                            <div class=\"letter-wrap\">\r\n                                                <h3 class=\"h6\">Moj račun</h3>\r\n                                                <span class=\"color-gray fn14\">Upravljanje podacima o računu, statusom pretplate i poviješću korištenja radi personalizacije postavki</span>\r\n                                            </div>\r\n                                        </a>\r\n                                    </div>\r\n                                </div>\r\n                            </div>\r\n                        </div>\r\n                    </section>\r\n                    <section id=\"support-sub-nav\" class=\"pls-nav-dropdown solution-part\" style=\"visibility: hidden; height: 0px; display: none;\">\r\n                        <div class=\"d-flex nav-drown-con\" style=\"height: 0px; transition: transform 0.2s ease-in-out 0s,opacity 0.2s ease-in-out 0s; opacity: 0; transform: translate(0px, -100%);\">\r\n                            <div class=\"prdocu-sub-left\">\r\n                                <div class=\"mr-auto\">\r\n                                    <picture>\r\n                                        <source class=\"p-icon\" srcset=\"site/image/icon/p_4.png.webp\" type=\"image/webp\" alt=\"\" aria-hidden=\"true\" />\r\n                                        <img class=\"p-icon\" src=\"site/image/icon/p_4.png\" alt=\"Ikona podrške centra za pomoć\" aria-hidden=\"true\" loading=\"lazy\" />\r\n                                    </picture>\r\n                                    <div class=\"mt-4\">\r\n                                        <h3 class=\"h6\">Centar za pomoć</h3>\r\n                                        <p class=\"color-gray fn14 mt-3 sub-left-desc\">Profesionalna korisnička služba, detaljna dokumentacija i brz odgovor</p>\r\n                                        <p class=\"color-gray fn14 mt-3 sub-left-desc\">&nbsp;</p>\r\n                                        <p class=\"color-gray fn14 mt-3 sub-left-desc\">Nemojte paničariti kada naiđete na probleme</p>\r\n                                        <p class=\"color-gray fn14 mt-3 sub-left-desc\">Problem → pronaći → riješen</p>\r\n                                        <p class=\"color-gray fn14 mt-3 sub-left-desc\">Učinite svoje iskustvo lakšim</p>\r\n                                        <a class=\"link block disa fn14 contact-business shake\" href=\"FAQ.aspx\">Zatražite pomoć<span class=\"iconfont4 icon-xiangzuo gt\"></span></a>\r\n                                    </div>\r\n                                </div>\r\n                            </div>\r\n                            <div class=\"bg-white prdocu-sub-right\">\r\n                                <div>\r\n                                    <h2 class=\"text-base-color h6\" style=\"margin: inherit;\">Centar za pomoć</h2>\r\n                                    <div class=\"mt-4 d-flex flex-wrap\">\r\n                                        <a href=\"FAQ.aspx\" class=\"pro-item color-default\">\r\n                                            <div class=\"letter-wrap\">\r\n                                                <h3 class=\"h6\">Često postavljana pitanja</h3>\r\n                                                <span class=\"color-gray fn14\">Brzo odgovorite na uobičajena pitanja korisnika i pružite detaljne vodiče za upotrebu i tehničku podršku</span>\r\n                                            </div>\r\n                                        </a>\r\n                                        <a href=\"AboutUs.aspx\" class=\"pro-item color-default\">\r\n                                            <div class=\"letter-wrap\">\r\n                                                <h3 class=\"h6\">O nama</h3>\r\n                                                <span class=\"color-gray fn14\">Saznajte više o povijesti razvoja, temeljnim funkcijama i konceptima usluga OCR pomoćnika za prepoznavanje teksta</span>\r\n                                            </div>\r\n                                        </a>\r\n                                        <a href=\"UserAgreement.aspx\" class=\"pro-item color-default\">\r\n                                            <div class=\"letter-wrap\">\r\n                                                <h3 class=\"h6\">Korisnički ugovor</h3>\r\n                                                <span class=\"color-gray fn14\">Detaljni uvjeti pružanja usluge te prava i obveze korisnika</span>\r\n                                            </div>\r\n                                        </a>\r\n                                        <a href=\"PrivacyPolicy.aspx\" class=\"pro-item color-default\">\r\n                                            <div class=\"letter-wrap\">\r\n                                                <h3 class=\"h6\">Ugovor o privatnosti</h3>\r\n                                                <span class=\"color-gray fn14\">Politika zaštite osobnih podataka i mjere sigurnosti podataka</span>\r\n                                            </div>\r\n                                        </a>\r\n                                        <a href=\"Status.aspx\" class=\"pro-item color-default\">\r\n                                            <div class=\"letter-wrap\">\r\n                                                <h3 class=\"h6\">Status sustava</h3>\r\n                                                <span class=\"color-gray fn14\">Pratite radni status globalnih identifikacijskih čvorova u stvarnom vremenu i pregledajte podatke o performansama sustava</span>\r\n                                            </div>\r\n                                        </a>\r\n                                        <a href=\"javascript:void(0);\" class=\"pro-item color-default\" onclick=\"if(window.openCustomerService) window.openCustomerService(); else alert('Kliknite ikonu plutajućeg prozora s desne strane da biste kontaktirali korisničku službu');\">\r\n                                            <div class=\"letter-wrap\">\r\n                                                <h3 class=\"h6\">Obratite se korisničkoj službi</h3>\r\n                                                <span class=\"color-gray fn14\">Internetska korisnička podrška za brz odgovor na vaša pitanja i potrebe</span>\r\n                                            </div>\r\n                                        </a>\r\n                                    </div>\r\n                                </div>\r\n                            </div>\r\n                        </div>\r\n                    </section>\r\n                </div>\r\n            </div>\r\n            <div class=\"header_pc_right\" style=\"display: flex; align-items: center; justify-content: flex-end;\">\r\n                <div id=\"translate\"></div>\r\n                <div id=\"user-login-container\" data-url=\"User.ashx?op=userinfo\" style=\"white-space: nowrap; padding-right: 20px;\"></div>\r\n            </div>\r\n        </header>\r\n        <header class=\"v4_header_mob\">\r\n            <div class=\"container\">\r\n                <div class=\"row no-gutters justify-content-between align-items-center\">\r\n                    <a href=\"Default.aspx\" title=\"Naslovnica | AI inteligentno prepoznavanje teksta\" class=\"mob-logo-wrap\">\r\n                        <picture>\r\n                            <source srcset=\"site/image/logo-s.png.webp\" type=\"image/webp\" />\r\n                            <img src=\"site/image/logo-s.png\" class=\"pc-logo\" style=\"height: 32px; width: 32px\" alt=\"OCR mobilni logotip pomoćnika za prepoznavanje teksta\" />\r\n                        </picture>\r\n                        <span style=\"font-size: 18px; font-weight: bold; color: #333;\">OCR pomoćnik za prepoznavanje teksta</span>\r\n                    </a>\r\n                    <div class=\"right-menu\" role=\"button\" tabindex=\"0\" aria-label=\"Otvaranje navigacijskog izbornika\" data-toggle=\"collapse\" data-target=\"#mob-header-collapse\" aria-expanded=\"false\" aria-controls=\"mob-header-collapse\">\r\n                        <nav-products id=\"nav_products\">\r\n                            <nav-button class=\"submail-mdi-nav-btn\" style=\"width: 60px\">\r\n                                <label id=\"mdi-submail-nav-btn\">\r\n                                    <span class=\"mdi-nav-products-1\">\r\n                                        <span class=\"nav-products-icon\"></span>\r\n                                    </span>\r\n                                    <span class=\"mdi-nav-products-2\">\r\n                                        <span class=\"nav-products-icon\"></span>\r\n                                    </span>\r\n                                    <span class=\"mdi-nav-products-3\">\r\n                                        <span class=\"nav-products-icon\"></span>\r\n                                    </span>\r\n                                </label>\r\n                            </nav-button>\r\n                        </nav-products>\r\n                    </div>\r\n                </div>\r\n            </div>\r\n            <div class=\"mob-nav-content\">\r\n                <div class=\"sidebar-fix\">\r\n                    <div class=\"container\">\r\n                        <div class=\"row\">\r\n                            <div class=\"col-4 bg-white sidebar-fix-left\" style=\"border-right: 1px solid #e1e6ed\">\r\n                                <div class=\"mob-nav-item active\" data=\"0\">\r\n                                    <div class=\"nav-header\"><a href=\"Default.aspx\" class=\"nav-header-letter px-3\"><span>Dom</span></a></div>\r\n                                </div>\r\n                                <div class=\"mob-nav-item\" data=\"1\">\r\n                                    <div class=\"nav-header\"><a href=\"Detail.aspx\" class=\"nav-header-letter px-3\"><span>funkcija</span></a></div>\r\n                                </div>\r\n                                <div class=\"mob-nav-item\" data=\"2\">\r\n                                    <div class=\"nav-header\"><a href=\"Ocr.aspx\" class=\"nav-header-letter px-3\"><span>iskustvo</span></a></div>\r\n                                </div>\r\n                                <div class=\"mob-nav-item\" data=\"3\">\r\n                                    <div class=\"nav-header\"><a href=\"Version.aspx\" class=\"nav-header-letter px-3\"><span>član</span></a></div>\r\n                                </div>\r\n                                <div class=\"mob-nav-item\" data=\"4\">\r\n                                    <div class=\"nav-header\"><a href=\"Download.aspx\" class=\"nav-header-letter px-3\"><span>Preuzimanje</span></a></div>\r\n                                </div>\r\n                                <div class=\"mob-nav-item\" data=\"5\">\r\n                                    <div class=\"nav-header\"><a href=\"Articles.aspx\" class=\"nav-header-letter px-3\"><span>dijeliti</span></a></div>\r\n                                </div>\r\n                                <div class=\"mob-nav-item\" data=\"6\">\r\n                                    <div class=\"nav-header\"><a href=\"FAQ.aspx\" class=\"nav-header-letter px-3\"><span>Pomoć</span></a></div>\r\n                                </div>\r\n                            </div>\r\n                            <div class=\"col-8 bg-white sidebar-fix-rigth\">\r\n                                <div class=\"sub-nav\">\r\n                                    <div class=\"sub-nav-item py-2 pb-3 px-4\">\r\n                                        <h2 class=\"h5\" style=\"margin: inherit;\">Učinkoviti alati za produktivnost</h2>\r\n                                        <p class=\"color-gray fn14 mt-3 sub-left-desc\">Inteligentno prepoznavanje, brza obrada i točan izlaz</p>\r\n                                        <p class=\"color-gray fn14 mt-3 sub-left-desc\">&nbsp;</p>\r\n                                        <p class=\"color-gray fn14 mt-3 sub-left-desc\">Prepoznavanje cijele stranice dokumenata u 3 sekunde</p>\r\n                                        <p class=\"color-gray fn14 mt-3 sub-left-desc\">98%+ točnost prepoznavanja</p>\r\n                                        <p class=\"color-gray fn14 mt-3 sub-left-desc\">Višejezična obrada u stvarnom vremenu bez odgađanja</p>\r\n                                        <a class=\"link block disa fn14 contact-business shake\" href=\"Download.aspx\">Preuzmite iskustvo odmah<span class=\"iconfont4 icon-xiangzuo gt\"></span></a>\r\n                                    </div>\r\n                                </div>\r\n                                <div class=\"sub-nav\">\r\n                                    <div class=\"sub-nav-item py-3 px-4\">\r\n                                        <h2 class=\"h5\" style=\"margin: inherit;\">Značajke proizvoda:</h2>\r\n                                        <p class=\"color-gray fn14 mt-3 sub-left-desc\">AI inteligentna identifikacija, rješenje na jednom mjestu</p>\r\n                                    </div>\r\n                                    <div class=\"sub-nav-item py-3 px-4\"><a href=\"Detail.aspx\">Uvod u funkciju</a></div>\r\n                                    <div class=\"sub-nav-item py-3 px-4\"><a href=\"Download.aspx\">Preuzimanje softvera</a></div>\r\n                                    <div class=\"sub-nav-item py-3 px-4\"><a href=\"Version.aspx\">Usporedba verzija</a></div>\r\n                                    <div class=\"sub-nav-item py-3 px-4\"><a href=\"Ocr.aspx\">Online iskustvo</a></div>\r\n                                    <div class=\"sub-nav-item py-3 px-4\"><a href=\"Status.aspx\">Status sustava</a></div>\r\n                                </div>\r\n                                <div class=\"sub-nav\">\r\n                                    <div class=\"sub-nav-item py-3 px-4\">\r\n                                        <h2 class=\"h5\" style=\"margin: inherit;\">Online iskustvo</h2>\r\n                                        <p class=\"color-gray fn14 mt-3 sub-left-desc\">Besplatno iskustvo online OCR funkcije</p>\r\n                                    </div>\r\n                                    <div class=\"sub-nav-item py-3 px-4\"><a href=\"Ocr.aspx\">Potpuna funkcionalnost</a></div>\r\n                                    <div class=\"sub-nav-item py-3 px-4\"><a href=\"Ocr.aspx?type=text_recognize\">Prepoznavanje riječi</a></div>\r\n                                    <div class=\"sub-nav-item py-3 px-4\"><a href=\"Ocr.aspx?type=table\">Identifikacija tablice</a></div>\r\n                                    <div class=\"sub-nav-item py-3 px-4\"><a href=\"Ocr.aspx?type=pdf2word\">PDF u Word</a></div>\r\n                                </div>\r\n                                <div class=\"sub-nav\">\r\n                                    <div class=\"sub-nav-item py-3 px-4\">\r\n                                        <h2 class=\"h5\" style=\"margin: inherit;\">Nadogradnje članstva</h2>\r\n                                        <p class=\"color-gray fn14 mt-3 sub-left-desc\">Otključajte sve značajke i uživajte u ekskluzivnim uslugama</p>\r\n                                    </div>\r\n                                    <div class=\"sub-nav-item py-3 px-4\"><a href=\"Version.aspx\">Pogodnosti članstva</a></div>\r\n                                    <div class=\"sub-nav-item py-3 px-4\"><a href=\"Upgrade.aspx\">Aktivirajte odmah</a></div>\r\n                                </div>\r\n                                <div class=\"sub-nav\">\r\n                                    <div class=\"sub-nav-item py-3 px-4\">\r\n                                        <h2 class=\"h5\" style=\"margin: inherit;\">Preuzimanje softvera</h2>\r\n                                        <p class=\"color-gray fn14 mt-3 sub-left-desc\">Besplatno preuzmite profesionalni OCR softver</p>\r\n                                    </div>\r\n                                    <div class=\"sub-nav-item py-3 px-4\"><a href=\"Download.aspx\">Preuzmite odmah</a></div>\r\n                                    <div class=\"sub-nav-item py-3 px-4\"><a href=\"Version.aspx\">Usporedba verzija</a></div>\r\n                                </div>\r\n                                <div class=\"sub-nav\">\r\n                                    <div class=\"sub-nav-item py-3 px-4\">\r\n                                        <h2 class=\"h5\" style=\"margin: inherit;\">Dijeljenje tehnologije</h2>\r\n                                        <p class=\"color-gray fn14 mt-3 sub-left-desc\">OCR tehnički članci i dijeljenje znanja</p>\r\n                                    </div>\r\n                                    <div class=\"sub-nav-item py-3 px-4\"><a href=\"Articles.aspx\">Svi članci</a></div>\r\n                                    \r\n                                    <div class=\"sub-nav-item py-3 px-4\"><a href=\"Articles.aspx?category=2\">Napredni vodič</a></div>\r\n                                    \r\n                                    <div class=\"sub-nav-item py-3 px-4\"><a href=\"Articles.aspx?category=3\">Tehnološko istraživanje</a></div>\r\n                                    \r\n                                    <div class=\"sub-nav-item py-3 px-4\"><a href=\"Articles.aspx?category=4\">Trendovi u industriji</a></div>\r\n                                    \r\n                                    <div class=\"sub-nav-item py-3 px-4\"><a href=\"Articles.aspx?category=5\">Slučajevi upotrebe:</a></div>\r\n                                    \r\n                                    <div class=\"sub-nav-item py-3 px-4\"><a href=\"Articles.aspx?category=6\">Pregled alata</a></div>\r\n                                    \r\n                                </div>\r\n                                <div class=\"sub-nav\">\r\n                                    <div class=\"sub-nav-item py-3 px-4\">\r\n                                        <h2 class=\"h5\" style=\"margin: inherit;\">Centar za pomoć</h2>\r\n                                        <p class=\"color-gray fn14 mt-3 sub-left-desc\">Profesionalna korisnička služba, intimna usluga</p>\r\n                                    </div>\r\n                                    <div class=\"sub-nav-item py-3 px-4\"><a href=\"FAQ.aspx\">Upotreba pomoći</a></div>\r\n                                    <div class=\"sub-nav-item py-3 px-4\"><a href=\"AboutUs.aspx\">O nama</a></div>\r\n                                    <div class=\"sub-nav-item py-3 px-4\"><a href=\"mailto:<EMAIL>\">Obratite se korisničkoj službi</a></div>\r\n                                    <div class=\"sub-nav-item py-3 px-4\"><a href=\"UserAgreement.aspx\">Uvjeti pružanja usluge</a></div>\r\n                                </div>\r\n                            </div>\r\n                        </div>\r\n                    </div>\r\n                </div>\r\n            </div>\r\n        </header>\r\n        <form method=\"post\" action=\"./Article.aspx?id=81&amp;slug=rnn-sequence-modeling&amp;lang=zh-Hans&amp;_translation_source=true\" id=\"aspnetForm\">\r\n<div class=\"aspNetHidden\">\r\n<input type=\"hidden\" name=\"__VIEWSTATE\" id=\"__VIEWSTATE\" value=\"Q9uUeUlUJtaRG68B9ZsvqxMA0nWMrG97wrg6XH6pF/PALpTNvdWvXETFldXabtYfiX/3jguNbdKAEx4O02Rz9ETQIoNHodxI3/t/Hx/oGKM=\" />\r\n</div>\r\n\r\n<div class=\"aspNetHidden\">\r\n\r\n\t<input type=\"hidden\" name=\"__VIEWSTATEGENERATOR\" id=\"__VIEWSTATEGENERATOR\" value=\"2173C2F0\" />\r\n</div>\r\n            <div>\r\n                \n\n<div class=\"reading-progress\" id=\"readingProgress\"></div>\n<div class=\"article-detail-container\">\n<div class=\"container\">\n<div class=\"row\">\n<div class=\"col-lg-8 col-xl-9 col-md-12\">\n                    \n                    <article class=\"article-detail\" id=\"articleDetail\" data-article-id=\"81\">\n                        <header class=\"article-header\">\n                            <h1 class=\"article-title\">【OCR serija dubokog učenja·4】 Rekurentne neuronske mreže i modeliranje sekvenci</h1>\n                            <div class=\"article-meta\">\n                                <div class=\"meta-item\">\n                                    <span class=\"meta-icon\">📅</span>\n                                    <span>Vrijeme objave: 2025-08-19</span>\n                                </div>\n                                <div class=\"meta-item\">\n                                    <span class=\"meta-icon\">👁️</span>\n                                    <span>Čitanje:<span class=\"view-count\">1200</span></span>\n                                </div>\n                                <div class=\"meta-item\">\n                                    <span class=\"meta-icon\">⏱️</span>\n                                    <span>50 minuta (9819 riječi)</span>\n                                </div>\n                                \n                                <div class=\"meta-item\">\n                                    <span class=\"meta-icon\">📁</span>\n                                    <span>Kategorija: Napredni vodiči</span>\n                                </div>\n                                \n                            </div>\n                            <div class=\"article-summary\"><p>Zaronite u primjenu RNN, LSTM, GRU u OCR-u. Detaljna analiza principa modeliranja sekvenci, rješenja gradijentnih problema i prednosti dvosmjernih RNN-ova.</p></div>\n                        </header>\n                        <div class=\"article-content\" id=\"articleContent\">## Uvod\r\n\r\nRekurentna neuronska mreža (RNN) je arhitektura neuronske mreže u dubokom učenju koja je specijalizirana za obradu podataka o sekvencama. U OCR zadatcima, prepoznavanje teksta u biti je problem pretvorbe slijeda u slijed: pretvaranje niza značajki slike u niz znakova teksta. Ovaj će članak istražiti kako RNN funkcionira, njegove glavne varijante i njegove specifične primjene u OCR-u, pružajući čitateljima sveobuhvatnu teorijsku osnovu i praktične smjernice.\r\n\r\n## Osnove RNN-a\r\n\r\n### Ograničenja tradicionalnih neuronskih mreža\r\n\r\nTradicionalne unaprijed neuronske mreže imaju temeljna ograničenja u obradi podataka o sekvencama. Te mreže pretpostavljaju da su ulazni podaci neovisni i homoraspodijeljeni te da ne mogu obuhvatiti vremenske ovisnosti između elemenata u nizu.\r\n\r\n**Problemi s mrežom za prosljeđivanje**:\r\n- Fiksna ulazna i izlazna duljina: Sekvence promjenjive duljine ne mogu se obraditi\r\n- Nedostatak sposobnosti pamćenja: Nemogućnost korištenja povijesnih podataka\r\n- Poteškoće u dijeljenju parametara: isti obrazac treba učiti više puta na različitim lokacijama\r\n- Osjetljivost položaja: Promjena redoslijeda ulaza može dovesti do potpuno različitih izlaza\r\n\r\nOva ograničenja su posebno uočljiva u OCR zadacima. Slijedovi teksta uvelike ovise o kontekstu, a rezultati prepoznavanja prethodnog znaka često pomažu u određivanju vjerojatnosti sljedećih znakova. Na primjer, kada identificirate englesku riječ \"the\", ako je \"th\" već prepoznato, tada će sljedeći znak vjerojatno biti \"e\".\r\n\r\n### Temeljna ideja RNN-a\r\n\r\nRNN rješava problem modeliranja sekvenci uvođenjem spajanja petlji. Osnovna ideja je dodati \"memorijski\" mehanizam u mrežu, tako da mreža može pohranjivati i koristiti informacije iz prethodnih trenutaka.\r\n\r\n**Matematički prikaz RNN-a**:\r\nU trenutku t, skriveno stanje RNN-a određeno h_t trenutnim ulaznim x_t i skrivenim stanjem prethodnog trenutka h_{t-1}:\r\n\r\nh_t = f(W_hh * h_{t-1} + W_xh * x_t + b_h)\r\n\r\nU to:\r\n- W_hh je matrica težine od skrivenog stanja do skrivenog stanja\r\n- W_xh je matrica težine unesena u skriveno stanje  \r\n- b_h je vektor pristranosti\r\n- f je aktivacijska funkcija (obično tanh ili ReLU)\r\n\r\nIzlazni y_t izračunava se iz trenutnog skrivenog stanja:\r\ny_t = W_hy * h_t + b_y\r\n\r\n**Prednosti RNN-a**:\r\n- Dijeljenje parametara: iste težine dijele se u svim vremenskim koracima\r\n- Obrada sekvenci promjenjive duljine: Može rukovati ulaznim sekvencama proizvoljne duljine\r\n- Sposobnost pamćenja: Skrivena stanja djeluju kao \"sjećanja\" mreže\r\n- Fleksibilan ulaz i izlaz: Podržava načine rada jedan-na-jedan, jedan-na-više, mnogo na jedan, mnogo na više i još mnogo\r\n\r\n### Prošireni prikaz RNN-a\r\n\r\nDa bismo bolje razumjeli kako RNN-ovi funkcioniraju, možemo ih proširiti u vremenskoj dimenziji. Prošireni RNN izgleda kao duboka mreža unaprijed, ali svi vremenski koraci dijele iste parametre.\r\n\r\n**Značaj odvijanja vremena**:\r\n- Lako razumljiv protok informacija: Moguće je jasno vidjeti kako se informacije prenose između vremenskih koraka\r\n- Izračun gradijenta: Gradijenti se izračunavaju pomoću algoritma povratnog širenja vremena (BPTT)\r\n- Razmatranja paralelizacije: Iako su RNN-ovi sami po sebi sekvencijalni, određene operacije mogu se paralelizirati\r\n\r\n**Matematički opis procesa odvijanja**:\r\nZa sekvence duljine T, RNN se širi na sljedeći način:\r\nh_1 = f(W_xh * x_1 + b_h)\r\nh_2 = f(W_hh * h_1 + W_xh * x_2 + b_h)\r\nh_3 = f(W_hh * h_2 + W_xh * x_3 + b_h)\r\n...\r\nh_T = f(W_hh * h_{T-1} + W_xh * x_T + b_h)\r\n\r\nOvaj rasklopljeni obrazac jasno pokazuje kako se informacije prenose između vremenskih koraka i kako se parametri dijele u svim vremenskim koracima.\r\n\r\n## Problem gradijentnog nestanka i eksplozije\r\n\r\n### Korijen problema\r\n\r\nPrilikom treniranja RNN-ova koristimo algoritam povratnog širenja kroz vrijeme (BPTT). Algoritam treba izračunati gradijent funkcije gubitka za svaki parametar vremenskog koraka.\r\n\r\n**Zakon lanca za izračun gradijenta**:\r\nKada je niz dugačak, gradijent se mora širiti unatrag kroz više vremenskih koraka. Prema pravilu lanca, gradijent će sadržavati višestruke množenja matrice težine:\r\n\r\n∂L/∂W = Σ_t (∂L/∂y_t) * (∂y_t/∂h_t) * (∂h_t/∂W)\r\n\r\ngdje ∂h_t/∂W uključuje umnožak svih međustanja od momenta t do momenta 1.\r\n\r\n**Matematička analiza nestanka gradijenta**:\r\nRazmotrite širenje gradijenata između vremenskih koraka:\r\n∂h_t/∂h_{t-1} = diag(f_prime(W_hh * h_{t-1} + W_xh * x_t + b_h)) * W_hh\r\n\r\nKada je duljina niza T, gradijent sadrži T-1 takav proizvodni pojam. Ako je maksimalna svojstvena vrijednost W_hh manja od 1, kontinuirano množenje matrice uzrokovat će eksponencijalni raspad gradijenta.\r\n\r\n**Matematička analiza gradijentnih eksplozija**:\r\nNasuprot tome, kada je maksimalna svojstvena vrijednost W_hh veća od 1, gradijent se eksponencijalno povećava:\r\n|| ∂h_t/∂h_1|| ≈ || W_hh|| ^{t-1}\r\n\r\nTo dovodi do nestabilnog treninga i pretjeranog ažuriranja parametara.\r\n\r\n### Detaljno objašnjenje rješenja\r\n\r\nIzrezivanje gradijenta:\r\nIzrezivanje gradijenta najizravniji je način rješavanja gradijentnih eksplozija. Kada norma gradijenta premaši postavljeni prag, gradijent se skalira na veličinu praga. Ova metoda je jednostavna i učinkovita, ali zahtijeva pažljiv odabir pragova. Premali prag ograničit će sposobnost učenja, a prevelik prag neće učinkovito spriječiti eksploziju gradijenta.\r\n\r\n**Strategija inicijalizacije težine**:\r\nPravilna inicijalizacija težine može ublažiti probleme s gradijentom:\r\n- Xavierova inicijalizacija: odstupanje težine je 1/n, gdje je n ulazna dimenzija\r\n- Inicijalizacija: Varijanca težine je 2/n, što je prikladno za funkcije aktivacije ReLU\r\n- Ortogonalna inicijalizacija: Inicijalizira matricu težine kao ortogonalnu matricu\r\n\r\n**Odabir aktivacijskih funkcija**:\r\nRazličite funkcije aktivacije imaju različite učinke na širenje gradijenta:\r\n- TANH: izlazni raspon [-1,1], maksimalna vrijednost gradijenta 1\r\n- ReLU: može ublažiti nestanak gradijenta, ali može uzrokovati smrt neurona\r\n- Leaky ReLU: Rješava problem neuronske smrti ReLU\r\n\r\n**Arhitektonska poboljšanja**:\r\nNajosnovnije rješenje bilo je poboljšanje RNN arhitekture, što je dovelo do pojave LSTM-a i GRU-a. Ove arhitekture rješavaju gradijente kroz mehanizme zatvaranja i specijalizirane dizajne protoka informacija.\r\n\r\n## LSTM: Mreža dugog kratkoročnog pamćenja\r\n\r\n### Motivacija dizajna za LSTM\r\n\r\nLSTM (Long Short-Term Memory) je varijanta RNN-a koju su predložili Hochreiter i Schmidhuber 1997. godine, posebno dizajnirana za rješavanje problema gradijentnog nestajanja i poteškoća u učenju ovisnim na daljinu.\r\n\r\n**LSTM-ove temeljne inovacije**:\r\n- Stanje stanice: Služi kao \"autocesta\" za informacije, omogućujući protok informacija izravno između vremenskih koraka\r\n- Mehanizam za zatvaranje: Precizna kontrola nad priljevom, zadržavanjem i izlazom informacija\r\n- Mehanizmi disociranog pamćenja: razlikovati kratkoročno pamćenje (skriveno stanje) i dugoročno pamćenje (stanično stanje)\r\n\r\n**Kako LSTM rješava probleme s gradijentom**:\r\nLSTM ažurira stanje stanice aditivnim, a ne multiplikativnim operacijama, što omogućuje lakši protok gradijenata u ranije vremenske korake. Ažurirana formula za stanje stanice:\r\n\r\nC_t = f_t ⊙ C_{t-1} + i_t ⊙ C_tilde_t\r\n\r\nOvdje se koristi zbrajanje na razini elementa, izbjegavajući kontinuirano množenje matrice u tradicionalnim RNN-ovima.\r\n\r\n### Detaljno objašnjenje LSTM arhitekture\r\n\r\nLSTM sadrži tri jedinice za zatvaranje i stanje stanice:\r\n\r\n**1. Zaboravi vrata**:\r\nVrata zaborava odlučuju koje će informacije odbaciti iz staničnog stanja:\r\nf_t = σ(W_f · [h_{t-1}, x_t] + b_f)\r\n\r\nIzlaz vrata zaborava je vrijednost između 0 i 1, pri čemu je 0 \"potpuno zaboravljeno\", a 1 \"potpuno zadržano\". Ova vrata omogućuju LSTM-u da selektivno zaboravi nevažne povijesne informacije.\r\n\r\n**2. Ulazna vrata**:\r\nUlazna vrata određuju koje su nove informacije pohranjene u stanju ćelije:\r\ni_t = σ(W_i · [h_{t-1}, x_t] + b_i)\r\nC_tilde_t = tanh(W_C · [h_{t-1}, x_t] + b_C)\r\n\r\nUlazna vrata sastoje se od dva dijela: sigmoidni sloj određuje koje vrijednosti treba ažurirati, a tanh sloj stvara vektore vrijednosti kandidata.\r\n\r\n**3. Ažuriranje statusa ćelije**:\r\nKombinirajte izlaze vrata zaborava i ulaznih vrata da biste ažurirali stanje ćelije:\r\nC_t = f_t ⊙ C_{t-1} + i_t ⊙ C_tilde_t\r\n\r\nOva formula je u središtu LSTM-a: selektivno zadržavanje i ažuriranje informacija putem operacija množenja i zbrajanja na razini elementa.\r\n\r\n**4. Izlazna vrata**:\r\nIzlazna vrata određuju koji su dijelovi ćelije izlazni:\r\no_t = σ(W_o · [h_{t-1}, x_t] + b_o)\r\nh_t = o_t ⊙ tanh(C_t)\r\n\r\nIzlazna vrata kontroliraju koji dijelovi stanja ćelije utječu na trenutni izlaz.\r\n\r\n### LSTM varijante\r\n\r\n**Špijunka LSTM**:\r\nOslanjajući se na standardni LSTM, LSTM špijunke omogućuje jedinici za zatvaranje da vidi stanje ćelije:\r\nf_t = σ(W_f · [C_{t-1}, h_{t-1}, x_t] + b_f)\r\ni_t = σ(W_i · [C_{t-1}, h_{t-1}, x_t] + b_i)\r\no_t = σ(W_o · [C_t, h_{t-1}, x_t] + b_o)\r\n\r\n**Spregnuti LSTM**:\r\nSpojite vrata zaborava s ulaznim vratima kako biste bili sigurni da je količina zaboravljenih informacija jednaka količini unesenih informacija:\r\nf_t = σ(W_f · [h_{t-1}, x_t] + b_f)\r\ni_t = 1 - f_t\r\n\r\nOvaj dizajn smanjuje broj parametara uz zadržavanje osnovne funkcionalnosti LSTM-a.\r\n\r\n## GRU: Jedinica s ograđenom petljom\r\n\r\n### Pojednostavljeni dizajn GRU-a\r\n\r\nGRU (Gated Recurrent Unit) je pojednostavljena verzija LSTM-a koju su predložili Cho i suradnici 2014. godine. GRU pojednostavljuje tri vrata LSTM-a na dva vrata i spaja stanično stanje i skriveno stanje.\r\n\r\n**GRU-ova filozofija dizajna**:\r\n- Pojednostavljena struktura: Smanjuje broj vrata i smanjuje složenost izračuna\r\n- Održavanje performansi: Pojednostavite uz održavanje performansi usporedivih s LSTM-om\r\n- Jednostavna implementacija: Jednostavnija konstrukcija omogućuje jednostavnu implementaciju i puštanje u rad\r\n\r\n### Mehanizam zatvaranja GRU-a\r\n\r\n**1. Resetiraj vrata**:\r\nr_t = σ(W_r · [h_{t-1}, x_t] + b_r)\r\n\r\nVrata za resetiranje određuju kako kombinirati novi ulaz s prethodnom memorijom. Kada se vrata za resetiranje približe 0, model zanemaruje prethodno skriveno stanje.\r\n\r\n**2. Vrata za ažuriranje**:\r\nz_t = σ(W_z · [h_{t-1}, x_t] + b_z)\r\n\r\nVrata za ažuriranje određuju koliko prošlih podataka treba zadržati i koliko novih informacija dodati. Kontrolira i zaboravljanje i unos, slično kombinaciji zaborava i ulaznih vrata u LSTM-u.\r\n\r\n**3. Status skrivenog kandidata**:\r\nh_tilde_t = tanh(W_h · [r_t ⊙ h_{t-1}, x_t] + b_h)\r\n\r\nSkrivena stanja kandidata koriste vrata za resetiranje za kontrolu učinaka prethodnog skrivenog stanja.\r\n\r\n**4. Konačno skriveno stanje**:\r\nh_t = (1 - z_t) ⊙ h_{t-1} + z_t ⊙ h_tilde_t\r\n\r\nKonačno skriveno stanje je ponderirani prosjek prethodnog skrivenog stanja i kandidata za skriveno stanje.\r\n\r\n### Dubinska usporedba GRU-a i LSTM-a\r\n\r\n**Usporedba broja parametara**:\r\n- LSTM: 4 matrice težine (vrata zaboravljanja, ulazna vrata, vrijednost kandidata, izlazna vrata)\r\n- GRU: 3 matrice težine (vrata za resetiranje, vrata za ažuriranje, vrijednost kandidata)\r\n- Broj parametara GRU-a iznosi približno 75% LSTM-a\r\n\r\n**Usporedba računalne složenosti**:\r\n- LSTM: zahtijeva izračun 4 izlaza vrata i ažuriranja stanja ćelije\r\n- GRU: Jednostavno izračunajte izlaz 2 vrata i skrivena ažuriranja statusa\r\n- GRU je obično 20-30% brži od LSTM-a\r\n\r\n**Usporedba performansi**:\r\n- U većini zadataka GRU i LSTM rade usporedivo\r\n- LSTM je možda nešto bolji od GRU-a u nekim zadacima dugog niza\r\n- GRU je bolji izbor u slučajevima kada su računalni resursi ograničeni\r\n\r\n## Dvosmjerni RNN-ovi\r\n\r\n### Nužnost dvosmjerne obrade\r\n\r\nU mnogim zadacima modeliranja sekvenci, izlaz sadašnjeg trenutka oslanja se ne samo na prošle već i na buduće informacije. Ovo je posebno važno u OCR zadacima, gdje prepoznavanje znakova često zahtijeva razmatranje konteksta cijele riječi ili rečenice.\r\n\r\n**Ograničenja jednosmjernih RNN-ova**:\r\n- Mogu se koristiti samo povijesne informacije, ne može se dobiti budući kontekst\r\n- Ograničena izvedba u određenim zadacima, posebno onima koji zahtijevaju globalne informacije\r\n- Ograničeno prepoznavanje dvosmislenih znakova\r\n\r\n**Prednosti dvosmjerne obrade**:\r\n- Potpune kontekstualne informacije: Iskoristite prošle i buduće informacije\r\n- Bolja višeznačnost: Višeznačnost s kontekstualnim informacijama\r\n- Poboljšana točnost prepoznavanja: Bolja izvedba u većini zadataka označavanja sekvenci\r\n\r\n### Dvosmjerna LSTM arhitektura\r\n\r\nDvosmjerni LSTM sastoji se od dva LSTM sloja:\r\n- Naprijed LSTM: Procesirajte sekvence s lijeva na desno\r\n- LSTM unatrag: Procesirajte sekvence s desna na lijevo\r\n\r\n**Matematički prikaz**:\r\nh_forward_t = LSTM_forward(x_t, h_forward_{t-1})\r\nh_backward_t = LSTM_backward(x_t, h_backward_{t+1})\r\nh_t = [h_forward_t; h_backward_t] # Šivanje skrivenih stanja naprijed i natrag\r\n\r\n**Proces obuke**:\r\n1. Naprijed LSTM obrađuje sekvence normalnim redoslijedom\r\n2. Unatrag LSTM obrađuje sekvence obrnutim redoslijedom\r\n3. U svakom vremenskom koraku povežite skrivena stanja u oba smjera\r\n4. Koristite spojeno stanje za predviđanje\r\n\r\n**Prednosti i nedostaci**:\r\nPrednost:\r\n- Potpune kontekstualne informacije\r\n- Bolje performanse\r\n- Tretman simetrije\r\n\r\nDonji položaj:\r\n- Udvostručite složenost izračuna\r\n- Ne može se obraditi u stvarnom vremenu (zahtijeva puni slijed)\r\n- Povećani zahtjevi za memorijom\r\n\r\n## Aplikacije za modeliranje sekvenci u OCR-u\r\n\r\n### Detaljno objašnjenje prepoznavanja teksta\r\n\r\nU OCR sustavima, prepoznavanje tekstnih linija tipična je primjena modeliranja sekvenci. Ovaj proces uključuje pretvaranje niza značajki slike u niz znakova.\r\n\r\n**Modeliranje problema**:\r\n- Ulaz: Slijed značajki slike X = {x_1, x_2, ..., x_T}\r\n- Izlaz: Niz znakova Y = {y_1, y_2, ..., y_S}\r\n- Izazov: Duljina ulazne sekvence T i duljina izlazne sekvence S često nisu jednake\r\n\r\n**Primjena CRNN arhitekture u prepoznavanju tekstnih linija**:\r\nCRNN (Convolutional Recurrent Neural Network) jedna je od najuspješnijih arhitektura u OCR-u:\r\n\r\n1. **CNN sloj za ekstrakciju značajki**:\r\n   - Izdvojite značajke slike pomoću konvolucijskih neuronskih mreža\r\n   - Pretvorite značajke 2D slike u 1D sekvence značajki\r\n   - Održavajte kontinuitet informacija o vremenu\r\n\r\n2. **RNN sloj za modeliranje sekvenci**:\r\n   - Modelirajte sekvence značajki pomoću dvosmjernih LSTM-ova\r\n   - Uhvatite kontekstualne ovisnosti između likova\r\n   - Izlazna distribucija vjerojatnosti znakova za svaki vremenski korak\r\n\r\n3. **CTC sloj za poravnanje**:\r\n   - Rješava neusklađenosti duljine ulazno/izlazne sekvence\r\n   - Nisu potrebne dimenzije poravnanja na razini znakova\r\n   - End-to-end obuka\r\n\r\n**Pretvorba ekstrakcije značajki u sekvencu**:\r\nMapu značajki koju je izdvojio CNN treba pretvoriti u oblik sekvence koji RNN može obraditi:\r\n- Segmentirajte kartu značajki u stupce, pri čemu je svaki stupac vremenski korak\r\n- Održavati kronologiju prostornih informacija\r\n- Osigurajte da je duljina sekvence značajki proporcionalna širini slike\r\n\r\n### Primjena mehanizma pažnje u OCR-u\r\n\r\nTradicionalni RNN-ovi još uvijek imaju informacijska uska grla kada se bave dugim sekvencama. Uvođenje mehanizama pažnje dodatno poboljšava mogućnosti modeliranja sekvenci.\r\n\r\n**Principi mehanizama pažnje**:\r\nMehanizam pažnje omogućuje modelu da se usredotoči na različite dijelove ulaznog slijeda prilikom generiranja svakog izlaza:\r\n- Riješeno informacijsko usko grlo kodiranih vektora fiksne duljine\r\n- Pruža objašnjivost modela odluka\r\n- Poboljšana obrada dugih sekvenci\r\n\r\n**Specifične primjene u OCR-u**:\r\n\r\n1. **Pažnja na razini znakova**:\r\n   - Usredotočite se na relevantna područja slike prilikom prepoznavanja svakog lika\r\n   - Prilagodite težinu pažnje u hodu\r\n   - Poboljšajte robusnost složenih pozadina\r\n\r\n2. **Pažnja na razini riječi**:\r\n   - Razmotrite kontekstualne informacije na razini vokabulara\r\n   - Iskoristite znanje o jezičnim modelima\r\n   - Poboljšajte točnost prepoznavanja cijelih riječi\r\n\r\n3. **Pažnja na više razina**:\r\n   - Primjena mehanizama pažnje u različitim rezolucijama\r\n   - Rukovanje tekstom različitih veličina\r\n   - Poboljšajte prilagodljivost promjenama razmjera\r\n\r\n**Matematički prikaz mehanizma pažnje**:\r\nZa izlazni slijed enkodera H = {h_1, h_2, ..., h_T} i stanje dekodera s_t:\r\n\r\ne_{t,i} = a(s_t, h_i) # Ocjena pažnje\r\nα_{t,i} = softmax(e_{t,i}) # Težina pažnje\r\nc_t = Σ_i α_{t,i} * h_i # kontekstni vektor\r\n\r\n## Strategije treninga i optimizacija\r\n\r\n### Strategija treninga od slijeda do slijeda\r\n\r\n**Prisiljavanje učitelja**:\r\nTijekom faze vježbanja koristite stvarni ciljni slijed kao ulaz dekodera:\r\n- Prednosti: velika brzina treninga, stabilna konvergencija\r\n- Nedostaci: Nedosljedne faze treninga i zaključivanja, što dovodi do nakupljanja pogrešaka\r\n\r\n**Planirano uzorkovanje**:\r\nPostupni prijelaz s prisiljavanja učitelja na korištenje vlastitih predviđanja modela tijekom treninga:\r\n- Koristite stvarne oznake u početnoj fazi i modelirajte predviđanja u kasnijim fazama\r\n- Smanjiti razlike u treningu i rasuđivanju\r\n- Poboljšajte robusnost modela\r\n\r\n**Učenje prema kurikulumu**:\r\nZapočnite s jednostavnim uzorcima i postupno povećavajte složenost uzoraka:\r\n- Kratke do duge sekvence: Prvo trenirajte kratke tekstove, a zatim duge tekstove\r\n- Jasne do zamućene slike: Postupno povećavajte složenost slike\r\n- Jednostavni do složeni fontovi: od tiskanog do rukopisa\r\n\r\n### Tehnike regularizacije\r\n\r\n**Primjena ispadanja u RNN-u**:\r\nPrimjena ispadanja u RNN-u zahtijeva posebnu pozornost:\r\n- Nemojte primjenjivati ispadanje na veze petlje\r\n- Dropout se može primijeniti na ulaznom i izlaznom sloju\r\n- Varijacijsko ispadanje: Koristite istu masku za napuštanje u svim vremenskim koracima\r\n\r\n**Smanjenje težine**:\r\nL2 regularizacija sprječava prekomjerno uklapanje:\r\nGubitak = Unakrsna entropija + λ * || W|| ²\r\n\r\ngdje je λ koeficijent regularizacije, koji treba optimizirati skupom za provjeru valjanosti.\r\n\r\n**Gradijentno obrezivanje**:\r\nUčinkovit način za sprječavanje gradijentnih eksplozija. Kada norma gradijenta premaši prag, proporcionalno skalirajte gradijent kako bi smjer gradijenta ostao nepromijenjen.\r\n\r\n**Rano zaustavljanje**:\r\nPratite performanse skupa za provjeru valjanosti i zaustavite obuku kada se performanse više ne poboljšavaju:\r\n- Spriječite prekomjerno opremanje\r\n- Ušteda računalnih resursa\r\n- Odaberite optimalni model\r\n\r\n### Podešavanje hiperparametara\r\n\r\n**Raspored brzine učenja**:\r\n- Početna stopa učenja: Obično se postavlja na 0,001-0,01\r\n- Propadanje brzine učenja: eksponencijalno propadanje ili propadanje ljestava\r\n- Prilagodljiva brzina učenja: Koristite optimizatore kao što su Adam, RMSprop itd\r\n\r\n**Odabir veličine serije**:\r\n- Male serije: Bolja izvedba generalizacije, ali dulje vrijeme treninga\r\n- Veliki volumen: Trening je brz, ali može utjecati na generalizaciju\r\n- Obično se odabiru veličine serija između 16-128\r\n\r\n**Obrada duljine sekvence**:\r\n- Fiksna duljina: Skratite ili ispunite sekvence na fiksne duljine\r\n- Dinamička duljina: Koristite podmetanje i maskiranje za rukovanje sekvencama promjenjive duljine\r\n- Strategija pakiranja: Grupirajte sekvence slične duljine\r\n\r\n## Procjena i analiza učinka\r\n\r\n### Procjena mjernih podataka\r\n\r\n**Točnost na razini znakova**:\r\nAccuracy_char = (Broj ispravno prepoznatih znakova) / (Ukupan broj znakova)\r\n\r\nOvo je najosnovniji pokazatelj evaluacije i izravno odražava mogućnosti prepoznavanja znakova modela.\r\n\r\n**Točnost serijske razine**:\r\nAccuracy_seq = (broj ispravno prepoznatih sekvenci) / (ukupan broj sekvenci)\r\n\r\nOvaj je pokazatelj rigorozniji i samo se potpuno ispravan slijed smatra ispravnim.\r\n\r\n**Udaljenost uređivanja (Levenshtein udaljenost)**:\r\nIzmjerite razliku između predviđenih i istinitih redova:\r\n- Minimalni broj operacija umetanja, uklanjanja i zamjene\r\n- Standardizirana udaljenost uređivanja: udaljenost uređivanja / duljina sekvence\r\n- BLEU rezultat: Često se koristi u strojnom prevođenju, a može se koristiti i za OCR procjenu\r\n\r\n### Analiza pogrešaka\r\n\r\n**Uobičajene vrste pogrešaka**:\r\n1. **Zbunjenost znakova**: Pogrešna identifikacija sličnih znakova\r\n   - Broj 0 i slovo O\r\n   - Broj 1 i slovo l\r\n   - Slova M i N\r\n\r\n2. **Pogreška slijeda**: Pogreška u redoslijedu znakova\r\n   - Položaji likova su obrnuti\r\n   - Umnožavanje ili izostavljanje znakova\r\n\r\n3. **Pogreška duljine**: Pogreška u predviđanju duljine sekvence\r\n   - Predugo: Umetnuti nepostojeći znakovi\r\n   - Prekratko: Nedostaju znakovi koji su prisutni\r\n\r\n**Metoda analize**:\r\n1. **Matrica zbunjenosti**: Analizira obrasce pogrešaka na razini znakova\r\n2. **Vizualizacija pažnje**: Shvatite zabrinutost modela\r\n3. **Analiza gradijenta**: Provjerite tijek gradijenta\r\n4. **Analiza aktivacije**: Promatrajte obrasce aktivacije na svim slojevima mreže\r\n\r\n### Dijagnostika modela\r\n\r\n**Otkrivanje preopterećenja**:\r\n- Gubici na treningu i dalje opadaju, gubici u validaciji rastu\r\n- Točnost treninga mnogo je veća od točnosti provjere valjanosti\r\n- Rješenje: Povećajte pravilnost i smanjite složenost modela\r\n\r\n**Detekcija nedovoljnog uklapanja**:\r\n- I gubici u obuci i validaciji su visoki\r\n- Model ne radi dobro na setu za vježbanje\r\n- Rješenje: Povećajte složenost modela i prilagodite brzinu učenja\r\n\r\n**Dijagnoza problema gradijenta**:\r\n- Gubitak gradijenta: Vrijednost gradijenta je premala, sporo učenje\r\n- Eksplozija gradijenta: Prekomjerne vrijednosti gradijenta dovode do nestabilnog treninga\r\n- Rješenje: Korištenje LSTM/GRU, gradijentno obrezivanje\r\n\r\n## Slučajevi primjene u stvarnom svijetu\r\n\r\n### Sustav prepoznavanja rukom pisanih znakova\r\n\r\n**Scenariji primjene**:\r\n- Digitalizirajte rukom pisane bilješke: Pretvorite papirnate bilješke u elektroničke dokumente\r\n- Automatsko popunjavanje obrazaca: automatski prepoznaje rukom pisani sadržaj obrasca\r\n- Identifikacija povijesnih dokumenata: Digitalizirajte drevne knjige i povijesne dokumente\r\n\r\n**Tehničke značajke**:\r\n- Velike varijacije znakova: Rukom pisani tekst ima visok stupanj personalizacije\r\n- Kontinuirana obrada olovkom: Veze između likova moraju se rješavati\r\n- Kontekstno važno: Koristite jezične modele za poboljšanje prepoznavanja\r\n\r\n**Arhitektura sustava**:\r\n1. **Modul za prethodnu obradu**:\r\n   - Uklanjanje šuma i poboljšanje slike\r\n   - Korekcija nagiba\r\n   - Dijeljenje redaka teksta\r\n\r\n2. **Modul za izdvajanje značajki**:\r\n   - CNN izdvaja vizualne značajke\r\n   - Spajanje značajki na više razinama\r\n   - Serijalizacija značajki\r\n\r\n3. **Modul za modeliranje sekvenci**:\r\n   - Dvosmjerno LSTM modeliranje\r\n   - Mehanizmi pažnje\r\n   - Kontekstualno kodiranje\r\n\r\n4. **Modul za dekodiranje**:\r\n   - CTC dekodiranje ili dekodiranje pažnje\r\n   - Naknadna obrada jezičnog modela\r\n   - Procjena pouzdanosti\r\n\r\n### Sustav prepoznavanja ispisanih dokumenata\r\n\r\n**Scenariji primjene**:\r\n- Digitalizacija dokumenata: Pretvaranje papirnatih dokumenata u formate koji se mogu uređivati\r\n- Prepoznavanje računa: Automatski obrađujte fakture, račune i druge račune\r\n- Prepoznavanje znakova: Identificirajte prometne znakove, znakove trgovina i još mnogo toga\r\n\r\n**Tehničke značajke**:\r\n- Uobičajeni font: Pravilniji od rukom pisanog teksta\r\n- Pravila tipografije: Mogu se koristiti informacije o izgledu\r\n- Zahtjevi za visoku točnost: Komercijalne primjene imaju stroge zahtjeve za točnost\r\n\r\n**Strategija optimizacije**:\r\n1. **Obuka s više fontova**: Koristi podatke za obuku iz više fontova\r\n2. **Poboljšanje podataka**: rotiranje, skaliranje, dodavanje šuma\r\n3. **Optimizacija naknadne obrade**: provjera pravopisa, ispravljanje gramatike\r\n4. **Procjena pouzdanosti**: Pruža ocjenu pouzdanosti za rezultate prepoznavanja\r\n\r\n### Sustav prepoznavanja teksta scene\r\n\r\n**Scenariji primjene**:\r\n- Prepoznavanje teksta u Street Viewu: prepoznavanje teksta u Google Street Viewu\r\n- Prepoznavanje naljepnica proizvoda: Automatska identifikacija proizvoda iz supermarketa\r\n- Prepoznavanje prometnih znakova: Primjene inteligentnih transportnih sustava\r\n\r\n**Tehnički izazovi**:\r\n- Složene pozadine: Tekst je ugrađen u složene prirodne scene\r\n- Teška deformacija: Perspektivna deformacija, deformacija savijanjem\r\n- Zahtjevi u stvarnom vremenu: Mobilne aplikacije moraju biti responzivne\r\n\r\n**Otopina**:\r\n1. **Robusna ekstrakcija značajki**: Koristi dublje CNN mreže\r\n2. **Obrada na više razina**: Rukovanje tekstom različitih veličina\r\n3. **Korekcija geometrije**: Automatski ispravlja geometrijske deformacije\r\n4. **Kompresija modela**: Optimizirajte model za mobilne uređaje\r\n\r\n## Sažetak\r\n\r\nRekurentne neuronske mreže pružaju moćan alat za modeliranje sekvenci u OCR-u. Od osnovnih RNN-ova do poboljšanih LSTM-ova i GRU do dvosmjerne obrade i mehanizama pažnje, razvoj ovih tehnologija uvelike je poboljšao performanse OCR sustava.\r\n\r\n**Ključni zaključci**:\r\n- RNN-ovi implementiraju modeliranje sekvenci putem spajanja petlje, ali postoji problem nestajanja gradijenta\r\n- LSTM i GRU rješavaju problem učenja ovisnog o daljini pomoću mehanizama zatvaranja\r\n- Dvosmjerni RNN-ovi mogu iskoristiti pune kontekstualne informacije\r\n- Mehanizmi pažnje dodatno poboljšavaju sposobnost modeliranja sekvenci\r\n- Odgovarajuće strategije treninga i tehnike regularizacije ključne su za izvedbu modela\r\n\r\n**Budući smjerovi razvoja**:\r\n- Integracija s Transformer arhitekturama\r\n- Učinkovitiji pristup modeliranju sekvenci\r\n- End-to-end multimodalno učenje\r\n- Ravnoteža stvarnog vremena i točnosti\r\n\r\nKako se tehnologija nastavlja razvijati, tehnike modeliranja sekvenci i dalje se razvijaju. Iskustvo i tehnologija koju su prikupili RNN-ovi i njihove varijante u području OCR-a postavili su čvrste temelje za razumijevanje i dizajniranje naprednijih metoda modeliranja sekvenci.</div>\n                        \n                        <div class=\"article-tags\">\n                            <h6>Etiketa:</h6>\n                            <div class=\"tags-list\">\n                                \n                                <span class=\"tag\">RNN</span>\n                                \n                                <span class=\"tag\">LSTM</span>\n                                \n                                <span class=\"tag\">GRU</span>\n                                \n                                <span class=\"tag\">Modeliranje sekvenci</span>\n                                \n                                <span class=\"tag\">Gradijent nestaje</span>\n                                \n                                <span class=\"tag\">Dvosmjerni RNN</span>\n                                \n                                <span class=\"tag\">Mehanizam pažnje</span>\n                                \n                                <span class=\"tag\">CRNN</span>\n                                \n                                <span class=\"tag\">OCR</span>\n                                \n                            </div>\n                        </div>\n                        \n                    </article>\n                    \n                    <footer class=\"article-footer\" id=\"articleFooter\">\n                        <div class=\"article-share\">\n                            <h6 style=\"color: #1a1c1f; margin-bottom: 1rem;\">Dijelite i upravljajte:</h6>\n                            <div class=\"share-buttons\">\n                                <a href=\"javascript:void(0)\" onclick=\"shareToWeibo()\" class=\"btn btn-weibo\">📱 Weibo dijeli</a>\n                                <a href=\"javascript:void(0)\" onclick=\"copyUrl()\" class=\"btn btn-copy\">🔗 Kopiraj vezu</a>\n                                <button type=\"button\" class=\"btn btn-print\" onclick=\"printArticle()\">🖨️ Ispiši članak</button>\n                            </div>\n                        </div>\n                    </footer>\n                    \n                </div>\n                <div class=\"col-lg-4 col-xl-3 col-md-12\">\n                    <div class=\"sidebar\">\n                        <div class=\"sidebar-card toc-card\" id=\"tocCard\" style=\"display: none;\"><h5>Tablica sadržaja</h5><div id=\"tableOfContents\" class=\"toc-list\"></div></div>\n                        <div class=\"sidebar-card\">\n                            <h5>Preporučena literatura</h5>\n                            <div id=\"recommendedArticles\">\n                                \n                                <div class=\"recommended-item\"><h6><a href=\"Article.aspx?slug=document-intelligence-future-outlook\">【Serija inteligentne obrade dokumenata·20】 Izgledi za razvoj tehnologije inteligentne obrade dokumenata</a></h6><p class=\"text-muted small\">2025-08-19 • 1203 Sljedeće čitanje</p></div>\n                                \n                                <div class=\"recommended-item\"><h6><a href=\"Article.aspx?slug=document-intelligence-quality-assurance\">【Serija inteligentne obrade dokumenata·19】 Sustav osiguranja kvalitete inteligentne obrade dokumenata</a></h6><p class=\"text-muted small\">2025-08-19 • 1200 Sljedeće čitanje</p></div>\n                                \n                                <div class=\"recommended-item\"><h6><a href=\"Article.aspx?slug=large-scale-document-processing-optimization\">【Serija inteligentne obrade dokumenata·18】 Optimizacija performansi obrade dokumenata velikih razmjera</a></h6><p class=\"text-muted small\">2025-08-19 • 1201 Sljedeće čitanje</p></div>\n                                \n                            </div>\n                        </div>\n                    </div>\n</div>\n</div>\n</div>\n</div>\n<script>\nvar articleId,viewCountKey,now=new Date().getTime();\ndocument.addEventListener('DOMContentLoaded',function(){var articleDetail=document.getElementById('articleDetail');if(articleDetail&&articleDetail.querySelector('.article-content')){formatContent();initReadingProgress();optimizeImages();updateViewCount();}});\nfunction updateViewCount(){articleId=getArticleId();if(!articleId)return;viewCountKey='article_view_'+articleId;var lastViewTime=localStorage.getItem(viewCountKey);if(lastViewTime&&(now-parseInt(lastViewTime))<3600000)return;var xhr=new XMLHttpRequest();xhr.open('POST','/Article.ashx',true);xhr.setRequestHeader('Content-Type','application/x-www-form-urlencoded');xhr.onreadystatechange=function(){if(xhr.readyState===4&&xhr.status===200){try{var response=JSON.parse(xhr.responseText);if(response.success){updateViewCountDisplay(response.viewCount);localStorage.setItem(viewCountKey,now.toString());}}catch(e){}}};xhr.send('action=updateviewcount&articleId='+encodeURIComponent(articleId));}\nfunction getArticleId(){var articleDetail=document.getElementById('articleDetail');if(articleDetail){var id=articleDetail.getAttribute('data-article-id');if(id)return id;}var urlParams=new URLSearchParams(window.location.search);return urlParams.get('id');}\nfunction updateViewCountDisplay(newCount){document.querySelectorAll('.view-count').forEach(function(element){element.textContent=newCount;});}\nfunction decodeHtmlEntities(text){var textArea=document.createElement('textarea');textArea.innerHTML=text;return textArea.value;}\nfunction formatContent(){var contentDiv=document.getElementById('articleContent');if(!contentDiv)return;var content=contentDiv.textContent||contentDiv.innerText;if(!content)return;content=decodeHtmlEntities(content);content=formatMarkdownContent(content);content=formatIndentation(content);content=formatLists(content);content=formatParagraphs(content);content=formatLinks(content);contentDiv.innerHTML=content;initTableOfContents();}\nfunction formatIndentation(content){var lines=content.split('\\n'),result=[];for(var i=0;i<lines.length;i++){var line=lines[i];if(line.trim().match(/^<[^>]+>/)){result.push(line);continue;}line=line.replace(/^\\t+/g,function(match){return '    '.repeat(match.length);});if(line.match(/^  +[^\\s]/)){var indentMatch=line.match(/^( +)(.*)$/);if(indentMatch){var indentLevel=Math.floor(indentMatch[1].length/2);line='<span class=\"text-indent-'+indentLevel+'\">'+indentMatch[2]+'</span>';}}result.push(line);}return result.join('\\n');}\nfunction formatMarkdownContent(content){content=content.replace(/^\\s*##### (.+)$/gm,'<h5>$1</h5>').replace(/^\\s*#### (.+)$/gm,'<h4>$1</h4>').replace(/^\\s*### (.+)$/gm,'<h3>$1</h3>').replace(/^\\s*## (.+)$/gm,'<h2>$1</h2>').replace(/^\\s*# (.+)$/gm,'<h1>$1</h1>').replace(/^> (.+)$/gm,'<blockquote><p>$1</p></blockquote>').replace(/\\*\\*([^*]+?)\\*\\*/g,'<strong>$1</strong>').replace(/\\*([^*\\n]+?)\\*/g,'<em>$1</em>').replace(/`([^`]+?)`/g,'<code class=\"inline-code\">$1</code>').replace(/^(\\*\\*[^*]+?\\*\\*)：/gm,'<div class=\"label-item\"><span class=\"label\">$1</span></div>').replace(/^([^：\\n]+?)：$/gm,'<div class=\"section-label\">$1：</div>').replace(/^(\\*\\*[^*]+?\\*\\*)：(.+)$/gm,'<div class=\"definition-item\"><span class=\"definition-label\">$1：</span><span class=\"definition-content\">$2</span></div>').replace(/^(note|note|note):(.+)$/gm,'<div class=\"note-item\"><span class=\"note-label\">$1：</span><span class=\"note-content\">$2</span></div>');content=formatLists(content);content=formatMathSymbols(content);content=formatParagraphs(content);return content;}\nfunction formatLists(content){var lines=content.split('\\n'),result=[],inOrderedList=false,inUnorderedList=false;for(var i=0;i<lines.length;i++){var line=lines[i],trimmed=line.trim();var orderedMatch=line.match(/^(\\d+)\\.\\s*(.+)$/);var unorderedMatch=line.match(/^[-*+]\\s*(.+)$/);if(orderedMatch){if(!inOrderedList){if(inUnorderedList){result.push('</ul>');inUnorderedList=false;}result.push('<ol class=\"formatted-list ordered\">');inOrderedList=true;}var listContent=orderedMatch[2].replace(/^\\*\\*([^*]+?)\\*\\*：(.+)$/,'<strong class=\"list-title\">$1：</strong><span class=\"list-content\">$2</span>');result.push('<li>'+listContent+'</li>');}else if(unorderedMatch){if(!inUnorderedList){if(inOrderedList){result.push('</ol>');inOrderedList=false;}result.push('<ul class=\"formatted-list unordered\">');inUnorderedList=true;}var listContent=unorderedMatch[1].replace(/^\\*\\*([^*]+?)\\*\\*：(.+)$/,'<strong class=\"list-title\">$1：</strong><span class=\"list-content\">$2</span>');result.push('<li>'+listContent+'</li>');}else{if(trimmed===''){if(i+1<lines.length){var nextLine=lines[i+1].trim();if(!nextLine.match(/^\\d+\\.\\s/)&&!nextLine.match(/^[-*+]\\s/)){if(inOrderedList){result.push('</ol>');inOrderedList=false;}if(inUnorderedList){result.push('</ul>');inUnorderedList=false;}}}result.push(line);}else{if(inOrderedList){result.push('</ol>');inOrderedList=false;}if(inUnorderedList){result.push('</ul>');inUnorderedList=false;}result.push(line);}}}if(inOrderedList)result.push('</ol>');if(inUnorderedList)result.push('</ul>');return result.join('\\n');}\nfunction formatMathSymbols(content){var mathSymbols={'α':'&alpha;','β':'&beta;','γ':'&gamma;','δ':'&delta;','ε':'&epsilon;','ζ':'&zeta;','η':'&eta;','θ':'&theta;','λ':'&lambda;','μ':'&mu;','π':'&pi;','σ':'&sigma;','∑':'&sum;','∏':'&prod;','∫':'&int;','∂':'&part;','∇':'&nabla;','∞':'&infin;','≤':'&le;','≥':'&ge;','≠':'&ne;','≈':'&asymp;','±':'&plusmn;','×':'&times;','÷':'&divide;','→':'&rarr;','←':'&larr;','↑':'&uarr;','↓':'&darr;','⇒':'&rArr;','⇐':'&lArr;'};var subMap={'₀':'0','₁':'1','₂':'2','₃':'3','₄':'4','₅':'5','₆':'6','₇':'7','₈':'8','₉':'9','ₖ':'k','ₜ':'t','ₛ':'s','₊':'+','₋':'-'};var supMap={'⁰':'0','¹':'1','²':'2','³':'3','⁴':'4','⁵':'5','⁶':'6','⁷':'7','⁸':'8','⁹':'9','ᵏ':'k','ᵗ':'t','ˢ':'s','⁺':'+','⁻':'-'};for(var symbol in mathSymbols){content=content.replace(new RegExp(escapeRegExp(symbol),'g'),mathSymbols[symbol]);}content=content.replace(/([a-zA-Z])₍([0-9]+)₎/g,'$1<sub>$2</sub>').replace(/([a-zA-Z])⁽([0-9]+)⁾/g,'$1<sup>$2</sup>').replace(/([a-zA-Z])([₀₁₂₃₄₅₆₇₈₉ₖₜₛ₊₋]+)/g,function(match,base,subscript){var converted=subscript.replace(/[₀₁₂₃₄₅₆₇₈₉ₖₜₛ₊₋]/g,function(char){return subMap[char]||char;});return base+'<sub>'+converted+'</sub>';}).replace(/([a-zA-Z])([⁰¹²³⁴⁵⁶⁷⁸⁹ᵏᵗˢ⁺⁻]+)/g,function(match,base,superscript){var converted=superscript.replace(/[⁰¹²³⁴⁵⁶⁷⁸⁹ᵏᵗˢ⁺⁻]/g,function(char){return supMap[char]||char;});return base+'<sup>'+converted+'</sup>';}).replace(/\\b([A-Za-z])_\\{([^}]+)\\}/g,'$1<sub>$2</sub>').replace(/\\b([A-Za-z])\\^\\{([^}]+)\\}/g,'$1<sup>$2</sup>');return content;}\nfunction formatParagraphs(content){var lines=content.split('\\n'),result=[],inParagraph=false,currentParagraph=[];for(var i=0;i<lines.length;i++){var originalLine=lines[i],line=originalLine.trim();if(line.match(/^<[^>]+>/)||line===''){if(inParagraph&&currentParagraph.length>0){var paragraphContent=currentParagraph.join(' ').trim();if(paragraphContent)result.push('<p class=\"content-paragraph indent-paragraph\">'+paragraphContent+'</p>');currentParagraph=[];inParagraph=false;}result.push(originalLine);continue;}if(line.match(/^#{1,6}\\s/)||line.match(/^\\d+\\.\\s/)||line.match(/^[-*+]\\s/)||line.match(/^>/)){if(inParagraph&&currentParagraph.length>0){var paragraphContent=currentParagraph.join(' ').trim();if(paragraphContent)result.push('<p class=\"content-paragraph indent-paragraph\">'+paragraphContent+'</p>');currentParagraph=[];inParagraph=false;}result.push(originalLine);continue;}if(line){currentParagraph.push(line);inParagraph=true;}}if(inParagraph&&currentParagraph.length>0){var paragraphContent=currentParagraph.join(' ').trim();if(paragraphContent)result.push('<p class=\"content-paragraph indent-paragraph\">'+paragraphContent+'</p>');}return result.join('\\n');}\nfunction escapeRegExp(string){return string.replace(/[.*+?^${}()|[\\]\\\\]/g,'\\\\$&');}\nfunction formatLinks(content){return content.replace(/<a\\s+href=\"(https?:\\/\\/[^\"]+)\"([^>]*)>/gi,function(match,url,attrs){return !attrs.includes('target=')?'<a href=\"'+url+'\"'+attrs+' target=\"_blank\" rel=\"noopener noreferrer\">':match;});}\nfunction escapeHtml(text){var div=document.createElement('div');div.textContent=text;return div.innerHTML;}\nfunction addTocClickEvents(){document.querySelectorAll('#tableOfContents a').forEach(function(link){link.addEventListener('click',function(e){e.preventDefault();var targetElement=document.getElementById(this.getAttribute('data-target'));if(targetElement){targetElement.scrollIntoView({behavior:'smooth',block:'start'});updateActiveTocItem(this);}});});}\nfunction addScrollHighlight(headings){var throttleTimer=null;function updateActiveHeading(){var windowHeight=window.innerHeight,activeHeading=null;for(var i=headings.length-1;i>=0;i--){var rect=headings[i].getBoundingClientRect();if(rect.top<=windowHeight*0.3){activeHeading=headings[i];break;}}if(activeHeading){var activeLink=document.querySelector('#tableOfContents a[data-target=\"'+activeHeading.id+'\"]');if(activeLink)updateActiveTocItem(activeLink);}}window.addEventListener('scroll',function(){if(throttleTimer)clearTimeout(throttleTimer);throttleTimer=setTimeout(updateActiveHeading,100);});updateActiveHeading();}\nfunction updateActiveTocItem(activeLink){document.querySelectorAll('#tableOfContents a').forEach(function(link){link.classList.remove('active');});if(activeLink){activeLink.classList.add('active');scrollTocToActiveItem(activeLink);}}\nfunction scrollTocToActiveItem(activeLink){var tocCard=document.getElementById('tocCard');if(!activeLink||!tocCard)return;var cardRect=tocCard.getBoundingClientRect();var activeRect=activeLink.getBoundingClientRect();var relativeTop=activeRect.top-cardRect.top;var relativeBottom=activeRect.bottom-cardRect.bottom;var cardStyle=window.getComputedStyle(tocCard);var paddingTop=parseFloat(cardStyle.paddingTop)||0;var titleHeight=tocCard.querySelector('h5')?tocCard.querySelector('h5').offsetHeight:0;var contentTop=paddingTop+titleHeight;if(relativeTop<contentTop){tocCard.scrollTop+=(relativeTop-contentTop-10);}else if(relativeBottom>0){tocCard.scrollTop+=(relativeBottom+10);}}\nfunction optimizeImages(){document.querySelectorAll('.article-content img').forEach(function(img){img.loading='lazy';img.onerror=function(){this.style.display='none';};if(!img.alt)img.alt='Članak sa slikama';});}\nfunction initReadingProgress(){var progressBar=document.getElementById('readingProgress');function updateProgress(){var scrollTop=window.pageYOffset||document.documentElement.scrollTop;var docHeight=document.documentElement.scrollHeight-window.innerHeight;progressBar.style.width=Math.min((scrollTop/docHeight)*100,100)+'%';}window.addEventListener('scroll',updateProgress);updateProgress();}\nfunction initTableOfContents(){var content=document.querySelector('.article-content');if(!content)return;var headings=content.querySelectorAll('h1, h2, h3, h4, h5, h6');if(headings.length<=1)return;var tocCard=document.getElementById('tocCard');var tocList=document.getElementById('tableOfContents');var html='',counters=[0,0,0,0,0,0];headings.forEach(function(heading,index){var id='heading-'+index;heading.id=id;var level=parseInt(heading.tagName.substring(1));var indent=(level-1)*15;counters[level-1]++;for(var i=level;i<counters.length;i++)counters[i]=0;var numberText='';for(var i=0;i<level;i++){if(counters[i]>0)numberText+=(numberText?'.':'')+counters[i];}numberText+='.';html+='<a href=\"#'+id+'\" class=\"toc-item toc-level-'+level+'\" style=\"padding-left: '+indent+'px;\" data-target=\"'+id+'\"><span class=\"toc-number\">'+numberText+'</span> <span class=\"toc-text\">'+heading.textContent+'</span></a>';});tocList.innerHTML=html;tocCard.style.display='block';addTocClickEvents();addScrollHighlight(headings);tocList.addEventListener('click',function(e){if(e.target.tagName==='A'){e.preventDefault();var targetElement=document.getElementById(e.target.getAttribute('data-target'));if(targetElement){targetElement.scrollIntoView({behavior:'smooth',block:'start'});targetElement.style.transition='all 0.3s ease';targetElement.style.backgroundColor='#e3f2fd';targetElement.style.padding='10px';targetElement.style.borderRadius='6px';setTimeout(function(){targetElement.style.backgroundColor='';targetElement.style.padding='';targetElement.style.borderRadius='';},2000);}}});}\nfunction shareToWeibo(){window.open('https://service.weibo.com/share/share.php?url='+encodeURIComponent(window.location.href)+'&title='+encodeURIComponent(document.title),'_blank');}\nfunction copyUrl(){navigator.clipboard.writeText(window.location.href).then(function(){alert('Veza je kopirana u međuspremnik');}).catch(function(){var textArea=document.createElement('textarea');textArea.value=window.location.href;textArea.style.cssText='position:fixed;left:-999999px;top:-999999px';document.body.appendChild(textArea);textArea.focus();textArea.select();try{alert(document.execCommand('copy')?'Veza je kopirana u međuspremnik':'Ako kopiranje ne uspije, kopirajte vezu ručno');}catch(err){alert('Ako kopiranje ne uspije, kopirajte vezu ručno');}document.body.removeChild(textArea);});}\nfunction printArticle(){var printWindow=window.open('','_blank');var articleContent=document.querySelector('.article-detail').innerHTML;var articleTitle=document.querySelector('.article-title').textContent;printWindow.document.write('<!DOCTYPE html><html lang=\"hr\"><title>'+articleTitle+'</title><style>body{font-family:-apple-system,BlinkMacSystemFont,\"Segoe UI\",Roboto,\"Helvetica Neue\",Arial,sans-serif;line-height:1.6;color:#333;max-width:800px;margin:0 auto;padding:20px}h1,h2,h3,h4,h5,h6{color:#1a1c1f;margin-top:1.5em;margin-bottom:0.5em}h1{font-size:2em;border-bottom:2px solid #e9ecef;padding-bottom:0.3em}h2{font-size:1.5em;border-bottom:1px solid #e9ecef;padding-bottom:0.3em}h3{font-size:1.25em}p{margin-bottom:1em}pre{background:#f8f9fa;border:1px solid #e9ecef;border-radius:4px;padding:1em;overflow-x:auto;font-family:Consolas,Monaco,\"Courier New\",monospace}code{background:#f1f3f4;padding:2px 4px;border-radius:3px;font-family:Consolas,Monaco,\"Courier New\",monospace}blockquote{border-left:4px solid #1764ff;background:#f8f9fa;padding:1em;margin:1em 0;border-radius:0 4px 4px 0}img{max-width:100%;height:auto}table{width:100%;border-collapse:collapse;margin:1em 0}th,td{border:1px solid #ddd;padding:8px;text-align:left}th{background-color:#f2f2f2}.article-meta,.article-footer,.share-buttons{display:none}</style><body>'+articleContent+'</body></html>');printWindow.document.close();printWindow.focus();setTimeout(function(){printWindow.print();printWindow.close();},250);}\nfunction improvedStickyPosition(){var sidebarCards=document.querySelectorAll('.sidebar-card');var originalPositions=[],isFixed=[];sidebarCards.forEach(function(card,index){var rect=card.getBoundingClientRect();originalPositions[index]=rect.top+window.pageYOffset;isFixed[index]=false;var placeholder=document.createElement('div');placeholder.style.cssText='height:'+card.offsetHeight+'px;display:none';placeholder.className='sticky-placeholder';card.parentNode.insertBefore(placeholder,card.nextSibling);});function updateStickyPosition(){var scrollTop=window.pageYOffset||document.documentElement.scrollTop;sidebarCards.forEach(function(card,index){var originalTop=originalPositions[index];var stickyTop=index>0?(document.querySelector('.toc-card').style.position==='fixed'?60+document.querySelector('.toc-card').offsetHeight+8:80):60;var placeholder=card.nextSibling;if(scrollTop>originalTop-stickyTop){if(!isFixed[index]){var colContainer=card.closest('[class*=\"col-\"]');var colRect=colContainer.getBoundingClientRect();var colStyle=window.getComputedStyle(colContainer);var paddingLeft=parseFloat(colStyle.paddingLeft)||0;var paddingRight=parseFloat(colStyle.paddingRight)||0;card.style.cssText='position:fixed;top:'+stickyTop+'px;left:'+(colRect.left+paddingLeft)+'px;width:'+(colRect.width-paddingLeft-paddingRight)+'px;z-index:9999;box-sizing:border-box';card.classList.add('fixed');placeholder.style.display='block';isFixed[index]=true;}}else if(isFixed[index]){card.style.cssText='';card.classList.remove('fixed');placeholder.style.display='none';isFixed[index]=false;}});}window.addEventListener('scroll',updateStickyPosition);window.addEventListener('resize',function(){setTimeout(function(){sidebarCards.forEach(function(card,index){if(isFixed[index]){var colContainer=card.closest('[class*=\"col-\"]');var colRect=colContainer.getBoundingClientRect();var colStyle=window.getComputedStyle(colContainer);var paddingLeft=parseFloat(colStyle.paddingLeft)||0;var paddingRight=parseFloat(colStyle.paddingRight)||0;card.style.left=(colRect.left+paddingLeft)+'px';card.style.width=(colRect.width-paddingLeft-paddingRight)+'px';}else{originalPositions[index]=card.getBoundingClientRect().top+window.pageYOffset;}});},100);});updateStickyPosition();}\ndocument.addEventListener('DOMContentLoaded',function(){setTimeout(improvedStickyPosition,1000);});\n    </script>\n\r\n            </div>\r\n        </form>\r\n\r\n        <div class=\"fixed-icon\">\r\n            <div style=\"width: 120px; position: relative; left: 1rem; top: -12px;\">\r\n            </div>\r\n            <a id=\"webchat\" class=\"wx-con disa\" href=\"http://wpa.qq.com/msgrd?v=3&uin=365833440&site=qq&menu=yes\" target=\"_blank\">\r\n                <img class=\"wechat-ico\" src=\"site/image/icon/qq.svg\" alt=\"OCR pomoćnik QQ online korisnička služba\" />\r\n                <div class=\"wx-text\">QQ Služba za korisnike (365833440)</div>\r\n                <label class=\"badge badge-danger\" style=\"background: rgb(255, 34, 122); display: inline-block;\">1</label>\r\n            </a>\r\n\r\n            <a class=\"wx-con\" href=\"https://qm.qq.com/cgi-bin/qm/qr?k=bohNy9GMdgD5bsYrB-fcIjy-s6Ot3zOg&jump_from=webapi&qr=1\" target=\"_blank\">\r\n                <img class=\"wechat-ico\" src=\"site/image/icon/qq.svg\" alt=\"OCR pomoćnik QQ korisnička komunikacijska grupa\" />\r\n                <div class=\"wx-text\">QQ grupa (100029010)</div>\r\n                <label class=\"badge badge-danger\" style=\"background: rgb(255, 34, 122); display: inline-block;\">2</label>\r\n            </a>\r\n            <a href=\"mailto:<EMAIL>\" class=\"wx-con\">\r\n                <img class=\"wechat-ico\" src=\"site/image/icon/im.svg\" alt=\"OCR pomoćnik obratite se korisničkoj službi putem e-pošte\" />\r\n                <div class=\"wx-text\" style=\"width: 240px; text-align: left; left: -15.3rem; padding: 10px 20px;\">\r\n                    <div style=\"font-size: 15px\">E-pošta: <EMAIL></div>\r\n                    <hr style=\"background: #c1c1c1; margin: 10px 10px 6px 0;\" />\r\n                    <span style=\"font-size: 13px; color: #c1c1c1\">Hvala vam na komentarima i prijedlozima!</span>\r\n                </div>\r\n                <label class=\"badge badge-danger\" style=\"background: rgb(255, 34, 122); display: inline-block;\">3</label>\r\n            </a>\r\n        </div>\r\n\r\n        <style>\r\n            .content {\r\n                width: 100%;\r\n                padding: 0 !important;\r\n                margin: 0 auto\r\n            }\r\n\r\n            h3.h6 {\r\n                font-size: 1rem !important;\r\n                font-weight: bold !important;\r\n                line-height: 1.2 !important;\r\n                margin-bottom: .5rem;\r\n                font-family: inherit\r\n            }\r\n        </style>\r\n        <footer class=\"page-footer\">\r\n            <div class=\"container\">\r\n                <div class=\"row foot-cont pt-4 no-gutters\" style=\"border-top: 1px solid #e1e6ed; max-width: 100%\">\r\n                    <div class=\"col-lg-12 hu-an p-0 mb-3 mt-3 text-l color-gray\" style=\"text-align: center;\">\r\n                        OCR pomoćnik za prepoznavanje teksta&nbsp;©️ 2025 ALL RIGHTS RESERVED. Sva prava pridržana&nbsp;&nbsp;&nbsp;|&nbsp;&nbsp;&nbsp;\r\n                        <a href=\"PrivacyPolicy.aspx\" style=\"text-decoration: underline;\">Ugovor o privatnosti</a>&nbsp;&nbsp;&nbsp;|&nbsp;&nbsp;&nbsp;\r\n                        <a href=\"UserAgreement.aspx\" style=\"text-decoration: underline;\">Korisnički ugovor</a>&nbsp;&nbsp;&nbsp;|&nbsp;&nbsp;&nbsp;\r\n                        <a href=\"Status.aspx\" style=\"text-decoration: underline;\">Status usluge</a>&nbsp;&nbsp;&nbsp;|&nbsp;&nbsp;&nbsp;\r\n                        <a class=\"mb-2 color-gray\" href=\"https://beian.miit.gov.cn/\" target=\"_blank\" style=\"text-decoration: underline;\">E ICP priprema br. 2021012692</a>\r\n                    </div>\r\n                </div>\r\n            </div>\r\n        </footer>\r\n        <style>\r\n            #translate > .translateSelectLanguage {\r\n                right: 2rem;\r\n                font-size: 1rem;\r\n                width: 150px;\r\n                padding: .3rem;\r\n                margin-right: 20px;\r\n                border: 1px solid #C9C9C9;\r\n                background: #fff;\r\n                color: #555\r\n            }\r\n        </style>\r\n    </div>\r\n    <script>\r\n        document.addEventListener('DOMContentLoaded', function () {\r\n            var setScroll = function (active) {\r\n                forEach(qa('.v4_header_pc,.v4_header_mob'), function (e) {\r\n                    if (active) addClass(e, 'scrollActive'); else removeClass(e, 'scrollActive');\r\n                });\r\n            };\r\n            if (getScrollY() > 60) setScroll(1);\r\n            window.addEventListener('scroll', function () { setScroll(getScrollY() > 60); });\r\n\r\n            forEach(qa('.v4_header_pc'), function (h) {\r\n                h.addEventListener('mouseenter', function () { addClass(h, 'active'); });\r\n                h.addEventListener('mouseleave', function () { removeClass(h, 'active'); });\r\n            });\r\n\r\n            var menuTimer;\r\n            var hideMenu = function (menuId) {\r\n                var m = document.getElementById(menuId);\r\n                if (m) {\r\n                    m.style.cssText = 'visibility:hidden;height:0;display:none';\r\n                    var n = q('.nav-drown-con', m);\r\n                    if (n) { n.style.cssText = 'height:0;opacity:0;transform:translate(0,-100%)'; }\r\n                }\r\n            };\r\n\r\n            forEach(qa('ul.top-nav>li'), function (i) {\r\n                i.addEventListener('mouseenter', function () {\r\n                    clearTimeout(menuTimer);\r\n                    var l = this.querySelector('a'), t = this.querySelector('.triangle'), s = l ? l.getAttribute('data-sub') : null;\r\n                    if (l) addClass(l, 'active'); if (t) addClass(t, 'active');\r\n                    if (s) {\r\n                        var m = document.getElementById(s);\r\n                        if (m) {\r\n                            m.style.cssText = 'display:block;visibility:visible';\r\n                            var siblings = m.parentNode ? toArray(m.parentNode.children) : [];\r\n                            forEach(siblings, function (c) { if (c !== m) c.style.display = 'none'; });\r\n                            var n = q('.nav-drown-con', m);\r\n                            if (n) { n.style.cssText = 'height:100%;opacity:1;transform:translate(0,0)'; }\r\n\r\n                            m.addEventListener('mouseenter', function () { clearTimeout(menuTimer); });\r\n                            m.addEventListener('mouseleave', function () { menuTimer = setTimeout(function () { hideMenu(s); }, 100); });\r\n                        }\r\n                    }\r\n                });\r\n                i.addEventListener('mouseleave', function () {\r\n                    var l = this.querySelector('a'), t = this.querySelector('.triangle'), s = l ? l.getAttribute('data-sub') : null;\r\n                    if (l) removeClass(l, 'active'); if (t) removeClass(t, 'active');\r\n                    if (s) { menuTimer = setTimeout(function () { hideMenu(s); }, 100); }\r\n                });\r\n            });\r\n\r\n            var r = q('.v4_header_mob .right-menu');\r\n            if (r) r.addEventListener('click', function () {\r\n                var arr = [r, q('.v4_header_mob'), q('#nav_products'), q('.mob-nav-content .sidebar-fix')];\r\n                for (var i = 0; i < arr.length; i++) {\r\n                    var e = arr[i];\r\n                    if (e) {\r\n                        var name = (i === 0 ? 'active' : (i === 1 ? 'active' : (i === 2 ? 'active1' : 'fixedActiveBg')));\r\n                        toggleClass(e, name);\r\n                    }\r\n                }\r\n                var f = q('.mob-nav-content .sidebar-fix');\r\n                if (f) {\r\n                    toggleClass(f, 'show');\r\n                    document.body.style.overflow = hasClass(f, 'show') ? 'hidden' : 'scroll';\r\n                }\r\n            });\r\n\r\n            document.addEventListener('click', function (e) {\r\n                e = e || window.event;\r\n                var target = e.target || e.srcElement;\r\n                var m = closest(target, '.sidebar-fix-left .mob-nav-item');\r\n                if (m) {\r\n                    var p = m.parentNode;\r\n                    if (p) forEach(toArray(p.children), function (c) { removeClass(c, 'active'); });\r\n                    addClass(m, 'active');\r\n                    var d = m.getAttribute('data');\r\n                    if (d) forEach(qa('.sidebar-fix-rigth .sub-nav'), function (n, i) { n.style.display = (i == d ? 'block' : 'none'); });\r\n                }\r\n            });\r\n        });\r\n    </script>\r\n</body>\r\n</html>\r\n\r\n\t            <script type=\"text/javascript\">\r\n                    var _hmt = _hmt || [];\r\n                    (function () {\r\n                        var hm = document.createElement('script');hm.async=1;\r\n                        hm.src = 'https://hm.baidu.com/hm.js?5ee048d7caaf0d439cf16309a1687754';\r\n                        var s = document.getElementsByTagName('script')[0];s.parentNode.insertBefore(hm, s);\r\n                    })();\r\n                </script><div id=\"translate\" class=\"ignore\" style=\"display: none\"></div><script src=\"/static/js/translate.js\"></script>"
﻿"<!DOCTYPE html>\r\n<html xmlns=\"http://www.w3.org/1999/xhtml\" lang=\"yue\">\r\n<head><link rel=\"canonical\" href=\"https://ocr.oldfish.cn/yue/Article.aspx?id=79&slug=deep-learning-math-basics\" /><link rel=\"alternate\" href=\"https://ocr.oldfish.cn/Article.aspx?id=79&slug=deep-learning-math-basics\" hreflang=\"x-default\" />\r\n<link rel=\"alternate\" href=\"https://ocr.oldfish.cn/Article.aspx?id=79&slug=deep-learning-math-basics\" hreflang=\"zh\" />\r\n<link rel=\"alternate\" href=\"https://ocr.oldfish.cn/Article.aspx?id=79&slug=deep-learning-math-basics\" hreflang=\"zh\" />\r\n<link rel=\"alternate\" href=\"https://ocr.oldfish.cn/zh-Hant/Article.aspx?id=79&slug=deep-learning-math-basics\" hreflang=\"zh-tw\" />\r\n<link rel=\"alternate\" href=\"https://ocr.oldfish.cn/yue/Article.aspx?id=79&slug=deep-learning-math-basics\" hreflang=\"zh-yue\" />\r\n<link rel=\"alternate\" href=\"https://ocr.oldfish.cn/en/Article.aspx?id=79&slug=deep-learning-math-basics\" hreflang=\"en\" />\r\n<link rel=\"alternate\" href=\"https://ocr.oldfish.cn/es/Article.aspx?id=79&slug=deep-learning-math-basics\" hreflang=\"es\" />\r\n<link rel=\"alternate\" href=\"https://ocr.oldfish.cn/hi/Article.aspx?id=79&slug=deep-learning-math-basics\" hreflang=\"hi\" />\r\n<link rel=\"alternate\" href=\"https://ocr.oldfish.cn/ar/Article.aspx?id=79&slug=deep-learning-math-basics\" hreflang=\"ar\" />\r\n<link rel=\"alternate\" href=\"https://ocr.oldfish.cn/pt/Article.aspx?id=79&slug=deep-learning-math-basics\" hreflang=\"pt\" />\r\n<link rel=\"alternate\" href=\"https://ocr.oldfish.cn/bn/Article.aspx?id=79&slug=deep-learning-math-basics\" hreflang=\"bn\" />\r\n<link rel=\"alternate\" href=\"https://ocr.oldfish.cn/ru/Article.aspx?id=79&slug=deep-learning-math-basics\" hreflang=\"ru\" />\r\n<link rel=\"alternate\" href=\"https://ocr.oldfish.cn/ja/Article.aspx?id=79&slug=deep-learning-math-basics\" hreflang=\"ja\" />\r\n<link rel=\"alternate\" href=\"https://ocr.oldfish.cn/pa/Article.aspx?id=79&slug=deep-learning-math-basics\" hreflang=\"pa\" />\r\n<link rel=\"alternate\" href=\"https://ocr.oldfish.cn/de/Article.aspx?id=79&slug=deep-learning-math-basics\" hreflang=\"de\" />\r\n<link rel=\"alternate\" href=\"https://ocr.oldfish.cn/fr/Article.aspx?id=79&slug=deep-learning-math-basics\" hreflang=\"fr\" />\r\n<link rel=\"alternate\" href=\"https://ocr.oldfish.cn/mr/Article.aspx?id=79&slug=deep-learning-math-basics\" hreflang=\"mr\" />\r\n<link rel=\"alternate\" href=\"https://ocr.oldfish.cn/te/Article.aspx?id=79&slug=deep-learning-math-basics\" hreflang=\"te\" />\r\n<link rel=\"alternate\" href=\"https://ocr.oldfish.cn/vi/Article.aspx?id=79&slug=deep-learning-math-basics\" hreflang=\"vi\" />\r\n<link rel=\"alternate\" href=\"https://ocr.oldfish.cn/ko/Article.aspx?id=79&slug=deep-learning-math-basics\" hreflang=\"ko\" />\r\n<link rel=\"alternate\" href=\"https://ocr.oldfish.cn/ta/Article.aspx?id=79&slug=deep-learning-math-basics\" hreflang=\"ta\" />\r\n<link rel=\"alternate\" href=\"https://ocr.oldfish.cn/ur/Article.aspx?id=79&slug=deep-learning-math-basics\" hreflang=\"ur\" />\r\n<link rel=\"alternate\" href=\"https://ocr.oldfish.cn/tr/Article.aspx?id=79&slug=deep-learning-math-basics\" hreflang=\"tr\" />\r\n<link rel=\"alternate\" href=\"https://ocr.oldfish.cn/it/Article.aspx?id=79&slug=deep-learning-math-basics\" hreflang=\"it\" />\r\n<link rel=\"alternate\" href=\"https://ocr.oldfish.cn/gu/Article.aspx?id=79&slug=deep-learning-math-basics\" hreflang=\"gu\" />\r\n<link rel=\"alternate\" href=\"https://ocr.oldfish.cn/pl/Article.aspx?id=79&slug=deep-learning-math-basics\" hreflang=\"pl\" />\r\n<link rel=\"alternate\" href=\"https://ocr.oldfish.cn/uk/Article.aspx?id=79&slug=deep-learning-math-basics\" hreflang=\"uk\" />\r\n<link rel=\"alternate\" href=\"https://ocr.oldfish.cn/ms/Article.aspx?id=79&slug=deep-learning-math-basics\" hreflang=\"ms\" />\r\n<link rel=\"alternate\" href=\"https://ocr.oldfish.cn/id/Article.aspx?id=79&slug=deep-learning-math-basics\" hreflang=\"id\" />\r\n<link rel=\"alternate\" href=\"https://ocr.oldfish.cn/ml/Article.aspx?id=79&slug=deep-learning-math-basics\" hreflang=\"ml\" />\r\n<link rel=\"alternate\" href=\"https://ocr.oldfish.cn/kn/Article.aspx?id=79&slug=deep-learning-math-basics\" hreflang=\"kn\" />\r\n<link rel=\"alternate\" href=\"https://ocr.oldfish.cn/fa/Article.aspx?id=79&slug=deep-learning-math-basics\" hreflang=\"fa\" />\r\n<link rel=\"alternate\" href=\"https://ocr.oldfish.cn/nl/Article.aspx?id=79&slug=deep-learning-math-basics\" hreflang=\"nl\" />\r\n<link rel=\"alternate\" href=\"https://ocr.oldfish.cn/th/Article.aspx?id=79&slug=deep-learning-math-basics\" hreflang=\"th\" />\r\n<link rel=\"alternate\" href=\"https://ocr.oldfish.cn/sw/Article.aspx?id=79&slug=deep-learning-math-basics\" hreflang=\"sw\" />\r\n<link rel=\"alternate\" href=\"https://ocr.oldfish.cn/ro/Article.aspx?id=79&slug=deep-learning-math-basics\" hreflang=\"ro\" />\r\n<link rel=\"alternate\" href=\"https://ocr.oldfish.cn/my/Article.aspx?id=79&slug=deep-learning-math-basics\" hreflang=\"my\" />\r\n<link rel=\"alternate\" href=\"https://ocr.oldfish.cn/or/Article.aspx?id=79&slug=deep-learning-math-basics\" hreflang=\"or\" />\r\n<link rel=\"alternate\" href=\"https://ocr.oldfish.cn/he/Article.aspx?id=79&slug=deep-learning-math-basics\" hreflang=\"he\" />\r\n<link rel=\"alternate\" href=\"https://ocr.oldfish.cn/am/Article.aspx?id=79&slug=deep-learning-math-basics\" hreflang=\"am\" />\r\n<link rel=\"alternate\" href=\"https://ocr.oldfish.cn/fil/Article.aspx?id=79&slug=deep-learning-math-basics\" hreflang=\"fil\" />\r\n<link rel=\"alternate\" href=\"https://ocr.oldfish.cn/sv/Article.aspx?id=79&slug=deep-learning-math-basics\" hreflang=\"sv\" />\r\n<link rel=\"alternate\" href=\"https://ocr.oldfish.cn/el/Article.aspx?id=79&slug=deep-learning-math-basics\" hreflang=\"el\" />\r\n<link rel=\"alternate\" href=\"https://ocr.oldfish.cn/cs/Article.aspx?id=79&slug=deep-learning-math-basics\" hreflang=\"cs\" />\r\n<link rel=\"alternate\" href=\"https://ocr.oldfish.cn/hu/Article.aspx?id=79&slug=deep-learning-math-basics\" hreflang=\"hu\" />\r\n<link rel=\"alternate\" href=\"https://ocr.oldfish.cn/be/Article.aspx?id=79&slug=deep-learning-math-basics\" hreflang=\"be\" />\r\n<link rel=\"alternate\" href=\"https://ocr.oldfish.cn/si/Article.aspx?id=79&slug=deep-learning-math-basics\" hreflang=\"si\" />\r\n<link rel=\"alternate\" href=\"https://ocr.oldfish.cn/ne/Article.aspx?id=79&slug=deep-learning-math-basics\" hreflang=\"ne\" />\r\n<link rel=\"alternate\" href=\"https://ocr.oldfish.cn/km/Article.aspx?id=79&slug=deep-learning-math-basics\" hreflang=\"km\" />\r\n<link rel=\"alternate\" href=\"https://ocr.oldfish.cn/sk/Article.aspx?id=79&slug=deep-learning-math-basics\" hreflang=\"sk\" />\r\n<link rel=\"alternate\" href=\"https://ocr.oldfish.cn/bg/Article.aspx?id=79&slug=deep-learning-math-basics\" hreflang=\"bg\" />\r\n<link rel=\"alternate\" href=\"https://ocr.oldfish.cn/fr-ca/Article.aspx?id=79&slug=deep-learning-math-basics\" hreflang=\"fr-ca\" />\r\n<link rel=\"alternate\" href=\"https://ocr.oldfish.cn/ha/Article.aspx?id=79&slug=deep-learning-math-basics\" hreflang=\"ha\" />\r\n<link rel=\"alternate\" href=\"https://ocr.oldfish.cn/yo/Article.aspx?id=79&slug=deep-learning-math-basics\" hreflang=\"yo\" />\r\n<link rel=\"alternate\" href=\"https://ocr.oldfish.cn/ig/Article.aspx?id=79&slug=deep-learning-math-basics\" hreflang=\"ig\" />\r\n<link rel=\"alternate\" href=\"https://ocr.oldfish.cn/ku/Article.aspx?id=79&slug=deep-learning-math-basics\" hreflang=\"ku\" />\r\n<link rel=\"alternate\" href=\"https://ocr.oldfish.cn/rw/Article.aspx?id=79&slug=deep-learning-math-basics\" hreflang=\"rw\" />\r\n<link rel=\"alternate\" href=\"https://ocr.oldfish.cn/ca/Article.aspx?id=79&slug=deep-learning-math-basics\" hreflang=\"ca\" />\r\n<link rel=\"alternate\" href=\"https://ocr.oldfish.cn/da/Article.aspx?id=79&slug=deep-learning-math-basics\" hreflang=\"da\" />\r\n<link rel=\"alternate\" href=\"https://ocr.oldfish.cn/fi/Article.aspx?id=79&slug=deep-learning-math-basics\" hreflang=\"fi\" />\r\n<link rel=\"alternate\" href=\"https://ocr.oldfish.cn/nb/Article.aspx?id=79&slug=deep-learning-math-basics\" hreflang=\"nb\" />\r\n<link rel=\"alternate\" href=\"https://ocr.oldfish.cn/hr/Article.aspx?id=79&slug=deep-learning-math-basics\" hreflang=\"hr\" />\r\n<link rel=\"alternate\" href=\"https://ocr.oldfish.cn/sr-Cyrl/Article.aspx?id=79&slug=deep-learning-math-basics\" hreflang=\"sr-cyrl\" />\r\n<link rel=\"alternate\" href=\"https://ocr.oldfish.cn/sr-Latn/Article.aspx?id=79&slug=deep-learning-math-basics\" hreflang=\"sr-latn\" />\r\n<link rel=\"alternate\" href=\"https://ocr.oldfish.cn/sq/Article.aspx?id=79&slug=deep-learning-math-basics\" hreflang=\"sq\" />\r\n<link rel=\"alternate\" href=\"https://ocr.oldfish.cn/so/Article.aspx?id=79&slug=deep-learning-math-basics\" hreflang=\"so\" />\r\n<link rel=\"alternate\" href=\"https://ocr.oldfish.cn/zu/Article.aspx?id=79&slug=deep-learning-math-basics\" hreflang=\"zu\" />\r\n<link rel=\"alternate\" href=\"https://ocr.oldfish.cn/ka/Article.aspx?id=79&slug=deep-learning-math-basics\" hreflang=\"ka\" />\r\n<meta http-equiv=\"Content-Type\" content=\"text/html; charset=UTF-8\" /><meta http-equiv=\"X-UA-Compatible\" content=\"IE=Edge,chrome=1\" /><meta name=\"renderer\" content=\"webkit\" /><meta name=\"viewport\" content=\"width=device-width, initial-scale=1.0, maximum-scale=5.0\" /><link rel=\"icon\" href=\"/favicon.ico\" type=\"image/x-icon\" /><meta name=\"robots\" content=\"all\" /><link rel=\"preconnect\" href=\"https://scm-file-new-1301864755.cos.ap-beijing.myqcloud.com\" /><link rel=\"preconnect\" href=\"https://cdn.oldfish.cn\" /><link rel=\"dns-prefetch\" href=\"https://hm.baidu.com\" /><link rel=\"dns-prefetch\" href=\"https://cdn.oldfish.cn\" /><link rel=\"preload\" href=\"/site/css/site.css\" as=\"style\" />\r\n<script>function toArray(l){var a=[];for(var i=0;i<(l?l.length:0);i++)a.push(l[i]);return a;}function forEach(l,f){for(var i=0,n=(l?l.length:0);i<n;i++)f(l[i],i);}function hasClass(e,c){if(!e||!c)return false;return(' '+e.className+' ').indexOf(' '+c+' ')>-1;}function addClass(e,c){if(!e||!c||hasClass(e,c))return;e.className=(e.className?e.className+' ':'')+c;}function removeClass(e,c){if(!e||!c)return;var u=' '+e.className+' ';while(u.indexOf(' '+c+' ')>-1){u=u.replace(' '+c+' ',' ');}e.className=u.replace(/^\\s+|\\s+$/g,'');}function toggleClass(e,c){if(!e||!c)return;if(hasClass(e,c))removeClass(e,c);else addClass(e,c);}function matchesSelector(e,s){if(!e||e.nodeType!==1)return false;if(e.matches)return e.matches(s);var p=e.parentNode||document;var m=toArray(p.querySelectorAll(s));for(var i=0;i<m.length;i++)if(m[i]===e)return true;return false;}function closest(e,s){var n=e;while(n&&n.nodeType===1){if(matchesSelector(n,s))return n;n=n.parentNode;}return null;}function safeRemove(e){if(e&&e.parentNode)e.parentNode.removeChild(e);}function qa(s,r){return toArray((r||document).querySelectorAll(s));}function q(s,r){return(r||document).querySelector(s);}function xhrGet(u,o,e){try{var x=new XMLHttpRequest();x.open('GET',u,true);x.onreadystatechange=function(){if(x.readyState===4){if(x.status>=200&&x.status<300)o&&o(x.responseText);else e&&e(x);}};x.send(null);}catch(r){e&&e(r);}}function getScrollY(){return window.pageYOffset||document.documentElement.scrollTop||document.body.scrollTop||0;}function getOrigin(){return window.location.protocol+'//'+window.location.host;}</script>\r\n<script>var isWebP=(function(){try{return document.createElement('canvas').toDataURL('image/webp').indexOf('data:image/webp')===0;}catch(e){return false;}})();document.addEventListener('DOMContentLoaded',function(){var c=document.getElementById('user-login-container');if(c){var u=c.getAttribute('data-url');if(u){xhrGet(u,function(h){c.innerHTML=h;forEach(qa('a',c),function(l){l.style.display='inline-block';l.style.marginLeft='10px';});});}}});</script>\r\n    \n    \n\n    <meta name=\"description\" content=\"深度學習OCR嘅數學基礎，包括線性代數、概率論、優化理論以及神經網絡嘅基本原理。 本文為後續技術文章奠定堅實的理論基礎。\" />\n    <meta name=\"keywords\" content=\"OCR，深度學習，數學基礎，線性代數，神經網絡，優化算法，概率論，OCR文字識別，圖轉文字，OCR技術\" />\n    <meta property=\"og:title\" content=\"【深度學習OCR系列·2】深度學習數學基礎與神經網絡原理\" />\n    <meta property=\"og:description\" content=\"深度學習OCR嘅數學基礎，包括線性代數、概率論、優化理論以及神經網絡嘅基本原理。 本文為後續技術文章奠定堅實的理論基礎。\" />\n    <meta property=\"og:type\" content=\"article\" />\n    <meta property=\"og:url\" content=\"https://ocr.oldfish.cn/Article.aspx?id=79&slug=deep-learning-math-basics&lang=zh-Hans&_translation_source=true\" />\n    <meta property=\"og:site_name\" content=\"OCR文字識別助手\" />\n    <meta name=\"twitter:card\" content=\"summary\" />\n    <meta name=\"twitter:title\" content=\"【深度學習OCR系列·2】深度學習數學基礎與神經網絡原理\" />\n    <meta name=\"twitter:description\" content=\"深度學習OCR嘅數學基礎，包括線性代數、概率論、優化理論以及神經網絡嘅基本原理。 本文為後續技術文章奠定堅實的理論基礎。\" />\n\n    \n    <script type=\"application/ld+json\">\n    {\n        \"@context\": \"https://schema.org\",\n        \"@type\": \"Article\",\n        \"headline\": \"【深度學習OCR系列·2】深度學習數學基礎與神經網絡原理\",\n        \"description\": \"深度學習OCR嘅數學基礎，包括線性代數、概率論、優化理論以及神經網絡嘅基本原理。 本文為後續技術文章奠定堅實的理論基礎。\",\n        \"publisher\": {\n            \"@type\": \"Organization\",\n            \"name\": \"OCR文字識別助手團隊\"\n        },\n        \"datePublished\": \"2025-08-19T06:29:47Z\",\n        \"dateModified\": \"2025-08-19T06:29:47Z\",\n        \"mainEntityOfPage\": {\n            \"@type\": \"WebPage\",\n            \"@id\": \"https://ocr.oldfish.cn/Article.aspx?id=79&slug=deep-learning-math-basics&lang=zh-Hans&_translation_source=true\"\n        }\n    }\n    </script>\n    \n    <style>\n        .reading-progress { position: fixed; top: 0; left: 0; width: 0%; height: 3px; background: linear-gradient(90deg, #1764ff, #0d47a1); z-index: 9999; transition: width 0.2s ease; box-shadow: 0 1px 3px rgba(23, 100, 255, 0.3); }\n        .home-container.root { overflow: visible !important; }\n        .article-detail-container { background: #f5f7fc; min-height: calc(100vh - 60px); padding: 80px 0 60px; position: relative; overflow: visible; }\n        .article-detail-container .row { margin: 0 -12px; align-items: flex-start !important; min-height: 100vh !important; }\n        .article-detail-container .row > [class*=\"col-\"] { padding: 0 12px; box-sizing: border-box; min-width: 0; }\n        .article-detail-container .row > .col-lg-8, .article-detail-container .row > .col-xl-9 { overflow: hidden; }\n        .article-detail-container .row > .col-lg-4, .article-detail-container .row > .col-xl-3 { overflow: visible !important; height: 100% !important; min-height: 100% !important; position: static !important; display: block !important; transform: none !important; will-change: auto !important; contain: none !important; perspective: none !important; filter: none !important; }\n        .article-detail { background: #fff; border-radius: 8px; padding: 1.5rem; margin-bottom: 2rem; box-shadow: 0 2px 10px rgba(0,0,0,0.1); width: 100%; box-sizing: border-box; }\n        .article-title { font-size: 1.7rem; color: #1a1c1f; line-height: 1.3; margin-bottom: 1.5rem; font-weight: 600; }\n        .article-meta { display: flex; align-items: center; flex-wrap: nowrap; gap: 1.2rem; color: #6c757d; font-size: 0.88rem; margin-bottom: 2rem; padding: 1.2rem 1.4rem; background: linear-gradient(135deg, #f8f9fa 0%, #ffffff 100%); border-radius: 12px; border-left: 4px solid #1764ff; box-shadow: 0 2px 8px rgba(0,0,0,0.06); overflow-x: hidden; transition: all 0.3s ease; }\n        .article-meta:hover { box-shadow: 0 4px 16px rgba(0,0,0,0.1); transform: translateY(-1px); }\n        .article-meta .meta-item { display: flex; align-items: center; gap: 0.25rem; font-weight: 500; color: #495057; white-space: nowrap; flex-shrink: 1; transition: color 0.2s ease; cursor: default; }\n        .article-meta .meta-item:hover { color: #1764ff; }\n        .article-meta .meta-item:hover .meta-icon { transform: scale(1.1); filter: drop-shadow(0 2px 6px rgba(13, 71, 161, 0.25)); }\n        .meta-icon { display: inline-block; font-size: 16px; flex-shrink: 0; vertical-align: middle; transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1); margin-right: 4px; opacity: 0.9; }\n        .meta-item:hover .meta-icon { opacity: 1; }\n        .meta-item:hover { color: #1764ff; }\n        .meta-item { position: relative; }\n        .meta-item::after { content: ''; position: absolute; bottom: -2px; left: 0; width: 0; height: 2px; background: linear-gradient(90deg, #1764ff, #00d4ff); transition: width 0.3s ease; }\n        .meta-item:hover::after { width: 100%; }\n        .article-category, .read-time { display: inline-block; padding: 6px 12px; border-radius: 16px; font-size: 0.8rem; font-weight: 600; }\n        .article-category { background: linear-gradient(135deg, #1764ff, #0d47a1); color: white; text-decoration: none; text-transform: uppercase; letter-spacing: 0.5px; transition: all 0.3s ease; }\n        .article-category:hover { background: linear-gradient(135deg, #0d47a1, #1764ff); color: white; text-decoration: none; transform: translateY(-1px); box-shadow: 0 4px 12px rgba(23, 100, 255, 0.3); }\n        .read-time { background: #e3f2fd; color: #1565c0; border: 1px solid #90caf9; }\n        .article-tags { margin: 20px 0 1.5rem; }\n        .article-tags .tag { display: inline-block; background: #1764ff; color: #fff; padding: 4px 12px; border-radius: 12px; font-size: 0.8rem; margin: 0 8px 4px 0; }\n        .article-summary { background: #e3f2fd; border-left: 4px solid #1764ff; padding: 1rem 1.5rem; margin-bottom: 2rem; border-radius: 0 4px 4px 0; }\n        .article-content { line-height: 1.8; font-size: 16px; color: #333; word-wrap: break-word; }\n        .article-content h1, .article-content h2, .article-content h3, .article-content h4, .article-content h5, .article-content h6 { margin: 2rem 0 1rem; font-weight: 600; line-height: 1.25; color: #1a1c1f; }\n        .article-content h1 { font-size: 2rem; border-bottom: 2px solid #e9ecef; padding-bottom: 0.5rem; }\n        .article-content h2 { font-size: 1.5rem; border-bottom: 1px solid #e9ecef; padding-bottom: 0.3rem; }\n        .article-content h3 { font-size: 1.25rem; }\n        .article-content h4 { font-size: 1.1rem; }\n        .article-content h5, .article-content h6 { font-size: 1rem; }\n        .article-content ul, .article-content ol { margin: 1rem 0; padding-left: 2rem; }\n        .article-content ul ul, .article-content ol ol, .article-content ul ol, .article-content ol ul { margin: 0.5rem 0; }\n        .article-content blockquote { margin: 1rem 0; padding: 0.5rem 1rem; border-left: 4px solid #1764ff; background-color: #f8f9fa; color: #6c757d; font-style: italic; }\n        .article-content pre { background-color: #f8f9fa; border: 1px solid #e9ecef; border-radius: 6px; padding: 1rem; margin: 1rem 0; overflow-x: auto; font-family: 'Courier New', Courier, monospace; font-size: 0.9em; line-height: 1.4; }\n        .article-content pre code { background: none; border: none; padding: 0; color: inherit; }\n        .article-content p { margin: 1rem 0; }\n        .article-content p:first-child { margin-top: 0; }\n        .article-content p:last-child { margin-bottom: 0; }\n        .article-footer { margin-top: 4rem; padding: 2.5rem; background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%); border-radius: 12px; border: 1px solid #e9ecef; position: relative; overflow: hidden; }\n        .article-footer::before { content: ''; position: absolute; top: 0; left: 0; right: 0; height: 3px; background: linear-gradient(90deg, #1764ff, #17a2b8, #28a745); }\n        .article-share h6 { color: #495057; font-weight: 600; margin-bottom: 1.5rem; font-size: 16px; }\n        .share-buttons { display: flex; gap: 12px; flex-wrap: wrap; }\n        .share-buttons .btn { padding: 10px 20px; border-radius: 25px; font-weight: 500; font-size: 14px; border: none; transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1); box-shadow: 0 2px 8px rgba(0,0,0,0.1); display: inline-flex; align-items: center; gap: 8px; text-decoration: none; }\n        .btn-weibo { background: linear-gradient(135deg, #e6162d, #c41230); color: white; }\n        .btn-copy { background: #1764ff; color: white; }\n        .btn-print { background: linear-gradient(135deg, #17a2b8, #138496); color: white; }\n        .btn-weibo:hover { background: linear-gradient(135deg, #c41230, #a00e26); box-shadow: 0 4px 16px rgba(230, 22, 45, 0.3); }\n        .btn-copy:hover { background: #0d47a1; box-shadow: 0 4px 16px rgba(23, 100, 255, 0.3); }\n        .btn-print:hover { background: linear-gradient(135deg, #138496, #117a8b); box-shadow: 0 4px 16px rgba(23, 162, 184, 0.3); }\n        .btn-weibo:hover, .btn-copy:hover, .btn-print:hover { transform: translateY(-2px); color: white; }\n        .article-actions { display: flex; justify-content: flex-end; align-items: center; }\n        .related-articles { margin-top: 3rem; }\n        .related-article-item { background: #fff; border-radius: 8px; padding: 1.5rem; margin-bottom: 1rem; box-shadow: 0 2px 8px rgba(0,0,0,0.1); transition: transform 0.3s ease; }\n        .related-article-item:hover { transform: translateY(-2px); }\n        .sidebar-card { background: linear-gradient(135deg, rgba(255,255,255,0.9) 0%, rgba(255,255,255,0.8) 100%); backdrop-filter: blur(20px); border: 1px solid rgba(255,255,255,0.3); border-radius: 16px; padding: 1rem; margin-bottom: 0.5rem; box-shadow: 0 4px 20px rgba(0,0,0,0.08), inset 0 1px 0 rgba(255,255,255,0.2); overflow: visible; box-sizing: border-box; width: 100%; z-index: 10; }\n        .sidebar-card.fixed { box-shadow: 0 8px 32px rgba(0,0,0,0.12); }\n        .sidebar-card::before { content: ''; position: absolute; top: 0; left: 0; right: 0; height: 3px; background: linear-gradient(90deg, #00d4ff, #1764ff, #9c27b0); border-radius: 20px 20px 0 0; }\n        .sidebar-card h5 { color: #1a202c; padding-bottom: 0.8rem; border-bottom: 2px solid rgba(23, 100, 255, 0.2); font-weight: 700; font-size: 1.1rem; }\n        .sidebar { min-height: 100vh; position: relative !important; display: block !important; flex-direction: column !important; gap: 1rem; }\n        .toc-card { flex: 0 1 auto; max-height: 60vh; overflow-y: auto; }\n        .sidebar-card:not(.toc-card) { flex: 1 1 auto; overflow: visible; }\n        .recommended-item { padding: 0.75rem 0; border-bottom: 1px solid rgba(0,0,0,0.05); }\n        .recommended-item:last-child { border-bottom: none; }\n        .recommended-item h6 { margin: 0 0 0.5rem 0; font-size: 0.9rem; line-height: 1.4; }\n        .recommended-item h6 a { color: #1a1c1f; text-decoration: none; transition: color 0.3s ease; }\n        .recommended-item h6 a:hover { color: #1764ff; }\n        .recommended-item .text-muted { font-size: 0.75rem; margin: 0; }\n        .toc-list { list-style: none; padding: 0; margin: 0; max-height: none; overflow-y: visible; overflow-x: hidden; }\n        .toc-list::-webkit-scrollbar { width: 4px; }\n        .toc-list::-webkit-scrollbar-track { background: rgba(0,0,0,0.1); border-radius: 2px; }\n        .toc-list::-webkit-scrollbar-thumb { background: linear-gradient(135deg, #00d4ff, #1764ff); border-radius: 2px; }\n        .toc-list::-webkit-scrollbar-thumb:hover { background: linear-gradient(135deg, #1764ff, #0d47a1); }\n        .toc-list a { color: #4a5568; text-decoration: none; font-size: 13px; font-weight: 500; display: block; padding: 8px 12px; border-radius: 8px; transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1); position: relative; margin-bottom: 4px; border: 1px solid transparent; }\n        .toc-list a::before { content: ''; position: absolute; left: 0; top: 0; bottom: 0; width: 3px; background: linear-gradient(135deg, #00d4ff, #1764ff); border-radius: 0 12px 12px 0; transform: scaleY(0); transition: transform 0.3s ease; }\n        .toc-list a:hover { color: #1764ff; background: linear-gradient(135deg, rgba(0, 212, 255, 0.1), rgba(23, 100, 255, 0.1)); border-color: rgba(23, 100, 255, 0.2); transform: translateX(8px); box-shadow: 0 4px 12px rgba(0, 212, 255, 0.2); }\n        .toc-list a:hover::before, .toc-list a.active::before { transform: scaleY(1); }\n        .toc-list a.active { color: #1764ff; background: linear-gradient(135deg, rgba(0, 212, 255, 0.15), rgba(23, 100, 255, 0.15)); font-weight: 700; border-color: rgba(23, 100, 255, 0.3); transform: translateX(4px); box-shadow: 0 6px 16px rgba(0, 212, 255, 0.25); }\n        .toc-number { color: #1764ff; font-weight: 600; margin-right: 0.5rem; font-size: 0.9em; }\n        .toc-text { flex: 1; }\n        .toc-level-1 { font-weight: 700; font-size: 15px; }\n        .toc-level-2 { font-size: 14px; }\n        .toc-level-3 { font-size: 13px; opacity: 0.9; }\n        .toc-level-4, .toc-level-5, .toc-level-6 { font-size: 12px; opacity: 0.8; }\n        .article-content .formatted-list { margin: 1.5rem 0; padding-left: 0; list-style: none; }\n        .article-content .formatted-list.ordered { counter-reset: list-counter; }\n        .article-content .formatted-list.ordered li { counter-increment: list-counter; position: relative; margin: 1rem 0; padding: 1rem 1rem 1rem 3rem; background: linear-gradient(135deg, rgba(23, 100, 255, 0.03), rgba(0, 212, 255, 0.03)); border-left: 4px solid #1764ff; border-radius: 0 8px 8px 0; transition: all 0.3s ease; }\n        .article-content .formatted-list.ordered li::before { content: counter(list-counter); position: absolute; left: 0.8rem; top: 1rem; background: linear-gradient(135deg, #1764ff, #00d4ff); color: white; width: 1.5rem; height: 1.5rem; border-radius: 50%; display: flex; align-items: center; justify-content: center; font-weight: 600; font-size: 0.9rem; }\n        .article-content .formatted-list.ordered li:hover { background: linear-gradient(135deg, rgba(23, 100, 255, 0.06), rgba(0, 212, 255, 0.06)); transform: translateX(4px); box-shadow: 0 2px 8px rgba(23, 100, 255, 0.15); }\n        .article-content .formatted-list.unordered li { position: relative; margin: 0.8rem 0; padding: 0.8rem 1rem 0.8rem 2.5rem; background: linear-gradient(135deg, rgba(40, 167, 69, 0.03), rgba(25, 135, 84, 0.03)); border-left: 4px solid #28a745; border-radius: 0 6px 6px 0; transition: all 0.3s ease; }\n        .article-content .formatted-list.unordered li::before { content: \"•\"; position: absolute; left: 1rem; top: 0.8rem; color: #28a745; font-size: 1.2rem; font-weight: bold; }\n        .article-content .formatted-list.unordered li:hover { background: linear-gradient(135deg, rgba(40, 167, 69, 0.06), rgba(25, 135, 84, 0.06)); transform: translateX(4px); box-shadow: 0 2px 8px rgba(40, 167, 69, 0.15); }\n        .article-content .list-title { color: #1764ff; font-weight: 600; display: inline-block; margin-bottom: 0.3rem; }\n        .article-content .list-content { color: #2c3e50; line-height: 1.6; display: block; }\n        .article-content .inline-code { background: #f8f9fa; border: 1px solid #e9ecef; border-radius: 4px; padding: 2px 6px; font-family: 'Consolas', 'Monaco', 'Courier New', monospace; font-size: 0.9em; color: #e83e8c; }\n        .article-content .label-item { margin: 0.8rem 0; }\n        .article-content .label { color: #1764ff; font-weight: 600; }\n        .article-content .section-label { color: #2c3e50; font-weight: 600; margin: 1rem 0 0.5rem 0; font-size: 1.1em; border-left: 4px solid #1764ff; padding-left: 0.8rem; }\n        .article-content p, .article-content .content-paragraph { margin: 1.2rem 0; line-height: 1.8; text-align: justify; color: #2c3e50; font-size: 1rem; }\n        .article-content .content-paragraph { text-indent: 0; margin: 1rem 0; }\n        .article-content .indent-paragraph { text-indent: 2em; margin: 1.2rem 0; }\n        .article-content .text-indent-1 { text-indent: 2em; display: block; }\n        .article-content .text-indent-2 { text-indent: 4em; display: block; }\n        .article-content .text-indent-3 { text-indent: 6em; display: block; }\n        .article-content .network-config { background: linear-gradient(135deg, #f8f9fa, #e9ecef); border: 1px solid #dee2e6; border-radius: 6px; padding: 0.8rem 1rem; margin: 0.5rem 0; font-family: 'Consolas', 'Monaco', 'Courier New', monospace; font-size: 0.9rem; box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05); }\n        .article-content .config-label { color: #1764ff; font-weight: 600; }\n        .article-content .config-desc { color: #495057; }\n        .article-content .algorithm-step { background: linear-gradient(135deg, rgba(23, 100, 255, 0.05), rgba(0, 212, 255, 0.05)); border-left: 4px solid #1764ff; padding: 0.8rem 1rem; margin: 0.5rem 0; border-radius: 0 6px 6px 0; transition: all 0.3s ease; }\n        .article-content .step-number { color: #1764ff; font-weight: 700; margin-right: 0.5rem; }\n        .article-content .step-desc { color: #2c3e50; line-height: 1.5; }\n        .article-content table { width: 100%; border-collapse: collapse; margin: 1.5rem 0; background: white; border-radius: 8px; overflow: hidden; box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1); }\n        .article-content table th { background: linear-gradient(135deg, #1764ff, #00d4ff); color: white; padding: 1rem; text-align: left; font-weight: 600; }\n        .article-content table td { padding: 0.8rem 1rem; border-bottom: 1px solid #e9ecef; }\n        .article-content table tr:hover { background: rgba(23, 100, 255, 0.05); }\n        .article-content strong { color: #1764ff; font-weight: 600; }\n        .article-content em { color: #6c757d; font-style: italic; }\n        .article-content a { color: #1764ff; text-decoration: none; border-bottom: 1px solid transparent; transition: all 0.3s ease; }\n        .article-content a:hover { border-bottom-color: #1764ff; color: #0056b3; }\n        .article-content .definition-item { background: linear-gradient(135deg, rgba(40, 167, 69, 0.05), rgba(25, 135, 84, 0.05)); border-left: 4px solid #28a745; padding: 1rem; margin: 1rem 0; border-radius: 0 8px 8px 0; }\n        .article-content .definition-label { color: #28a745; font-weight: 600; display: block; margin-bottom: 0.5rem; }\n        .article-content .definition-content { color: #2c3e50; line-height: 1.6; }\n        .article-content .note-item { background: linear-gradient(135deg, rgba(255, 193, 7, 0.1), rgba(255, 193, 7, 0.05)); border: 1px solid rgba(255, 193, 7, 0.3); border-radius: 8px; padding: 1rem; margin: 1rem 0; position: relative; }\n        .article-content .note-item::before { content: \"💡\"; position: absolute; left: 1rem; top: 1rem; font-size: 1.2em; }\n        .article-content .note-label { color: #856404; font-weight: 600; margin-left: 2rem; }\n        .article-content .note-content { color: #2c3e50; line-height: 1.6; margin-left: 2rem; display: block; margin-top: 0.3rem; }\n        .article-content .math-formula { background: #f8f9fa; border: 1px solid #e9ecef; border-radius: 6px; padding: 1rem; margin: 1rem 0; text-align: center; font-family: 'Times New Roman', serif; font-size: 1.1em; color: #2c3e50; }\n        @media (min-width: 1400px) { .article-content { font-size: 17px; line-height: 1.8; } .article-detail { padding: 2.5rem; } }\n        @media (max-width: 1199px) and (min-width: 992px) { .article-detail { padding: 1.5rem; } .sidebar-card { padding: 1rem; } .toc-list a { font-size: 12px; padding: 6px 8px; } }\n        @media (max-width: 768px) {\n            .article-detail-container { padding: 60px 15px; }\n            .article-content .network-config, .article-content .algorithm-step, .article-content .definition-item, .article-content .note-item { margin: 0.5rem -1rem; border-radius: 0; }\n            .article-content .note-item::before { left: 0.5rem; }\n            .article-content .note-label, .article-content .note-content { margin-left: 1.5rem; }\n            .article-detail { padding: 1.5rem; }\n            .article-title { font-size: 1.6rem; line-height: 1.3; }\n            .article-meta { flex-wrap: wrap; gap: 1rem; padding: 1rem; font-size: 0.8rem; }\n            .article-meta .meta-item { flex-shrink: 1; }\n            .meta-icon { font-size: 14px; margin-right: 3px; }\n            .article-content { font-size: 15px; }\n            .article-content h1 { font-size: 1.6rem; }\n            .article-content h2 { font-size: 1.4rem; }\n            .article-content h3 { font-size: 1.2rem; }\n            .article-content h4 { font-size: 1.1rem; }\n            .article-content pre { padding: 0.75rem; font-size: 0.85em; overflow-x: auto; }\n            .article-content code { font-size: 0.85em; padding: 1px 4px; }\n            .sidebar { margin-top: 2rem; }\n            .sidebar-card { position: static; }\n            .toc-card { max-height: none; overflow-y: visible; }\n            .share-buttons { flex-direction: column; gap: 0.5rem; }\n            .share-buttons .btn { width: 100%; justify-content: center; }\n        }\n        @media print { body * { visibility: hidden; } .article-detail, .article-detail * { visibility: visible; } .article-detail { position: absolute; left: 0; top: 0; width: 100%; background: white; box-shadow: none; border: none; margin: 0; padding: 20px; } .article-meta { border: none; background: none; padding: 0; margin-bottom: 20px; } .article-footer, .reading-progress, .back-to-top { display: none !important; } .article-content { font-size: 12pt; line-height: 1.6; } .article-content h1, .article-content h2, .article-content h3, .article-content h4, .article-content h5, .article-content h6 { page-break-after: avoid; margin-top: 20pt; margin-bottom: 10pt; } .article-content pre, .article-content blockquote { page-break-inside: avoid; } }\n    </style>\n\n<script type=\"application/ld+json\">{\"@context\":\"https://schema.org\",\"@type\":\"BreadcrumbList\",\"itemListElement\":[{\"@type\":\"ListItem\",\"position\":1,\"name\":\"首頁\",\"item\":\"https://ocr.oldfish.cn/default.aspx\"},{\"@type\":\"ListItem\",\"position\":2,\"name\":\"技術文章\",\"item\":\"https://ocr.oldfish.cn/Articles.aspx\"},{\"@type\":\"ListItem\",\"position\":3,\"name\":\"文章詳情\",\"item\":\"https://ocr.oldfish.cn/Article.aspx?id=79&slug=deep-learning-math-basics&lang=zh-Hans&_translation_source=true\"}]}</script>\n<link rel=\"stylesheet\" href=\"/site/css/site.css\" type=\"text/css\" /><title>【深度學習OCR系列·2】深度學習數學基礎與神經網絡原理</title><meta http-equiv=\"Content-Language\" content=\"yue\" />\n</head>\r\n<body>\r\n    <div class=\"home-container root\">\r\n        <header class=\"v4_header_pc\">\r\n            <div class=\"header_pc_left\">\r\n                <a href=\"Default.aspx\" title=\"首頁| AI智能文字識別\" class=\"pc-logo-wrap ml-4 mr-5\">\r\n                    <picture>\r\n                        <source srcset=\"site/image/logo-s.png.webp\" type=\"image/webp\" />\r\n                        <img src=\"site/image/logo-s.png\" class=\"pc-logo\" style=\"height: 100%\" alt=\"OCR文字識別助手官網Logo - AI智能文字識別平台\" />\r\n                    </picture>\r\n                    <span style=\"font-size: 18px; font-weight: bold; color: #333;\">OCR文字識別助手</span>\r\n                </a>\r\n                <ul class=\"top-nav\" role=\"navigation\" aria-label=\"主導航\">\r\n                    <li><a class=\"color-default\" href=\"Default.aspx\" title=\"OCR文字識別助手首頁\">首頁</a></li>\r\n                    <li><a class=\"color-default\" data-sub=\"product-sub-nav\" href=\"Detail.aspx\" title=\"OCR產品功能介紹\">產品功能</a><div class=\"triangle\"></div>\r\n                    </li>\r\n                    <li><a class=\"color-default\" data-sub=\"experience-sub-nav\" href=\"Ocr.aspx\" title=\"在線體驗OCR功能\">在線體驗</a><div class=\"triangle\"></div>\r\n                    </li>\r\n                    <li><a class=\"color-default\" data-sub=\"vip-sub-nav\" href=\"Version.aspx\" title=\"OCR會員升級服務\">會員升級</a><div class=\"triangle\"></div>\r\n                    </li>\r\n                    <li><a class=\"color-default\" href=\"Download.aspx\" title=\"免費下載OCR文字識別助手\">免費下載</a></li>\r\n                    <li><a class=\"color-default\" data-sub=\"articles-sub-nav\" href=\"Articles.aspx\" title=\"OCR技術文章同知識分享\">技術分享</a><div class=\"triangle\"></div>\r\n                    </li>\r\n                    <li><a class=\"color-default\" data-sub=\"support-sub-nav\" href=\"FAQ.aspx\" title=\"OCR使用幫助和技術支持\">幫助中心</a><div class=\"triangle\"></div>\r\n                    </li>\r\n                </ul>\r\n                <div class=\"product-con\">\r\n                    <section id=\"product-sub-nav\" class=\"pls-nav-dropdown\" style=\"visibility: hidden; height: 0px; display: none;\">\r\n                        <div class=\"d-flex nav-drown-con\" style=\"height: 0px; transition: transform 0.2s ease-in-out 0s,opacity 0.2s ease-in-out 0s; opacity: 0; transform: translate(0px, -100%);\">\r\n                            <div class=\"prdocu-sub-left\">\r\n                                <div class=\"mr-auto\">\r\n                                    <picture>\r\n                                        <source class=\"p-icon\" srcset=\"site/image/icon/p_1.png.webp\" type=\"image/webp\" alt=\"\" aria-hidden=\"true\" />\r\n                                        <img class=\"p-icon\" src=\"site/image/icon/p_1.png\" alt=\"OCR產品功能圖標\" aria-hidden=\"true\" loading=\"lazy\" />\r\n                                    </picture>\r\n                                    <div class=\"mt-4\">\r\n                                        <h3 class=\"h6\">OCR文字識別助手</h3>\r\n                                        <p class=\"color-gray fn14 mt-3 sub-left-desc\">提升效率·降低成本·創造價值</p>\r\n                                        <p class=\"color-gray fn14 mt-3 sub-left-desc\">&nbsp;</p>\r\n                                        <p class=\"color-gray fn14 mt-3 sub-left-desc\">智能識別·高速處理·精準輸出</p>\r\n                                        <p class=\"color-gray fn14 mt-3 sub-left-desc\">由文字到表格，由公式到翻譯</p>\r\n                                        <p class=\"color-gray fn14 mt-3 sub-left-desc\">讓每一次文字處理都如此簡單</p>\r\n                                        <a class=\"link block disa fn14 contact-business shake\" href=\"Detail.aspx\">瞭解功能<span class=\"iconfont4 icon-xiangzuo gt\"></span></a>\r\n                                    </div>\r\n                                </div>\r\n                            </div>\r\n                            <div class=\"bg-white prdocu-sub-right\">\r\n                                <div>\r\n                                    <h2 class=\"text-base-color h6\">產品功能</h2>\r\n                                    <div class=\"mt-4 d-flex flex-wrap\">\r\n                                        <a href=\"Detail.aspx\" class=\"pro-item color-default\" title=\"查看OCR助手核心功能詳細介紹\">\r\n                                            <div class=\"letter-wrap\">\r\n                                                <h3 class=\"h6\">核心功能</h3>\r\n                                                <span class=\"color-gray fn14\">詳細了解OCR助手的核心功能和技術優勢，98%+識別率</span>\r\n                                            </div>\r\n                                        </a>\r\n                                        <a href=\"Version.aspx\" class=\"pro-item color-default\" title=\"對比OCR助手各版本功能差異\">\r\n                                            <div class=\"letter-wrap\">\r\n                                                <h3 class=\"h6\">版本對比</h3>\r\n                                                <span class=\"color-gray fn14\">詳細對比免費版、個人版、專業版、旗艦版功能差異</span>\r\n                                            </div>\r\n                                        </a>\r\n                                        <a href=\"FAQ.aspx\" class=\"pro-item color-default\" title=\"查看OCR助手常見問題解答\">\r\n                                            <div class=\"letter-wrap\">\r\n                                                <h3 class=\"h6\">產品問答</h3>\r\n                                                <span class=\"color-gray fn14\">快速了解產品功能、使用方法和常見問題的詳細解答</span>\r\n                                            </div>\r\n                                        </a>\r\n                                        <a href=\"Download.aspx\" class=\"pro-item color-default\" title=\"免費下載OCR文字識別助手\">\r\n                                            <div class=\"letter-wrap\">\r\n                                                <h3 class=\"h6\">免費試用</h3>\r\n                                                <span class=\"color-gray fn14\">立即下載安裝OCR助手，免費體驗強大嘅文字識別功能</span>\r\n                                            </div>\r\n                                        </a>\r\n                                        <div style=\"width: 100%; margin: 15px 0;\">\r\n                                            <h4 class=\"h6\" style=\"color: #007cfa; margin-bottom: 10px;\">在線OCR識別</h4>\r\n                                        </div>\r\n                                        <a href=\"Ocr.aspx?type=text_recognize\" class=\"pro-item color-default\" title=\"在線體驗通用文字識別功能\">\r\n                                            <div class=\"letter-wrap\">\r\n                                                <h3 class=\"h6\">通用文字識別</h3>\r\n                                                <span class=\"color-gray fn14\">多語種高精度文字智能提取，支持印刷體與場景複雜圖識別多</span>\r\n                                            </div>\r\n                                        </a>\r\n                                        <a href=\"Ocr.aspx?type=table\" class=\"pro-item color-default\">\r\n                                            <div class=\"letter-wrap\">\r\n                                                <h3 class=\"h6\">通用表格識別</h3>\r\n                                                <span class=\"color-gray fn14\">表格圖片智能轉Excel文件，自動處理複雜錶結構和合併單元格</span>\r\n                                            </div>\r\n                                        </a>\r\n                                        <a href=\"Ocr.aspx?type=handwritten_ocr\" class=\"pro-item color-default\">\r\n                                            <div class=\"letter-wrap\">\r\n                                                <h3 class=\"h6\">手寫識別</h3>\r\n                                                <span class=\"color-gray fn14\">智能識別中英文手寫內容，支持課堂筆記、病歷記錄等場景</span>\r\n                                            </div>\r\n                                        </a>\r\n                                        <a href=\"Ocr.aspx?type=pdf2word\" class=\"pro-item color-default\">\r\n                                            <div class=\"letter-wrap\">\r\n                                                <h3 class=\"h6\">PDF轉Word</h3>\r\n                                                <span class=\"color-gray fn14\">PDF文檔快速轉為Word格式，完美保留原始排版同圖文布局</span>\r\n                                            </div>\r\n                                        </a>\r\n                                    </div>\r\n                                </div>\r\n                            </div>\r\n                        </div>\r\n                    </section>\r\n                    <section id=\"experience-sub-nav\" class=\"pls-nav-dropdown solution-part\" style=\"visibility: hidden; height: 0px; display: none;\">\r\n                        <div class=\"d-flex nav-drown-con\" style=\"height: 0px; transition: transform 0.2s ease-in-out 0s,opacity 0.2s ease-in-out 0s; opacity: 0; transform: translate(0px, -100%);\">\r\n                            <div class=\"prdocu-sub-left\">\r\n                                <div class=\"mr-auto\">\r\n                                    <picture>\r\n                                        <source srcset=\"site/image/icon/p_4.png.webp\" type=\"image/webp\" />\r\n                                        <img class=\"p-icon\" src=\"site/image/icon/p_4.png\" alt=\"在線OCR體驗中心圖標\" aria-hidden=\"true\" loading=\"lazy\" />\r\n                                    </picture>\r\n                                    <div class=\"mt-4\">\r\n                                        <h3 class=\"h6\">OCR文字識別助手</h3>\r\n                                        <p class=\"color-gray fn14 mt-3 sub-left-desc\">文字、表格、公式、文檔、翻譯</p>\r\n                                        <p class=\"color-gray fn14 mt-3 sub-left-desc\">&nbsp;</p>\r\n                                        <p class=\"color-gray fn14 mt-3 sub-left-desc\">三步完成所有文字處理需求</p>\r\n                                        <p class=\"color-gray fn14 mt-3 sub-left-desc\">截图→識別→應用</p>\r\n                                        <p class=\"color-gray fn14 mt-3 sub-left-desc\">令工作效率提升300%</p>\r\n                                        <a class=\"link block disa fn14 contact-business shake\" href=\"Ocr.aspx\">立即體驗<span class=\"iconfont4 icon-xiangzuo gt\"></span></a>\r\n                                    </div>\r\n                                </div>\r\n                            </div>\r\n                            <div class=\"bg-white prdocu-sub-right\">\r\n                                <div>\r\n                                    <div class=\"mt-4 d-flex flex-wrap\">\r\n                                        <div style=\"width: 100%; margin-bottom: 15px;\">\r\n                                            <h4 class=\"h6\" style=\"color: #007cfa; margin-bottom: 10px;\">OCR功能體驗</h4>\r\n                                        </div>\r\n                                        <a href=\"Ocr.aspx?type=index\" class=\"pro-item color-default\">\r\n                                            <div class=\"letter-wrap\">\r\n                                                <h3 class=\"h6\">全部功能</h3>\r\n                                                <span class=\"color-gray fn14\">一站式體驗所有OCR智能功能，快速找到適合您需求的最佳解決方案</span>\r\n                                            </div>\r\n                                        </a>\r\n                                        <a href=\"Ocr.aspx?type=text_recognize\" class=\"pro-item color-default\">\r\n                                            <div class=\"letter-wrap\">\r\n                                                <h3 class=\"h6\">通用文字識別</h3>\r\n                                                <span class=\"color-gray fn14\">多語種高精度文字智能提取，支持印刷體與場景複雜圖識別多</span>\r\n                                            </div>\r\n                                        </a>\r\n                                        <a href=\"Ocr.aspx?type=table\" class=\"pro-item color-default\">\r\n                                            <div class=\"letter-wrap\">\r\n                                                <h3 class=\"h6\">通用表格識別</h3>\r\n                                                <span class=\"color-gray fn14\">表格圖片智能轉Excel文件，自動處理複雜錶結構和合併單元格</span>\r\n                                            </div>\r\n                                        </a>\r\n                                        <a href=\"Ocr.aspx?type=handwritten_ocr\" class=\"pro-item color-default\">\r\n                                            <div class=\"letter-wrap\">\r\n                                                <h3 class=\"h6\">手寫識別</h3>\r\n                                                <span class=\"color-gray fn14\">智能識別中英文手寫內容，支持課堂筆記、病歷記錄等場景</span>\r\n                                            </div>\r\n                                        </a>\r\n                                        <a href=\"Ocr.aspx?type=pdf2word\" class=\"pro-item color-default\">\r\n                                            <div class=\"letter-wrap\">\r\n                                                <h3 class=\"h6\">PDF轉Word</h3>\r\n                                                <span class=\"color-gray fn14\">PDF文檔快速轉為Word格式，完美保留原始排版同圖文布局</span>\r\n                                            </div>\r\n                                        </a>\r\n                                        <a href=\"Ocr.aspx?type=pdf2markdown\" class=\"pro-item color-default\">\r\n                                            <div class=\"letter-wrap\">\r\n                                                <h3 class=\"h6\">PDF轉Markdown</h3>\r\n                                                <span class=\"color-gray fn14\">PDF文檔智能轉為MD格式，代碼塊和文本結構自動優化處理</span>\r\n                                            </div>\r\n                                        </a>\r\n                                        <div style=\"width: 100%; margin: 15px 0;\">\r\n                                            <h4 class=\"h6\" style=\"color: #007cfa; margin-bottom: 10px;\">文檔處理工具</h4>\r\n                                        </div>\r\n                                        <a href=\"Ocr.aspx?type=word2pdf\" class=\"pro-item color-default\">\r\n                                            <div class=\"letter-wrap\">\r\n                                                <h3 class=\"h6\">Word轉PDF</h3>\r\n                                                <span class=\"color-gray fn14\">Word文檔一鍵轉PDF，完美保留原格式，適合存檔和正式文件分享</span>\r\n                                            </div>\r\n                                        </a>\r\n                                        <a href=\"Ocr.aspx?type=word2jpg\" class=\"pro-item color-default\">\r\n                                            <div class=\"letter-wrap\">\r\n                                                <h3 class=\"h6\">Word轉圖片</h3>\r\n                                                <span class=\"color-gray fn14\">Word快勞智能轉JPG圖，支持多頁處理，便於社交媒體分享</span>\r\n                                            </div>\r\n                                        </a>\r\n                                        <a href=\"Ocr.aspx?type=pdf2jpg\" class=\"pro-item color-default\">\r\n                                            <div class=\"letter-wrap\">\r\n                                                <h3 class=\"h6\">PDF轉圖</h3>\r\n                                                <span class=\"color-gray fn14\">PDF文檔高清轉換為JPG圖片，支持批量處理和自定義分辨率</span>\r\n                                            </div>\r\n                                        </a>\r\n                                        <a href=\"Ocr.aspx?type=image2pdf\" class=\"pro-item color-default\">\r\n                                            <div class=\"letter-wrap\">\r\n                                                <h3 class=\"h6\">圖片轉PDF</h3>\r\n                                                <span class=\"color-gray fn14\">多張圖合併為PDF文檔，支持排序同頁面設置</span>\r\n                                            </div>\r\n                                        </a>\r\n                                        <div style=\"width: 100%; margin: 15px 0;\">\r\n                                            <h4 class=\"h6\" style=\"color: #007cfa; margin-bottom: 10px;\">開發者工具</h4>\r\n                                        </div>\r\n                                        <a href=\"Tool.aspx?type=Json\" class=\"pro-item color-default\">\r\n                                            <div class=\"letter-wrap\">\r\n                                                <h3 class=\"h6\">JSON格式化</h3>\r\n                                                <span class=\"color-gray fn14\">智能美化JSON代碼結構，支持壓縮和展開，便於開發調試</span>\r\n                                            </div>\r\n                                        </a>\r\n                                        <a href=\"Tool.aspx?type=Regex\" class=\"pro-item color-default\">\r\n                                            <div class=\"letter-wrap\">\r\n                                                <h3 class=\"h6\">正則表達式</h3>\r\n                                                <span class=\"color-gray fn14\">實時驗證正則表達式匹配效果，內置常用模式庫</span>\r\n                                            </div>\r\n                                        </a>\r\n                                        <a href=\"Tool.aspx?type=Encode\" class=\"pro-item color-default\">\r\n                                            <div class=\"letter-wrap\">\r\n                                                <h3 class=\"h6\">文本編碼轉換</h3>\r\n                                                <span class=\"color-gray fn14\">支持Base64/URL/Unicode等多種編碼格式互相轉換</span>\r\n                                            </div>\r\n                                        </a>\r\n                                        <a href=\"Tool.aspx?type=Diff\" class=\"pro-item color-default\">\r\n                                            <div class=\"letter-wrap\">\r\n                                                <h3 class=\"h6\">文本比對合併</h3>\r\n                                                <span class=\"color-gray fn14\">高亮顯示文本差異，支持逐行對比和智能合併</span>\r\n                                            </div>\r\n                                        </a>\r\n                                        <a href=\"Tool.aspx?type=Color\" class=\"pro-item color-default\">\r\n                                            <div class=\"letter-wrap\">\r\n                                                <h3 class=\"h6\">顏色工具</h3>\r\n                                                <span class=\"color-gray fn14\">RGB/HEX顏色代碼轉換，在線取色器，前端開發必備工具</span>\r\n                                            </div>\r\n                                        </a>\r\n                                        <a href=\"Tool.aspx?type=Count\" class=\"pro-item color-default\">\r\n                                            <div class=\"letter-wrap\">\r\n                                                <h3 class=\"h6\">字數統計</h3>\r\n                                                <span class=\"color-gray fn14\">智能統計字符、詞彙同段落數量，自動優化文本排版</span>\r\n                                            </div>\r\n                                        </a>\r\n                                        <a href=\"Tool.aspx?type=Timespan\" class=\"pro-item color-default\">\r\n                                            <div class=\"letter-wrap\">\r\n                                                <h3 class=\"h6\">時間戳轉換</h3>\r\n                                                <span class=\"color-gray fn14\">時間與Unix時間戳互相轉換，支持多種格式和時區設置</span>\r\n                                            </div>\r\n                                        </a>\r\n                                        <a href=\"Tool.aspx?type=Calc\" class=\"pro-item color-default\">\r\n                                            <div class=\"letter-wrap\">\r\n                                                <h3 class=\"h6\">計算器工具</h3>\r\n                                                <span class=\"color-gray fn14\">在線科學計算器，支持基礎運算和高級數學函數計算</span>\r\n                                            </div>\r\n                                        </a>\r\n                                    </div>\r\n                                </div>\r\n                            </div>\r\n                        </div>\r\n                    </section>\r\n                    <section id=\"articles-sub-nav\" class=\"pls-nav-dropdown solution-part\" style=\"visibility: hidden; height: 0px; display: none;\">\r\n                        <div class=\"d-flex nav-drown-con\" style=\"height: 0px; transition: transform 0.2s ease-in-out 0s,opacity 0.2s ease-in-out 0s; opacity: 0; transform: translate(0px, -100%);\">\r\n                            <div class=\"prdocu-sub-left\">\r\n                                <div class=\"mr-auto\">\r\n                                    <picture>\r\n                                        <source srcset=\"site/image/icon/p_4.png.webp\" type=\"image/webp\" />\r\n                                        <img class=\"p-icon\" src=\"site/image/icon/p_4.png\" alt=\"技術分享中心圖標\" aria-hidden=\"true\" loading=\"lazy\" />\r\n                                    </picture>\r\n                                    <div class=\"mt-4\">\r\n                                        <h3 class=\"h6\">OCR技術分享</h3>\r\n                                        <p class=\"color-gray fn14 mt-3 sub-left-desc\">技術教程、應用案例、工具舉薦</p>\r\n                                        <p class=\"color-gray fn14 mt-3 sub-left-desc\">&nbsp;</p>\r\n                                        <p class=\"color-gray fn14 mt-3 sub-left-desc\">由入門到精通嘅完整學習路徑</p>\r\n                                        <p class=\"color-gray fn14 mt-3 sub-left-desc\">實戰案例→技術解析→工具應用</p>\r\n                                        <p class=\"color-gray fn14 mt-3 sub-left-desc\">助力您的OCR技術提升之路</p>\r\n                                        <a class=\"link block disa fn14 contact-business shake\" href=\"Articles.aspx\">瀏覽文章<span class=\"iconfont4 icon-xiangzuo gt\"></span></a>\r\n                                    </div>\r\n                                </div>\r\n                            </div>\r\n                            <div class=\"bg-white prdocu-sub-right\">\r\n                                <div>\r\n                                    <h2 class=\"text-base-color h6\">技術分享</h2>\r\n                                    <div class=\"mt-4 d-flex flex-wrap\">\r\n                                        <a href=\"Articles.aspx\" class=\"pro-item color-default\" title=\"查看所有OCR技術文章\">\r\n                                            <div class=\"letter-wrap\">\r\n                                                <h3 class=\"h6\">全部文章</h3>\r\n                                                <span class=\"color-gray fn14\">瀏覽所有OCR技術文章，涵蓋由基礎到高級嘅完整知識體系</span>\r\n                                            </div>\r\n                                        </a>\r\n                                        \r\n                                        <a href=\"Articles.aspx?category=2\" class=\"pro-item color-default\" title=\"OCR技術教程同入門指南\">\r\n                                            <div class=\"letter-wrap\">\r\n                                                <h3 class=\"h6\">進階指南</h3>\r\n                                                <span class=\"color-gray fn14\">由入門到精通嘅OCR技術教程，詳細嘅操作指南同實戰演練</span>\r\n                                            </div>\r\n                                        </a>\r\n                                        \r\n                                        <a href=\"Articles.aspx?category=3\" class=\"pro-item color-default\" title=\"OCR技術原理、算法同應用\">\r\n                                            <div class=\"letter-wrap\">\r\n                                                <h3 class=\"h6\">技術探索</h3>\r\n                                                <span class=\"color-gray fn14\">探索OCR技術前沿，由原理到應用，深度解析核心算法</span>\r\n                                            </div>\r\n                                        </a>\r\n                                        \r\n                                        <a href=\"Articles.aspx?category=4\" class=\"pro-item color-default\" title=\"OCR行業最新動態和發展趨勢\">\r\n                                            <div class=\"letter-wrap\">\r\n                                                <h3 class=\"h6\">行業趨勢</h3>\r\n                                                <span class=\"color-gray fn14\">OCR技術發展趨勢、市場分析、行業動態同未來展望嘅深度洞察</span>\r\n                                            </div>\r\n                                        </a>\r\n                                        \r\n                                        <a href=\"Articles.aspx?category=5\" class=\"pro-item color-default\" title=\"OCR技術喺各行業嘅應用案例\">\r\n                                            <div class=\"letter-wrap\">\r\n                                                <h3 class=\"h6\">應用案例</h3>\r\n                                                <span class=\"color-gray fn14\">OCR技術喺各行業嘅實際應用案例、解決方案同最佳實踐分享</span>\r\n                                            </div>\r\n                                        </a>\r\n                                        \r\n                                        <a href=\"Articles.aspx?category=6\" class=\"pro-item color-default\" title=\"OCR軟件工具嘅專業評測、對比分析和使用舉薦指南\">\r\n                                            <div class=\"letter-wrap\">\r\n                                                <h3 class=\"h6\">工具評測</h3>\r\n                                                <span class=\"color-gray fn14\">評測各類OCR文字識別軟件和工具，提供詳細嘅功能對比同選擇建議</span>\r\n                                            </div>\r\n                                        </a>\r\n                                        \r\n                                    </div>\r\n                                </div>\r\n                            </div>\r\n                        </div>\r\n                    </section>\r\n                    <section id=\"vip-sub-nav\" class=\"pls-nav-dropdown solution-part\" style=\"visibility: hidden; height: 0px; display: none;\">\r\n                        <div class=\"d-flex nav-drown-con\" style=\"height: 0px; transition: transform 0.2s ease-in-out 0s,opacity 0.2s ease-in-out 0s; opacity: 0; transform: translate(0px, -100%);\">\r\n                            <div class=\"prdocu-sub-left\">\r\n                                <div class=\"mr-auto\">\r\n                                    <picture>\r\n                                        <source class=\"p-icon\" srcset=\"site/image/icon/p_1.png.webp\" type=\"image/webp\" alt=\"\" aria-hidden=\"true\" />\r\n                                        <img class=\"p-icon\" src=\"site/image/icon/p_1.png\" alt=\"會員升級服務圖標\" aria-hidden=\"true\" loading=\"lazy\" />\r\n                                    </picture>\r\n                                    <div class=\"mt-4\">\r\n                                        <h3 class=\"h6\">會員升級服務</h3>\r\n                                        <p class=\"color-gray fn14 mt-3 sub-left-desc\">解鎖全部高級功能·享受專屬服務</p>\r\n                                        <p class=\"color-gray fn14 mt-3 sub-left-desc\">&nbsp;</p>\r\n                                        <p class=\"color-gray fn14 mt-3 sub-left-desc\">離線識別·批量處理·無限制使用</p>\r\n                                        <p class=\"color-gray fn14 mt-3 sub-left-desc\">專業版→旗艦版→企業版</p>\r\n                                        <p class=\"color-gray fn14 mt-3 sub-left-desc\">總有一款適合您的需求</p>\r\n                                        <a class=\"link block disa fn14 contact-business shake\" href=\"Version.aspx\">查看詳情<span class=\"iconfont4 icon-xiangzuo gt\"></span></a>\r\n                                    </div>\r\n                                </div>\r\n                            </div>\r\n                            <div class=\"bg-white prdocu-sub-right\">\r\n                                <div>\r\n                                    <h2 class=\"text-base-color h6\" style=\"margin: inherit;\">會員升級</h2>\r\n                                    <div class=\"mt-4 d-flex flex-wrap\">\r\n                                        <a href=\"Version.aspx\" class=\"pro-item color-default\">\r\n                                            <div class=\"letter-wrap\">\r\n                                                <h3 class=\"h6\">會員特權</h3>\r\n                                                <span class=\"color-gray fn14\">詳細了解各版本功能差異，選擇最適合您的會員等級</span>\r\n                                            </div>\r\n                                        </a>\r\n                                        <a href=\"Upgrade.aspx\" class=\"pro-item color-default\">\r\n                                            <div class=\"letter-wrap\">\r\n                                                <h3 class=\"h6\">立即升級</h3>\r\n                                                <span class=\"color-gray fn14\">快速升級VIP會員，解鎖更多高級功能和專屬服務</span>\r\n                                            </div>\r\n                                        </a>\r\n                                        <a href=\"User.aspx\" class=\"pro-item color-default\">\r\n                                            <div class=\"letter-wrap\">\r\n                                                <h3 class=\"h6\">我嘅賬戶</h3>\r\n                                                <span class=\"color-gray fn14\">管理賬戶信息、訂閱狀態和使用記錄，個性化設置</span>\r\n                                            </div>\r\n                                        </a>\r\n                                    </div>\r\n                                </div>\r\n                            </div>\r\n                        </div>\r\n                    </section>\r\n                    <section id=\"support-sub-nav\" class=\"pls-nav-dropdown solution-part\" style=\"visibility: hidden; height: 0px; display: none;\">\r\n                        <div class=\"d-flex nav-drown-con\" style=\"height: 0px; transition: transform 0.2s ease-in-out 0s,opacity 0.2s ease-in-out 0s; opacity: 0; transform: translate(0px, -100%);\">\r\n                            <div class=\"prdocu-sub-left\">\r\n                                <div class=\"mr-auto\">\r\n                                    <picture>\r\n                                        <source class=\"p-icon\" srcset=\"site/image/icon/p_4.png.webp\" type=\"image/webp\" alt=\"\" aria-hidden=\"true\" />\r\n                                        <img class=\"p-icon\" src=\"site/image/icon/p_4.png\" alt=\"幫助中心支持圖標\" aria-hidden=\"true\" loading=\"lazy\" />\r\n                                    </picture>\r\n                                    <div class=\"mt-4\">\r\n                                        <h3 class=\"h6\">幫助中心</h3>\r\n                                        <p class=\"color-gray fn14 mt-3 sub-left-desc\">專業客服·詳細文檔·快速響應</p>\r\n                                        <p class=\"color-gray fn14 mt-3 sub-left-desc\">&nbsp;</p>\r\n                                        <p class=\"color-gray fn14 mt-3 sub-left-desc\">遇到問題唔使怕</p>\r\n                                        <p class=\"color-gray fn14 mt-3 sub-left-desc\">問題→查找→解決</p>\r\n                                        <p class=\"color-gray fn14 mt-3 sub-left-desc\">讓您的使用體驗更順暢</p>\r\n                                        <a class=\"link block disa fn14 contact-business shake\" href=\"FAQ.aspx\">獲取幫助<span class=\"iconfont4 icon-xiangzuo gt\"></span></a>\r\n                                    </div>\r\n                                </div>\r\n                            </div>\r\n                            <div class=\"bg-white prdocu-sub-right\">\r\n                                <div>\r\n                                    <h2 class=\"text-base-color h6\" style=\"margin: inherit;\">幫助中心</h2>\r\n                                    <div class=\"mt-4 d-flex flex-wrap\">\r\n                                        <a href=\"FAQ.aspx\" class=\"pro-item color-default\">\r\n                                            <div class=\"letter-wrap\">\r\n                                                <h3 class=\"h6\">常見問題</h3>\r\n                                                <span class=\"color-gray fn14\">快速解答用戶常見疑問，提供詳細嘅使用指南同技術支持</span>\r\n                                            </div>\r\n                                        </a>\r\n                                        <a href=\"AboutUs.aspx\" class=\"pro-item color-default\">\r\n                                            <div class=\"letter-wrap\">\r\n                                                <h3 class=\"h6\">關於我哋</h3>\r\n                                                <span class=\"color-gray fn14\">瞭解OCR文字識別助手嘅發展歷程、核心功能和服務理念</span>\r\n                                            </div>\r\n                                        </a>\r\n                                        <a href=\"UserAgreement.aspx\" class=\"pro-item color-default\">\r\n                                            <div class=\"letter-wrap\">\r\n                                                <h3 class=\"h6\">用戶協議</h3>\r\n                                                <span class=\"color-gray fn14\">詳細嘅服務條款和用戶權利義務說明</span>\r\n                                            </div>\r\n                                        </a>\r\n                                        <a href=\"PrivacyPolicy.aspx\" class=\"pro-item color-default\">\r\n                                            <div class=\"letter-wrap\">\r\n                                                <h3 class=\"h6\">私隱協議</h3>\r\n                                                <span class=\"color-gray fn14\">個人信息保護政策和數據安全保障措施</span>\r\n                                            </div>\r\n                                        </a>\r\n                                        <a href=\"Status.aspx\" class=\"pro-item color-default\">\r\n                                            <div class=\"letter-wrap\">\r\n                                                <h3 class=\"h6\">系統狀態</h3>\r\n                                                <span class=\"color-gray fn14\">實時監控全球識別節點運行狀態，查看系統性能數據</span>\r\n                                            </div>\r\n                                        </a>\r\n                                        <a href=\"javascript:void(0);\" class=\"pro-item color-default\" onclick=\"if(window.openCustomerService) window.openCustomerService(); else alert('請點擊右側浮窗圖標聯繫客服');\">\r\n                                            <div class=\"letter-wrap\">\r\n                                                <h3 class=\"h6\">聯繫客服</h3>\r\n                                                <span class=\"color-gray fn14\">在線客服撐，快速響應您的問題和需求</span>\r\n                                            </div>\r\n                                        </a>\r\n                                    </div>\r\n                                </div>\r\n                            </div>\r\n                        </div>\r\n                    </section>\r\n                </div>\r\n            </div>\r\n            <div class=\"header_pc_right\" style=\"display: flex; align-items: center; justify-content: flex-end;\">\r\n                <div id=\"translate\"></div>\r\n                <div id=\"user-login-container\" data-url=\"User.ashx?op=userinfo\" style=\"white-space: nowrap; padding-right: 20px;\"></div>\r\n            </div>\r\n        </header>\r\n        <header class=\"v4_header_mob\">\r\n            <div class=\"container\">\r\n                <div class=\"row no-gutters justify-content-between align-items-center\">\r\n                    <a href=\"Default.aspx\" title=\"首頁| AI智能文字識別\" class=\"mob-logo-wrap\">\r\n                        <picture>\r\n                            <source srcset=\"site/image/logo-s.png.webp\" type=\"image/webp\" />\r\n                            <img src=\"site/image/logo-s.png\" class=\"pc-logo\" style=\"height: 32px; width: 32px\" alt=\"OCR文字識別助手移動端Logo\" />\r\n                        </picture>\r\n                        <span style=\"font-size: 18px; font-weight: bold; color: #333;\">OCR文字識別助手</span>\r\n                    </a>\r\n                    <div class=\"right-menu\" role=\"button\" tabindex=\"0\" aria-label=\"打開導航餐牌\" data-toggle=\"collapse\" data-target=\"#mob-header-collapse\" aria-expanded=\"false\" aria-controls=\"mob-header-collapse\">\r\n                        <nav-products id=\"nav_products\">\r\n                            <nav-button class=\"submail-mdi-nav-btn\" style=\"width: 60px\">\r\n                                <label id=\"mdi-submail-nav-btn\">\r\n                                    <span class=\"mdi-nav-products-1\">\r\n                                        <span class=\"nav-products-icon\"></span>\r\n                                    </span>\r\n                                    <span class=\"mdi-nav-products-2\">\r\n                                        <span class=\"nav-products-icon\"></span>\r\n                                    </span>\r\n                                    <span class=\"mdi-nav-products-3\">\r\n                                        <span class=\"nav-products-icon\"></span>\r\n                                    </span>\r\n                                </label>\r\n                            </nav-button>\r\n                        </nav-products>\r\n                    </div>\r\n                </div>\r\n            </div>\r\n            <div class=\"mob-nav-content\">\r\n                <div class=\"sidebar-fix\">\r\n                    <div class=\"container\">\r\n                        <div class=\"row\">\r\n                            <div class=\"col-4 bg-white sidebar-fix-left\" style=\"border-right: 1px solid #e1e6ed\">\r\n                                <div class=\"mob-nav-item active\" data=\"0\">\r\n                                    <div class=\"nav-header\"><a href=\"Default.aspx\" class=\"nav-header-letter px-3\"><span>首頁</span></a></div>\r\n                                </div>\r\n                                <div class=\"mob-nav-item\" data=\"1\">\r\n                                    <div class=\"nav-header\"><a href=\"Detail.aspx\" class=\"nav-header-letter px-3\"><span>功能</span></a></div>\r\n                                </div>\r\n                                <div class=\"mob-nav-item\" data=\"2\">\r\n                                    <div class=\"nav-header\"><a href=\"Ocr.aspx\" class=\"nav-header-letter px-3\"><span>體驗</span></a></div>\r\n                                </div>\r\n                                <div class=\"mob-nav-item\" data=\"3\">\r\n                                    <div class=\"nav-header\"><a href=\"Version.aspx\" class=\"nav-header-letter px-3\"><span>會員</span></a></div>\r\n                                </div>\r\n                                <div class=\"mob-nav-item\" data=\"4\">\r\n                                    <div class=\"nav-header\"><a href=\"Download.aspx\" class=\"nav-header-letter px-3\"><span>下載</span></a></div>\r\n                                </div>\r\n                                <div class=\"mob-nav-item\" data=\"5\">\r\n                                    <div class=\"nav-header\"><a href=\"Articles.aspx\" class=\"nav-header-letter px-3\"><span>分享</span></a></div>\r\n                                </div>\r\n                                <div class=\"mob-nav-item\" data=\"6\">\r\n                                    <div class=\"nav-header\"><a href=\"FAQ.aspx\" class=\"nav-header-letter px-3\"><span>幫手</span></a></div>\r\n                                </div>\r\n                            </div>\r\n                            <div class=\"col-8 bg-white sidebar-fix-rigth\">\r\n                                <div class=\"sub-nav\">\r\n                                    <div class=\"sub-nav-item py-2 pb-3 px-4\">\r\n                                        <h2 class=\"h5\" style=\"margin: inherit;\">高效率生產力工具</h2>\r\n                                        <p class=\"color-gray fn14 mt-3 sub-left-desc\">智能識別·高速處理·精準輸出</p>\r\n                                        <p class=\"color-gray fn14 mt-3 sub-left-desc\">&nbsp;</p>\r\n                                        <p class=\"color-gray fn14 mt-3 sub-left-desc\">3秒識別整頁文檔</p>\r\n                                        <p class=\"color-gray fn14 mt-3 sub-left-desc\">98%+識別準確率</p>\r\n                                        <p class=\"color-gray fn14 mt-3 sub-left-desc\">多語種實時處理無延遲</p>\r\n                                        <a class=\"link block disa fn14 contact-business shake\" href=\"Download.aspx\">立即下載體驗<span class=\"iconfont4 icon-xiangzuo gt\"></span></a>\r\n                                    </div>\r\n                                </div>\r\n                                <div class=\"sub-nav\">\r\n                                    <div class=\"sub-nav-item py-3 px-4\">\r\n                                        <h2 class=\"h5\" style=\"margin: inherit;\">產品功能</h2>\r\n                                        <p class=\"color-gray fn14 mt-3 sub-left-desc\">AI智能識別，一站式解決方案</p>\r\n                                    </div>\r\n                                    <div class=\"sub-nav-item py-3 px-4\"><a href=\"Detail.aspx\">功能介紹</a></div>\r\n                                    <div class=\"sub-nav-item py-3 px-4\"><a href=\"Download.aspx\">軟件下載</a></div>\r\n                                    <div class=\"sub-nav-item py-3 px-4\"><a href=\"Version.aspx\">版本對比</a></div>\r\n                                    <div class=\"sub-nav-item py-3 px-4\"><a href=\"Ocr.aspx\">在線體驗</a></div>\r\n                                    <div class=\"sub-nav-item py-3 px-4\"><a href=\"Status.aspx\">系統狀態</a></div>\r\n                                </div>\r\n                                <div class=\"sub-nav\">\r\n                                    <div class=\"sub-nav-item py-3 px-4\">\r\n                                        <h2 class=\"h5\" style=\"margin: inherit;\">在線體驗</h2>\r\n                                        <p class=\"color-gray fn14 mt-3 sub-left-desc\">免費在線OCR功能體驗</p>\r\n                                    </div>\r\n                                    <div class=\"sub-nav-item py-3 px-4\"><a href=\"Ocr.aspx\">全部功能</a></div>\r\n                                    <div class=\"sub-nav-item py-3 px-4\"><a href=\"Ocr.aspx?type=text_recognize\">文字識別</a></div>\r\n                                    <div class=\"sub-nav-item py-3 px-4\"><a href=\"Ocr.aspx?type=table\">表格識別</a></div>\r\n                                    <div class=\"sub-nav-item py-3 px-4\"><a href=\"Ocr.aspx?type=pdf2word\">PDF轉Word</a></div>\r\n                                </div>\r\n                                <div class=\"sub-nav\">\r\n                                    <div class=\"sub-nav-item py-3 px-4\">\r\n                                        <h2 class=\"h5\" style=\"margin: inherit;\">會員升級</h2>\r\n                                        <p class=\"color-gray fn14 mt-3 sub-left-desc\">解鎖全部功能，享受專屬服務</p>\r\n                                    </div>\r\n                                    <div class=\"sub-nav-item py-3 px-4\"><a href=\"Version.aspx\">會員權益</a></div>\r\n                                    <div class=\"sub-nav-item py-3 px-4\"><a href=\"Upgrade.aspx\">立即開通</a></div>\r\n                                </div>\r\n                                <div class=\"sub-nav\">\r\n                                    <div class=\"sub-nav-item py-3 px-4\">\r\n                                        <h2 class=\"h5\" style=\"margin: inherit;\">軟件下載</h2>\r\n                                        <p class=\"color-gray fn14 mt-3 sub-left-desc\">免費下載專業OCR軟件</p>\r\n                                    </div>\r\n                                    <div class=\"sub-nav-item py-3 px-4\"><a href=\"Download.aspx\">立即下載</a></div>\r\n                                    <div class=\"sub-nav-item py-3 px-4\"><a href=\"Version.aspx\">版本對比</a></div>\r\n                                </div>\r\n                                <div class=\"sub-nav\">\r\n                                    <div class=\"sub-nav-item py-3 px-4\">\r\n                                        <h2 class=\"h5\" style=\"margin: inherit;\">技術分享</h2>\r\n                                        <p class=\"color-gray fn14 mt-3 sub-left-desc\">OCR技術文章同知識分享</p>\r\n                                    </div>\r\n                                    <div class=\"sub-nav-item py-3 px-4\"><a href=\"Articles.aspx\">全部文章</a></div>\r\n                                    \r\n                                    <div class=\"sub-nav-item py-3 px-4\"><a href=\"Articles.aspx?category=2\">進階指南</a></div>\r\n                                    \r\n                                    <div class=\"sub-nav-item py-3 px-4\"><a href=\"Articles.aspx?category=3\">技術探索</a></div>\r\n                                    \r\n                                    <div class=\"sub-nav-item py-3 px-4\"><a href=\"Articles.aspx?category=4\">行業趨勢</a></div>\r\n                                    \r\n                                    <div class=\"sub-nav-item py-3 px-4\"><a href=\"Articles.aspx?category=5\">應用案例</a></div>\r\n                                    \r\n                                    <div class=\"sub-nav-item py-3 px-4\"><a href=\"Articles.aspx?category=6\">工具評測</a></div>\r\n                                    \r\n                                </div>\r\n                                <div class=\"sub-nav\">\r\n                                    <div class=\"sub-nav-item py-3 px-4\">\r\n                                        <h2 class=\"h5\" style=\"margin: inherit;\">幫助中心</h2>\r\n                                        <p class=\"color-gray fn14 mt-3 sub-left-desc\">專業客服，貼心服務</p>\r\n                                    </div>\r\n                                    <div class=\"sub-nav-item py-3 px-4\"><a href=\"FAQ.aspx\">使用幫助</a></div>\r\n                                    <div class=\"sub-nav-item py-3 px-4\"><a href=\"AboutUs.aspx\">關於我哋</a></div>\r\n                                    <div class=\"sub-nav-item py-3 px-4\"><a href=\"mailto:<EMAIL>\">聯繫客服</a></div>\r\n                                    <div class=\"sub-nav-item py-3 px-4\"><a href=\"UserAgreement.aspx\">服務條款</a></div>\r\n                                </div>\r\n                            </div>\r\n                        </div>\r\n                    </div>\r\n                </div>\r\n            </div>\r\n        </header>\r\n        <form method=\"post\" action=\"./Article.aspx?id=79&amp;slug=deep-learning-math-basics&amp;lang=zh-Hans&amp;_translation_source=true\" id=\"aspnetForm\">\r\n<div class=\"aspNetHidden\">\r\n<input type=\"hidden\" name=\"__VIEWSTATE\" id=\"__VIEWSTATE\" value=\"DNnd8kgfgEhJ12o7Zmw7SthuvhRNQjSD3TnKJBuhRtBAX0/GJwkWZIgwpu6QztAfxTgwBu0YhqXClARXoN9NvX/4rwsh+1EHABOW8AnJ5w8=\" />\r\n</div>\r\n\r\n<div class=\"aspNetHidden\">\r\n\r\n\t<input type=\"hidden\" name=\"__VIEWSTATEGENERATOR\" id=\"__VIEWSTATEGENERATOR\" value=\"2173C2F0\" />\r\n</div>\r\n            <div>\r\n                \n\n<div class=\"reading-progress\" id=\"readingProgress\"></div>\n<div class=\"article-detail-container\">\n<div class=\"container\">\n<div class=\"row\">\n<div class=\"col-lg-8 col-xl-9 col-md-12\">\n                    \n                    <article class=\"article-detail\" id=\"articleDetail\" data-article-id=\"79\">\n                        <header class=\"article-header\">\n                            <h1 class=\"article-title\">【深度學習OCR系列·2】深度學習數學基礎與神經網絡原理</h1>\n                            <div class=\"article-meta\">\n                                <div class=\"meta-item\">\n                                    <span class=\"meta-icon\">📅</span>\n                                    <span>發佈時間：2025年08月19日</span>\n                                </div>\n                                <div class=\"meta-item\">\n                                    <span class=\"meta-icon\">👁️</span>\n                                    <span>閱讀量：<span class=\"view-count\">1180</span></span>\n                                </div>\n                                <div class=\"meta-item\">\n                                    <span class=\"meta-icon\">⏱️</span>\n                                    <span>約66分鐘（ 13195字）</span>\n                                </div>\n                                \n                                <div class=\"meta-item\">\n                                    <span class=\"meta-icon\">📁</span>\n                                    <span>類別：進階指南</span>\n                                </div>\n                                \n                            </div>\n                            <div class=\"article-summary\"><p>深度學習OCR嘅數學基礎，包括線性代數、概率論、優化理論以及神經網絡嘅基本原理。 本文為後續技術文章奠定堅實的理論基礎。</p></div>\n                        </header>\n                        <div class=\"article-content\" id=\"articleContent\">##引言\r\n\r\n深度學習OCR技術嘅成功離不開實淨嘅數學基礎。 本文把系統介紹深度學習中涉及嘅核心數學概念，包括線性代數、概率論、優化理論以及神經網絡嘅基本原理。 呢啲數學工具係理解同實現高效OCR系統嘅基石。\r\n\r\n##線性代數基礎\r\n\r\n###向量與矩陣運算\r\n\r\n在深度學習中，數據通常以向量和矩阵嘅形式表示：\r\n\r\n**向量運算**：\r\n-向量加灋：v 2 + v 2 = [v 2 + v ₂ + v ₂ ₁，v ₁ 2 + v₂ ₂，...，v ₁n + v ₂n]\r\n-標量乘法：αv = [αv₁，αv₂，...，αvn]\r\n-點積：v2· v₂ = Σᵢ v₁ᵢv₂ᵢ\r\n\r\n**矩陣運算**：\r\n-矩阵乘法：C = AB，其中Cij = ök AikBkj\r\n-轉置：AT，其中（ AT ） ij = Aji\r\n-逆矩阵：AA−¹= I\r\n\r\n###特徵值與特徵向量\r\n\r\n對於方陣A，如果存在標量λ和非零向量v使得：\r\n\r\n則λ稱為特徵值，v稱為對應的特徵向量。\r\n\r\n###奇異值分解（SVD）\r\n\r\n任意矩阵A都可以分解為：\r\n\r\n其中U同V係正交矩陣，“係對角矩陣。\r\n\r\n##概率論與統計學基礎\r\n\r\n###概率分佈\r\n\r\n**常見概率分佈 **：\r\n\r\n1. **正態分佈**：\r\n   p(x) = (1/√(2πσ²)) exp(-(x-μ)²/(2σ²))\r\n\r\n2. **伯努利分佈**：\r\n   p(x) = pˣ(1-p)¹⁻ˣ\r\n\r\n3. **多項式分佈**：\r\n   p(x₁,...,xₖ) = (n!) /(x₁!... xₖ!) p₁^x₁... pₖ^xₖ\r\n\r\n###貝葉斯定理\r\n\r\nP(A| B) = P(B| A)P(A)/P(B)\r\n\r\n在機器學習中，贝叶斯定理用于：\r\n-參數估計\r\n-模型選擇\r\n-不塙定性量化\r\n\r\n###信息論基礎\r\n\r\n**釧**：\r\nH(X) = -Σᵢ p(xᵢ)log p(xᵢ)\r\n\r\n**交叉硎**：\r\nH(p,q) = -Σᵢ p(xᵢ)log q(xᵢ)\r\n\r\n**KL散度**：\r\nDₖL(p|| q) = Σᵢ p(xᵢ)log(p(xᵢ)/q(xᵢ))\r\n\r\n##優化理論\r\n\r\n###梯度下降法\r\n\r\n**基本梯度下降**：\r\nθₜ₊₁ = θₜ - α∇f(θₜ)\r\n\r\n其中α是學習率，∇f（θt）是梯度。\r\n\r\n**隨機梯度下降（SGD）**：\r\nθₜ₊₁ = θₜ - α∇f(θₜ; xᵢ, yᵢ)\r\n\r\n**小批量梯度下降**：\r\nθₜ₊₁ = θₜ - α(1/m)Σᵢ∇f(θₜ; xᵢ, yᵢ)\r\n\r\n###高級優化算法\r\n\r\n**動量法**：\r\nvₜ₊₁ = βvₜ + α∇f(θₜ)\r\nθₜ₊₁ = θₜ - vₜ₊₁\r\n\r\n**Adam 優化器 **：\r\nmₜ₊₁ = β₁mₜ + (1-β₁)∇f(θₜ)\r\nvₜ₊₁ = β₂vₜ + (1-β₂)(∇f(θₜ))²\r\nθₜ₊₁ = θₜ - α(m̂ₜ₊₁)/(√v̂ₜ₊₁ + ε)\r\n\r\n##神經網絡基本原理\r\n\r\n###感知機模型\r\n\r\n**單層感知機**：\r\n\r\n其中f係激活函數，w係權重，B係偏置。\r\n\r\n**多層感知機（MLP）**：\r\n-輸入層：接收原始數據\r\n-隱藏層：特徵變換和非線性映射\r\n-輸出層：產生最終預測結果\r\n\r\n###激活函數\r\n\r\n**常用激活函數**：\r\n\r\n1. **Sigmoid**：\r\n   σ(x) = 1/(1 + e⁻ˣ)\r\n\r\n2. **Tanh**：\r\n   tanh(x) = (eˣ - e⁻ˣ)/(eˣ + e⁻ˣ)\r\n\r\n3. **ReLU**：\r\n   ReLU(x) = max(0, x)\r\n\r\n4. **Leaky ReLU**：\r\n   LeakyReLU(x) = max(αx, x)\r\n\r\n5. **GELU**：\r\n   GELU(x) = x · Φ(x)\r\n\r\n###反向傳播算法\r\n\r\n**鏈式法則**：\r\n∂L/∂w = (∂L/∂y)(∂y/∂z)(∂z/∂w)\r\n\r\n**梯度計算**：\r\n對於網絡層l：\r\nδˡ = (∂L/∂zˡ)\r\n∂L/∂wˡ = δˡ(aˡ⁻¹)ᵀ\r\n∂L/∂bˡ = δˡ\r\n\r\n**反向傳播步驟**：\r\n1.前向傳播計算輸出\r\n2.計算輸出層誤差\r\n3.反向傳播誤差\r\n4.更新權重和偏置\r\n\r\n##損失函數\r\n\r\n###回歸任務損失函數\r\n\r\n**均方誤差（MSE）**：\r\n\r\n**平均絕對誤差（MAE）**：\r\n\r\n**Huber損失**：\r\n    {δ|y-ŷ| - ½δ² otherwise\r\n\r\n###分類任務損失函數\r\n\r\n**交叉熵損失 **：\r\n\r\n**Focal損失**：\r\n\r\n**Hinge損失 **：\r\n\r\n##正則化技術\r\n\r\n### L1和L2正則化\r\n\r\n**L1正則化（Lasso）**：\r\n\r\n**L2正則化（Ridge）**：\r\n\r\n**Elastic Net**：\r\n\r\n### Dropout\r\n\r\n在訓練過程中隨機將一些神經元的輸出設為0：\r\nyᵢ = {xᵢ/p with probability p\r\n     {0 with probability 1-p\r\n\r\n### Batch Normalization\r\n\r\n對每個小批量進行標準化：\r\nx̂ᵢ = (xᵢ - μ)/√(σ² + ε)\r\nyᵢ = γx̂ᵢ + β\r\n\r\n## OCR中嘅數學應用\r\n\r\n###圖像預處理嘅數學基礎\r\n\r\n**卷積運算**：\r\n(f * g) (t) = Σₘ f(m)g(t-m)\r\n\r\n**傅里葉變換**：\r\nF(ω) = ∫ f(t)e⁻ⁱωᵗdt\r\n\r\n**高斯濾波**：\r\nG(x,y) = (1/(2πσ²))e⁻⁽ˣ²⁺ʸ²⁾/²σ²\r\n\r\n###序列建模嘅數學基礎\r\n\r\n**循環神經網絡**：\r\nhₜ = tanh(Wₕₕhₜ₋₁ + Wₓₕxₜ + bₕ)\r\nyₜ = Wₕᵧhₜ + bᵧ\r\n\r\n**LSTM門控機制**：\r\nfₜ = σ(Wf·[ hₜ₋₁, xₜ] + bf)\r\niₜ = σ(Wi·[ hₜ₋₁, xₜ] + bi)\r\nC̃ₜ = tanh(WC·[ hₜ₋₁, xₜ] + bC)\r\nCₜ = fₜ * Cₜ₋₁ + iₜ * C̃ₜ\r\noₜ = σ(Wo·[ hₜ₋₁, xₜ] + bo)\r\nhₜ = oₜ * tanh(Cₜ)\r\n\r\n###注意力機制嘅數學表示\r\n\r\n**自注意力**：\r\nAttention(Q,K,V) = softmax(QKᵀ/√dₖ)V\r\n\r\n**多頭注意力**：\r\nMultiHead(Q,K,V) = Concat(head₁,...,headₕ)W^O\r\n其中headi = Attention （ QWi^Q，KWi^K，VWi^V ）\r\n\r\n##數值計算考慮\r\n\r\n###數值穩定性\r\n\r\n**梯度消失**：\r\n当梯度值過鐘頭，深層網絡難以訓練。\r\n\r\n**梯度爆炸**：\r\n当梯度值過大時，參數更新唔穩定。\r\n\r\n**解決方案**：\r\n-梯度裁剪\r\n-殘差連接\r\n-批標準化\r\n-合適嘅權重初始化\r\n\r\n###浮點數精度\r\n\r\n**IEEE 754 標準**：\r\n-單精度（ 32位）：1位符號+ 8位指數+ 23位尾數\r\n-雙精度（64位）：1位符號 + 11位指數 + 52位尾數\r\n\r\n**數值誤差**：\r\n-捨入誤差\r\n-截斷誤差\r\n-累積誤差\r\n\r\n##深度學習中嘅數學應用\r\n\r\n###矩阵運算喺神經網絡中嘅應用\r\n\r\n在神經網絡中，矩阵运算係核心操作：\r\n\r\n1. **權重矩陣**：存儲神經元之間的連接強度\r\n2. **輸入向量**：表示輸入數據的特徵\r\n3. **輸出計算 **：透過矩陣乘法計算層間傳播\r\n\r\n矩阵乘法嘅並行性使得神經網絡能夠高效處理大批量數據，係深度學習能夠實現嘅重要數學基礎。\r\n\r\n###概率論喺損失函數中嘅應用\r\n\r\n概率論為深度學習提供了理論框架：\r\n\r\n1. **最大似然估計**：許多損失函數都基於最大似然原理\r\n2. **貝葉斯推理**：為模型不確定性提供理論基礎\r\n3. **信息論**：交叉熵等損失函數來源於信息論\r\n\r\n###優化理論嘅實際意義\r\n\r\n優化算法嘅選擇直接影響模型訓練效果：\r\n\r\n1. **收斂速度 **：不同算法的收斂速度差異很大\r\n2. **穩定性**：算法的穩定性影響訓練的可靠性\r\n3. **泛化能力**：優化過程影響模型的泛化性能\r\n\r\n##數學基礎與OCR嘅聯繫\r\n\r\n###圖像處理中嘅線性代數\r\n\r\n喺OCR嘅圖像處理階段，線性代數發揮重要作用：\r\n\r\n1. **圖像變換 **：旋轉、縮放、平移等幾何變換\r\n2. **濾波操作**：通過卷積運算實現圖像增強\r\n3. **特徵提取**：主成分分析（ PCA ）等降維技術\r\n\r\n###概率模型喺文字識別中嘅應用\r\n\r\n概率論為OCR提供了處理不確定性的工具：\r\n\r\n1. **字符識別**：基於概率的字符分類\r\n2. **語言模型**：利用統計語言模型改善識別結果\r\n3. **置信度評估 **：為識別結果提供可信度評估\r\n\r\n###優化算法喺模型訓練中嘅作用\r\n\r\n優化算法決定咗OCR模型嘅訓練效果：\r\n\r\n1. **參數更新**：通過梯度下降更新網絡參數\r\n2. **損失最小化**：尋找最優的參數配置\r\n3. **正則化 **：防止過擬合，提高泛化能力\r\n\r\n##實踐中嘅數學思維\r\n\r\n###數學建模嘅重要性\r\n\r\n在深度學習OCR中，數學建模能力決定咗我哋能否：\r\n\r\n1. **準確描述問題**：將實際的OCR問題轉化為數學優化問題\r\n2. **選擇合適方法 **：根據問題特點選擇最適合的數學工具\r\n3. **分析模型行為**：理解模型的收斂性、穩定性和泛化能力\r\n4. **優化模型性能**：通過數學分析找到性能瓶頸並改進\r\n\r\n###理論與實踐的結合\r\n\r\n數學理論為OCR實踐提供指導：\r\n\r\n1. **算法設計 **：基於數學原理設計更有效的算法\r\n2. **參數調優 **：利用數學分析指導超參數選擇\r\n3. **問題診斷 **：通過數學分析診斷訓練中的問題\r\n4. **性能預測**：基於理論分析預測模型性能\r\n\r\n###數學直覺嘅培養\r\n\r\n培養數學直覺對OCR開發至關重要：\r\n\r\n1. **幾何直覺 **：理解高維空間中的數據分佈和變換\r\n2. **概率直覺 **：理解不確定性和隨機性的影響\r\n3. **優化直覺 **：理解損失函數的形狀和優化過程\r\n4. **統計直覺**：理解數據的統計特性和模型的統計行為\r\n\r\n##技術發展趨勢\r\n\r\n###人工智能技術融合\r\n\r\n當前技術發展呈現出多技術融合嘅趨勢：\r\n\r\n**深度學習與傳統方法結合**：\r\n-結合傳統圖像處理技術嘅優勢\r\n-利用深度學習嘅強大學習能力\r\n-實現優勢互補，提高整體性能\r\n-降低對大量標註數據嘅依賴\r\n\r\n**模態技術多融合**：\r\n-文本、圖像、語音等多模態信息融合\r\n-提供更豐富嘅上下文信息\r\n-提高系統嘅理解同處理能力\r\n-支持更複雜嘅應用場景\r\n\r\n###算法優化與創新\r\n\r\n**模型架構創新**：\r\n-新型神經網絡架構嘅不斷湧現\r\n-針對特定任務嘅專用架構設計\r\n-自動化架構搜索技術嘅應用\r\n-輕量化模型設計嘅重要性\r\n\r\n**訓練方法改進**：\r\n-自監督學習減少標註需求\r\n-遷移學習提高訓練效率\r\n-對抗訓練增強模型魯棒性\r\n-聯邦學習保護數據私隱\r\n\r\n###工程化與產業化\r\n\r\n**系統集成優化**：\r\n-端到端系統設計理念\r\n-糢塊化架構提高可維護性\r\n-標準化接口促進技術復用\r\n-雲原生架構支持彈性擴展\r\n\r\n**性能優化技術**：\r\n-模型壓縮與加速技術\r\n-硬件加速器嘅廣泛應用\r\n-邊緣計算部署優化\r\n-實時處理能力提升\r\n\r\n##實際應用挑戰\r\n\r\n###技術挑戰\r\n\r\n**準確性要求**：\r\n-不同應用場景對準確性要求差異好大\r\n-錯誤成本高嘅場景需要極高準確率\r\n-平衡準確性與處理速度嘅關係\r\n-提供可信度評估同不塙定性量化\r\n\r\n**魯棒性需求**：\r\n-應對各種干擾因素的影響\r\n-處理數據分布變化嘅挑戰\r\n-適應不同環境同條件\r\n-保持長期穩定嘅性能表現\r\n\r\n###工程挑戰\r\n\r\n**系統集成複雜性**：\r\n-多個技術組件嘅協調配合\r\n-不同系統間嘅接口標準化\r\n-版本兼容性和升級管理\r\n-故障診斷和恢復機制\r\n\r\n**部署與維護**：\r\n-大規模部署嘅管理複雜性\r\n-持續監控和性能優化\r\n-模型更新同版本打理\r\n-用戶培訓和技術支持\r\n\r\n##解決方案與最佳實踐\r\n\r\n###技術解決方案\r\n\r\n**分層架構設計**：\r\n-基礎層：核心算法同模型\r\n-服務層：業務邏輯和流程控制\r\n-接口層：用戶交互和系統集成\r\n-數據層：數據存儲同打理\r\n\r\n**質量保證體系**：\r\n-全面嘅測試策略同方法\r\n-持續集成和持續部署\r\n-性能監控和預警機制\r\n-用戶反饋收集和處理\r\n\r\n###管理最佳實踐\r\n\r\n**項目管理**：\r\n-敏捷開發方法嘅應用\r\n-跨團隊協作機制建立\r\n-風險識別和控制措施\r\n-進度跟蹤和質量控制\r\n\r\n**團隊建設**：\r\n-技術人員能力培養\r\n-知識管理和經驗分享\r\n-創新文化和學習氛圍\r\n-激勵機制和職業發展\r\n\r\n##未來展望\r\n\r\n###技術發展方向\r\n\r\n**智能化水平提升**：\r\n-從自動化向智能化發展\r\n-具備學習和適應能力\r\n-支持複雜決策和推理\r\n-實現人機協作新模式\r\n\r\n**應用領域拓展**：\r\n-向更多垂直領域擴展\r\n-支持更複雜嘅業務場景\r\n-與其他技術深度融合\r\n-創造新嘅應用價值\r\n\r\n###產業發展趨勢\r\n\r\n**標準化進程**：\r\n-技術標準嘅制定同推廣\r\n-行業規範嘅建立同完善\r\n-互操作性嘅提升\r\n-生態系統嘅健康發展\r\n\r\n**商業模式創新**：\r\n-服務化和平台化發展\r\n-開源與商業嘅平衡\r\n-數據價值嘅挖掘利用\r\n-新嘅商業機會湧現\r\n## OCR技術嘅特殊考慮\r\n\r\n###文字識別嘅獨特挑戰\r\n\r\n**多語言支持**：\r\n-不同語言嘅文字特徵差異\r\n-複雜文字系統嘅處理難度\r\n-混合語言文檔嘅識別挑戰\r\n-古文字同特殊字體嘅撐\r\n\r\n**場景適應性**：\r\n-自然場景文字嘅複雜性\r\n-文檔圖像嘅質素變化\r\n-手寫文字嘅個性化特徵\r\n-藝術字體嘅識別難度\r\n\r\n### OCR系統優化策略\r\n\r\n**數據處理優化**：\r\n-圖像預處理技術嘅改進\r\n-數據增強方法嘅創新\r\n-合成數據嘅生成同利用\r\n-標註質素嘅控制同提升\r\n\r\n**模型設計優化**：\r\n-針對文字特徵嘅網絡設計\r\n-多尺度特徵融合技術\r\n-注意力機制嘅有效應用\r\n-端到端優化嘅實現方法\r\n\r\n##文檔智能處理技術體系\r\n\r\n###技術架構設計\r\n\r\n文檔智能處理系統採用分層架構設計，確保各個組件嘅協調配合：\r\n\r\n**基礎層技術**：\r\n-文檔格式解析：支持PDF、Word、圖像等多種格式\r\n-圖像預處理：去噪、校正、增強等基礎處理\r\n-版面分析：識別文檔嘅物理結構同邏輯結構\r\n-文本識別：準確提取文檔中嘅文字內容\r\n\r\n**理解層技術**：\r\n-語義分析：理解文本嘅深層含義同上下文關係\r\n-實體識別：識別人名、地名、機構名等關鍵實體\r\n-關係抽取：發現實體間嘅語義關係\r\n-知識圖譜：構建結構化嘅知識表示\r\n\r\n**應用層技術**：\r\n-智能問答：基於文檔內容嘅自動問答\r\n-內容摘要：自動生成文檔摘要和關鍵信息\r\n-信息檢索：高效嘅文檔搜索和匹配\r\n-決策撐：基於文檔分析嘅智能決策\r\n\r\n###核心算法原理\r\n\r\n**模態融合多算法**：\r\n-文本同圖像信息嘅聯合建模\r\n-跨模態注意力機制\r\n-多模態特徵對正技術\r\n-統一表示學習方法\r\n\r\n**結構化信息提取**：\r\n-表格識別和解析算法\r\n-列表同層次結構識別\r\n-圖表信息提取技術\r\n-版面元素關係建模\r\n\r\n**語義理解技術**：\r\n-深度語言模型應用\r\n-上下文感知嘅文本理解\r\n-領域知識融入方法\r\n-推理同邏輯分析能力\r\n\r\n##應用場景與解決方案\r\n\r\n###金融行業應用\r\n\r\n**風險控制文檔處理**：\r\n-貸款申請材料自動審核\r\n-財務報表信息提取\r\n-合規文檔檢查\r\n-風險評估報告生成\r\n\r\n**客戶服務優化**：\r\n-客戶諮詢文檔分析\r\n-投訴處理自動化\r\n-產品推薦系統\r\n-個性化服務定製\r\n\r\n###法律行業應用\r\n\r\n**法律文檔分析**：\r\n-合同條款自動提取\r\n-法律風險識別\r\n-案例檢索同匹配\r\n-法規合規性檢查\r\n\r\n**訴訟支持系統**：\r\n-證據文檔整理\r\n-案件相關性分析\r\n-判決書信息提取\r\n-法律研究輔助\r\n\r\n###醫療行業應用\r\n\r\n**病歷管理系統**：\r\n-電子病歷結構化\r\n-診斷信息提取\r\n-治療方案分析\r\n-醫療質素評估\r\n\r\n**醫學研究支持**：\r\n-文獻信息挖掘\r\n-臨床試驗數據分析\r\n-藥物相互作用檢測\r\n-疾病關聯性研究\r\n\r\n##技術挑戰與解決策略\r\n\r\n###準確性挑戰\r\n\r\n**複雜文檔處理**：\r\n-多欄布局嘅準確識別\r\n-表格同圖表嘅精確解析\r\n-手寫同印刷混合文檔\r\n-低質素掃描件處理\r\n\r\n**解決策略**：\r\n-深度學習模型優化\r\n-多模型集成方法\r\n-數據增強技術\r\n-後處理規則優化\r\n\r\n###效率挑戰\r\n\r\n**大規模處理需求**：\r\n-海量文檔嘅批量處理\r\n-實時響應要求\r\n-計算資源優化\r\n-存儲空間管理\r\n\r\n**優化方案**：\r\n-分佈式處理架構\r\n-緩存機制設計\r\n-模型壓縮技術\r\n-硬件加速應用\r\n\r\n###適應性挑戰\r\n\r\n**多樣化需求**：\r\n-不同行業嘅特殊要求\r\n-多語言文檔支持\r\n-個性化定製需求\r\n-新興應用場景\r\n\r\n**解決方法**：\r\n-糢塊化系統設計\r\n-可配置嘅處理流程\r\n-遷移學習技術\r\n-持續學習機制\r\n\r\n##質素保證體系\r\n\r\n###準確性保證\r\n\r\n**多層驗證機制**：\r\n-算法層面嘅準確性驗證\r\n-業務邏輯嘅合理性檢查\r\n-人工審核嘅質素控制\r\n-用戶反饋嘅持續改進\r\n\r\n**質素評估指標**：\r\n-信息提取準確率\r\n-結構識別完整性\r\n-语义理解正確性\r\n-用戶滿意度評價\r\n\r\n###可靠性保證\r\n\r\n**系統穩定性**：\r\n-容錯機制設計\r\n-異常處理策略\r\n-性能監控體系\r\n-故障恢復機制\r\n\r\n**數據安全性**：\r\n-私隱保護措施\r\n-數據加密技術\r\n-訪問控制機制\r\n-審計日誌記錄\r\n\r\n##未來發展方向\r\n\r\n###技術發展趨勢\r\n\r\n**智能化水平提升**：\r\n-更強嘅理解同推理能力\r\n-自主學習和適應能力\r\n-跨領域知識遷移\r\n-人機拍檔優化\r\n\r\n**技術融合創新**：\r\n-與大語言模型嘅深度融合\r\n-多模態技術嘅進一步發展\r\n-知識圖譜技術嘅應用\r\n-邊緣計算嘅部署優化\r\n\r\n###應用拓展前景\r\n\r\n**新興應用領域**：\r\n-智慧城市建設\r\n-數字政府服務\r\n-在線教育平台\r\n-智能製造系統\r\n\r\n**服務模式創新**：\r\n-雲原生服務架構\r\n- API經濟模式\r\n-生態系統建設\r\n-開放平台戰略\r\n\r\n##技術原理深度解析\r\n\r\n###理論基礎\r\n\r\n該技術嘅理論基礎建立喺多個學科嘅交叉融合之上，包括計算機科學、數學、統計學和認知科學等領域嘅重要理論成果。\r\n\r\n**數學理論支撐**：\r\n-線性代數：為數據表示同變換提供數學工具\r\n-概率論：處理不塙定性同隨機性問題\r\n-優化理論：指導模型參數嘅學習同調整\r\n-信息論：量化信息內容同傳輸效率\r\n\r\n**計算機科學基礎**：\r\n-算法設計：高效算法嘅設計同分析\r\n-數據結構：適合嘅數據組織和存儲方式\r\n-並行計算：充分利用現代計算資源\r\n-系統架構：可擴展和可維護嘅系統設計\r\n\r\n###核心算法機制\r\n\r\n**特徵學習機制**：\r\n現代深度學習方法能夠自動學習數據的層次化特徵表示，這一能力是傳統方法難以企及的。 透過多層非線性變換，網絡能夠由原始數據中提取出越嚟越抽象同高級嘅特徵。\r\n\r\n**注意力機制原理**：\r\n注意力機制模擬咗人類認知過程中嘅選擇性注意，要模型能夠動態咁關注輸入嘅不同部分。 呢種機制不僅提高咗模型嘅性能，仲增強埋模型嘅可解釋性。\r\n\r\n**優化算法設計**：\r\n深度學習模型嘅訓練依賴於高效嘅優化算法。 由基礎嘅梯度下降到現代嘅自適應優化方法，算法嘅選擇同調優對模型性能有着決定性影響。\r\n\r\n##實際應用場景分析\r\n\r\n###工業應用實踐\r\n\r\n**製造業應用**：\r\n在製造業中，該技術被廣泛應用於質量控制、生產監控、設備維護等環節。 透過實時分析生產數據，能夠及時發現問題並採取相應措施。\r\n\r\n**服務業應用**：\r\n服務業中嘅應用主要集中喺客戶服務、業務流程優化、決策支持等方面。 智能化嘅服務系統能夠提供更加個性化和高效嘅服務體驗。\r\n\r\n**金融業應用 **：\r\n金融行業對準確性和實時性要求極高，該技術喺風險控制、欺詐檢測、投資決策等方面發揮著重要作用。\r\n\r\n###技術集成策略\r\n\r\n**系統集成方法**：\r\n在實際應用中，往往需要把多種技術進行有機結合，形成完整嘅解決方案。 要求我哋不僅要掌握單一技術，仲要理解不同技術間嘅協調配合。\r\n\r\n**數據流設計**：\r\n合理嘅數據流設計係系統成功嘅關鍵。 由數據採集、預處理、分析到結果輸出，每個環節都需要精心設計和優化。\r\n\r\n**接口標準化**：\r\n標準化嘅接口設計益咗系統嘅擴展同維護，都便於與其他系統嘅集成。\r\n\r\n##性能優化策略\r\n\r\n###算法層面優化\r\n\r\n**模型結構優化**：\r\n透過改進網絡架構、調整層數和參數數量等方式，可以喺保持性能嘅同時提高計算效率。\r\n\r\n**訓練策略優化**：\r\n採用合適的訓練策略，如學習率調度、批量大小選擇、正則化技術等，能夠顯著提高模型的訓練效果。\r\n\r\n**推理優化**：\r\n在部署階段，透過模型壓縮、量化、剪枝等技術，可以大幅減少計算資源需求。\r\n\r\n###系統層面優化\r\n\r\n**硬件加速**：\r\n充分利用GPU、TPU等專用硬件嘅並行計算能力，可以顯著提升系統性能。\r\n\r\n**分佈式計算**：\r\n對於大規模應用，分佈式計算架構是必不可少的。 合理嘅任務分配和負載均衡策略能夠最大化系統吞吐量。\r\n\r\n**緩存機制**：\r\n智能嘅緩存策略可以減少重複計算，提高系統響應速度。\r\n\r\n##質素保證體系\r\n\r\n###測試驗證方法\r\n\r\n**功能測試**：\r\n全面嘅功能測試確保系統各項功能正常工作，包括正常情況和異常情況嘅處理。\r\n\r\n**性能測試**：\r\n性能測試評估系統喺不同負載下嘅表現，確保系統能夠滿足實際應用嘅性能要求。\r\n\r\n**魯棒性測試**：\r\n魯棒性測試驗證系統喺面對各種干擾和異常情況時嘅穩定性和可靠性。\r\n\r\n###持續改進機制\r\n\r\n**監控體系**：\r\n建立完善嘅監控體系，實時跟蹤系統運行狀態和性能指標。\r\n\r\n**反饋機制**：\r\n建立用戶反饋收集和處理機制，及時發現和解決問題。\r\n\r\n**版本管理**：\r\n規範嘅版本管理流程確保系統嘅穩定性和可追溯性。\r\n\r\n##發展趨勢與展望\r\n\r\n###技術發展方向\r\n\r\n**智能化程度提升**：\r\n未來嘅技術發展將朝着更高嘅智能化水平發展，具備更強嘅自主學習和適應能力。\r\n\r\n**跨領域融合**：\r\n不同技術領域嘅融合將產生新嘅突破，帶來更多嘅應用可能性。\r\n\r\n**標準化進程**：\r\n技術標準化將促進行業的健康發展，降低應用門檻。\r\n\r\n###應用前景展望\r\n\r\n**新興應用領域**：\r\n隨著技術嘅成熟，將會湧現出更多新嘅應用領域同場景。\r\n\r\n**社會影響**：\r\n技術嘅廣泛應用將對社會產生深遠影響，改變人們的工作和生活方式。\r\n\r\n**挑戰與機遇 **：\r\n技術發展既帶來機遇，都面臨挑戰，需要我哋積極應對和把握。\r\n\r\n##最佳實踐指南\r\n\r\n###項目實施建議\r\n\r\n**需求分析**：\r\n深入理解業務需求係項目成功嘅基礎，需要與業務方充分溝通。\r\n\r\n**技術選型**：\r\n根據具體需求選擇合適嘅技術方案，平衡性能、成本和複雜度。\r\n\r\n**團隊建設**：\r\n組建具備相應技能嘅團隊，確保項目嘅順利實施。\r\n\r\n###風險控制措施\r\n\r\n**技術風險**：\r\n識別和評估技術風險，制定相應嘅應對策略。\r\n\r\n**項目風險**：\r\n建立項目風險管理機制，及時發現和處理風險。\r\n\r\n**運營風險**：\r\n考慮系統上線之後嘅運營風險，制定應急預案。\r\n\r\n##總結\r\n\r\n文檔智能處理技術作為人工智能喺快勞領域嘅重要應用，推動各行各業緊嘅數字化轉型。 透過不斷嘅技術創新和應用實踐，技術將在提高工作傚率、降低成本、改善用戶體驗等方面發揮越嚟越重要嘅作用。\r\n\r\n##技術原理深度解析\r\n\r\n###理論基礎\r\n\r\n該技術嘅理論基礎建立喺多個學科嘅交叉融合之上，包括計算機科學、數學、統計學和認知科學等領域嘅重要理論成果。\r\n\r\n**數學理論支撐**：\r\n-線性代數：為數據表示同變換提供數學工具\r\n-概率論：處理不塙定性同隨機性問題\r\n-優化理論：指導模型參數嘅學習同調整\r\n-信息論：量化信息內容同傳輸效率\r\n\r\n**計算機科學基礎**：\r\n-算法設計：高效算法嘅設計同分析\r\n-數據結構：適合嘅數據組織和存儲方式\r\n-並行計算：充分利用現代計算資源\r\n-系統架構：可擴展和可維護嘅系統設計\r\n\r\n###核心算法機制\r\n\r\n**特徵學習機制**：\r\n現代深度學習方法能夠自動學習數據的層次化特徵表示，這一能力是傳統方法難以企及的。 透過多層非線性變換，網絡能夠由原始數據中提取出越嚟越抽象同高級嘅特徵。\r\n\r\n**注意力機制原理**：\r\n注意力機制模擬咗人類認知過程中嘅選擇性注意，要模型能夠動態咁關注輸入嘅不同部分。 呢種機制不僅提高咗模型嘅性能，仲增強埋模型嘅可解釋性。\r\n\r\n**優化算法設計**：\r\n深度學習模型嘅訓練依賴於高效嘅優化算法。 由基礎嘅梯度下降到現代嘅自適應優化方法，算法嘅選擇同調優對模型性能有着決定性影響。\r\n\r\n##實際應用場景分析\r\n\r\n###工業應用實踐\r\n\r\n**製造業應用**：\r\n在製造業中，該技術被廣泛應用於質量控制、生產監控、設備維護等環節。 透過實時分析生產數據，能夠及時發現問題並採取相應措施。\r\n\r\n**服務業應用**：\r\n服務業中嘅應用主要集中喺客戶服務、業務流程優化、決策支持等方面。 智能化嘅服務系統能夠提供更加個性化和高效嘅服務體驗。\r\n\r\n**金融業應用 **：\r\n金融行業對準確性和實時性要求極高，該技術喺風險控制、欺詐檢測、投資決策等方面發揮著重要作用。\r\n\r\n###技術集成策略\r\n\r\n**系統集成方法**：\r\n在實際應用中，往往需要把多種技術進行有機結合，形成完整嘅解決方案。 要求我哋不僅要掌握單一技術，仲要理解不同技術間嘅協調配合。\r\n\r\n**數據流設計**：\r\n合理嘅數據流設計係系統成功嘅關鍵。 由數據採集、預處理、分析到結果輸出，每個環節都需要精心設計和優化。\r\n\r\n**接口標準化**：\r\n標準化嘅接口設計益咗系統嘅擴展同維護，都便於與其他系統嘅集成。\r\n\r\n##性能優化策略\r\n\r\n###算法層面優化\r\n\r\n**模型結構優化**：\r\n透過改進網絡架構、調整層數和參數數量等方式，可以喺保持性能嘅同時提高計算效率。\r\n\r\n**訓練策略優化**：\r\n採用合適的訓練策略，如學習率調度、批量大小選擇、正則化技術等，能夠顯著提高模型的訓練效果。\r\n\r\n**推理優化**：\r\n在部署階段，透過模型壓縮、量化、剪枝等技術，可以大幅減少計算資源需求。\r\n\r\n###系統層面優化\r\n\r\n**硬件加速**：\r\n充分利用GPU、TPU等專用硬件嘅並行計算能力，可以顯著提升系統性能。\r\n\r\n**分佈式計算**：\r\n對於大規模應用，分佈式計算架構是必不可少的。 合理嘅任務分配和負載均衡策略能夠最大化系統吞吐量。\r\n\r\n**緩存機制**：\r\n智能嘅緩存策略可以減少重複計算，提高系統響應速度。\r\n\r\n##質素保證體系\r\n\r\n###測試驗證方法\r\n\r\n**功能測試**：\r\n全面嘅功能測試確保系統各項功能正常工作，包括正常情況和異常情況嘅處理。\r\n\r\n**性能測試**：\r\n性能測試評估系統喺不同負載下嘅表現，確保系統能夠滿足實際應用嘅性能要求。\r\n\r\n**魯棒性測試**：\r\n魯棒性測試驗證系統喺面對各種干擾和異常情況時嘅穩定性和可靠性。\r\n\r\n###持續改進機制\r\n\r\n**監控體系**：\r\n建立完善嘅監控體系，實時跟蹤系統運行狀態和性能指標。\r\n\r\n**反饋機制**：\r\n建立用戶反饋收集和處理機制，及時發現和解決問題。\r\n\r\n**版本管理**：\r\n規範嘅版本管理流程確保系統嘅穩定性和可追溯性。\r\n\r\n##發展趨勢與展望\r\n\r\n###技術發展方向\r\n\r\n**智能化程度提升**：\r\n未來嘅技術發展將朝着更高嘅智能化水平發展，具備更強嘅自主學習和適應能力。\r\n\r\n**跨領域融合**：\r\n不同技術領域嘅融合將產生新嘅突破，帶來更多嘅應用可能性。\r\n\r\n**標準化進程**：\r\n技術標準化將促進行業的健康發展，降低應用門檻。\r\n\r\n###應用前景展望\r\n\r\n**新興應用領域**：\r\n隨著技術嘅成熟，將會湧現出更多新嘅應用領域同場景。\r\n\r\n**社會影響**：\r\n技術嘅廣泛應用將對社會產生深遠影響，改變人們的工作和生活方式。\r\n\r\n**挑戰與機遇 **：\r\n技術發展既帶來機遇，都面臨挑戰，需要我哋積極應對和把握。\r\n\r\n##最佳實踐指南\r\n\r\n###項目實施建議\r\n\r\n**需求分析**：\r\n深入理解業務需求係項目成功嘅基礎，需要與業務方充分溝通。\r\n\r\n**技術選型**：\r\n根據具體需求選擇合適嘅技術方案，平衡性能、成本和複雜度。\r\n\r\n**團隊建設**：\r\n組建具備相應技能嘅團隊，確保項目嘅順利實施。\r\n\r\n###風險控制措施\r\n\r\n**技術風險**：\r\n識別和評估技術風險，制定相應嘅應對策略。\r\n\r\n**項目風險**：\r\n建立項目風險管理機制，及時發現和處理風險。\r\n\r\n**運營風險**：\r\n考慮系統上線之後嘅運營風險，制定應急預案。\r\n\r\n##總結\r\n\r\n本文系統介紹咗深度學習OCR所需嘅數學基礎，包括：\r\n\r\n1. **線性代數 **：向量、矩陣運算，特徵值分解，SVD等\r\n2. **概率論 **：概率分佈，貝葉斯定理，信息論基礎\r\n3. **優化理論**：梯度下降及其變體，高級優化算法\r\n4. **神經網絡原理**：感知機，激活函數，反向傳播\r\n5. **損失函數**：回歸和分類任務的常用損失函數\r\n6. **正則化技術**：防止過擬合的數學方法\r\n\r\n呢啲數學工具為理解後續嘅CNN、RNN、Attention等深度學習技術奠定了堅實基礎。 喺跟住文章中，我哋將基於呢啲數學原理，深入探討具體嘅OCR技術實現。</div>\n                        \n                        <div class=\"article-tags\">\n                            <h6>標籤：</h6>\n                            <div class=\"tags-list\">\n                                \n                                <span class=\"tag\">OCR</span>\n                                \n                                <span class=\"tag\">深度學習</span>\n                                \n                                <span class=\"tag\">數學基礎</span>\n                                \n                                <span class=\"tag\">線性代數</span>\n                                \n                                <span class=\"tag\">神經網絡</span>\n                                \n                                <span class=\"tag\">優化算法</span>\n                                \n                                <span class=\"tag\">概率論</span>\n                                \n                            </div>\n                        </div>\n                        \n                    </article>\n                    \n                    <footer class=\"article-footer\" id=\"articleFooter\">\n                        <div class=\"article-share\">\n                            <h6 style=\"color: #1a1c1f; margin-bottom: 1rem;\">分享和操作：</h6>\n                            <div class=\"share-buttons\">\n                                <a href=\"javascript:void(0)\" onclick=\"shareToWeibo()\" class=\"btn btn-weibo\">📱 微博分享</a>\n                                <a href=\"javascript:void(0)\" onclick=\"copyUrl()\" class=\"btn btn-copy\">🔗 複製連結</a>\n                                <button type=\"button\" class=\"btn btn-print\" onclick=\"printArticle()\">🖨️ 打印文章</button>\n                            </div>\n                        </div>\n                    </footer>\n                    \n                </div>\n                <div class=\"col-lg-4 col-xl-3 col-md-12\">\n                    <div class=\"sidebar\">\n                        <div class=\"sidebar-card toc-card\" id=\"tocCard\" style=\"display: none;\"><h5>文章目錄</h5><div id=\"tableOfContents\" class=\"toc-list\"></div></div>\n                        <div class=\"sidebar-card\">\n                            <h5>舉薦閱讀</h5>\n                            <div id=\"recommendedArticles\">\n                                \n                                <div class=\"recommended-item\"><h6><a href=\"Article.aspx?slug=document-intelligence-future-outlook\">【文檔智能處理系列·20】文檔智能處理技術發展展望</a></h6><p class=\"text-muted small\">2025-08-19 • 1203 次閱讀</p></div>\n                                \n                                <div class=\"recommended-item\"><h6><a href=\"Article.aspx?slug=document-intelligence-quality-assurance\">【文檔智能處理系列·19】文檔智能處理質量保證體系</a></h6><p class=\"text-muted small\">2025-08-19 • 1200 次閱讀</p></div>\n                                \n                                <div class=\"recommended-item\"><h6><a href=\"Article.aspx?slug=large-scale-document-processing-optimization\">【文檔智能處理系列·18】大規模文檔處理性能優化</a></h6><p class=\"text-muted small\">2025-08-19 • 1201 次閱讀</p></div>\n                                \n                            </div>\n                        </div>\n                    </div>\n</div>\n</div>\n</div>\n</div>\n<script>\nvar articleId,viewCountKey,now=new Date().getTime();\ndocument.addEventListener('DOMContentLoaded',function(){var articleDetail=document.getElementById('articleDetail');if(articleDetail&&articleDetail.querySelector('.article-content')){formatContent();initReadingProgress();optimizeImages();updateViewCount();}});\nfunction updateViewCount(){articleId=getArticleId();if(!articleId)return;viewCountKey='article_view_'+articleId;var lastViewTime=localStorage.getItem(viewCountKey);if(lastViewTime&&(now-parseInt(lastViewTime))<3600000)return;var xhr=new XMLHttpRequest();xhr.open('POST','/Article.ashx',true);xhr.setRequestHeader('Content-Type','application/x-www-form-urlencoded');xhr.onreadystatechange=function(){if(xhr.readyState===4&&xhr.status===200){try{var response=JSON.parse(xhr.responseText);if(response.success){updateViewCountDisplay(response.viewCount);localStorage.setItem(viewCountKey,now.toString());}}catch(e){}}};xhr.send('action=updateviewcount&articleId='+encodeURIComponent(articleId));}\nfunction getArticleId(){var articleDetail=document.getElementById('articleDetail');if(articleDetail){var id=articleDetail.getAttribute('data-article-id');if(id)return id;}var urlParams=new URLSearchParams(window.location.search);return urlParams.get('id');}\nfunction updateViewCountDisplay(newCount){document.querySelectorAll('.view-count').forEach(function(element){element.textContent=newCount;});}\nfunction decodeHtmlEntities(text){var textArea=document.createElement('textarea');textArea.innerHTML=text;return textArea.value;}\nfunction formatContent(){var contentDiv=document.getElementById('articleContent');if(!contentDiv)return;var content=contentDiv.textContent||contentDiv.innerText;if(!content)return;content=decodeHtmlEntities(content);content=formatMarkdownContent(content);content=formatIndentation(content);content=formatLists(content);content=formatParagraphs(content);content=formatLinks(content);contentDiv.innerHTML=content;initTableOfContents();}\nfunction formatIndentation(content){var lines=content.split('\\n'),result=[];for(var i=0;i<lines.length;i++){var line=lines[i];if(line.trim().match(/^<[^>]+>/)){result.push(line);continue;}line=line.replace(/^\\t+/g,function(match){return '    '.repeat(match.length);});if(line.match(/^  +[^\\s]/)){var indentMatch=line.match(/^( +)(.*)$/);if(indentMatch){var indentLevel=Math.floor(indentMatch[1].length/2);line='<span class=\"text-indent-'+indentLevel+'\">'+indentMatch[2]+'</span>';}}result.push(line);}return result.join('\\n');}\nfunction formatMarkdownContent(content){content=content.replace(/^\\s*##### (.+)$/gm,'<h5>$1</h5>').replace(/^\\s*#### (.+)$/gm,'<h4>$1</h4>').replace(/^\\s*### (.+)$/gm,'<h3>$1</h3>').replace(/^\\s*## (.+)$/gm,'<h2>$1</h2>').replace(/^\\s*# (.+)$/gm,'<h1>$1</h1>').replace(/^> (.+)$/gm,'<blockquote><p>$1</p></blockquote>').replace(/\\*\\*([^*]+?)\\*\\*/g,'<strong>$1</strong>').replace(/\\*([^*\\n]+?)\\*/g,'<em>$1</em>').replace(/`([^`]+?)`/g,'<code class=\"inline-code\">$1</code>').replace(/^(\\*\\*[^*]+?\\*\\*)：/gm,'<div class=\"label-item\"><span class=\"label\">$1</span></div>').replace(/^([^：\\n]+?)：$/gm,'<div class=\"section-label\">$1：</div>').replace(/^(\\*\\*[^*]+?\\*\\*)：(.+)$/gm,'<div class=\"definition-item\"><span class=\"definition-label\">$1：</span><span class=\"definition-content\">$2</span></div>'）.replace（/^（註|備註|說明）:(.+）$/gm，'<div class=\"note-item\"><span class=\"note-label\">$1：</span><span class=\"note-content\">$2</span></div>');content=formatLists(content);content=formatMathSymbols(content);content=formatParagraphs(content);return content;}\nfunction formatLists(content){var lines=content.split('\\n'),result=[],inOrderedList=false,inUnorderedList=false;for(var i=0;i<lines.length;i++){var line=lines[i],trimmed=line.trim();var orderedMatch=line.match(/^(\\d+)\\.\\s*(.+)$/);var unorderedMatch=line.match(/^[-*+]\\s*(.+)$/);if(orderedMatch){if(!inOrderedList){if(inUnorderedList){result.push('</ul>');inUnorderedList=false;}result.push('<ol class=\"formatted-list ordered\">');inOrderedList=true;}var listContent=orderedMatch[2].replace(/^\\*\\*([^*]+?)\\*\\*：(.+)$/,'<strong class=\"list-title\">$1：</strong><span class=\"list-content\">$2</span>');result.push('<li>'+listContent+'</li>');}else if(unorderedMatch){if(!inUnorderedList){if(inOrderedList){result.push('</ol>');inOrderedList=false;}result.push('<ul class=\"formatted-list unordered\">');inUnorderedList=true;}var listContent=unorderedMatch[1].replace(/^\\*\\*([^*]+?)\\*\\*：(.+)$/,'<strong class=\"list-title\">$1：</strong><span class=\"list-content\">$2</span>');result.push('<li>'+listContent+'</li>');}else{if(trimmed===''){if(i+1<lines.length){var nextLine=lines[i+1].trim();if(!nextLine.match(/^\\d+\\.\\s/)&&!nextLine.match(/^[-*+]\\s/)){if(inOrderedList){result.push('</ol>');inOrderedList=false;}if(inUnorderedList){result.push('</ul>');inUnorderedList=false;}}}result.push(line);}else{if(inOrderedList){result.push('</ol>');inOrderedList=false;}if(inUnorderedList){result.push('</ul>');inUnorderedList=false;}result.push(line);}}}if(inOrderedList)result.push('</ol>');if(inUnorderedList)result.push('</ul>');return result.join('\\n');}\nfunction formatMathSymbols(content){var mathSymbols={'α':'&alpha;','β':'&beta;','γ':'&gamma;','δ':'&delta;','ε':'&epsilon;','ζ':'&zeta;','η':'&eta;','θ':'&theta;','λ':'&lambda;','μ':'&mu;','π':'&pi;','σ':'&sigma;','∑':'&sum;','∏':'&prod;','∫':'&int;','∂':'&part;','∇':'&nabla;','∞':'&infin;','≤':'&le;','≥':'&ge;','≠':'&ne;','≈':'&asymp;','±':'&plusmn;','×':'&times;','÷':'&divide;','→':'&rarr;','←':'&larr;','↑':'&uarr;','↓':'&darr;','⇒':'&rArr;','⇐':'&lArr;'};var subMap={'₀':'0','₁':'1','₂':'2','₃':'3','₄':'4','₅':'5','₆':'6','₇':'7','₈':'8','₉':'9','ₖ':'k','ₜ':'t','ₛ':'s','₊':'+','₋':'-'};var supMap={'⁰':'0','¹':'1','²':'2','³':'3','⁴':'4','⁵':'5','⁶':'6','⁷':'7','⁸':'8','⁹':'9','ᵏ':'k','ᵗ':'t','ˢ':'s','⁺':'+','⁻':'-'};for(var symbol in mathSymbols){content=content.replace(new RegExp(escapeRegExp(symbol),'g'),mathSymbols[symbol]);}content=content.replace(/([a-zA-Z])₍([0-9]+)₎/g,'$1<sub>$2</sub>').replace(/([a-zA-Z])⁽([0-9]+)⁾/g,'$1<sup>$2</sup>').replace(/([a-zA-Z])([₀₁₂₃₄₅₆₇₈₉ₖₜₛ₊₋]+)/g,function(match,base,subscript){var converted=subscript.replace(/[₀₁₂₃₄₅₆₇₈₉ₖₜₛ₊₋]/g,function(char){return subMap[char]||char;});return base+'<sub>'+converted+'</sub>';}).replace(/([a-zA-Z])([⁰¹²³⁴⁵⁶⁷⁸⁹ᵏᵗˢ⁺⁻]+)/g,function(match,base,superscript){var converted=superscript.replace(/[⁰¹²³⁴⁵⁶⁷⁸⁹ᵏᵗˢ⁺⁻]/g,function(char){return supMap[char]||char;});return base+'<sup>'+converted+'</sup>';}).replace(/\\b([A-Za-z])_\\{([^}]+)\\}/g,'$1<sub>$2</sub>').replace(/\\b([A-Za-z])\\^\\{([^}]+)\\}/g,'$1<sup>$2</sup>');return content;}\nfunction formatParagraphs(content){var lines=content.split('\\n'),result=[],inParagraph=false,currentParagraph=[];for(var i=0;i<lines.length;i++){var originalLine=lines[i],line=originalLine.trim();if(line.match(/^<[^>]+>/)||line===''){if(inParagraph&&currentParagraph.length>0){var paragraphContent=currentParagraph.join(' ').trim();if(paragraphContent)result.push('<p class=\"content-paragraph indent-paragraph\">'+paragraphContent+'</p>');currentParagraph=[];inParagraph=false;}result.push(originalLine);continue;}if(line.match(/^#{1,6}\\s/)||line.match(/^\\d+\\.\\s/)||line.match(/^[-*+]\\s/)||line.match(/^>/)){if(inParagraph&&currentParagraph.length>0){var paragraphContent=currentParagraph.join(' ').trim();if(paragraphContent)result.push('<p class=\"content-paragraph indent-paragraph\">'+paragraphContent+'</p>');currentParagraph=[];inParagraph=false;}result.push(originalLine);continue;}if(line){currentParagraph.push(line);inParagraph=true;}}if(inParagraph&&currentParagraph.length>0){var paragraphContent=currentParagraph.join(' ').trim();if(paragraphContent)result.push('<p class=\"content-paragraph indent-paragraph\">'+paragraphContent+'</p>');}return result.join('\\n');}\nfunction escapeRegExp(string){return string.replace(/[.*+?^${}()|[\\]\\\\]/g,'\\\\$&');}\nfunction formatLinks(content){return content.replace(/<a\\s+href=\"(https?:\\/\\/[^\"]+)\"([^>]*)>/gi,function(match,url,attrs){return !attrs.includes('target=')?'<a href=\"'+url+'\"'+attrs+' target=\"_blank\" rel=\"noopener noreferrer\">':match;});}\nfunction escapeHtml(text){var div=document.createElement('div');div.textContent=text;return div.innerHTML;}\nfunction addTocClickEvents(){document.querySelectorAll('#tableOfContents a').forEach(function(link){link.addEventListener('click',function(e){e.preventDefault();var targetElement=document.getElementById(this.getAttribute('data-target'));if(targetElement){targetElement.scrollIntoView({behavior:'smooth',block:'start'});updateActiveTocItem(this);}});});}\nfunction addScrollHighlight(headings){var throttleTimer=null;function updateActiveHeading(){var windowHeight=window.innerHeight,activeHeading=null;for(var i=headings.length-1;i>=0;i--){var rect=headings[i].getBoundingClientRect();if(rect.top<=windowHeight*0.3){activeHeading=headings[i];break;}}if(activeHeading){var activeLink=document.querySelector('#tableOfContents a[data-target=\"'+activeHeading.id+'\"]');if(activeLink)updateActiveTocItem(activeLink);}}window.addEventListener('scroll',function(){if(throttleTimer)clearTimeout(throttleTimer);throttleTimer=setTimeout(updateActiveHeading,100);});updateActiveHeading();}\nfunction updateActiveTocItem(activeLink){document.querySelectorAll('#tableOfContents a').forEach(function(link){link.classList.remove('active');});if(activeLink){activeLink.classList.add('active');scrollTocToActiveItem(activeLink);}}\nfunction scrollTocToActiveItem(activeLink){var tocCard=document.getElementById('tocCard');if(!activeLink||!tocCard)return;var cardRect=tocCard.getBoundingClientRect();var activeRect=activeLink.getBoundingClientRect();var relativeTop=activeRect.top-cardRect.top;var relativeBottom=activeRect.bottom-cardRect.bottom;var cardStyle=window.getComputedStyle(tocCard);var paddingTop=parseFloat(cardStyle.paddingTop)||0;var titleHeight=tocCard.querySelector('h5')?tocCard.querySelector('h5').offsetHeight:0;var contentTop=paddingTop+titleHeight;if(relativeTop<contentTop){tocCard.scrollTop+=(relativeTop-contentTop-10);}else if(relativeBottom>0){tocCard.scrollTop+=(relativeBottom+10);}}\nfunction optimizeImages(){document.querySelectorAll('.article-content img').forEach(function(img){img.loading='lazy';img.onerror=function(){this.style.display='none';};if(!img.alt)img.alt='文章配圖';});}\nfunction initReadingProgress(){var progressBar=document.getElementById('readingProgress');function updateProgress(){var scrollTop=window.pageYOffset||document.documentElement.scrollTop;var docHeight=document.documentElement.scrollHeight-window.innerHeight;progressBar.style.width=Math.min((scrollTop/docHeight)*100,100)+'%';}window.addEventListener('scroll',updateProgress);updateProgress();}\nfunction initTableOfContents(){var content=document.querySelector('.article-content');if(!content)return;var headings=content.querySelectorAll('h1, h2, h3, h4, h5, h6');if(headings.length<=1)return;var tocCard=document.getElementById('tocCard');var tocList=document.getElementById('tableOfContents');var html='',counters=[0,0,0,0,0,0];headings.forEach(function(heading,index){var id='heading-'+index;heading.id=id;var level=parseInt(heading.tagName.substring(1));var indent=(level-1)*15;counters[level-1]++;for(var i=level;i<counters.length;i++)counters[i]=0;var numberText='';for(var i=0;i<level;i++){if(counters[i]>0)numberText+=(numberText?'.':'')+counters[i];}numberText+='.';html+='<a href=\"#'+id+'\" class=\"toc-item toc-level-'+level+'\" style=\"padding-left: '+indent+'px;\" data-target=\"'+id+'\"><span class=\"toc-number\">'+numberText+'</span> <span class=\"toc-text\">'+heading.textContent+'</span></a>';});tocList.innerHTML=html;tocCard.style.display='block';addTocClickEvents();addScrollHighlight(headings);tocList.addEventListener('click',function(e){if(e.target.tagName==='A'){e.preventDefault();var targetElement=document.getElementById(e.target.getAttribute('data-target'));if(targetElement){targetElement.scrollIntoView({behavior:'smooth',block:'start'});targetElement.style.transition='all 0.3s ease';targetElement.style.backgroundColor='#e3f2fd';targetElement.style.padding='10px';targetElement.style.borderRadius='6px';setTimeout(function(){targetElement.style.backgroundColor='';targetElement.style.padding='';targetElement.style.borderRadius='';},2000);}}});}\nfunction shareToWeibo(){window.open('https://service.weibo.com/share/share.php?url='+encodeURIComponent(window.location.href)+'&title='+encodeURIComponent(document.title),'_blank');}\nfunction copyUrl(){navigator.clipboard.writeText(window.location.href).then(function(){alert('連結已複製到剪貼板');}).catch(function(){var textArea=document.createElement('textarea');textArea.value=window.location.href;textArea.style.cssText='position:fixed;left:-999999px;top:-999999px';document.body.appendChild(textArea);textArea.focus();textArea.select();try{alert(document.execCommand('copy')?'連結已複製到剪貼板':'複製失敗，請手動複製連結');}catch(err){alert('複製失敗，請手動複製連結');}document.body.removeChild(textArea);});}\nfunction printArticle(){var printWindow=window.open('','_blank');var articleContent=document.querySelector('.article-detail').innerHTML;var articleTitle=document.querySelector('.article-title').textContent;printWindow.document.write('<!DOCTYPE html><html lang=\"yue\"><title>'+articleTitle+'</title><style>body{font-family:-apple-system,BlinkMacSystemFont,\"Segoe UI\",Roboto,\"Helvetica Neue\",Arial,sans-serif;line-height:1.6;color:#333;max-width:800px;margin:0 auto;padding:20px}h1,h2,h3,h4,h5,h6{color:#1a1c1f;margin-top:1.5em;margin-bottom:0.5em}h1{font-size:2em;border-bottom:2px solid #e9ecef;padding-bottom:0.3em}h2{font-size:1.5em;border-bottom:1px solid #e9ecef;padding-bottom:0.3em}h3{font-size:1.25em}p{margin-bottom:1em}pre{background:#f8f9fa;border:1px solid #e9ecef;border-radius:4px;padding:1em;overflow-x:auto;font-family:Consolas,Monaco,\"Courier New\",monospace}code{background:#f1f3f4;padding:2px 4px;border-radius:3px;font-family:Consolas,Monaco,\"Courier New\",monospace}blockquote{border-left:4px solid #1764ff;background:#f8f9fa;padding:1em;margin:1em 0;border-radius:0 4px 4px 0}img{max-width:100%;height:auto}table{width:100%;border-collapse:collapse;margin:1em 0}th,td{border:1px solid #ddd;padding:8px;text-align:left}th{background-color:#f2f2f2}.article-meta,.article-footer,.share-buttons{display:none}</style><body>'+articleContent+'</body></html>');printWindow.document.close();printWindow.focus();setTimeout(function(){printWindow.print();printWindow.close();},250);}\nfunction improvedStickyPosition(){var sidebarCards=document.querySelectorAll('.sidebar-card');var originalPositions=[],isFixed=[];sidebarCards.forEach(function(card,index){var rect=card.getBoundingClientRect();originalPositions[index]=rect.top+window.pageYOffset;isFixed[index]=false;var placeholder=document.createElement('div');placeholder.style.cssText='height:'+card.offsetHeight+'px;display:none';placeholder.className='sticky-placeholder';card.parentNode.insertBefore(placeholder,card.nextSibling);});function updateStickyPosition(){var scrollTop=window.pageYOffset||document.documentElement.scrollTop;sidebarCards.forEach(function(card,index){var originalTop=originalPositions[index];var stickyTop=index>0?(document.querySelector('.toc-card').style.position==='fixed'?60+document.querySelector('.toc-card').offsetHeight+8:80):60;var placeholder=card.nextSibling;if(scrollTop>originalTop-stickyTop){if(!isFixed[index]){var colContainer=card.closest('[class*=\"col-\"]');var colRect=colContainer.getBoundingClientRect();var colStyle=window.getComputedStyle(colContainer);var paddingLeft=parseFloat(colStyle.paddingLeft)||0;var paddingRight=parseFloat(colStyle.paddingRight)||0;card.style.cssText='position:fixed;top:'+stickyTop+'px;left:'+(colRect.left+paddingLeft)+'px;width:'+(colRect.width-paddingLeft-paddingRight)+'px;z-index:9999;box-sizing:border-box';card.classList.add('fixed');placeholder.style.display='block';isFixed[index]=true;}}else if(isFixed[index]){card.style.cssText='';card.classList.remove('fixed');placeholder.style.display='none';isFixed[index]=false;}});}window.addEventListener('scroll',updateStickyPosition);window.addEventListener('resize',function(){setTimeout(function(){sidebarCards.forEach(function(card,index){if(isFixed[index]){var colContainer=card.closest('[class*=\"col-\"]');var colRect=colContainer.getBoundingClientRect();var colStyle=window.getComputedStyle(colContainer);var paddingLeft=parseFloat(colStyle.paddingLeft)||0;var paddingRight=parseFloat(colStyle.paddingRight)||0;card.style.left=(colRect.left+paddingLeft)+'px';card.style.width=(colRect.width-paddingLeft-paddingRight)+'px';}else{originalPositions[index]=card.getBoundingClientRect().top+window.pageYOffset;}});},100);});updateStickyPosition();}\ndocument.addEventListener('DOMContentLoaded',function(){setTimeout(improvedStickyPosition,1000);});\n    </script>\n\r\n            </div>\r\n        </form>\r\n\r\n        <div class=\"fixed-icon\">\r\n            <div style=\"width: 120px; position: relative; left: 1rem; top: -12px;\">\r\n            </div>\r\n            <a id=\"webchat\" class=\"wx-con disa\" href=\"http://wpa.qq.com/msgrd?v=3&uin=365833440&site=qq&menu=yes\" target=\"_blank\">\r\n                <img class=\"wechat-ico\" src=\"site/image/icon/qq.svg\" alt=\"OCR助手QQ在線客服\" />\r\n                <div class=\"wx-text\">QQ客服（365833440）</div>\r\n                <label class=\"badge badge-danger\" style=\"background: rgb(255, 34, 122); display: inline-block;\">1</label>\r\n            </a>\r\n\r\n            <a class=\"wx-con\" href=\"https://qm.qq.com/cgi-bin/qm/qr?k=bohNy9GMdgD5bsYrB-fcIjy-s6Ot3zOg&jump_from=webapi&qr=1\" target=\"_blank\">\r\n                <img class=\"wechat-ico\" src=\"site/image/icon/qq.svg\" alt=\"OCR助手QQ用戶交流群\" />\r\n                <div class=\"wx-text\">QQ群（100029010）</div>\r\n                <label class=\"badge badge-danger\" style=\"background: rgb(255, 34, 122); display: inline-block;\">2</label>\r\n            </a>\r\n            <a href=\"mailto:<EMAIL>\" class=\"wx-con\">\r\n                <img class=\"wechat-ico\" src=\"site/image/icon/im.svg\" alt=\"OCR助手郵件聯繫客服\" />\r\n                <div class=\"wx-text\" style=\"width: 240px; text-align: left; left: -15.3rem; padding: 10px 20px;\">\r\n                    <div style=\"font-size: 15px\">郵箱：<EMAIL></div>\r\n                    <hr style=\"background: #c1c1c1; margin: 10px 10px 6px 0;\" />\r\n                    <span style=\"font-size: 13px; color: #c1c1c1\">感謝您的意見和建議！</span>\r\n                </div>\r\n                <label class=\"badge badge-danger\" style=\"background: rgb(255, 34, 122); display: inline-block;\">3</label>\r\n            </a>\r\n        </div>\r\n\r\n        <style>\r\n            .content {\r\n                width: 100%;\r\n                padding: 0 !important;\r\n                margin: 0 auto\r\n            }\r\n\r\n            h3.h6 {\r\n                font-size: 1rem !important;\r\n                font-weight: bold !important;\r\n                line-height: 1.2 !important;\r\n                margin-bottom: .5rem;\r\n                font-family: inherit\r\n            }\r\n        </style>\r\n        <footer class=\"page-footer\">\r\n            <div class=\"container\">\r\n                <div class=\"row foot-cont pt-4 no-gutters\" style=\"border-top: 1px solid #e1e6ed; max-width: 100%\">\r\n                    <div class=\"col-lg-12 hu-an p-0 mb-3 mt-3 text-l color-gray\" style=\"text-align: center;\">\r\n                        OCR文字識別助手&nbsp;©️ 2025 ALL RIGHTS RESERVED. 保留所有權利&nbsp;&nbsp;&nbsp;|&nbsp;&nbsp;&nbsp;\r\n                        <a href=\"PrivacyPolicy.aspx\" style=\"text-decoration: underline;\">私隱協議</a>&nbsp;&nbsp;&nbsp;|&nbsp;&nbsp;&nbsp;\r\n                        <a href=\"UserAgreement.aspx\" style=\"text-decoration: underline;\">用戶協議</a>&nbsp;&nbsp;&nbsp;|&nbsp;&nbsp;&nbsp;\r\n                        <a href=\"Status.aspx\" style=\"text-decoration: underline;\">服務狀態</a>&nbsp;&nbsp;&nbsp;|&nbsp;&nbsp;&nbsp;\r\n                        <a class=\"mb-2 color-gray\" href=\"https://beian.miit.gov.cn/\" target=\"_blank\" style=\"text-decoration: underline;\">鄂ICP備2021012692號</a>\r\n                    </div>\r\n                </div>\r\n            </div>\r\n        </footer>\r\n        <style>\r\n            #translate > .translateSelectLanguage {\r\n                right: 2rem;\r\n                font-size: 1rem;\r\n                width: 150px;\r\n                padding: .3rem;\r\n                margin-right: 20px;\r\n                border: 1px solid #C9C9C9;\r\n                background: #fff;\r\n                color: #555\r\n            }\r\n        </style>\r\n    </div>\r\n    <script>\r\n        document.addEventListener('DOMContentLoaded', function () {\r\n            var setScroll = function (active) {\r\n                forEach(qa('.v4_header_pc,.v4_header_mob'), function (e) {\r\n                    if (active) addClass(e, 'scrollActive'); else removeClass(e, 'scrollActive');\r\n                });\r\n            };\r\n            if (getScrollY() > 60) setScroll(1);\r\n            window.addEventListener('scroll', function () { setScroll(getScrollY() > 60); });\r\n\r\n            forEach(qa('.v4_header_pc'), function (h) {\r\n                h.addEventListener('mouseenter', function () { addClass(h, 'active'); });\r\n                h.addEventListener('mouseleave', function () { removeClass(h, 'active'); });\r\n            });\r\n\r\n            var menuTimer;\r\n            var hideMenu = function (menuId) {\r\n                var m = document.getElementById(menuId);\r\n                if (m) {\r\n                    m.style.cssText = 'visibility:hidden;height:0;display:none';\r\n                    var n = q('.nav-drown-con', m);\r\n                    if (n) { n.style.cssText = 'height:0;opacity:0;transform:translate(0,-100%)'; }\r\n                }\r\n            };\r\n\r\n            forEach(qa('ul.top-nav>li'), function (i) {\r\n                i.addEventListener('mouseenter', function () {\r\n                    clearTimeout(menuTimer);\r\n                    var l = this.querySelector('a'), t = this.querySelector('.triangle'), s = l ? l.getAttribute('data-sub') : null;\r\n                    if (l) addClass(l, 'active'); if (t) addClass(t, 'active');\r\n                    if (s) {\r\n                        var m = document.getElementById(s);\r\n                        if (m) {\r\n                            m.style.cssText = 'display:block;visibility:visible';\r\n                            var siblings = m.parentNode ? toArray(m.parentNode.children) : [];\r\n                            forEach(siblings, function (c) { if (c !== m) c.style.display = 'none'; });\r\n                            var n = q('.nav-drown-con', m);\r\n                            if (n) { n.style.cssText = 'height:100%;opacity:1;transform:translate(0,0)'; }\r\n\r\n                            m.addEventListener('mouseenter', function () { clearTimeout(menuTimer); });\r\n                            m.addEventListener('mouseleave', function () { menuTimer = setTimeout(function () { hideMenu(s); }, 100); });\r\n                        }\r\n                    }\r\n                });\r\n                i.addEventListener('mouseleave', function () {\r\n                    var l = this.querySelector('a'), t = this.querySelector('.triangle'), s = l ? l.getAttribute('data-sub') : null;\r\n                    if (l) removeClass(l, 'active'); if (t) removeClass(t, 'active');\r\n                    if (s) { menuTimer = setTimeout(function () { hideMenu(s); }, 100); }\r\n                });\r\n            });\r\n\r\n            var r = q('.v4_header_mob .right-menu');\r\n            if (r) r.addEventListener('click', function () {\r\n                var arr = [r, q('.v4_header_mob'), q('#nav_products'), q('.mob-nav-content .sidebar-fix')];\r\n                for (var i = 0; i < arr.length; i++) {\r\n                    var e = arr[i];\r\n                    if (e) {\r\n                        var name = (i === 0 ? 'active' : (i === 1 ? 'active' : (i === 2 ? 'active1' : 'fixedActiveBg')));\r\n                        toggleClass(e, name);\r\n                    }\r\n                }\r\n                var f = q('.mob-nav-content .sidebar-fix');\r\n                if (f) {\r\n                    toggleClass(f, 'show');\r\n                    document.body.style.overflow = hasClass(f, 'show') ? 'hidden' : 'scroll';\r\n                }\r\n            });\r\n\r\n            document.addEventListener('click', function (e) {\r\n                e = e || window.event;\r\n                var target = e.target || e.srcElement;\r\n                var m = closest(target, '.sidebar-fix-left .mob-nav-item');\r\n                if (m) {\r\n                    var p = m.parentNode;\r\n                    if (p) forEach(toArray(p.children), function (c) { removeClass(c, 'active'); });\r\n                    addClass(m, 'active');\r\n                    var d = m.getAttribute('data');\r\n                    if (d) forEach(qa('.sidebar-fix-rigth .sub-nav'), function (n, i) { n.style.display = (i == d ? 'block' : 'none'); });\r\n                }\r\n            });\r\n        });\r\n    </script>\r\n</body>\r\n</html>\r\n\r\n\t            <script type=\"text/javascript\">\r\n                    var _hmt = _hmt || [];\r\n                    (function () {\r\n                        var hm = document.createElement('script');hm.async=1;\r\n                        hm.src = 'https://hm.baidu.com/hm.js?5ee048d7caaf0d439cf16309a1687754';\r\n                        var s = document.getElementsByTagName('script')[0];s.parentNode.insertBefore(hm, s);\r\n                    })();\r\n                </script><div id=\"translate\" class=\"ignore\" style=\"display: none\"></div><script src=\"/static/js/translate.js\"></script>"
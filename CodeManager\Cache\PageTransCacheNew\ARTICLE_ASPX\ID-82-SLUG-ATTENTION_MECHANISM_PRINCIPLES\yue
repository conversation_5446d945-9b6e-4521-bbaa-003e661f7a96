﻿"<!DOCTYPE html>\r\n<html xmlns=\"http://www.w3.org/1999/xhtml\" lang=\"yue\">\r\n<head><link rel=\"canonical\" href=\"https://ocr.oldfish.cn/yue/Article.aspx?id=82&slug=attention-mechanism-principles\" /><link rel=\"alternate\" href=\"https://ocr.oldfish.cn/Article.aspx?id=82&slug=attention-mechanism-principles\" hreflang=\"x-default\" />\r\n<link rel=\"alternate\" href=\"https://ocr.oldfish.cn/Article.aspx?id=82&slug=attention-mechanism-principles\" hreflang=\"zh\" />\r\n<link rel=\"alternate\" href=\"https://ocr.oldfish.cn/Article.aspx?id=82&slug=attention-mechanism-principles\" hreflang=\"zh\" />\r\n<link rel=\"alternate\" href=\"https://ocr.oldfish.cn/zh-Hant/Article.aspx?id=82&slug=attention-mechanism-principles\" hreflang=\"zh-tw\" />\r\n<link rel=\"alternate\" href=\"https://ocr.oldfish.cn/yue/Article.aspx?id=82&slug=attention-mechanism-principles\" hreflang=\"zh-yue\" />\r\n<link rel=\"alternate\" href=\"https://ocr.oldfish.cn/en/Article.aspx?id=82&slug=attention-mechanism-principles\" hreflang=\"en\" />\r\n<link rel=\"alternate\" href=\"https://ocr.oldfish.cn/es/Article.aspx?id=82&slug=attention-mechanism-principles\" hreflang=\"es\" />\r\n<link rel=\"alternate\" href=\"https://ocr.oldfish.cn/hi/Article.aspx?id=82&slug=attention-mechanism-principles\" hreflang=\"hi\" />\r\n<link rel=\"alternate\" href=\"https://ocr.oldfish.cn/ar/Article.aspx?id=82&slug=attention-mechanism-principles\" hreflang=\"ar\" />\r\n<link rel=\"alternate\" href=\"https://ocr.oldfish.cn/pt/Article.aspx?id=82&slug=attention-mechanism-principles\" hreflang=\"pt\" />\r\n<link rel=\"alternate\" href=\"https://ocr.oldfish.cn/bn/Article.aspx?id=82&slug=attention-mechanism-principles\" hreflang=\"bn\" />\r\n<link rel=\"alternate\" href=\"https://ocr.oldfish.cn/ru/Article.aspx?id=82&slug=attention-mechanism-principles\" hreflang=\"ru\" />\r\n<link rel=\"alternate\" href=\"https://ocr.oldfish.cn/ja/Article.aspx?id=82&slug=attention-mechanism-principles\" hreflang=\"ja\" />\r\n<link rel=\"alternate\" href=\"https://ocr.oldfish.cn/pa/Article.aspx?id=82&slug=attention-mechanism-principles\" hreflang=\"pa\" />\r\n<link rel=\"alternate\" href=\"https://ocr.oldfish.cn/de/Article.aspx?id=82&slug=attention-mechanism-principles\" hreflang=\"de\" />\r\n<link rel=\"alternate\" href=\"https://ocr.oldfish.cn/fr/Article.aspx?id=82&slug=attention-mechanism-principles\" hreflang=\"fr\" />\r\n<link rel=\"alternate\" href=\"https://ocr.oldfish.cn/mr/Article.aspx?id=82&slug=attention-mechanism-principles\" hreflang=\"mr\" />\r\n<link rel=\"alternate\" href=\"https://ocr.oldfish.cn/te/Article.aspx?id=82&slug=attention-mechanism-principles\" hreflang=\"te\" />\r\n<link rel=\"alternate\" href=\"https://ocr.oldfish.cn/vi/Article.aspx?id=82&slug=attention-mechanism-principles\" hreflang=\"vi\" />\r\n<link rel=\"alternate\" href=\"https://ocr.oldfish.cn/ko/Article.aspx?id=82&slug=attention-mechanism-principles\" hreflang=\"ko\" />\r\n<link rel=\"alternate\" href=\"https://ocr.oldfish.cn/ta/Article.aspx?id=82&slug=attention-mechanism-principles\" hreflang=\"ta\" />\r\n<link rel=\"alternate\" href=\"https://ocr.oldfish.cn/ur/Article.aspx?id=82&slug=attention-mechanism-principles\" hreflang=\"ur\" />\r\n<link rel=\"alternate\" href=\"https://ocr.oldfish.cn/tr/Article.aspx?id=82&slug=attention-mechanism-principles\" hreflang=\"tr\" />\r\n<link rel=\"alternate\" href=\"https://ocr.oldfish.cn/it/Article.aspx?id=82&slug=attention-mechanism-principles\" hreflang=\"it\" />\r\n<link rel=\"alternate\" href=\"https://ocr.oldfish.cn/gu/Article.aspx?id=82&slug=attention-mechanism-principles\" hreflang=\"gu\" />\r\n<link rel=\"alternate\" href=\"https://ocr.oldfish.cn/pl/Article.aspx?id=82&slug=attention-mechanism-principles\" hreflang=\"pl\" />\r\n<link rel=\"alternate\" href=\"https://ocr.oldfish.cn/uk/Article.aspx?id=82&slug=attention-mechanism-principles\" hreflang=\"uk\" />\r\n<link rel=\"alternate\" href=\"https://ocr.oldfish.cn/ms/Article.aspx?id=82&slug=attention-mechanism-principles\" hreflang=\"ms\" />\r\n<link rel=\"alternate\" href=\"https://ocr.oldfish.cn/id/Article.aspx?id=82&slug=attention-mechanism-principles\" hreflang=\"id\" />\r\n<link rel=\"alternate\" href=\"https://ocr.oldfish.cn/ml/Article.aspx?id=82&slug=attention-mechanism-principles\" hreflang=\"ml\" />\r\n<link rel=\"alternate\" href=\"https://ocr.oldfish.cn/kn/Article.aspx?id=82&slug=attention-mechanism-principles\" hreflang=\"kn\" />\r\n<link rel=\"alternate\" href=\"https://ocr.oldfish.cn/fa/Article.aspx?id=82&slug=attention-mechanism-principles\" hreflang=\"fa\" />\r\n<link rel=\"alternate\" href=\"https://ocr.oldfish.cn/nl/Article.aspx?id=82&slug=attention-mechanism-principles\" hreflang=\"nl\" />\r\n<link rel=\"alternate\" href=\"https://ocr.oldfish.cn/th/Article.aspx?id=82&slug=attention-mechanism-principles\" hreflang=\"th\" />\r\n<link rel=\"alternate\" href=\"https://ocr.oldfish.cn/sw/Article.aspx?id=82&slug=attention-mechanism-principles\" hreflang=\"sw\" />\r\n<link rel=\"alternate\" href=\"https://ocr.oldfish.cn/ro/Article.aspx?id=82&slug=attention-mechanism-principles\" hreflang=\"ro\" />\r\n<link rel=\"alternate\" href=\"https://ocr.oldfish.cn/my/Article.aspx?id=82&slug=attention-mechanism-principles\" hreflang=\"my\" />\r\n<link rel=\"alternate\" href=\"https://ocr.oldfish.cn/or/Article.aspx?id=82&slug=attention-mechanism-principles\" hreflang=\"or\" />\r\n<link rel=\"alternate\" href=\"https://ocr.oldfish.cn/he/Article.aspx?id=82&slug=attention-mechanism-principles\" hreflang=\"he\" />\r\n<link rel=\"alternate\" href=\"https://ocr.oldfish.cn/am/Article.aspx?id=82&slug=attention-mechanism-principles\" hreflang=\"am\" />\r\n<link rel=\"alternate\" href=\"https://ocr.oldfish.cn/fil/Article.aspx?id=82&slug=attention-mechanism-principles\" hreflang=\"fil\" />\r\n<link rel=\"alternate\" href=\"https://ocr.oldfish.cn/sv/Article.aspx?id=82&slug=attention-mechanism-principles\" hreflang=\"sv\" />\r\n<link rel=\"alternate\" href=\"https://ocr.oldfish.cn/el/Article.aspx?id=82&slug=attention-mechanism-principles\" hreflang=\"el\" />\r\n<link rel=\"alternate\" href=\"https://ocr.oldfish.cn/cs/Article.aspx?id=82&slug=attention-mechanism-principles\" hreflang=\"cs\" />\r\n<link rel=\"alternate\" href=\"https://ocr.oldfish.cn/hu/Article.aspx?id=82&slug=attention-mechanism-principles\" hreflang=\"hu\" />\r\n<link rel=\"alternate\" href=\"https://ocr.oldfish.cn/be/Article.aspx?id=82&slug=attention-mechanism-principles\" hreflang=\"be\" />\r\n<link rel=\"alternate\" href=\"https://ocr.oldfish.cn/si/Article.aspx?id=82&slug=attention-mechanism-principles\" hreflang=\"si\" />\r\n<link rel=\"alternate\" href=\"https://ocr.oldfish.cn/ne/Article.aspx?id=82&slug=attention-mechanism-principles\" hreflang=\"ne\" />\r\n<link rel=\"alternate\" href=\"https://ocr.oldfish.cn/km/Article.aspx?id=82&slug=attention-mechanism-principles\" hreflang=\"km\" />\r\n<link rel=\"alternate\" href=\"https://ocr.oldfish.cn/sk/Article.aspx?id=82&slug=attention-mechanism-principles\" hreflang=\"sk\" />\r\n<link rel=\"alternate\" href=\"https://ocr.oldfish.cn/bg/Article.aspx?id=82&slug=attention-mechanism-principles\" hreflang=\"bg\" />\r\n<link rel=\"alternate\" href=\"https://ocr.oldfish.cn/fr-ca/Article.aspx?id=82&slug=attention-mechanism-principles\" hreflang=\"fr-ca\" />\r\n<link rel=\"alternate\" href=\"https://ocr.oldfish.cn/ha/Article.aspx?id=82&slug=attention-mechanism-principles\" hreflang=\"ha\" />\r\n<link rel=\"alternate\" href=\"https://ocr.oldfish.cn/yo/Article.aspx?id=82&slug=attention-mechanism-principles\" hreflang=\"yo\" />\r\n<link rel=\"alternate\" href=\"https://ocr.oldfish.cn/ig/Article.aspx?id=82&slug=attention-mechanism-principles\" hreflang=\"ig\" />\r\n<link rel=\"alternate\" href=\"https://ocr.oldfish.cn/ku/Article.aspx?id=82&slug=attention-mechanism-principles\" hreflang=\"ku\" />\r\n<link rel=\"alternate\" href=\"https://ocr.oldfish.cn/rw/Article.aspx?id=82&slug=attention-mechanism-principles\" hreflang=\"rw\" />\r\n<link rel=\"alternate\" href=\"https://ocr.oldfish.cn/ca/Article.aspx?id=82&slug=attention-mechanism-principles\" hreflang=\"ca\" />\r\n<link rel=\"alternate\" href=\"https://ocr.oldfish.cn/da/Article.aspx?id=82&slug=attention-mechanism-principles\" hreflang=\"da\" />\r\n<link rel=\"alternate\" href=\"https://ocr.oldfish.cn/fi/Article.aspx?id=82&slug=attention-mechanism-principles\" hreflang=\"fi\" />\r\n<link rel=\"alternate\" href=\"https://ocr.oldfish.cn/nb/Article.aspx?id=82&slug=attention-mechanism-principles\" hreflang=\"nb\" />\r\n<link rel=\"alternate\" href=\"https://ocr.oldfish.cn/hr/Article.aspx?id=82&slug=attention-mechanism-principles\" hreflang=\"hr\" />\r\n<link rel=\"alternate\" href=\"https://ocr.oldfish.cn/sr-Cyrl/Article.aspx?id=82&slug=attention-mechanism-principles\" hreflang=\"sr-cyrl\" />\r\n<link rel=\"alternate\" href=\"https://ocr.oldfish.cn/sr-Latn/Article.aspx?id=82&slug=attention-mechanism-principles\" hreflang=\"sr-latn\" />\r\n<link rel=\"alternate\" href=\"https://ocr.oldfish.cn/sq/Article.aspx?id=82&slug=attention-mechanism-principles\" hreflang=\"sq\" />\r\n<link rel=\"alternate\" href=\"https://ocr.oldfish.cn/so/Article.aspx?id=82&slug=attention-mechanism-principles\" hreflang=\"so\" />\r\n<link rel=\"alternate\" href=\"https://ocr.oldfish.cn/zu/Article.aspx?id=82&slug=attention-mechanism-principles\" hreflang=\"zu\" />\r\n<link rel=\"alternate\" href=\"https://ocr.oldfish.cn/ka/Article.aspx?id=82&slug=attention-mechanism-principles\" hreflang=\"ka\" />\r\n<meta http-equiv=\"Content-Type\" content=\"text/html; charset=UTF-8\" /><meta http-equiv=\"X-UA-Compatible\" content=\"IE=Edge,chrome=1\" /><meta name=\"renderer\" content=\"webkit\" /><meta name=\"viewport\" content=\"width=device-width, initial-scale=1.0, maximum-scale=5.0\" /><link rel=\"icon\" href=\"/favicon.ico\" type=\"image/x-icon\" /><meta name=\"robots\" content=\"all\" /><link rel=\"preconnect\" href=\"https://scm-file-new-1301864755.cos.ap-beijing.myqcloud.com\" /><link rel=\"preconnect\" href=\"https://cdn.oldfish.cn\" /><link rel=\"dns-prefetch\" href=\"https://hm.baidu.com\" /><link rel=\"dns-prefetch\" href=\"https://cdn.oldfish.cn\" /><link rel=\"preload\" href=\"/site/css/site.css\" as=\"style\" />\r\n<script>function toArray(l){var a=[];for(var i=0;i<(l?l.length:0);i++)a.push(l[i]);return a;}function forEach(l,f){for(var i=0,n=(l?l.length:0);i<n;i++)f(l[i],i);}function hasClass(e,c){if(!e||!c)return false;return(' '+e.className+' ').indexOf(' '+c+' ')>-1;}function addClass(e,c){if(!e||!c||hasClass(e,c))return;e.className=(e.className?e.className+' ':'')+c;}function removeClass(e,c){if(!e||!c)return;var u=' '+e.className+' ';while(u.indexOf(' '+c+' ')>-1){u=u.replace(' '+c+' ',' ');}e.className=u.replace(/^\\s+|\\s+$/g,'');}function toggleClass(e,c){if(!e||!c)return;if(hasClass(e,c))removeClass(e,c);else addClass(e,c);}function matchesSelector(e,s){if(!e||e.nodeType!==1)return false;if(e.matches)return e.matches(s);var p=e.parentNode||document;var m=toArray(p.querySelectorAll(s));for(var i=0;i<m.length;i++)if(m[i]===e)return true;return false;}function closest(e,s){var n=e;while(n&&n.nodeType===1){if(matchesSelector(n,s))return n;n=n.parentNode;}return null;}function safeRemove(e){if(e&&e.parentNode)e.parentNode.removeChild(e);}function qa(s,r){return toArray((r||document).querySelectorAll(s));}function q(s,r){return(r||document).querySelector(s);}function xhrGet(u,o,e){try{var x=new XMLHttpRequest();x.open('GET',u,true);x.onreadystatechange=function(){if(x.readyState===4){if(x.status>=200&&x.status<300)o&&o(x.responseText);else e&&e(x);}};x.send(null);}catch(r){e&&e(r);}}function getScrollY(){return window.pageYOffset||document.documentElement.scrollTop||document.body.scrollTop||0;}function getOrigin(){return window.location.protocol+'//'+window.location.host;}</script>\r\n<script>var isWebP=(function(){try{return document.createElement('canvas').toDataURL('image/webp').indexOf('data:image/webp')===0;}catch(e){return false;}})();document.addEventListener('DOMContentLoaded',function(){var c=document.getElementById('user-login-container');if(c){var u=c.getAttribute('data-url');if(u){xhrGet(u,function(h){c.innerHTML=h;forEach(qa('a',c),function(l){l.style.display='inline-block';l.style.marginLeft='10px';});});}}});</script>\r\n    \n    \n\n    <meta name=\"description\" content=\"深入探討注意力機制嘅數學原理、多頭注意力、自注意力機制以及喺OCR中嘅具體應用。 詳細分析注意力權重計算、位置編碼和性能優化策略。\" />\n    <meta name=\"keywords\" content=\"注意力機制，多頭注意力，自注意力，位置編碼，交叉注意力，稀疏注意力，OCR，Transformer，OCR文字識別，圖片轉文字，OCR技術\" />\n    <meta property=\"og:title\" content=\"【深度學習OCR系列·5】注意力機制原理與實現\" />\n    <meta property=\"og:description\" content=\"深入探討注意力機制嘅數學原理、多頭注意力、自注意力機制以及喺OCR中嘅具體應用。 詳細分析注意力權重計算、位置編碼和性能優化策略。\" />\n    <meta property=\"og:type\" content=\"article\" />\n    <meta property=\"og:url\" content=\"https://ocr.oldfish.cn/Article.aspx?id=82&slug=attention-mechanism-principles&lang=zh-Hans&_translation_source=true\" />\n    <meta property=\"og:site_name\" content=\"OCR文字識別助手\" />\n    <meta name=\"twitter:card\" content=\"summary\" />\n    <meta name=\"twitter:title\" content=\"【深度學習OCR系列·5】注意力機制原理與實現\" />\n    <meta name=\"twitter:description\" content=\"深入探討注意力機制嘅數學原理、多頭注意力、自注意力機制以及喺OCR中嘅具體應用。 詳細分析注意力權重計算、位置編碼和性能優化策略。\" />\n\n    \n    <script type=\"application/ld+json\">\n    {\n        \"@context\": \"https://schema.org\",\n        \"@type\": \"Article\",\n        \"headline\": \"【深度學習OCR系列·5】注意力機制原理與實現\",\n        \"description\": \"深入探討注意力機制嘅數學原理、多頭注意力、自注意力機制以及喺OCR中嘅具體應用。 詳細分析注意力權重計算、位置編碼和性能優化策略。\",\n        \"publisher\": {\n            \"@type\": \"Organization\",\n            \"name\": \"OCR文字識別助手團隊\"\n        },\n        \"datePublished\": \"2025-08-19T06:32:10Z\",\n        \"dateModified\": \"2025-08-19T06:32:10Z\",\n        \"mainEntityOfPage\": {\n            \"@type\": \"WebPage\",\n            \"@id\": \"https://ocr.oldfish.cn/Article.aspx?id=82&slug=attention-mechanism-principles&lang=zh-Hans&_translation_source=true\"\n        }\n    }\n    </script>\n    \n    <style>\n        .reading-progress { position: fixed; top: 0; left: 0; width: 0%; height: 3px; background: linear-gradient(90deg, #1764ff, #0d47a1); z-index: 9999; transition: width 0.2s ease; box-shadow: 0 1px 3px rgba(23, 100, 255, 0.3); }\n        .home-container.root { overflow: visible !important; }\n        .article-detail-container { background: #f5f7fc; min-height: calc(100vh - 60px); padding: 80px 0 60px; position: relative; overflow: visible; }\n        .article-detail-container .row { margin: 0 -12px; align-items: flex-start !important; min-height: 100vh !important; }\n        .article-detail-container .row > [class*=\"col-\"] { padding: 0 12px; box-sizing: border-box; min-width: 0; }\n        .article-detail-container .row > .col-lg-8, .article-detail-container .row > .col-xl-9 { overflow: hidden; }\n        .article-detail-container .row > .col-lg-4, .article-detail-container .row > .col-xl-3 { overflow: visible !important; height: 100% !important; min-height: 100% !important; position: static !important; display: block !important; transform: none !important; will-change: auto !important; contain: none !important; perspective: none !important; filter: none !important; }\n        .article-detail { background: #fff; border-radius: 8px; padding: 1.5rem; margin-bottom: 2rem; box-shadow: 0 2px 10px rgba(0,0,0,0.1); width: 100%; box-sizing: border-box; }\n        .article-title { font-size: 1.7rem; color: #1a1c1f; line-height: 1.3; margin-bottom: 1.5rem; font-weight: 600; }\n        .article-meta { display: flex; align-items: center; flex-wrap: nowrap; gap: 1.2rem; color: #6c757d; font-size: 0.88rem; margin-bottom: 2rem; padding: 1.2rem 1.4rem; background: linear-gradient(135deg, #f8f9fa 0%, #ffffff 100%); border-radius: 12px; border-left: 4px solid #1764ff; box-shadow: 0 2px 8px rgba(0,0,0,0.06); overflow-x: hidden; transition: all 0.3s ease; }\n        .article-meta:hover { box-shadow: 0 4px 16px rgba(0,0,0,0.1); transform: translateY(-1px); }\n        .article-meta .meta-item { display: flex; align-items: center; gap: 0.25rem; font-weight: 500; color: #495057; white-space: nowrap; flex-shrink: 1; transition: color 0.2s ease; cursor: default; }\n        .article-meta .meta-item:hover { color: #1764ff; }\n        .article-meta .meta-item:hover .meta-icon { transform: scale(1.1); filter: drop-shadow(0 2px 6px rgba(13, 71, 161, 0.25)); }\n        .meta-icon { display: inline-block; font-size: 16px; flex-shrink: 0; vertical-align: middle; transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1); margin-right: 4px; opacity: 0.9; }\n        .meta-item:hover .meta-icon { opacity: 1; }\n        .meta-item:hover { color: #1764ff; }\n        .meta-item { position: relative; }\n        .meta-item::after { content: ''; position: absolute; bottom: -2px; left: 0; width: 0; height: 2px; background: linear-gradient(90deg, #1764ff, #00d4ff); transition: width 0.3s ease; }\n        .meta-item:hover::after { width: 100%; }\n        .article-category, .read-time { display: inline-block; padding: 6px 12px; border-radius: 16px; font-size: 0.8rem; font-weight: 600; }\n        .article-category { background: linear-gradient(135deg, #1764ff, #0d47a1); color: white; text-decoration: none; text-transform: uppercase; letter-spacing: 0.5px; transition: all 0.3s ease; }\n        .article-category:hover { background: linear-gradient(135deg, #0d47a1, #1764ff); color: white; text-decoration: none; transform: translateY(-1px); box-shadow: 0 4px 12px rgba(23, 100, 255, 0.3); }\n        .read-time { background: #e3f2fd; color: #1565c0; border: 1px solid #90caf9; }\n        .article-tags { margin: 20px 0 1.5rem; }\n        .article-tags .tag { display: inline-block; background: #1764ff; color: #fff; padding: 4px 12px; border-radius: 12px; font-size: 0.8rem; margin: 0 8px 4px 0; }\n        .article-summary { background: #e3f2fd; border-left: 4px solid #1764ff; padding: 1rem 1.5rem; margin-bottom: 2rem; border-radius: 0 4px 4px 0; }\n        .article-content { line-height: 1.8; font-size: 16px; color: #333; word-wrap: break-word; }\n        .article-content h1, .article-content h2, .article-content h3, .article-content h4, .article-content h5, .article-content h6 { margin: 2rem 0 1rem; font-weight: 600; line-height: 1.25; color: #1a1c1f; }\n        .article-content h1 { font-size: 2rem; border-bottom: 2px solid #e9ecef; padding-bottom: 0.5rem; }\n        .article-content h2 { font-size: 1.5rem; border-bottom: 1px solid #e9ecef; padding-bottom: 0.3rem; }\n        .article-content h3 { font-size: 1.25rem; }\n        .article-content h4 { font-size: 1.1rem; }\n        .article-content h5, .article-content h6 { font-size: 1rem; }\n        .article-content ul, .article-content ol { margin: 1rem 0; padding-left: 2rem; }\n        .article-content ul ul, .article-content ol ol, .article-content ul ol, .article-content ol ul { margin: 0.5rem 0; }\n        .article-content blockquote { margin: 1rem 0; padding: 0.5rem 1rem; border-left: 4px solid #1764ff; background-color: #f8f9fa; color: #6c757d; font-style: italic; }\n        .article-content pre { background-color: #f8f9fa; border: 1px solid #e9ecef; border-radius: 6px; padding: 1rem; margin: 1rem 0; overflow-x: auto; font-family: 'Courier New', Courier, monospace; font-size: 0.9em; line-height: 1.4; }\n        .article-content pre code { background: none; border: none; padding: 0; color: inherit; }\n        .article-content p { margin: 1rem 0; }\n        .article-content p:first-child { margin-top: 0; }\n        .article-content p:last-child { margin-bottom: 0; }\n        .article-footer { margin-top: 4rem; padding: 2.5rem; background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%); border-radius: 12px; border: 1px solid #e9ecef; position: relative; overflow: hidden; }\n        .article-footer::before { content: ''; position: absolute; top: 0; left: 0; right: 0; height: 3px; background: linear-gradient(90deg, #1764ff, #17a2b8, #28a745); }\n        .article-share h6 { color: #495057; font-weight: 600; margin-bottom: 1.5rem; font-size: 16px; }\n        .share-buttons { display: flex; gap: 12px; flex-wrap: wrap; }\n        .share-buttons .btn { padding: 10px 20px; border-radius: 25px; font-weight: 500; font-size: 14px; border: none; transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1); box-shadow: 0 2px 8px rgba(0,0,0,0.1); display: inline-flex; align-items: center; gap: 8px; text-decoration: none; }\n        .btn-weibo { background: linear-gradient(135deg, #e6162d, #c41230); color: white; }\n        .btn-copy { background: #1764ff; color: white; }\n        .btn-print { background: linear-gradient(135deg, #17a2b8, #138496); color: white; }\n        .btn-weibo:hover { background: linear-gradient(135deg, #c41230, #a00e26); box-shadow: 0 4px 16px rgba(230, 22, 45, 0.3); }\n        .btn-copy:hover { background: #0d47a1; box-shadow: 0 4px 16px rgba(23, 100, 255, 0.3); }\n        .btn-print:hover { background: linear-gradient(135deg, #138496, #117a8b); box-shadow: 0 4px 16px rgba(23, 162, 184, 0.3); }\n        .btn-weibo:hover, .btn-copy:hover, .btn-print:hover { transform: translateY(-2px); color: white; }\n        .article-actions { display: flex; justify-content: flex-end; align-items: center; }\n        .related-articles { margin-top: 3rem; }\n        .related-article-item { background: #fff; border-radius: 8px; padding: 1.5rem; margin-bottom: 1rem; box-shadow: 0 2px 8px rgba(0,0,0,0.1); transition: transform 0.3s ease; }\n        .related-article-item:hover { transform: translateY(-2px); }\n        .sidebar-card { background: linear-gradient(135deg, rgba(255,255,255,0.9) 0%, rgba(255,255,255,0.8) 100%); backdrop-filter: blur(20px); border: 1px solid rgba(255,255,255,0.3); border-radius: 16px; padding: 1rem; margin-bottom: 0.5rem; box-shadow: 0 4px 20px rgba(0,0,0,0.08), inset 0 1px 0 rgba(255,255,255,0.2); overflow: visible; box-sizing: border-box; width: 100%; z-index: 10; }\n        .sidebar-card.fixed { box-shadow: 0 8px 32px rgba(0,0,0,0.12); }\n        .sidebar-card::before { content: ''; position: absolute; top: 0; left: 0; right: 0; height: 3px; background: linear-gradient(90deg, #00d4ff, #1764ff, #9c27b0); border-radius: 20px 20px 0 0; }\n        .sidebar-card h5 { color: #1a202c; padding-bottom: 0.8rem; border-bottom: 2px solid rgba(23, 100, 255, 0.2); font-weight: 700; font-size: 1.1rem; }\n        .sidebar { min-height: 100vh; position: relative !important; display: block !important; flex-direction: column !important; gap: 1rem; }\n        .toc-card { flex: 0 1 auto; max-height: 60vh; overflow-y: auto; }\n        .sidebar-card:not(.toc-card) { flex: 1 1 auto; overflow: visible; }\n        .recommended-item { padding: 0.75rem 0; border-bottom: 1px solid rgba(0,0,0,0.05); }\n        .recommended-item:last-child { border-bottom: none; }\n        .recommended-item h6 { margin: 0 0 0.5rem 0; font-size: 0.9rem; line-height: 1.4; }\n        .recommended-item h6 a { color: #1a1c1f; text-decoration: none; transition: color 0.3s ease; }\n        .recommended-item h6 a:hover { color: #1764ff; }\n        .recommended-item .text-muted { font-size: 0.75rem; margin: 0; }\n        .toc-list { list-style: none; padding: 0; margin: 0; max-height: none; overflow-y: visible; overflow-x: hidden; }\n        .toc-list::-webkit-scrollbar { width: 4px; }\n        .toc-list::-webkit-scrollbar-track { background: rgba(0,0,0,0.1); border-radius: 2px; }\n        .toc-list::-webkit-scrollbar-thumb { background: linear-gradient(135deg, #00d4ff, #1764ff); border-radius: 2px; }\n        .toc-list::-webkit-scrollbar-thumb:hover { background: linear-gradient(135deg, #1764ff, #0d47a1); }\n        .toc-list a { color: #4a5568; text-decoration: none; font-size: 13px; font-weight: 500; display: block; padding: 8px 12px; border-radius: 8px; transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1); position: relative; margin-bottom: 4px; border: 1px solid transparent; }\n        .toc-list a::before { content: ''; position: absolute; left: 0; top: 0; bottom: 0; width: 3px; background: linear-gradient(135deg, #00d4ff, #1764ff); border-radius: 0 12px 12px 0; transform: scaleY(0); transition: transform 0.3s ease; }\n        .toc-list a:hover { color: #1764ff; background: linear-gradient(135deg, rgba(0, 212, 255, 0.1), rgba(23, 100, 255, 0.1)); border-color: rgba(23, 100, 255, 0.2); transform: translateX(8px); box-shadow: 0 4px 12px rgba(0, 212, 255, 0.2); }\n        .toc-list a:hover::before, .toc-list a.active::before { transform: scaleY(1); }\n        .toc-list a.active { color: #1764ff; background: linear-gradient(135deg, rgba(0, 212, 255, 0.15), rgba(23, 100, 255, 0.15)); font-weight: 700; border-color: rgba(23, 100, 255, 0.3); transform: translateX(4px); box-shadow: 0 6px 16px rgba(0, 212, 255, 0.25); }\n        .toc-number { color: #1764ff; font-weight: 600; margin-right: 0.5rem; font-size: 0.9em; }\n        .toc-text { flex: 1; }\n        .toc-level-1 { font-weight: 700; font-size: 15px; }\n        .toc-level-2 { font-size: 14px; }\n        .toc-level-3 { font-size: 13px; opacity: 0.9; }\n        .toc-level-4, .toc-level-5, .toc-level-6 { font-size: 12px; opacity: 0.8; }\n        .article-content .formatted-list { margin: 1.5rem 0; padding-left: 0; list-style: none; }\n        .article-content .formatted-list.ordered { counter-reset: list-counter; }\n        .article-content .formatted-list.ordered li { counter-increment: list-counter; position: relative; margin: 1rem 0; padding: 1rem 1rem 1rem 3rem; background: linear-gradient(135deg, rgba(23, 100, 255, 0.03), rgba(0, 212, 255, 0.03)); border-left: 4px solid #1764ff; border-radius: 0 8px 8px 0; transition: all 0.3s ease; }\n        .article-content .formatted-list.ordered li::before { content: counter(list-counter); position: absolute; left: 0.8rem; top: 1rem; background: linear-gradient(135deg, #1764ff, #00d4ff); color: white; width: 1.5rem; height: 1.5rem; border-radius: 50%; display: flex; align-items: center; justify-content: center; font-weight: 600; font-size: 0.9rem; }\n        .article-content .formatted-list.ordered li:hover { background: linear-gradient(135deg, rgba(23, 100, 255, 0.06), rgba(0, 212, 255, 0.06)); transform: translateX(4px); box-shadow: 0 2px 8px rgba(23, 100, 255, 0.15); }\n        .article-content .formatted-list.unordered li { position: relative; margin: 0.8rem 0; padding: 0.8rem 1rem 0.8rem 2.5rem; background: linear-gradient(135deg, rgba(40, 167, 69, 0.03), rgba(25, 135, 84, 0.03)); border-left: 4px solid #28a745; border-radius: 0 6px 6px 0; transition: all 0.3s ease; }\n        .article-content .formatted-list.unordered li::before { content: \"•\"; position: absolute; left: 1rem; top: 0.8rem; color: #28a745; font-size: 1.2rem; font-weight: bold; }\n        .article-content .formatted-list.unordered li:hover { background: linear-gradient(135deg, rgba(40, 167, 69, 0.06), rgba(25, 135, 84, 0.06)); transform: translateX(4px); box-shadow: 0 2px 8px rgba(40, 167, 69, 0.15); }\n        .article-content .list-title { color: #1764ff; font-weight: 600; display: inline-block; margin-bottom: 0.3rem; }\n        .article-content .list-content { color: #2c3e50; line-height: 1.6; display: block; }\n        .article-content .inline-code { background: #f8f9fa; border: 1px solid #e9ecef; border-radius: 4px; padding: 2px 6px; font-family: 'Consolas', 'Monaco', 'Courier New', monospace; font-size: 0.9em; color: #e83e8c; }\n        .article-content .label-item { margin: 0.8rem 0; }\n        .article-content .label { color: #1764ff; font-weight: 600; }\n        .article-content .section-label { color: #2c3e50; font-weight: 600; margin: 1rem 0 0.5rem 0; font-size: 1.1em; border-left: 4px solid #1764ff; padding-left: 0.8rem; }\n        .article-content p, .article-content .content-paragraph { margin: 1.2rem 0; line-height: 1.8; text-align: justify; color: #2c3e50; font-size: 1rem; }\n        .article-content .content-paragraph { text-indent: 0; margin: 1rem 0; }\n        .article-content .indent-paragraph { text-indent: 2em; margin: 1.2rem 0; }\n        .article-content .text-indent-1 { text-indent: 2em; display: block; }\n        .article-content .text-indent-2 { text-indent: 4em; display: block; }\n        .article-content .text-indent-3 { text-indent: 6em; display: block; }\n        .article-content .network-config { background: linear-gradient(135deg, #f8f9fa, #e9ecef); border: 1px solid #dee2e6; border-radius: 6px; padding: 0.8rem 1rem; margin: 0.5rem 0; font-family: 'Consolas', 'Monaco', 'Courier New', monospace; font-size: 0.9rem; box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05); }\n        .article-content .config-label { color: #1764ff; font-weight: 600; }\n        .article-content .config-desc { color: #495057; }\n        .article-content .algorithm-step { background: linear-gradient(135deg, rgba(23, 100, 255, 0.05), rgba(0, 212, 255, 0.05)); border-left: 4px solid #1764ff; padding: 0.8rem 1rem; margin: 0.5rem 0; border-radius: 0 6px 6px 0; transition: all 0.3s ease; }\n        .article-content .step-number { color: #1764ff; font-weight: 700; margin-right: 0.5rem; }\n        .article-content .step-desc { color: #2c3e50; line-height: 1.5; }\n        .article-content table { width: 100%; border-collapse: collapse; margin: 1.5rem 0; background: white; border-radius: 8px; overflow: hidden; box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1); }\n        .article-content table th { background: linear-gradient(135deg, #1764ff, #00d4ff); color: white; padding: 1rem; text-align: left; font-weight: 600; }\n        .article-content table td { padding: 0.8rem 1rem; border-bottom: 1px solid #e9ecef; }\n        .article-content table tr:hover { background: rgba(23, 100, 255, 0.05); }\n        .article-content strong { color: #1764ff; font-weight: 600; }\n        .article-content em { color: #6c757d; font-style: italic; }\n        .article-content a { color: #1764ff; text-decoration: none; border-bottom: 1px solid transparent; transition: all 0.3s ease; }\n        .article-content a:hover { border-bottom-color: #1764ff; color: #0056b3; }\n        .article-content .definition-item { background: linear-gradient(135deg, rgba(40, 167, 69, 0.05), rgba(25, 135, 84, 0.05)); border-left: 4px solid #28a745; padding: 1rem; margin: 1rem 0; border-radius: 0 8px 8px 0; }\n        .article-content .definition-label { color: #28a745; font-weight: 600; display: block; margin-bottom: 0.5rem; }\n        .article-content .definition-content { color: #2c3e50; line-height: 1.6; }\n        .article-content .note-item { background: linear-gradient(135deg, rgba(255, 193, 7, 0.1), rgba(255, 193, 7, 0.05)); border: 1px solid rgba(255, 193, 7, 0.3); border-radius: 8px; padding: 1rem; margin: 1rem 0; position: relative; }\n        .article-content .note-item::before { content: \"💡\"; position: absolute; left: 1rem; top: 1rem; font-size: 1.2em; }\n        .article-content .note-label { color: #856404; font-weight: 600; margin-left: 2rem; }\n        .article-content .note-content { color: #2c3e50; line-height: 1.6; margin-left: 2rem; display: block; margin-top: 0.3rem; }\n        .article-content .math-formula { background: #f8f9fa; border: 1px solid #e9ecef; border-radius: 6px; padding: 1rem; margin: 1rem 0; text-align: center; font-family: 'Times New Roman', serif; font-size: 1.1em; color: #2c3e50; }\n        @media (min-width: 1400px) { .article-content { font-size: 17px; line-height: 1.8; } .article-detail { padding: 2.5rem; } }\n        @media (max-width: 1199px) and (min-width: 992px) { .article-detail { padding: 1.5rem; } .sidebar-card { padding: 1rem; } .toc-list a { font-size: 12px; padding: 6px 8px; } }\n        @media (max-width: 768px) {\n            .article-detail-container { padding: 60px 15px; }\n            .article-content .network-config, .article-content .algorithm-step, .article-content .definition-item, .article-content .note-item { margin: 0.5rem -1rem; border-radius: 0; }\n            .article-content .note-item::before { left: 0.5rem; }\n            .article-content .note-label, .article-content .note-content { margin-left: 1.5rem; }\n            .article-detail { padding: 1.5rem; }\n            .article-title { font-size: 1.6rem; line-height: 1.3; }\n            .article-meta { flex-wrap: wrap; gap: 1rem; padding: 1rem; font-size: 0.8rem; }\n            .article-meta .meta-item { flex-shrink: 1; }\n            .meta-icon { font-size: 14px; margin-right: 3px; }\n            .article-content { font-size: 15px; }\n            .article-content h1 { font-size: 1.6rem; }\n            .article-content h2 { font-size: 1.4rem; }\n            .article-content h3 { font-size: 1.2rem; }\n            .article-content h4 { font-size: 1.1rem; }\n            .article-content pre { padding: 0.75rem; font-size: 0.85em; overflow-x: auto; }\n            .article-content code { font-size: 0.85em; padding: 1px 4px; }\n            .sidebar { margin-top: 2rem; }\n            .sidebar-card { position: static; }\n            .toc-card { max-height: none; overflow-y: visible; }\n            .share-buttons { flex-direction: column; gap: 0.5rem; }\n            .share-buttons .btn { width: 100%; justify-content: center; }\n        }\n        @media print { body * { visibility: hidden; } .article-detail, .article-detail * { visibility: visible; } .article-detail { position: absolute; left: 0; top: 0; width: 100%; background: white; box-shadow: none; border: none; margin: 0; padding: 20px; } .article-meta { border: none; background: none; padding: 0; margin-bottom: 20px; } .article-footer, .reading-progress, .back-to-top { display: none !important; } .article-content { font-size: 12pt; line-height: 1.6; } .article-content h1, .article-content h2, .article-content h3, .article-content h4, .article-content h5, .article-content h6 { page-break-after: avoid; margin-top: 20pt; margin-bottom: 10pt; } .article-content pre, .article-content blockquote { page-break-inside: avoid; } }\n    </style>\n\n<script type=\"application/ld+json\">{\"@context\":\"https://schema.org\",\"@type\":\"BreadcrumbList\",\"itemListElement\":[{\"@type\":\"ListItem\",\"position\":1,\"name\":\"首頁\",\"item\":\"https://ocr.oldfish.cn/default.aspx\"},{\"@type\":\"ListItem\",\"position\":2,\"name\":\"技術文章\",\"item\":\"https://ocr.oldfish.cn/Articles.aspx\"},{\"@type\":\"ListItem\",\"position\":3,\"name\":\"文章詳情\",\"item\":\"https://ocr.oldfish.cn/Article.aspx?id=82&slug=attention-mechanism-principles&lang=zh-Hans&_translation_source=true\"}]}</script>\n<link rel=\"stylesheet\" href=\"/site/css/site.css\" type=\"text/css\" /><title>【深度學習OCR系列·5】注意力機制原理與實現</title><meta http-equiv=\"Content-Language\" content=\"yue\" />\n</head>\r\n<body>\r\n    <div class=\"home-container root\">\r\n        <header class=\"v4_header_pc\">\r\n            <div class=\"header_pc_left\">\r\n                <a href=\"Default.aspx\" title=\"首頁| AI智能文字識別\" class=\"pc-logo-wrap ml-4 mr-5\">\r\n                    <picture>\r\n                        <source srcset=\"site/image/logo-s.png.webp\" type=\"image/webp\" />\r\n                        <img src=\"site/image/logo-s.png\" class=\"pc-logo\" style=\"height: 100%\" alt=\"OCR文字識別助手官網Logo - AI智能文字識別平台\" />\r\n                    </picture>\r\n                    <span style=\"font-size: 18px; font-weight: bold; color: #333;\">OCR文字識別助手</span>\r\n                </a>\r\n                <ul class=\"top-nav\" role=\"navigation\" aria-label=\"主導航\">\r\n                    <li><a class=\"color-default\" href=\"Default.aspx\" title=\"OCR文字識別助手首頁\">首頁</a></li>\r\n                    <li><a class=\"color-default\" data-sub=\"product-sub-nav\" href=\"Detail.aspx\" title=\"OCR產品功能介紹\">產品功能</a><div class=\"triangle\"></div>\r\n                    </li>\r\n                    <li><a class=\"color-default\" data-sub=\"experience-sub-nav\" href=\"Ocr.aspx\" title=\"在線體驗OCR功能\">在線體驗</a><div class=\"triangle\"></div>\r\n                    </li>\r\n                    <li><a class=\"color-default\" data-sub=\"vip-sub-nav\" href=\"Version.aspx\" title=\"OCR會員升級服務\">會員升級</a><div class=\"triangle\"></div>\r\n                    </li>\r\n                    <li><a class=\"color-default\" href=\"Download.aspx\" title=\"免費下載OCR文字識別助手\">免費下載</a></li>\r\n                    <li><a class=\"color-default\" data-sub=\"articles-sub-nav\" href=\"Articles.aspx\" title=\"OCR技術文章同知識分享\">技術分享</a><div class=\"triangle\"></div>\r\n                    </li>\r\n                    <li><a class=\"color-default\" data-sub=\"support-sub-nav\" href=\"FAQ.aspx\" title=\"OCR使用幫助和技術支持\">幫助中心</a><div class=\"triangle\"></div>\r\n                    </li>\r\n                </ul>\r\n                <div class=\"product-con\">\r\n                    <section id=\"product-sub-nav\" class=\"pls-nav-dropdown\" style=\"visibility: hidden; height: 0px; display: none;\">\r\n                        <div class=\"d-flex nav-drown-con\" style=\"height: 0px; transition: transform 0.2s ease-in-out 0s,opacity 0.2s ease-in-out 0s; opacity: 0; transform: translate(0px, -100%);\">\r\n                            <div class=\"prdocu-sub-left\">\r\n                                <div class=\"mr-auto\">\r\n                                    <picture>\r\n                                        <source class=\"p-icon\" srcset=\"site/image/icon/p_1.png.webp\" type=\"image/webp\" alt=\"\" aria-hidden=\"true\" />\r\n                                        <img class=\"p-icon\" src=\"site/image/icon/p_1.png\" alt=\"OCR產品功能圖標\" aria-hidden=\"true\" loading=\"lazy\" />\r\n                                    </picture>\r\n                                    <div class=\"mt-4\">\r\n                                        <h3 class=\"h6\">OCR文字識別助手</h3>\r\n                                        <p class=\"color-gray fn14 mt-3 sub-left-desc\">提升效率·降低成本·創造價值</p>\r\n                                        <p class=\"color-gray fn14 mt-3 sub-left-desc\">&nbsp;</p>\r\n                                        <p class=\"color-gray fn14 mt-3 sub-left-desc\">智能識別·高速處理·精準輸出</p>\r\n                                        <p class=\"color-gray fn14 mt-3 sub-left-desc\">由文字到表格，由公式到翻譯</p>\r\n                                        <p class=\"color-gray fn14 mt-3 sub-left-desc\">讓每一次文字處理都如此簡單</p>\r\n                                        <a class=\"link block disa fn14 contact-business shake\" href=\"Detail.aspx\">瞭解功能<span class=\"iconfont4 icon-xiangzuo gt\"></span></a>\r\n                                    </div>\r\n                                </div>\r\n                            </div>\r\n                            <div class=\"bg-white prdocu-sub-right\">\r\n                                <div>\r\n                                    <h2 class=\"text-base-color h6\">產品功能</h2>\r\n                                    <div class=\"mt-4 d-flex flex-wrap\">\r\n                                        <a href=\"Detail.aspx\" class=\"pro-item color-default\" title=\"查看OCR助手核心功能詳細介紹\">\r\n                                            <div class=\"letter-wrap\">\r\n                                                <h3 class=\"h6\">核心功能</h3>\r\n                                                <span class=\"color-gray fn14\">詳細了解OCR助手的核心功能和技術優勢，98%+識別率</span>\r\n                                            </div>\r\n                                        </a>\r\n                                        <a href=\"Version.aspx\" class=\"pro-item color-default\" title=\"對比OCR助手各版本功能差異\">\r\n                                            <div class=\"letter-wrap\">\r\n                                                <h3 class=\"h6\">版本對比</h3>\r\n                                                <span class=\"color-gray fn14\">詳細對比免費版、個人版、專業版、旗艦版功能差異</span>\r\n                                            </div>\r\n                                        </a>\r\n                                        <a href=\"FAQ.aspx\" class=\"pro-item color-default\" title=\"查看OCR助手常見問題解答\">\r\n                                            <div class=\"letter-wrap\">\r\n                                                <h3 class=\"h6\">產品問答</h3>\r\n                                                <span class=\"color-gray fn14\">快速了解產品功能、使用方法和常見問題的詳細解答</span>\r\n                                            </div>\r\n                                        </a>\r\n                                        <a href=\"Download.aspx\" class=\"pro-item color-default\" title=\"免費下載OCR文字識別助手\">\r\n                                            <div class=\"letter-wrap\">\r\n                                                <h3 class=\"h6\">免費試用</h3>\r\n                                                <span class=\"color-gray fn14\">立即下載安裝OCR助手，免費體驗強大嘅文字識別功能</span>\r\n                                            </div>\r\n                                        </a>\r\n                                        <div style=\"width: 100%; margin: 15px 0;\">\r\n                                            <h4 class=\"h6\" style=\"color: #007cfa; margin-bottom: 10px;\">在線OCR識別</h4>\r\n                                        </div>\r\n                                        <a href=\"Ocr.aspx?type=text_recognize\" class=\"pro-item color-default\" title=\"在線體驗通用文字識別功能\">\r\n                                            <div class=\"letter-wrap\">\r\n                                                <h3 class=\"h6\">通用文字識別</h3>\r\n                                                <span class=\"color-gray fn14\">多語種高精度文字智能提取，支持印刷體與場景複雜圖識別多</span>\r\n                                            </div>\r\n                                        </a>\r\n                                        <a href=\"Ocr.aspx?type=table\" class=\"pro-item color-default\">\r\n                                            <div class=\"letter-wrap\">\r\n                                                <h3 class=\"h6\">通用表格識別</h3>\r\n                                                <span class=\"color-gray fn14\">表格圖片智能轉Excel文件，自動處理複雜錶結構和合併單元格</span>\r\n                                            </div>\r\n                                        </a>\r\n                                        <a href=\"Ocr.aspx?type=handwritten_ocr\" class=\"pro-item color-default\">\r\n                                            <div class=\"letter-wrap\">\r\n                                                <h3 class=\"h6\">手寫識別</h3>\r\n                                                <span class=\"color-gray fn14\">智能識別中英文手寫內容，支持課堂筆記、病歷記錄等場景</span>\r\n                                            </div>\r\n                                        </a>\r\n                                        <a href=\"Ocr.aspx?type=pdf2word\" class=\"pro-item color-default\">\r\n                                            <div class=\"letter-wrap\">\r\n                                                <h3 class=\"h6\">PDF轉Word</h3>\r\n                                                <span class=\"color-gray fn14\">PDF文檔快速轉為Word格式，完美保留原始排版同圖文布局</span>\r\n                                            </div>\r\n                                        </a>\r\n                                    </div>\r\n                                </div>\r\n                            </div>\r\n                        </div>\r\n                    </section>\r\n                    <section id=\"experience-sub-nav\" class=\"pls-nav-dropdown solution-part\" style=\"visibility: hidden; height: 0px; display: none;\">\r\n                        <div class=\"d-flex nav-drown-con\" style=\"height: 0px; transition: transform 0.2s ease-in-out 0s,opacity 0.2s ease-in-out 0s; opacity: 0; transform: translate(0px, -100%);\">\r\n                            <div class=\"prdocu-sub-left\">\r\n                                <div class=\"mr-auto\">\r\n                                    <picture>\r\n                                        <source srcset=\"site/image/icon/p_4.png.webp\" type=\"image/webp\" />\r\n                                        <img class=\"p-icon\" src=\"site/image/icon/p_4.png\" alt=\"在線OCR體驗中心圖標\" aria-hidden=\"true\" loading=\"lazy\" />\r\n                                    </picture>\r\n                                    <div class=\"mt-4\">\r\n                                        <h3 class=\"h6\">OCR文字識別助手</h3>\r\n                                        <p class=\"color-gray fn14 mt-3 sub-left-desc\">文字、表格、公式、文檔、翻譯</p>\r\n                                        <p class=\"color-gray fn14 mt-3 sub-left-desc\">&nbsp;</p>\r\n                                        <p class=\"color-gray fn14 mt-3 sub-left-desc\">三步完成所有文字處理需求</p>\r\n                                        <p class=\"color-gray fn14 mt-3 sub-left-desc\">截图→識別→應用</p>\r\n                                        <p class=\"color-gray fn14 mt-3 sub-left-desc\">令工作效率提升300%</p>\r\n                                        <a class=\"link block disa fn14 contact-business shake\" href=\"Ocr.aspx\">立即體驗<span class=\"iconfont4 icon-xiangzuo gt\"></span></a>\r\n                                    </div>\r\n                                </div>\r\n                            </div>\r\n                            <div class=\"bg-white prdocu-sub-right\">\r\n                                <div>\r\n                                    <div class=\"mt-4 d-flex flex-wrap\">\r\n                                        <div style=\"width: 100%; margin-bottom: 15px;\">\r\n                                            <h4 class=\"h6\" style=\"color: #007cfa; margin-bottom: 10px;\">OCR功能體驗</h4>\r\n                                        </div>\r\n                                        <a href=\"Ocr.aspx?type=index\" class=\"pro-item color-default\">\r\n                                            <div class=\"letter-wrap\">\r\n                                                <h3 class=\"h6\">全部功能</h3>\r\n                                                <span class=\"color-gray fn14\">一站式體驗所有OCR智能功能，快速找到適合您需求的最佳解決方案</span>\r\n                                            </div>\r\n                                        </a>\r\n                                        <a href=\"Ocr.aspx?type=text_recognize\" class=\"pro-item color-default\">\r\n                                            <div class=\"letter-wrap\">\r\n                                                <h3 class=\"h6\">通用文字識別</h3>\r\n                                                <span class=\"color-gray fn14\">多語種高精度文字智能提取，支持印刷體與場景複雜圖識別多</span>\r\n                                            </div>\r\n                                        </a>\r\n                                        <a href=\"Ocr.aspx?type=table\" class=\"pro-item color-default\">\r\n                                            <div class=\"letter-wrap\">\r\n                                                <h3 class=\"h6\">通用表格識別</h3>\r\n                                                <span class=\"color-gray fn14\">表格圖片智能轉Excel文件，自動處理複雜錶結構和合併單元格</span>\r\n                                            </div>\r\n                                        </a>\r\n                                        <a href=\"Ocr.aspx?type=handwritten_ocr\" class=\"pro-item color-default\">\r\n                                            <div class=\"letter-wrap\">\r\n                                                <h3 class=\"h6\">手寫識別</h3>\r\n                                                <span class=\"color-gray fn14\">智能識別中英文手寫內容，支持課堂筆記、病歷記錄等場景</span>\r\n                                            </div>\r\n                                        </a>\r\n                                        <a href=\"Ocr.aspx?type=pdf2word\" class=\"pro-item color-default\">\r\n                                            <div class=\"letter-wrap\">\r\n                                                <h3 class=\"h6\">PDF轉Word</h3>\r\n                                                <span class=\"color-gray fn14\">PDF文檔快速轉為Word格式，完美保留原始排版同圖文布局</span>\r\n                                            </div>\r\n                                        </a>\r\n                                        <a href=\"Ocr.aspx?type=pdf2markdown\" class=\"pro-item color-default\">\r\n                                            <div class=\"letter-wrap\">\r\n                                                <h3 class=\"h6\">PDF轉Markdown</h3>\r\n                                                <span class=\"color-gray fn14\">PDF文檔智能轉為MD格式，代碼塊和文本結構自動優化處理</span>\r\n                                            </div>\r\n                                        </a>\r\n                                        <div style=\"width: 100%; margin: 15px 0;\">\r\n                                            <h4 class=\"h6\" style=\"color: #007cfa; margin-bottom: 10px;\">文檔處理工具</h4>\r\n                                        </div>\r\n                                        <a href=\"Ocr.aspx?type=word2pdf\" class=\"pro-item color-default\">\r\n                                            <div class=\"letter-wrap\">\r\n                                                <h3 class=\"h6\">Word轉PDF</h3>\r\n                                                <span class=\"color-gray fn14\">Word文檔一鍵轉PDF，完美保留原格式，適合存檔和正式文件分享</span>\r\n                                            </div>\r\n                                        </a>\r\n                                        <a href=\"Ocr.aspx?type=word2jpg\" class=\"pro-item color-default\">\r\n                                            <div class=\"letter-wrap\">\r\n                                                <h3 class=\"h6\">Word轉圖片</h3>\r\n                                                <span class=\"color-gray fn14\">Word快勞智能轉JPG圖，支持多頁處理，便於社交媒體分享</span>\r\n                                            </div>\r\n                                        </a>\r\n                                        <a href=\"Ocr.aspx?type=pdf2jpg\" class=\"pro-item color-default\">\r\n                                            <div class=\"letter-wrap\">\r\n                                                <h3 class=\"h6\">PDF轉圖</h3>\r\n                                                <span class=\"color-gray fn14\">PDF文檔高清轉換為JPG圖片，支持批量處理和自定義分辨率</span>\r\n                                            </div>\r\n                                        </a>\r\n                                        <a href=\"Ocr.aspx?type=image2pdf\" class=\"pro-item color-default\">\r\n                                            <div class=\"letter-wrap\">\r\n                                                <h3 class=\"h6\">圖片轉PDF</h3>\r\n                                                <span class=\"color-gray fn14\">多張圖合併為PDF文檔，支持排序同頁面設置</span>\r\n                                            </div>\r\n                                        </a>\r\n                                        <div style=\"width: 100%; margin: 15px 0;\">\r\n                                            <h4 class=\"h6\" style=\"color: #007cfa; margin-bottom: 10px;\">開發者工具</h4>\r\n                                        </div>\r\n                                        <a href=\"Tool.aspx?type=Json\" class=\"pro-item color-default\">\r\n                                            <div class=\"letter-wrap\">\r\n                                                <h3 class=\"h6\">JSON格式化</h3>\r\n                                                <span class=\"color-gray fn14\">智能美化JSON代碼結構，支持壓縮和展開，便於開發調試</span>\r\n                                            </div>\r\n                                        </a>\r\n                                        <a href=\"Tool.aspx?type=Regex\" class=\"pro-item color-default\">\r\n                                            <div class=\"letter-wrap\">\r\n                                                <h3 class=\"h6\">正則表達式</h3>\r\n                                                <span class=\"color-gray fn14\">實時驗證正則表達式匹配效果，內置常用模式庫</span>\r\n                                            </div>\r\n                                        </a>\r\n                                        <a href=\"Tool.aspx?type=Encode\" class=\"pro-item color-default\">\r\n                                            <div class=\"letter-wrap\">\r\n                                                <h3 class=\"h6\">文本編碼轉換</h3>\r\n                                                <span class=\"color-gray fn14\">支持Base64/URL/Unicode等多種編碼格式互相轉換</span>\r\n                                            </div>\r\n                                        </a>\r\n                                        <a href=\"Tool.aspx?type=Diff\" class=\"pro-item color-default\">\r\n                                            <div class=\"letter-wrap\">\r\n                                                <h3 class=\"h6\">文本比對合併</h3>\r\n                                                <span class=\"color-gray fn14\">高亮顯示文本差異，支持逐行對比和智能合併</span>\r\n                                            </div>\r\n                                        </a>\r\n                                        <a href=\"Tool.aspx?type=Color\" class=\"pro-item color-default\">\r\n                                            <div class=\"letter-wrap\">\r\n                                                <h3 class=\"h6\">顏色工具</h3>\r\n                                                <span class=\"color-gray fn14\">RGB/HEX顏色代碼轉換，在線取色器，前端開發必備工具</span>\r\n                                            </div>\r\n                                        </a>\r\n                                        <a href=\"Tool.aspx?type=Count\" class=\"pro-item color-default\">\r\n                                            <div class=\"letter-wrap\">\r\n                                                <h3 class=\"h6\">字數統計</h3>\r\n                                                <span class=\"color-gray fn14\">智能統計字符、詞彙同段落數量，自動優化文本排版</span>\r\n                                            </div>\r\n                                        </a>\r\n                                        <a href=\"Tool.aspx?type=Timespan\" class=\"pro-item color-default\">\r\n                                            <div class=\"letter-wrap\">\r\n                                                <h3 class=\"h6\">時間戳轉換</h3>\r\n                                                <span class=\"color-gray fn14\">時間與Unix時間戳互相轉換，支持多種格式和時區設置</span>\r\n                                            </div>\r\n                                        </a>\r\n                                        <a href=\"Tool.aspx?type=Calc\" class=\"pro-item color-default\">\r\n                                            <div class=\"letter-wrap\">\r\n                                                <h3 class=\"h6\">計算器工具</h3>\r\n                                                <span class=\"color-gray fn14\">在線科學計算器，支持基礎運算和高級數學函數計算</span>\r\n                                            </div>\r\n                                        </a>\r\n                                    </div>\r\n                                </div>\r\n                            </div>\r\n                        </div>\r\n                    </section>\r\n                    <section id=\"articles-sub-nav\" class=\"pls-nav-dropdown solution-part\" style=\"visibility: hidden; height: 0px; display: none;\">\r\n                        <div class=\"d-flex nav-drown-con\" style=\"height: 0px; transition: transform 0.2s ease-in-out 0s,opacity 0.2s ease-in-out 0s; opacity: 0; transform: translate(0px, -100%);\">\r\n                            <div class=\"prdocu-sub-left\">\r\n                                <div class=\"mr-auto\">\r\n                                    <picture>\r\n                                        <source srcset=\"site/image/icon/p_4.png.webp\" type=\"image/webp\" />\r\n                                        <img class=\"p-icon\" src=\"site/image/icon/p_4.png\" alt=\"技術分享中心圖標\" aria-hidden=\"true\" loading=\"lazy\" />\r\n                                    </picture>\r\n                                    <div class=\"mt-4\">\r\n                                        <h3 class=\"h6\">OCR技術分享</h3>\r\n                                        <p class=\"color-gray fn14 mt-3 sub-left-desc\">技術教程、應用案例、工具舉薦</p>\r\n                                        <p class=\"color-gray fn14 mt-3 sub-left-desc\">&nbsp;</p>\r\n                                        <p class=\"color-gray fn14 mt-3 sub-left-desc\">由入門到精通嘅完整學習路徑</p>\r\n                                        <p class=\"color-gray fn14 mt-3 sub-left-desc\">實戰案例→技術解析→工具應用</p>\r\n                                        <p class=\"color-gray fn14 mt-3 sub-left-desc\">助力您的OCR技術提升之路</p>\r\n                                        <a class=\"link block disa fn14 contact-business shake\" href=\"Articles.aspx\">瀏覽文章<span class=\"iconfont4 icon-xiangzuo gt\"></span></a>\r\n                                    </div>\r\n                                </div>\r\n                            </div>\r\n                            <div class=\"bg-white prdocu-sub-right\">\r\n                                <div>\r\n                                    <h2 class=\"text-base-color h6\">技術分享</h2>\r\n                                    <div class=\"mt-4 d-flex flex-wrap\">\r\n                                        <a href=\"Articles.aspx\" class=\"pro-item color-default\" title=\"查看所有OCR技術文章\">\r\n                                            <div class=\"letter-wrap\">\r\n                                                <h3 class=\"h6\">全部文章</h3>\r\n                                                <span class=\"color-gray fn14\">瀏覽所有OCR技術文章，涵蓋由基礎到高級嘅完整知識體系</span>\r\n                                            </div>\r\n                                        </a>\r\n                                        \r\n                                        <a href=\"Articles.aspx?category=2\" class=\"pro-item color-default\" title=\"OCR技術教程同入門指南\">\r\n                                            <div class=\"letter-wrap\">\r\n                                                <h3 class=\"h6\">進階指南</h3>\r\n                                                <span class=\"color-gray fn14\">由入門到精通嘅OCR技術教程，詳細嘅操作指南同實戰演練</span>\r\n                                            </div>\r\n                                        </a>\r\n                                        \r\n                                        <a href=\"Articles.aspx?category=3\" class=\"pro-item color-default\" title=\"OCR技術原理、算法同應用\">\r\n                                            <div class=\"letter-wrap\">\r\n                                                <h3 class=\"h6\">技術探索</h3>\r\n                                                <span class=\"color-gray fn14\">探索OCR技術前沿，由原理到應用，深度解析核心算法</span>\r\n                                            </div>\r\n                                        </a>\r\n                                        \r\n                                        <a href=\"Articles.aspx?category=4\" class=\"pro-item color-default\" title=\"OCR行業最新動態和發展趨勢\">\r\n                                            <div class=\"letter-wrap\">\r\n                                                <h3 class=\"h6\">行業趨勢</h3>\r\n                                                <span class=\"color-gray fn14\">OCR技術發展趨勢、市場分析、行業動態同未來展望嘅深度洞察</span>\r\n                                            </div>\r\n                                        </a>\r\n                                        \r\n                                        <a href=\"Articles.aspx?category=5\" class=\"pro-item color-default\" title=\"OCR技術喺各行業嘅應用案例\">\r\n                                            <div class=\"letter-wrap\">\r\n                                                <h3 class=\"h6\">應用案例</h3>\r\n                                                <span class=\"color-gray fn14\">OCR技術喺各行業嘅實際應用案例、解決方案同最佳實踐分享</span>\r\n                                            </div>\r\n                                        </a>\r\n                                        \r\n                                        <a href=\"Articles.aspx?category=6\" class=\"pro-item color-default\" title=\"OCR軟件工具嘅專業評測、對比分析和使用舉薦指南\">\r\n                                            <div class=\"letter-wrap\">\r\n                                                <h3 class=\"h6\">工具評測</h3>\r\n                                                <span class=\"color-gray fn14\">評測各類OCR文字識別軟件和工具，提供詳細嘅功能對比同選擇建議</span>\r\n                                            </div>\r\n                                        </a>\r\n                                        \r\n                                    </div>\r\n                                </div>\r\n                            </div>\r\n                        </div>\r\n                    </section>\r\n                    <section id=\"vip-sub-nav\" class=\"pls-nav-dropdown solution-part\" style=\"visibility: hidden; height: 0px; display: none;\">\r\n                        <div class=\"d-flex nav-drown-con\" style=\"height: 0px; transition: transform 0.2s ease-in-out 0s,opacity 0.2s ease-in-out 0s; opacity: 0; transform: translate(0px, -100%);\">\r\n                            <div class=\"prdocu-sub-left\">\r\n                                <div class=\"mr-auto\">\r\n                                    <picture>\r\n                                        <source class=\"p-icon\" srcset=\"site/image/icon/p_1.png.webp\" type=\"image/webp\" alt=\"\" aria-hidden=\"true\" />\r\n                                        <img class=\"p-icon\" src=\"site/image/icon/p_1.png\" alt=\"會員升級服務圖標\" aria-hidden=\"true\" loading=\"lazy\" />\r\n                                    </picture>\r\n                                    <div class=\"mt-4\">\r\n                                        <h3 class=\"h6\">會員升級服務</h3>\r\n                                        <p class=\"color-gray fn14 mt-3 sub-left-desc\">解鎖全部高級功能·享受專屬服務</p>\r\n                                        <p class=\"color-gray fn14 mt-3 sub-left-desc\">&nbsp;</p>\r\n                                        <p class=\"color-gray fn14 mt-3 sub-left-desc\">離線識別·批量處理·無限制使用</p>\r\n                                        <p class=\"color-gray fn14 mt-3 sub-left-desc\">專業版→旗艦版→企業版</p>\r\n                                        <p class=\"color-gray fn14 mt-3 sub-left-desc\">總有一款適合您的需求</p>\r\n                                        <a class=\"link block disa fn14 contact-business shake\" href=\"Version.aspx\">查看詳情<span class=\"iconfont4 icon-xiangzuo gt\"></span></a>\r\n                                    </div>\r\n                                </div>\r\n                            </div>\r\n                            <div class=\"bg-white prdocu-sub-right\">\r\n                                <div>\r\n                                    <h2 class=\"text-base-color h6\" style=\"margin: inherit;\">會員升級</h2>\r\n                                    <div class=\"mt-4 d-flex flex-wrap\">\r\n                                        <a href=\"Version.aspx\" class=\"pro-item color-default\">\r\n                                            <div class=\"letter-wrap\">\r\n                                                <h3 class=\"h6\">會員特權</h3>\r\n                                                <span class=\"color-gray fn14\">詳細了解各版本功能差異，選擇最適合您的會員等級</span>\r\n                                            </div>\r\n                                        </a>\r\n                                        <a href=\"Upgrade.aspx\" class=\"pro-item color-default\">\r\n                                            <div class=\"letter-wrap\">\r\n                                                <h3 class=\"h6\">立即升級</h3>\r\n                                                <span class=\"color-gray fn14\">快速升級VIP會員，解鎖更多高級功能和專屬服務</span>\r\n                                            </div>\r\n                                        </a>\r\n                                        <a href=\"User.aspx\" class=\"pro-item color-default\">\r\n                                            <div class=\"letter-wrap\">\r\n                                                <h3 class=\"h6\">我嘅賬戶</h3>\r\n                                                <span class=\"color-gray fn14\">管理賬戶信息、訂閱狀態和使用記錄，個性化設置</span>\r\n                                            </div>\r\n                                        </a>\r\n                                    </div>\r\n                                </div>\r\n                            </div>\r\n                        </div>\r\n                    </section>\r\n                    <section id=\"support-sub-nav\" class=\"pls-nav-dropdown solution-part\" style=\"visibility: hidden; height: 0px; display: none;\">\r\n                        <div class=\"d-flex nav-drown-con\" style=\"height: 0px; transition: transform 0.2s ease-in-out 0s,opacity 0.2s ease-in-out 0s; opacity: 0; transform: translate(0px, -100%);\">\r\n                            <div class=\"prdocu-sub-left\">\r\n                                <div class=\"mr-auto\">\r\n                                    <picture>\r\n                                        <source class=\"p-icon\" srcset=\"site/image/icon/p_4.png.webp\" type=\"image/webp\" alt=\"\" aria-hidden=\"true\" />\r\n                                        <img class=\"p-icon\" src=\"site/image/icon/p_4.png\" alt=\"幫助中心支持圖標\" aria-hidden=\"true\" loading=\"lazy\" />\r\n                                    </picture>\r\n                                    <div class=\"mt-4\">\r\n                                        <h3 class=\"h6\">幫助中心</h3>\r\n                                        <p class=\"color-gray fn14 mt-3 sub-left-desc\">專業客服·詳細文檔·快速響應</p>\r\n                                        <p class=\"color-gray fn14 mt-3 sub-left-desc\">&nbsp;</p>\r\n                                        <p class=\"color-gray fn14 mt-3 sub-left-desc\">遇到問題唔使怕</p>\r\n                                        <p class=\"color-gray fn14 mt-3 sub-left-desc\">問題→查找→解決</p>\r\n                                        <p class=\"color-gray fn14 mt-3 sub-left-desc\">讓您的使用體驗更順暢</p>\r\n                                        <a class=\"link block disa fn14 contact-business shake\" href=\"FAQ.aspx\">獲取幫助<span class=\"iconfont4 icon-xiangzuo gt\"></span></a>\r\n                                    </div>\r\n                                </div>\r\n                            </div>\r\n                            <div class=\"bg-white prdocu-sub-right\">\r\n                                <div>\r\n                                    <h2 class=\"text-base-color h6\" style=\"margin: inherit;\">幫助中心</h2>\r\n                                    <div class=\"mt-4 d-flex flex-wrap\">\r\n                                        <a href=\"FAQ.aspx\" class=\"pro-item color-default\">\r\n                                            <div class=\"letter-wrap\">\r\n                                                <h3 class=\"h6\">常見問題</h3>\r\n                                                <span class=\"color-gray fn14\">快速解答用戶常見疑問，提供詳細嘅使用指南同技術支持</span>\r\n                                            </div>\r\n                                        </a>\r\n                                        <a href=\"AboutUs.aspx\" class=\"pro-item color-default\">\r\n                                            <div class=\"letter-wrap\">\r\n                                                <h3 class=\"h6\">關於我哋</h3>\r\n                                                <span class=\"color-gray fn14\">瞭解OCR文字識別助手嘅發展歷程、核心功能和服務理念</span>\r\n                                            </div>\r\n                                        </a>\r\n                                        <a href=\"UserAgreement.aspx\" class=\"pro-item color-default\">\r\n                                            <div class=\"letter-wrap\">\r\n                                                <h3 class=\"h6\">用戶協議</h3>\r\n                                                <span class=\"color-gray fn14\">詳細嘅服務條款和用戶權利義務說明</span>\r\n                                            </div>\r\n                                        </a>\r\n                                        <a href=\"PrivacyPolicy.aspx\" class=\"pro-item color-default\">\r\n                                            <div class=\"letter-wrap\">\r\n                                                <h3 class=\"h6\">私隱協議</h3>\r\n                                                <span class=\"color-gray fn14\">個人信息保護政策和數據安全保障措施</span>\r\n                                            </div>\r\n                                        </a>\r\n                                        <a href=\"Status.aspx\" class=\"pro-item color-default\">\r\n                                            <div class=\"letter-wrap\">\r\n                                                <h3 class=\"h6\">系統狀態</h3>\r\n                                                <span class=\"color-gray fn14\">實時監控全球識別節點運行狀態，查看系統性能數據</span>\r\n                                            </div>\r\n                                        </a>\r\n                                        <a href=\"javascript:void(0);\" class=\"pro-item color-default\" onclick=\"if(window.openCustomerService) window.openCustomerService(); else alert('請點擊右側浮窗圖標聯繫客服');\">\r\n                                            <div class=\"letter-wrap\">\r\n                                                <h3 class=\"h6\">聯繫客服</h3>\r\n                                                <span class=\"color-gray fn14\">在線客服撐，快速響應您的問題和需求</span>\r\n                                            </div>\r\n                                        </a>\r\n                                    </div>\r\n                                </div>\r\n                            </div>\r\n                        </div>\r\n                    </section>\r\n                </div>\r\n            </div>\r\n            <div class=\"header_pc_right\" style=\"display: flex; align-items: center; justify-content: flex-end;\">\r\n                <div id=\"translate\"></div>\r\n                <div id=\"user-login-container\" data-url=\"User.ashx?op=userinfo\" style=\"white-space: nowrap; padding-right: 20px;\"></div>\r\n            </div>\r\n        </header>\r\n        <header class=\"v4_header_mob\">\r\n            <div class=\"container\">\r\n                <div class=\"row no-gutters justify-content-between align-items-center\">\r\n                    <a href=\"Default.aspx\" title=\"首頁| AI智能文字識別\" class=\"mob-logo-wrap\">\r\n                        <picture>\r\n                            <source srcset=\"site/image/logo-s.png.webp\" type=\"image/webp\" />\r\n                            <img src=\"site/image/logo-s.png\" class=\"pc-logo\" style=\"height: 32px; width: 32px\" alt=\"OCR文字識別助手移動端Logo\" />\r\n                        </picture>\r\n                        <span style=\"font-size: 18px; font-weight: bold; color: #333;\">OCR文字識別助手</span>\r\n                    </a>\r\n                    <div class=\"right-menu\" role=\"button\" tabindex=\"0\" aria-label=\"打開導航餐牌\" data-toggle=\"collapse\" data-target=\"#mob-header-collapse\" aria-expanded=\"false\" aria-controls=\"mob-header-collapse\">\r\n                        <nav-products id=\"nav_products\">\r\n                            <nav-button class=\"submail-mdi-nav-btn\" style=\"width: 60px\">\r\n                                <label id=\"mdi-submail-nav-btn\">\r\n                                    <span class=\"mdi-nav-products-1\">\r\n                                        <span class=\"nav-products-icon\"></span>\r\n                                    </span>\r\n                                    <span class=\"mdi-nav-products-2\">\r\n                                        <span class=\"nav-products-icon\"></span>\r\n                                    </span>\r\n                                    <span class=\"mdi-nav-products-3\">\r\n                                        <span class=\"nav-products-icon\"></span>\r\n                                    </span>\r\n                                </label>\r\n                            </nav-button>\r\n                        </nav-products>\r\n                    </div>\r\n                </div>\r\n            </div>\r\n            <div class=\"mob-nav-content\">\r\n                <div class=\"sidebar-fix\">\r\n                    <div class=\"container\">\r\n                        <div class=\"row\">\r\n                            <div class=\"col-4 bg-white sidebar-fix-left\" style=\"border-right: 1px solid #e1e6ed\">\r\n                                <div class=\"mob-nav-item active\" data=\"0\">\r\n                                    <div class=\"nav-header\"><a href=\"Default.aspx\" class=\"nav-header-letter px-3\"><span>首頁</span></a></div>\r\n                                </div>\r\n                                <div class=\"mob-nav-item\" data=\"1\">\r\n                                    <div class=\"nav-header\"><a href=\"Detail.aspx\" class=\"nav-header-letter px-3\"><span>功能</span></a></div>\r\n                                </div>\r\n                                <div class=\"mob-nav-item\" data=\"2\">\r\n                                    <div class=\"nav-header\"><a href=\"Ocr.aspx\" class=\"nav-header-letter px-3\"><span>體驗</span></a></div>\r\n                                </div>\r\n                                <div class=\"mob-nav-item\" data=\"3\">\r\n                                    <div class=\"nav-header\"><a href=\"Version.aspx\" class=\"nav-header-letter px-3\"><span>會員</span></a></div>\r\n                                </div>\r\n                                <div class=\"mob-nav-item\" data=\"4\">\r\n                                    <div class=\"nav-header\"><a href=\"Download.aspx\" class=\"nav-header-letter px-3\"><span>下載</span></a></div>\r\n                                </div>\r\n                                <div class=\"mob-nav-item\" data=\"5\">\r\n                                    <div class=\"nav-header\"><a href=\"Articles.aspx\" class=\"nav-header-letter px-3\"><span>分享</span></a></div>\r\n                                </div>\r\n                                <div class=\"mob-nav-item\" data=\"6\">\r\n                                    <div class=\"nav-header\"><a href=\"FAQ.aspx\" class=\"nav-header-letter px-3\"><span>幫手</span></a></div>\r\n                                </div>\r\n                            </div>\r\n                            <div class=\"col-8 bg-white sidebar-fix-rigth\">\r\n                                <div class=\"sub-nav\">\r\n                                    <div class=\"sub-nav-item py-2 pb-3 px-4\">\r\n                                        <h2 class=\"h5\" style=\"margin: inherit;\">高效率生產力工具</h2>\r\n                                        <p class=\"color-gray fn14 mt-3 sub-left-desc\">智能識別·高速處理·精準輸出</p>\r\n                                        <p class=\"color-gray fn14 mt-3 sub-left-desc\">&nbsp;</p>\r\n                                        <p class=\"color-gray fn14 mt-3 sub-left-desc\">3秒識別整頁文檔</p>\r\n                                        <p class=\"color-gray fn14 mt-3 sub-left-desc\">98%+識別準確率</p>\r\n                                        <p class=\"color-gray fn14 mt-3 sub-left-desc\">多語種實時處理無延遲</p>\r\n                                        <a class=\"link block disa fn14 contact-business shake\" href=\"Download.aspx\">立即下載體驗<span class=\"iconfont4 icon-xiangzuo gt\"></span></a>\r\n                                    </div>\r\n                                </div>\r\n                                <div class=\"sub-nav\">\r\n                                    <div class=\"sub-nav-item py-3 px-4\">\r\n                                        <h2 class=\"h5\" style=\"margin: inherit;\">產品功能</h2>\r\n                                        <p class=\"color-gray fn14 mt-3 sub-left-desc\">AI智能識別，一站式解決方案</p>\r\n                                    </div>\r\n                                    <div class=\"sub-nav-item py-3 px-4\"><a href=\"Detail.aspx\">功能介紹</a></div>\r\n                                    <div class=\"sub-nav-item py-3 px-4\"><a href=\"Download.aspx\">軟件下載</a></div>\r\n                                    <div class=\"sub-nav-item py-3 px-4\"><a href=\"Version.aspx\">版本對比</a></div>\r\n                                    <div class=\"sub-nav-item py-3 px-4\"><a href=\"Ocr.aspx\">在線體驗</a></div>\r\n                                    <div class=\"sub-nav-item py-3 px-4\"><a href=\"Status.aspx\">系統狀態</a></div>\r\n                                </div>\r\n                                <div class=\"sub-nav\">\r\n                                    <div class=\"sub-nav-item py-3 px-4\">\r\n                                        <h2 class=\"h5\" style=\"margin: inherit;\">在線體驗</h2>\r\n                                        <p class=\"color-gray fn14 mt-3 sub-left-desc\">免費在線OCR功能體驗</p>\r\n                                    </div>\r\n                                    <div class=\"sub-nav-item py-3 px-4\"><a href=\"Ocr.aspx\">全部功能</a></div>\r\n                                    <div class=\"sub-nav-item py-3 px-4\"><a href=\"Ocr.aspx?type=text_recognize\">文字識別</a></div>\r\n                                    <div class=\"sub-nav-item py-3 px-4\"><a href=\"Ocr.aspx?type=table\">表格識別</a></div>\r\n                                    <div class=\"sub-nav-item py-3 px-4\"><a href=\"Ocr.aspx?type=pdf2word\">PDF轉Word</a></div>\r\n                                </div>\r\n                                <div class=\"sub-nav\">\r\n                                    <div class=\"sub-nav-item py-3 px-4\">\r\n                                        <h2 class=\"h5\" style=\"margin: inherit;\">會員升級</h2>\r\n                                        <p class=\"color-gray fn14 mt-3 sub-left-desc\">解鎖全部功能，享受專屬服務</p>\r\n                                    </div>\r\n                                    <div class=\"sub-nav-item py-3 px-4\"><a href=\"Version.aspx\">會員權益</a></div>\r\n                                    <div class=\"sub-nav-item py-3 px-4\"><a href=\"Upgrade.aspx\">立即開通</a></div>\r\n                                </div>\r\n                                <div class=\"sub-nav\">\r\n                                    <div class=\"sub-nav-item py-3 px-4\">\r\n                                        <h2 class=\"h5\" style=\"margin: inherit;\">軟件下載</h2>\r\n                                        <p class=\"color-gray fn14 mt-3 sub-left-desc\">免費下載專業OCR軟件</p>\r\n                                    </div>\r\n                                    <div class=\"sub-nav-item py-3 px-4\"><a href=\"Download.aspx\">立即下載</a></div>\r\n                                    <div class=\"sub-nav-item py-3 px-4\"><a href=\"Version.aspx\">版本對比</a></div>\r\n                                </div>\r\n                                <div class=\"sub-nav\">\r\n                                    <div class=\"sub-nav-item py-3 px-4\">\r\n                                        <h2 class=\"h5\" style=\"margin: inherit;\">技術分享</h2>\r\n                                        <p class=\"color-gray fn14 mt-3 sub-left-desc\">OCR技術文章同知識分享</p>\r\n                                    </div>\r\n                                    <div class=\"sub-nav-item py-3 px-4\"><a href=\"Articles.aspx\">全部文章</a></div>\r\n                                    \r\n                                    <div class=\"sub-nav-item py-3 px-4\"><a href=\"Articles.aspx?category=2\">進階指南</a></div>\r\n                                    \r\n                                    <div class=\"sub-nav-item py-3 px-4\"><a href=\"Articles.aspx?category=3\">技術探索</a></div>\r\n                                    \r\n                                    <div class=\"sub-nav-item py-3 px-4\"><a href=\"Articles.aspx?category=4\">行業趨勢</a></div>\r\n                                    \r\n                                    <div class=\"sub-nav-item py-3 px-4\"><a href=\"Articles.aspx?category=5\">應用案例</a></div>\r\n                                    \r\n                                    <div class=\"sub-nav-item py-3 px-4\"><a href=\"Articles.aspx?category=6\">工具評測</a></div>\r\n                                    \r\n                                </div>\r\n                                <div class=\"sub-nav\">\r\n                                    <div class=\"sub-nav-item py-3 px-4\">\r\n                                        <h2 class=\"h5\" style=\"margin: inherit;\">幫助中心</h2>\r\n                                        <p class=\"color-gray fn14 mt-3 sub-left-desc\">專業客服，貼心服務</p>\r\n                                    </div>\r\n                                    <div class=\"sub-nav-item py-3 px-4\"><a href=\"FAQ.aspx\">使用幫助</a></div>\r\n                                    <div class=\"sub-nav-item py-3 px-4\"><a href=\"AboutUs.aspx\">關於我哋</a></div>\r\n                                    <div class=\"sub-nav-item py-3 px-4\"><a href=\"mailto:<EMAIL>\">聯繫客服</a></div>\r\n                                    <div class=\"sub-nav-item py-3 px-4\"><a href=\"UserAgreement.aspx\">服務條款</a></div>\r\n                                </div>\r\n                            </div>\r\n                        </div>\r\n                    </div>\r\n                </div>\r\n            </div>\r\n        </header>\r\n        <form method=\"post\" action=\"./Article.aspx?id=82&amp;slug=attention-mechanism-principles&amp;lang=zh-Hans&amp;_translation_source=true\" id=\"aspnetForm\">\r\n<div class=\"aspNetHidden\">\r\n<input type=\"hidden\" name=\"__VIEWSTATE\" id=\"__VIEWSTATE\" value=\"BqrhS+P8MW5VC3DOBfGwj6vOm3+QcH2E0p5DXnNjlyGuctPQR14YJZeSsWbjw86X57yDpdIFIGGGE04n8DnmgHdYirvyFNNsxJ81r0FsIZo=\" />\r\n</div>\r\n\r\n<div class=\"aspNetHidden\">\r\n\r\n\t<input type=\"hidden\" name=\"__VIEWSTATEGENERATOR\" id=\"__VIEWSTATEGENERATOR\" value=\"2173C2F0\" />\r\n</div>\r\n            <div>\r\n                \n\n<div class=\"reading-progress\" id=\"readingProgress\"></div>\n<div class=\"article-detail-container\">\n<div class=\"container\">\n<div class=\"row\">\n<div class=\"col-lg-8 col-xl-9 col-md-12\">\n                    \n                    <article class=\"article-detail\" id=\"articleDetail\" data-article-id=\"82\">\n                        <header class=\"article-header\">\n                            <h1 class=\"article-title\">【深度學習OCR系列·5】注意力機制原理與實現</h1>\n                            <div class=\"article-meta\">\n                                <div class=\"meta-item\">\n                                    <span class=\"meta-icon\">📅</span>\n                                    <span>發佈時間：2025年08月19日</span>\n                                </div>\n                                <div class=\"meta-item\">\n                                    <span class=\"meta-icon\">👁️</span>\n                                    <span>閱讀量：<span class=\"view-count\">1250</span></span>\n                                </div>\n                                <div class=\"meta-item\">\n                                    <span class=\"meta-icon\">⏱️</span>\n                                    <span>約58分鐘（ 11464字）</span>\n                                </div>\n                                \n                                <div class=\"meta-item\">\n                                    <span class=\"meta-icon\">📁</span>\n                                    <span>類別：進階指南</span>\n                                </div>\n                                \n                            </div>\n                            <div class=\"article-summary\"><p>深入探討注意力機制嘅數學原理、多頭注意力、自注意力機制以及喺OCR中嘅具體應用。 詳細分析注意力權重計算、位置編碼和性能優化策略。</p></div>\n                        </header>\n                        <div class=\"article-content\" id=\"articleContent\">##引言\r\n\r\n注意力機制（ Attention Mechanism ）係深度學習領域嘅一項重要創新，它模擬咗人類認知過程中嘅選擇性注意能力。 在OCR任務中，注意力機制能夠幫助模型動態咁關注圖像中嘅重要區域，顯著提升文字識別嘅準確性和效率。 本文將深入探討注意力機制嘅理論基礎、數學原理、實現方法以及喺OCR中嘅具體應用，為讀者提供全面嘅技術理解同實踐指導。\r\n\r\n##注意力機制嘅生物學啟發\r\n\r\n###人類視覺注意力系統\r\n\r\n人類嘅視覺系統具有強大嘅選擇性注意能力，呢種能力令我哋能夠喺複雜嘅視覺環境中高效地提取有用信息。 当我们閱讀一段文字時，眼會自動聚焦在當前識別緊嘅字符上，而對周圍嘅信息進行適度嘅抑制。\r\n\r\n**人類注意力的特點 **：\r\n-選擇性：能夠由大量信息中選擇重要嘅部分\r\n-動態性：注意力焦點會隨著任務需求動態調整\r\n-層次性：可以喺不同嘅抽象層次上分配注意力\r\n-並行性：可以同時關注多個相關區域\r\n-上下文敏感：注意力分配受到上下文信息嘅影響\r\n\r\n**視覺注意力的神經機制**：\r\n在神經科學研究中，視覺注意力涉及多個腦區嘅協調工作：\r\n-頂葉皮層：負責空間注意力嘅控制\r\n-前額葉皮層：負責目標導向嘅注意力控制\r\n-視覺皮層：負責特徵檢測和表示\r\n-丘脑：作為注意力信息嘅中繼企\r\n\r\n###計算模型嘅需求\r\n\r\n傳統嘅神經網絡喺處理序列數據時，通常把所有輸入信息壓縮到一個固定長度嘅向量中。 呢種方法存在明顯嘅信息瓶頸問題，特別係喺處理長序列時，早期嘅信息易被後續信息覆蓋。\r\n\r\n**傳統方法的局限**：\r\n-信息瓶頸：固定長度嘅編碼向量難以保存所有重要信息\r\n-長距離依賴：難以建模輸入序列中相距較遠嘅元素之間嘅關係\r\n-計算效率：需要處理成個序列才能得到最終結果\r\n-可解釋性：難以理解模型嘅決策過程\r\n-靈活性：無法根據任務需求動態調整信息處理策略\r\n\r\n**注意力機制的解決方案 **：\r\n注意力機制透過引入動態權重分配機制，允許模型喺處理每個輸出時選擇性地關注輸入嘅不同部分：\r\n-動態選擇：根據當前任務需求動態選擇相關信息\r\n-全局訪問：可以直接訪問輸入序列嘅任意位置\r\n-並行計算：支持並行化處理，提高計算效率\r\n-可解釋性：注意力權重提供咗模型決策嘅可視化解釋\r\n\r\n##注意力機制嘅數學原理\r\n\r\n###基本注意力模型\r\n\r\n注意力機制嘅核心思想係為輸入序列嘅每個元素分配一個權重，呢個權重反映了該元素對當前任務嘅重要程度。\r\n\r\n**數學表示**：\r\n畀定輸入序列X = {x：，x：，xn}同查詢向量q，注意力機制計算每個輸入元素嘅注意力權重：\r\n\r\nα_i = f （ q，x_i） #注意力得分函數\r\nα̃_i = softmax （α_i） = exp （α_i） / ój exp （αj） #歸一化權重\r\n\r\n最終嘅上下文向量透過加權求和得到：\r\nc = Σᵢ α̃_i · x_i\r\n\r\n**注意力機制的組成要素**：\r\n1. **查詢（ Query ）**：表示當前需要關注嘅信息\r\n2. **鍵（ Key ）**：用于計算注意力權重嘅參考信息\r\n3. **值（Value）**：實際參與加權求和的信息\r\n4. **注意力函數**：計算查詢和鍵之間相似度的函數\r\n\r\n###注意力得分函數詳解\r\n\r\n注意力得分函數決定了如何計算查詢和輸入之間的相關性。 不同嘅得分函數適用於不同的應用場景。\r\n\r\n**1. 點積注意力（Dot-Product Attention）**：\r\nα_i = q^T · x_i\r\n\r\n係最簡單嘅注意力機制，計算效率高，但要求查詢和輸入具有相同嘅維度。\r\n\r\n**優點**：\r\n-計算簡單，效率高\r\n-參數量少，唔需要額外嘅可學習參數\r\n-在高維空間中能夠有效區分相似同唔相似嘅向量\r\n\r\n**缺點**：\r\n-要求查詢和鍵具有相同嘅維度\r\n-在高維空間中可能出現數值唔穩定問題\r\n-缺乏學習能力，無法適應複雜嘅相似度關係\r\n\r\n**2. 縮放點積注意力（Scaled Dot-Product Attention）**：\r\nα_i = (q^T · x_i) / √d\r\n\r\n其中d係向量的維度。 縮放因子防止咗喺高維空間中點積值過大導致嘅梯度消失問題。\r\n\r\n**縮放的必要性**：\r\n當維度d好大時，點積既方差會增大，導致softmax函數進入飽和區域，梯度變得好細。 透過除以√d，可以保持點積的方差穩定。\r\n\r\n**數學推導**：\r\n假設q和k的元素是獨立的隨機變量，均值為0，方差為1，則：\r\n- q^T · k的方差為d\r\n- （q^T·k） / √d的方差為1\r\n\r\n**3. 加性注意力（Additive Attention）**：\r\nα_i = v^T · tanh(W_q · q + W_x · x_i)\r\n\r\n通過可學習的參數矩陣W_q和W_x將查詢和輸入映射到相同的空間，然後計算相似度。\r\n\r\n**優勢分析**：\r\n-靈活性：可以處理不同維度嘅查詢同鍵\r\n-學習能力：透過可學習參數適應複雜嘅相似度關係\r\n-表達能力：非線性變換提供更強嘅表達能力\r\n\r\n**參數分析**：\r\n-W_q ∈ R^{d_h×d_q}：查詢投影矩陣\r\n- W_x ∈ R^{d_h×d_x}：鍵投影矩陣\r\n-v∈ R^{d_h}：注意力權重向量\r\n-d_h：隱藏層維度\r\n\r\n**4. 多層感知機注意力（MLP Attention）**：\r\nα_i = MLP([q; x_i])\r\n\r\n使用多層感知機直接學習查詢和輸入之間的相關性函數。\r\n\r\n**網絡結構**：\r\nMLP通常包含2-3層全連接層：\r\n-輸入層：拼接查詢和鍵向量\r\n-隱藏層：使用ReLU或tanh激活函數\r\n-輸出層：輸出標量注意力得分\r\n\r\n**優缺點分析**：\r\n優點：\r\n-最強嘅表達能力\r\n-可以學習複雜嘅非線性關係\r\n-對輸入維度冇限制\r\n\r\n缺點：\r\n-參數量大，易過擬合\r\n-計算複雜度高\r\n-訓練時間長\r\n\r\n###多頭注意力機制\r\n\r\n多頭注意力（ Multi-Head Attention ）係Transformer架構嘅核心組件，它允許模型喺不同嘅表示子空間中並行地關注不同類型嘅信息。\r\n\r\n**數學定義**：\r\nMultiHead(Q, K, V) = Concat(head₁, head₂, ..., headₕ) · W^O\r\n\r\n其中每個注意力頭定義為：\r\nheadᵢ = Attention(Q· W_i^Q, K· W_i^K, V·W_i^V)\r\n\r\n**參數矩陣**：\r\n- W_i^Q ∈ R^{d_model×d_k}：第I個頭的查詢投影矩陣\r\n- W_i^K ∈ R^{d_model×d_k}：第I個頭的鍵投影矩陣\r\n-W_i^V ∈ R^{d_model×d_v}：第I個頭的值投影矩陣\r\n- W^O ∈ R^{h·d_v×d_model}：輸出投影矩陣\r\n\r\n**多頭注意力的優勢**：\r\n1. **多樣性**：不同的頭可以關注不同類型的特徵\r\n2. **並行性**：多個頭可以並行計算，提高效率\r\n3. **表達能力 **：增強了模型的表示學習能力\r\n4. **穩定性**：多個頭的集成效果更加穩定\r\n5. **專業化 **：每個頭可以專門處理特定類型的關係\r\n\r\n**頭數選擇的考慮**：\r\n-頭數過少：可能無法捕獲足夠嘅信息多樣性\r\n-頭數過多：增加計算複雜度，可能導致過擬合\r\n-常用選擇：8頭或16頭，根據模型大小和任務複雜度調整\r\n\r\n**維度分配策略**：\r\n通常設置d_k = d_v = d_model / h，確保總參數量合理：\r\n-保持總計算量相對穩定\r\n-每個頭有足夠嘅表示能力\r\n-避免維度過細導致嘅信息損失\r\n\r\n##自注意力機制\r\n\r\n###自注意力的概念\r\n\r\n自注意力（ Self-Attention ）係注意力機制嘅一種特殊形式，其中查詢、鍵同值都來自同一個輸入序列。 呢種機制允許序列中嘅每個元素關注序列中嘅所有其他元素。\r\n\r\n**數學表示**：\r\n對於輸入序列X = {x：，x2，...，xn}：\r\n-查詢矩阵：Q = X· W^Q\r\n-鍵矩阵：K = X· W^K  \r\n-值矩阵：V = X· W^V\r\n\r\n注意力輸出：\r\nAttention(Q, K, V) = softmax(QK^T / √d_k) · V\r\n\r\n**自注意力的計算過程**：\r\n1. **線性變換 **：將輸入序列通過三個不同的線性變換得到 Q、 K、 V\r\n2. **相似度計算 **：計算所有位置對之間的相似度矩陣\r\n3. **權重歸一化**：使用softmax函數歸一化注意力權重\r\n4. **加權求和**：根據注意力權重對值向量進行加權求和\r\n\r\n###自注意力的優勢\r\n\r\n**1. 長距離依賴建模**：\r\n自注意力可以直接建模序列中任意兩個位置之間嘅關係，不受距離限制。 對於OCR任務特別重要，因為字符嘅識別往往需要考慮較遠位置嘅上下文信息。\r\n\r\n**時間複雜度分析**：\r\n- RNN：O （n）的序列計算，難以並行化\r\n- CNN：O （ log n ）嘅層數才能覆蓋全序列\r\n- Self-Attention：O （ 1 ）嘅路徑長度，直接連接任意位置\r\n\r\n**2. 並行計算**：\r\n與RNN不同，自注意力的計算可以完全並行化，大大提高了訓練效率。\r\n\r\n**並行化優勢**：\r\n-所有位置嘅注意力權重可以同時計算\r\n-矩阵運算可以充分利用GPU嘅並行計算能力\r\n-訓練時間相比RNN大幅減少\r\n\r\n**3. 可解釋性**：\r\n注意力權重矩阵提供咗模型決策嘅可視化解釋，便於理解模型嘅工作機制。\r\n\r\n**可視化分析**：\r\n-注意力熱力圖：顯示每個位置對其他位置嘅關注程度\r\n-注意力模式：分析不同頭關注嘅模式\r\n-層次化分析：觀察不同層嘅注意力模式變化\r\n\r\n**4. 靈活性**：\r\n可以輕鬆地擴展到不同長度嘅序列，無需修改模型架構。\r\n\r\n###位置編碼\r\n\r\n由於自注意力機制本身唔包含位置信息，需要透過位置編碼嚟為模型提供序列中元素嘅位置信息。\r\n\r\n**位置編碼的必要性**：\r\n自注意力機制係置換不變嘅，即改變輸入序列嘅順序唔會影響輸出。 但喺OCR任務中，字符嘅位置信息至關重要。\r\n\r\n**正弦位置編碼**：\r\nPE(pos, 2i) = sin(pos / 10000^(2i/d_model))\r\nPE(pos, 2i+1) = cos(pos / 10000^(2i/d_model))\r\n\r\n其中：\r\n- pos：位置索引\r\n- I：維度索引\r\n- d_model：模型維度\r\n\r\n**正弦位置編碼的優點**：\r\n-塙定性：不需要學習，減少參數量\r\n-外推性：可以處理比訓練時更長嘅序列\r\n-周期性：具有良好的周期性質，便於模型學習相對位置關係\r\n\r\n**可學習位置編碼**：\r\n把位置編碼作為可學習嘅參數，透過訓練過程自動學習最優嘅位置表示。\r\n\r\n**實現方式**：\r\n-為每個位置分配一個可學習嘅向量\r\n-與輸入嵌入相加得到最終輸入\r\n-透過反向傳播更新位置編碼\r\n\r\n**可學習位置編碼的優缺點**：\r\n優點：\r\n-適應性強，可以學習任務特定嘅位置表示\r\n-性能通常略優於固定位置編碼\r\n\r\n缺點：\r\n-增加參數量\r\n-無法處理超出訓練長度嘅序列\r\n-需要更多訓練數據\r\n\r\n**相對位置編碼**：\r\n唔直接編碼絕對位置，而是編碼相對位置關係。\r\n\r\n**實現原理**：\r\n-在注意力計算中加入相對位置偏置\r\n-只關注元素間嘅相對距離，而非絕對位置\r\n-更好嘅泛化能力\r\n\r\n## OCR中嘅注意力應用\r\n\r\n###序列到序列嘅注意力\r\n\r\n在OCR任務中，最常見嘅應用係喺序列到序列模型中使用注意力機制。 編碼器將輸入圖像編碼為特徵序列，解碼器喺生成每個字符時透過注意力機制關注編碼器嘅相關部分。\r\n\r\n**編碼器 - 解碼器架構 **：\r\n1. **編碼器**：CNN提取圖像特徵，RNN編碼為序列表示\r\n2. **注意力模塊**：計算解碼器狀態與編碼器輸出的注意力權重\r\n3. **解碼器 **：基於注意力加權的上下文向量生成字符序列\r\n\r\n**注意力計算過程**：\r\n在解碼時刻t，解碼器狀態為s_t，編碼器輸出為H = {h₁，h₂，...，hn}：\r\n\r\ne_ti = a （s_t，h_i） #注意力得分\r\nα_ti = softmax （e_ti） #注意力權重\r\nc_t = Σᵢ α_ti · h_i#上下文向量\r\n\r\n**注意力函數的選擇**：\r\n常用嘅注意力函數包括：\r\n-點積注意力：e_ti = s_t^T· h_i\r\n-加性注意力：e_ti = v^T· tanh(W_s · s_t + W_h · h_i)\r\n-雙線性注意力：e_ti = s_t^T· W · h_i\r\n\r\n###視覺注意力糢塊\r\n\r\n視覺注意力直接喺圖像特徵圖上應用注意力機制，要模型能夠關注圖像中嘅重要區域。\r\n\r\n**空間注意力**：\r\n對特徵圖嘅每個空間位置計算注意力權重：\r\nA(i,j) = σ(W_a · [F(i,j); g])\r\n\r\n其中：\r\n- F（i，j）：位置（i，j）的特徵向量\r\n- g：全局上下文信息\r\n-W_a：可學習的權重矩陣\r\n-σ：sigmoid激活函數\r\n\r\n**空間注意力的實現步驟**：\r\n1. **特徵提取**：使用CNN提取圖像特徵圖\r\n2. **全局信息聚合**：通過全局平均池化或全局最大池化獲得全局特徵\r\n3. **注意力計算**：結合局部特徵和全局特徵計算注意力權重\r\n4. **特徵增強**：使用注意力權重增強原始特徵\r\n\r\n**通道注意力**：\r\n對特徵圖嘅每個通道計算注意力權重：\r\nA_c = σ(W_c · GAP(F_c))\r\n\r\n其中：\r\n- GAP：全局平均池化\r\n- F_c：第c個通道的特徵圖\r\n- W_c：通道注意力的權重矩陣\r\n\r\n**通道注意力的原理**：\r\n-不同通道捕獲不同類型嘅特徵\r\n-透過注意力機制選擇重要嘅特徵通道\r\n-抑制唔相關嘅特徵，增強有用嘅特徵\r\n\r\n**混合注意力**：\r\n結合空間注意力和通道注意力：\r\nF_output = F ⊙ A_spatial ⊙ A_channel\r\n\r\n其中⊙表示元素級乘法。\r\n\r\n**混合注意力的優勢**：\r\n-同時考慮空間同通道維度嘅重要性\r\n-更精細嘅特徵選擇能力\r\n-更好嘅性能表現\r\n\r\n###多尺度注意力\r\n\r\nOCR任務中嘅文字具有不同的尺度，多尺度注意力機制可以喺不同分辨率上關注相關信息。\r\n\r\n**特徵金字塔注意力**：\r\n喺不同尺度嘅特徵圖上分別應用注意力機制，然後融合多尺度嘅注意力結果。\r\n\r\n**實現架構**：\r\n1. **多尺度特徵提取**：使用特徵金字塔網絡提取不同尺度的特徵\r\n2. **尺度特定注意力 **：在每個尺度上獨立計算注意力權重\r\n3. **跨尺度融合 **：將不同尺度的注意力結果進行融合\r\n4. **最終預測**：基於融合後的特徵進行最終預測\r\n\r\n**自適應尺度選擇**：\r\n根據當前識別任務的需求，動態選擇最適合嘅特徵尺度。\r\n\r\n**選擇策略**：\r\n-基於內容嘅選擇：根據圖像內容自動選擇合適嘅尺度\r\n-基於任務嘅選擇：根據識別任務嘅特點選擇尺度\r\n-動態權重分配：為不同尺度分配動態權重\r\n\r\n##注意力機制嘅變體\r\n\r\n###稀疏注意力\r\n\r\n標準的自注意力機制的計算複雜度為O（n²），對於長序列來說計算成本很高。 稀疏注意力透過限制注意力嘅範圍嚟降低計算複雜度。\r\n\r\n**局部注意力**：\r\n每個位置只關注其周圍嘅固定窗口內嘅位置。\r\n\r\n**數學表示**：\r\n對於位置I，只計算與位置[i-w，i+w]範圍內的注意力權重，其中w是窗口大小。\r\n\r\n**優缺點分析**：\r\n優點：\r\n-計算複雜度降低到O （ n·w ）\r\n-保持了局部上下文信息\r\n-適合處理長序列\r\n\r\n缺點：\r\n-無法捕獲長距離依賴\r\n-窗口大小需要仔細調優\r\n-可能賴重要嘅全局信息\r\n\r\n**分塊注意力**：\r\n把序列分成多個塊，每個位置只關注同一塊內嘅其他位置。\r\n\r\n**實現方式**：\r\n1.將長度為n的序列分成n/b個塊，每塊大小為B\r\n2.喺每個塊內計算完整嘅注意力\r\n3.塊間唔進行注意力計算\r\n\r\n**計算複雜度**：O（n·b），其中B<<n\r\n\r\n**隨機注意力**：\r\n每個位置隨機選擇一部分位置進行注意力計算。\r\n\r\n**隨機選擇策略**：\r\n-固定隨機：確定隨機連接模式定\r\n-動態隨機：訓練過程中動態選擇連接\r\n-結構化隨機：結合局部和隨機連接\r\n\r\n###線性注意力\r\n\r\n線性注意力通過數學變換將注意力計算的複雜度從O（n²）降低到O（n）。\r\n\r\n**核化注意力**：\r\n使用核函數近似softmax操作：\r\nAttention(Q, K, V) ≈ φ(Q) · (φ(K)^T · V)\r\n\r\n其中φ係特徵映射函數。\r\n\r\n**常用核函數**：\r\n- ReLU核：φ（ x ） = ReLU （ x ）\r\n- ELU核：φ（x） = ELU （x） + 1\r\n-隨機特徵核：使用隨機傅里葉特徵\r\n\r\n**線性注意力的優勢**：\r\n-計算複雜度線性增長\r\n-內存需求大幅降低\r\n-適合處理超長序列\r\n\r\n**性能權衡**：\r\n-準確性：通常畧低於標準注意力\r\n-效率：顯著提高計算效率\r\n-適用性：適合資源受限嘅場景\r\n\r\n###交叉注意力\r\n\r\n在多模態任務中，交叉注意力允許不同模態之間嘅信息交互。\r\n\r\n**圖像 - 文本交叉注意力 **：\r\n文本特徵作為查詢，圖像特徵作為鍵和值，實現文本對圖像嘅關注。\r\n\r\n**數學表示**：\r\nCrossAttention(Q_text, K_image, V_image) = softmax(Q_text · K_image^T / √d) · V_image\r\n\r\n**應用場景**：\r\n-圖像描述生成\r\n-視覺問答\r\n-多模態文檔理解\r\n\r\n**雙向交叉注意力**：\r\n同時計算圖像對文本同文本對圖像嘅注意力。\r\n\r\n**實現方式**：\r\n1.圖像到文本：Attention （Q_image，K_text，V_text）\r\n2.文本到圖像：Attention （Q_text，K_image，V_image）\r\n3.特徵融合：把兩個方向嘅注意力結果進行融合\r\n\r\n##訓練策略與優化\r\n\r\n###注意力監督\r\n\r\n透過提供注意力嘅監督信號嚟指導模型學習正確嘅注意力模式。\r\n\r\n**注意力對正損失**：\r\nL_align = || A - A_gt|| ²\r\n\r\n其中：\r\n- A：預測的注意力權重矩陣\r\n- A_gt：真實的注意力標籤\r\n\r\n**監督信號的獲取**：\r\n-人工標註：專家標注重要區域\r\n-啟發式方法：基於規則生成注意力標籤\r\n-弱監督：使用粗粒度嘅監督信號\r\n\r\n**注意力正則化**：\r\n鼓勵注意力權重的稀疏性或平滑性：\r\nL_reg = λ₁ · || A|| ₁ + λ₂ · || ∇A|| ²\r\n\r\n其中：\r\n- || A|| 2：L1 正則化，鼓勵稀疏性\r\n- || ∇A|| 2：平滑性正則化，鼓勵相鄰位置的注意力權重相似\r\n\r\n**多任務學習**：\r\n把注意力預測作為輔助任務，與主任務聯合訓練。\r\n\r\n**損失函數設計**：\r\nL_total = L_main + α · L_attention + β · L_reg\r\n\r\n其中α同β係平衡不同損失項嘅超參數。\r\n\r\n###注意力可視化\r\n\r\n注意力權重嘅可視化有助於理解模型嘅工作機制和調試模型問題。\r\n\r\n**熱力圖可視化 **：\r\n把注意力權重映射為熱力圖，曡加喺原始圖像上顯示模型關注嘅區域。\r\n\r\n**實現步驟**：\r\n1.提取注意力權重矩阵\r\n2.將權重值映射到顏色空間\r\n3.較熱力圖尺寸與原圖匹配\r\n4.曡加顯示或並排顯示\r\n\r\n**注意力軌跡**：\r\n顯示解碼過程中注意力焦點嘅移動軌跡，幫助理解模型嘅識別過程。\r\n\r\n**軌跡分析**：\r\n-注意力移動嘅順序\r\n-注意力停留嘅時間\r\n-注意力跳躍嘅模式\r\n-異常注意力行為嘅識別\r\n\r\n**多頭注意力可視化**：\r\n分別可視化不同注意力頭的權重分佈，分析各頭的專業化程度。\r\n\r\n**分析維度**：\r\n-頭間差異：不同頭關注嘅區域差異\r\n-頭嘅專業化：某些頭專門處理特定類型嘅特徵\r\n-頭嘅重要性：不同頭對最終結果嘅貢獻\r\n\r\n###計算優化\r\n\r\n**內存優化**：\r\n-梯度檢查點：在長序列訓練中使用梯度檢查點減少內存佔用\r\n-混合精度：使用FP16訓練減少內存需求\r\n-注意力緩存：緩存計算過嘅注意力權重\r\n\r\n**計算加速**：\r\n-矩阵分塊：把大矩阵分塊計算，減少內存峰值\r\n-稀疏計算：利用注意力權重嘅稀疏性加速計算\r\n-硬件優化：針對特定硬件優化注意力計算\r\n\r\n**並行化策略**：\r\n-數據並行：喺多GPU上並行處理不同嘅樣本\r\n-模型並行：把注意力計算分布到多個設備\r\n-流水綫並行：把不同層嘅計算流水綫化\r\n\r\n##性能評估與分析\r\n\r\n###注意力質素評估\r\n\r\n**注意力準確率**：\r\n衡量注意力權重與人工標註嘅對正程度。\r\n\r\n計算公式：\r\nAccuracy = （正確關注嘅位置數） / （總位置數）\r\n\r\n**注意力集中度**：\r\n使用熵或基尼系數衡量注意力分佈的集中程度。\r\n\r\n熵計算：\r\nH(A) = -Σᵢ αᵢ · log(αᵢ)\r\n\r\n其中αI是第I個位置的注意力權重。\r\n\r\n**注意力穩定性**：\r\n評估相似輸入的注意力模式嘅一致性。\r\n\r\n穩定性指標：\r\nStability = 1 - || A₁ - A₂|| ₂ / 2\r\n\r\n其中A2同A2係相似輸入嘅注意力權重矩阵。\r\n\r\n###計算效率分析\r\n\r\n**時間複雜度**：\r\n分析不同注意力機制嘅計算複雜度同實際運行時間。\r\n\r\n複雜度比較：\r\n-標準注意力：O （n²d ）\r\n-稀疏注意力：O（n·k·d），k<<n\r\n-線性注意力：O（n·d2）\r\n\r\n**內存使用**：\r\n評估注意力機制對GPU內存嘅需求。\r\n\r\n內存分析：\r\n-注意力權重矩阵：O （n²）\r\n-中間計算結果：O （n·d）\r\n-梯度存儲：O （n²d ）\r\n\r\n**能耗分析**：\r\n喺移動設備上評估注意力機制嘅能耗影響。\r\n\r\n能耗因素：\r\n-計算強度：浮點運算次數\r\n-內存訪問：數據傳輸開銷\r\n-硬件利用率：計算資源嘅有效利用\r\n\r\n##實際應用案例\r\n\r\n###手寫文字識別\r\n\r\n在手寫文字識別中，注意力機制幫助模型關注當前識別緊嘅字符，忽略其他干擾信息。\r\n\r\n**應用效果**：\r\n-識別準確率提升15-20%\r\n-對複雜背景嘅鲁棒性增強\r\n-處理不規則排列嘅文字能力提升\r\n\r\n**技術實現**：\r\n1. **空間注意力**：關注字符所在的空間區域\r\n2. **時序注意力 **：利用字符間的時序關係\r\n3. **多尺度注意力 **：處理不同大小的字符\r\n\r\n**案例分析**：\r\n在手寫英文單詞識別任務中，注意力機制能夠：\r\n-準確定位每個字符嘅位置\r\n-處理字符間嘅連筆現象\r\n-利用單詞級別嘅語言模型知識\r\n\r\n###場景文字識別\r\n\r\n在自然場景中，文字往往嵌入喺複雜嘅背景中，注意力機制能夠有效地分離文字同背景。\r\n\r\n**技術特點**：\r\n-多尺度注意力處理不同大小嘅文字\r\n-空間注意力定位文字區域\r\n-通道注意力選擇有用嘅特徵\r\n\r\n**挑戰與解決方案 **：\r\n1. **背景干擾 **：使用空間注意力過濾背景噪聲\r\n2. **光照變化 **：通過通道注意力適應不同光照條件\r\n3. **幾何變形 **：結合幾何校正和注意力機制\r\n\r\n**性能提升**：\r\n-在ICDAR數據集上準確率提升10-15%\r\n-對複雜場景嘅適應性顯著增強\r\n-推理速度保持在可接受範圍內\r\n\r\n###文檔分析\r\n\r\n在文檔分析任務中，注意力機制幫助模型理解文檔嘅結構同層次關係。\r\n\r\n**應用場景**：\r\n-表格識別：關注表格嘅行列結構\r\n-版面分析：識別標題、正文、圖等元素\r\n-信息抽取：定位關鍵信息嘅位置\r\n\r\n**技術創新**：\r\n1. **層次化注意力 **：在不同層次上應用注意力\r\n2. **結構化注意力 **：考慮文檔的結構信息\r\n3. **模態多注意力 **：融合文本和視覺信息\r\n\r\n**實際效果**：\r\n-表格識別準確率提升20%以上\r\n-複雜版面嘅處理能力顯著增強\r\n-信息抽取嘅精確度大幅提升\r\n\r\n##未來發展趨勢\r\n\r\n###高效注意力機制\r\n\r\n隨著序列長度嘅增加，注意力機制嘅計算成本成為瓶頸。 未來嘅研究方向包括：\r\n\r\n**算法優化**：\r\n-更高效嘅稀疏注意力模式\r\n-近似計算方法嘅改進\r\n-硬件友好嘅注意力設計\r\n\r\n**架構創新**：\r\n-分層注意力機制\r\n-動態注意力路由\r\n-自適應計算圖\r\n\r\n**理論突破**：\r\n-注意力機制嘅理論分析\r\n-最優注意力模式嘅數學證明\r\n-注意力與其他機制嘅統一理論\r\n\r\n###多模態注意力\r\n\r\n未來嘅OCR系統將更多地融合多種模態嘅信息：\r\n\r\n**視覺-語言融合**：\r\n-圖像同文本嘅聯合注意力\r\n-跨模態嘅信息傳遞\r\n-統一嘅零模態表示\r\n\r\n**時序信息融合**：\r\n-視頻OCR中嘅時序注意力\r\n-動態場景嘅文字跟蹤\r\n-時空聯合建模\r\n\r\n**多傳感器融合**：\r\n-結合深度信息嘅3D注意力\r\n-多光譜圖像嘅注意力機制\r\n-傳感器數據嘅聯合建模\r\n\r\n###可解釋性增強\r\n\r\n提高注意力機制嘅可解釋性係重要嘅研究方向：\r\n\r\n**注意力解釋**：\r\n-更直觀嘅可視化方法\r\n-注意力模式嘅語義解釋\r\n-錯誤分析和調試工具\r\n\r\n**因果推理**：\r\n-注意力嘅因果關係分析\r\n-反事實推理方法\r\n-魯棒性驗證技術\r\n\r\n**人機交互**：\r\n-交互式注意力調整\r\n-用戶反饋嘅融入\r\n-個性化注意力模式\r\n\r\n##總結\r\n\r\n注意力機制作為深度學習嘅重要組成部分，OCR領域發揮住越嚟越重要嘅作用緊。 由基礎嘅序列到序列注意力到複雜嘅多頭自注意力，由空間注意力到多尺度注意力，呢啲技術嘅發展極大地提升咗OCR系統嘅性能。\r\n\r\n**關鍵要點**：\r\n-注意力機制模擬咗人類嘅選擇性注意能力，解決了信息瓶頸問題\r\n-數學原理基於加權求和，透過學習注意力權重實現信息選擇\r\n-多頭注意力和自注意力係現代注意力機制嘅核心技術\r\n-喺OCR中嘅應用包括序列建模、視覺關注、多尺度處理等\r\n-未來發展方向包括效率優化、多模態融合、可解釋性增強等\r\n\r\n**實踐建議**：\r\n-根據具體任務選擇合適嘅注意力機制\r\n-注意計算效率和性能嘅平衡\r\n-充分利用注意力嘅可解釋性進行模型調試\r\n-關注最新嘅研究進展同技術發展\r\n\r\n隨著技術嘅不斷發展，注意力機制將繼續演進，為OCR同其他人工智能應用提供更強大嘅工具。 理解和掌握注意力機制嘅原理同應用，對於從事OCR研究和開發嘅技術人員來說至關重要。</div>\n                        \n                        <div class=\"article-tags\">\n                            <h6>標籤：</h6>\n                            <div class=\"tags-list\">\n                                \n                                <span class=\"tag\">注意力機制</span>\n                                \n                                <span class=\"tag\">多頭注意力</span>\n                                \n                                <span class=\"tag\">自注意力</span>\n                                \n                                <span class=\"tag\">位置編碼</span>\n                                \n                                <span class=\"tag\">交叉注意力</span>\n                                \n                                <span class=\"tag\">稀疏注意力</span>\n                                \n                                <span class=\"tag\">OCR</span>\n                                \n                                <span class=\"tag\">Transformer</span>\n                                \n                            </div>\n                        </div>\n                        \n                    </article>\n                    \n                    <footer class=\"article-footer\" id=\"articleFooter\">\n                        <div class=\"article-share\">\n                            <h6 style=\"color: #1a1c1f; margin-bottom: 1rem;\">分享和操作：</h6>\n                            <div class=\"share-buttons\">\n                                <a href=\"javascript:void(0)\" onclick=\"shareToWeibo()\" class=\"btn btn-weibo\">📱 微博分享</a>\n                                <a href=\"javascript:void(0)\" onclick=\"copyUrl()\" class=\"btn btn-copy\">🔗 複製連結</a>\n                                <button type=\"button\" class=\"btn btn-print\" onclick=\"printArticle()\">🖨️ 打印文章</button>\n                            </div>\n                        </div>\n                    </footer>\n                    \n                </div>\n                <div class=\"col-lg-4 col-xl-3 col-md-12\">\n                    <div class=\"sidebar\">\n                        <div class=\"sidebar-card toc-card\" id=\"tocCard\" style=\"display: none;\"><h5>文章目錄</h5><div id=\"tableOfContents\" class=\"toc-list\"></div></div>\n                        <div class=\"sidebar-card\">\n                            <h5>舉薦閱讀</h5>\n                            <div id=\"recommendedArticles\">\n                                \n                                <div class=\"recommended-item\"><h6><a href=\"Article.aspx?slug=document-intelligence-future-outlook\">【文檔智能處理系列·20】文檔智能處理技術發展展望</a></h6><p class=\"text-muted small\">2025-08-19 • 1203 次閱讀</p></div>\n                                \n                                <div class=\"recommended-item\"><h6><a href=\"Article.aspx?slug=document-intelligence-quality-assurance\">【文檔智能處理系列·19】文檔智能處理質量保證體系</a></h6><p class=\"text-muted small\">2025-08-19 • 1200 次閱讀</p></div>\n                                \n                                <div class=\"recommended-item\"><h6><a href=\"Article.aspx?slug=large-scale-document-processing-optimization\">【文檔智能處理系列·18】大規模文檔處理性能優化</a></h6><p class=\"text-muted small\">2025-08-19 • 1201 次閱讀</p></div>\n                                \n                            </div>\n                        </div>\n                    </div>\n</div>\n</div>\n</div>\n</div>\n<script>\nvar articleId,viewCountKey,now=new Date().getTime();\ndocument.addEventListener('DOMContentLoaded',function(){var articleDetail=document.getElementById('articleDetail');if(articleDetail&&articleDetail.querySelector('.article-content')){formatContent();initReadingProgress();optimizeImages();updateViewCount();}});\nfunction updateViewCount(){articleId=getArticleId();if(!articleId)return;viewCountKey='article_view_'+articleId;var lastViewTime=localStorage.getItem(viewCountKey);if(lastViewTime&&(now-parseInt(lastViewTime))<3600000)return;var xhr=new XMLHttpRequest();xhr.open('POST','/Article.ashx',true);xhr.setRequestHeader('Content-Type','application/x-www-form-urlencoded');xhr.onreadystatechange=function(){if(xhr.readyState===4&&xhr.status===200){try{var response=JSON.parse(xhr.responseText);if(response.success){updateViewCountDisplay(response.viewCount);localStorage.setItem(viewCountKey,now.toString());}}catch(e){}}};xhr.send('action=updateviewcount&articleId='+encodeURIComponent(articleId));}\nfunction getArticleId(){var articleDetail=document.getElementById('articleDetail');if(articleDetail){var id=articleDetail.getAttribute('data-article-id');if(id)return id;}var urlParams=new URLSearchParams(window.location.search);return urlParams.get('id');}\nfunction updateViewCountDisplay(newCount){document.querySelectorAll('.view-count').forEach(function(element){element.textContent=newCount;});}\nfunction decodeHtmlEntities(text){var textArea=document.createElement('textarea');textArea.innerHTML=text;return textArea.value;}\nfunction formatContent(){var contentDiv=document.getElementById('articleContent');if(!contentDiv)return;var content=contentDiv.textContent||contentDiv.innerText;if(!content)return;content=decodeHtmlEntities(content);content=formatMarkdownContent(content);content=formatIndentation(content);content=formatLists(content);content=formatParagraphs(content);content=formatLinks(content);contentDiv.innerHTML=content;initTableOfContents();}\nfunction formatIndentation(content){var lines=content.split('\\n'),result=[];for(var i=0;i<lines.length;i++){var line=lines[i];if(line.trim().match(/^<[^>]+>/)){result.push(line);continue;}line=line.replace(/^\\t+/g,function(match){return '    '.repeat(match.length);});if(line.match(/^  +[^\\s]/)){var indentMatch=line.match(/^( +)(.*)$/);if(indentMatch){var indentLevel=Math.floor(indentMatch[1].length/2);line='<span class=\"text-indent-'+indentLevel+'\">'+indentMatch[2]+'</span>';}}result.push(line);}return result.join('\\n');}\nfunction formatMarkdownContent(content){content=content.replace(/^\\s*##### (.+)$/gm,'<h5>$1</h5>').replace(/^\\s*#### (.+)$/gm,'<h4>$1</h4>').replace(/^\\s*### (.+)$/gm,'<h3>$1</h3>').replace(/^\\s*## (.+)$/gm,'<h2>$1</h2>').replace(/^\\s*# (.+)$/gm,'<h1>$1</h1>').replace(/^> (.+)$/gm,'<blockquote><p>$1</p></blockquote>').replace(/\\*\\*([^*]+?)\\*\\*/g,'<strong>$1</strong>').replace(/\\*([^*\\n]+?)\\*/g,'<em>$1</em>').replace(/`([^`]+?)`/g,'<code class=\"inline-code\">$1</code>').replace(/^(\\*\\*[^*]+?\\*\\*)：/gm,'<div class=\"label-item\"><span class=\"label\">$1</span></div>').replace(/^([^：\\n]+?)：$/gm,'<div class=\"section-label\">$1：</div>').replace(/^(\\*\\*[^*]+?\\*\\*)：(.+)$/gm,'<div class=\"definition-item\"><span class=\"definition-label\">$1：</span><span class=\"definition-content\">$2</span></div>'）.replace（/^（註|備註|說明）:(.+）$/gm，'<div class=\"note-item\"><span class=\"note-label\">$1：</span><span class=\"note-content\">$2</span></div>');content=formatLists(content);content=formatMathSymbols(content);content=formatParagraphs(content);return content;}\nfunction formatLists(content){var lines=content.split('\\n'),result=[],inOrderedList=false,inUnorderedList=false;for(var i=0;i<lines.length;i++){var line=lines[i],trimmed=line.trim();var orderedMatch=line.match(/^(\\d+)\\.\\s*(.+)$/);var unorderedMatch=line.match(/^[-*+]\\s*(.+)$/);if(orderedMatch){if(!inOrderedList){if(inUnorderedList){result.push('</ul>');inUnorderedList=false;}result.push('<ol class=\"formatted-list ordered\">');inOrderedList=true;}var listContent=orderedMatch[2].replace(/^\\*\\*([^*]+?)\\*\\*：(.+)$/,'<strong class=\"list-title\">$1：</strong><span class=\"list-content\">$2</span>');result.push('<li>'+listContent+'</li>');}else if(unorderedMatch){if(!inUnorderedList){if(inOrderedList){result.push('</ol>');inOrderedList=false;}result.push('<ul class=\"formatted-list unordered\">');inUnorderedList=true;}var listContent=unorderedMatch[1].replace(/^\\*\\*([^*]+?)\\*\\*：(.+)$/,'<strong class=\"list-title\">$1：</strong><span class=\"list-content\">$2</span>');result.push('<li>'+listContent+'</li>');}else{if(trimmed===''){if(i+1<lines.length){var nextLine=lines[i+1].trim();if(!nextLine.match(/^\\d+\\.\\s/)&&!nextLine.match(/^[-*+]\\s/)){if(inOrderedList){result.push('</ol>');inOrderedList=false;}if(inUnorderedList){result.push('</ul>');inUnorderedList=false;}}}result.push(line);}else{if(inOrderedList){result.push('</ol>');inOrderedList=false;}if(inUnorderedList){result.push('</ul>');inUnorderedList=false;}result.push(line);}}}if(inOrderedList)result.push('</ol>');if(inUnorderedList)result.push('</ul>');return result.join('\\n');}\nfunction formatMathSymbols(content){var mathSymbols={'α':'&alpha;','β':'&beta;','γ':'&gamma;','δ':'&delta;','ε':'&epsilon;','ζ':'&zeta;','η':'&eta;','θ':'&theta;','λ':'&lambda;','μ':'&mu;','π':'&pi;','σ':'&sigma;','∑':'&sum;','∏':'&prod;','∫':'&int;','∂':'&part;','∇':'&nabla;','∞':'&infin;','≤':'&le;','≥':'&ge;','≠':'&ne;','≈':'&asymp;','±':'&plusmn;','×':'&times;','÷':'&divide;','→':'&rarr;','←':'&larr;','↑':'&uarr;','↓':'&darr;','⇒':'&rArr;','⇐':'&lArr;'};var subMap={'₀':'0','₁':'1','₂':'2','₃':'3','₄':'4','₅':'5','₆':'6','₇':'7','₈':'8','₉':'9','ₖ':'k','ₜ':'t','ₛ':'s','₊':'+','₋':'-'};var supMap={'⁰':'0','¹':'1','²':'2','³':'3','⁴':'4','⁵':'5','⁶':'6','⁷':'7','⁸':'8','⁹':'9','ᵏ':'k','ᵗ':'t','ˢ':'s','⁺':'+','⁻':'-'};for(var symbol in mathSymbols){content=content.replace(new RegExp(escapeRegExp(symbol),'g'),mathSymbols[symbol]);}content=content.replace(/([a-zA-Z])₍([0-9]+)₎/g,'$1<sub>$2</sub>').replace(/([a-zA-Z])⁽([0-9]+)⁾/g,'$1<sup>$2</sup>').replace(/([a-zA-Z])([₀₁₂₃₄₅₆₇₈₉ₖₜₛ₊₋]+)/g,function(match,base,subscript){var converted=subscript.replace(/[₀₁₂₃₄₅₆₇₈₉ₖₜₛ₊₋]/g,function(char){return subMap[char]||char;});return base+'<sub>'+converted+'</sub>';}).replace(/([a-zA-Z])([⁰¹²³⁴⁵⁶⁷⁸⁹ᵏᵗˢ⁺⁻]+)/g,function(match,base,superscript){var converted=superscript.replace(/[⁰¹²³⁴⁵⁶⁷⁸⁹ᵏᵗˢ⁺⁻]/g,function(char){return supMap[char]||char;});return base+'<sup>'+converted+'</sup>';}).replace(/\\b([A-Za-z])_\\{([^}]+)\\}/g,'$1<sub>$2</sub>').replace(/\\b([A-Za-z])\\^\\{([^}]+)\\}/g,'$1<sup>$2</sup>');return content;}\nfunction formatParagraphs(content){var lines=content.split('\\n'),result=[],inParagraph=false,currentParagraph=[];for(var i=0;i<lines.length;i++){var originalLine=lines[i],line=originalLine.trim();if(line.match(/^<[^>]+>/)||line===''){if(inParagraph&&currentParagraph.length>0){var paragraphContent=currentParagraph.join(' ').trim();if(paragraphContent)result.push('<p class=\"content-paragraph indent-paragraph\">'+paragraphContent+'</p>');currentParagraph=[];inParagraph=false;}result.push(originalLine);continue;}if(line.match(/^#{1,6}\\s/)||line.match(/^\\d+\\.\\s/)||line.match(/^[-*+]\\s/)||line.match(/^>/)){if(inParagraph&&currentParagraph.length>0){var paragraphContent=currentParagraph.join(' ').trim();if(paragraphContent)result.push('<p class=\"content-paragraph indent-paragraph\">'+paragraphContent+'</p>');currentParagraph=[];inParagraph=false;}result.push(originalLine);continue;}if(line){currentParagraph.push(line);inParagraph=true;}}if(inParagraph&&currentParagraph.length>0){var paragraphContent=currentParagraph.join(' ').trim();if(paragraphContent)result.push('<p class=\"content-paragraph indent-paragraph\">'+paragraphContent+'</p>');}return result.join('\\n');}\nfunction escapeRegExp(string){return string.replace(/[.*+?^${}()|[\\]\\\\]/g,'\\\\$&');}\nfunction formatLinks(content){return content.replace(/<a\\s+href=\"(https?:\\/\\/[^\"]+)\"([^>]*)>/gi,function(match,url,attrs){return !attrs.includes('target=')?'<a href=\"'+url+'\"'+attrs+' target=\"_blank\" rel=\"noopener noreferrer\">':match;});}\nfunction escapeHtml(text){var div=document.createElement('div');div.textContent=text;return div.innerHTML;}\nfunction addTocClickEvents(){document.querySelectorAll('#tableOfContents a').forEach(function(link){link.addEventListener('click',function(e){e.preventDefault();var targetElement=document.getElementById(this.getAttribute('data-target'));if(targetElement){targetElement.scrollIntoView({behavior:'smooth',block:'start'});updateActiveTocItem(this);}});});}\nfunction addScrollHighlight(headings){var throttleTimer=null;function updateActiveHeading(){var windowHeight=window.innerHeight,activeHeading=null;for(var i=headings.length-1;i>=0;i--){var rect=headings[i].getBoundingClientRect();if(rect.top<=windowHeight*0.3){activeHeading=headings[i];break;}}if(activeHeading){var activeLink=document.querySelector('#tableOfContents a[data-target=\"'+activeHeading.id+'\"]');if(activeLink)updateActiveTocItem(activeLink);}}window.addEventListener('scroll',function(){if(throttleTimer)clearTimeout(throttleTimer);throttleTimer=setTimeout(updateActiveHeading,100);});updateActiveHeading();}\nfunction updateActiveTocItem(activeLink){document.querySelectorAll('#tableOfContents a').forEach(function(link){link.classList.remove('active');});if(activeLink){activeLink.classList.add('active');scrollTocToActiveItem(activeLink);}}\nfunction scrollTocToActiveItem(activeLink){var tocCard=document.getElementById('tocCard');if(!activeLink||!tocCard)return;var cardRect=tocCard.getBoundingClientRect();var activeRect=activeLink.getBoundingClientRect();var relativeTop=activeRect.top-cardRect.top;var relativeBottom=activeRect.bottom-cardRect.bottom;var cardStyle=window.getComputedStyle(tocCard);var paddingTop=parseFloat(cardStyle.paddingTop)||0;var titleHeight=tocCard.querySelector('h5')?tocCard.querySelector('h5').offsetHeight:0;var contentTop=paddingTop+titleHeight;if(relativeTop<contentTop){tocCard.scrollTop+=(relativeTop-contentTop-10);}else if(relativeBottom>0){tocCard.scrollTop+=(relativeBottom+10);}}\nfunction optimizeImages(){document.querySelectorAll('.article-content img').forEach(function(img){img.loading='lazy';img.onerror=function(){this.style.display='none';};if(!img.alt)img.alt='文章配圖';});}\nfunction initReadingProgress(){var progressBar=document.getElementById('readingProgress');function updateProgress(){var scrollTop=window.pageYOffset||document.documentElement.scrollTop;var docHeight=document.documentElement.scrollHeight-window.innerHeight;progressBar.style.width=Math.min((scrollTop/docHeight)*100,100)+'%';}window.addEventListener('scroll',updateProgress);updateProgress();}\nfunction initTableOfContents(){var content=document.querySelector('.article-content');if(!content)return;var headings=content.querySelectorAll('h1, h2, h3, h4, h5, h6');if(headings.length<=1)return;var tocCard=document.getElementById('tocCard');var tocList=document.getElementById('tableOfContents');var html='',counters=[0,0,0,0,0,0];headings.forEach(function(heading,index){var id='heading-'+index;heading.id=id;var level=parseInt(heading.tagName.substring(1));var indent=(level-1)*15;counters[level-1]++;for(var i=level;i<counters.length;i++)counters[i]=0;var numberText='';for(var i=0;i<level;i++){if(counters[i]>0)numberText+=(numberText?'.':'')+counters[i];}numberText+='.';html+='<a href=\"#'+id+'\" class=\"toc-item toc-level-'+level+'\" style=\"padding-left: '+indent+'px;\" data-target=\"'+id+'\"><span class=\"toc-number\">'+numberText+'</span> <span class=\"toc-text\">'+heading.textContent+'</span></a>';});tocList.innerHTML=html;tocCard.style.display='block';addTocClickEvents();addScrollHighlight(headings);tocList.addEventListener('click',function(e){if(e.target.tagName==='A'){e.preventDefault();var targetElement=document.getElementById(e.target.getAttribute('data-target'));if(targetElement){targetElement.scrollIntoView({behavior:'smooth',block:'start'});targetElement.style.transition='all 0.3s ease';targetElement.style.backgroundColor='#e3f2fd';targetElement.style.padding='10px';targetElement.style.borderRadius='6px';setTimeout(function(){targetElement.style.backgroundColor='';targetElement.style.padding='';targetElement.style.borderRadius='';},2000);}}});}\nfunction shareToWeibo(){window.open('https://service.weibo.com/share/share.php?url='+encodeURIComponent(window.location.href)+'&title='+encodeURIComponent(document.title),'_blank');}\nfunction copyUrl(){navigator.clipboard.writeText(window.location.href).then(function(){alert('連結已複製到剪貼板');}).catch(function(){var textArea=document.createElement('textarea');textArea.value=window.location.href;textArea.style.cssText='position:fixed;left:-999999px;top:-999999px';document.body.appendChild(textArea);textArea.focus();textArea.select();try{alert(document.execCommand('copy')?'連結已複製到剪貼板':'複製失敗，請手動複製連結');}catch(err){alert('複製失敗，請手動複製連結');}document.body.removeChild(textArea);});}\nfunction printArticle(){var printWindow=window.open('','_blank');var articleContent=document.querySelector('.article-detail').innerHTML;var articleTitle=document.querySelector('.article-title').textContent;printWindow.document.write('<!DOCTYPE html><html lang=\"yue\"><title>'+articleTitle+'</title><style>body{font-family:-apple-system,BlinkMacSystemFont,\"Segoe UI\",Roboto,\"Helvetica Neue\",Arial,sans-serif;line-height:1.6;color:#333;max-width:800px;margin:0 auto;padding:20px}h1,h2,h3,h4,h5,h6{color:#1a1c1f;margin-top:1.5em;margin-bottom:0.5em}h1{font-size:2em;border-bottom:2px solid #e9ecef;padding-bottom:0.3em}h2{font-size:1.5em;border-bottom:1px solid #e9ecef;padding-bottom:0.3em}h3{font-size:1.25em}p{margin-bottom:1em}pre{background:#f8f9fa;border:1px solid #e9ecef;border-radius:4px;padding:1em;overflow-x:auto;font-family:Consolas,Monaco,\"Courier New\",monospace}code{background:#f1f3f4;padding:2px 4px;border-radius:3px;font-family:Consolas,Monaco,\"Courier New\",monospace}blockquote{border-left:4px solid #1764ff;background:#f8f9fa;padding:1em;margin:1em 0;border-radius:0 4px 4px 0}img{max-width:100%;height:auto}table{width:100%;border-collapse:collapse;margin:1em 0}th,td{border:1px solid #ddd;padding:8px;text-align:left}th{background-color:#f2f2f2}.article-meta,.article-footer,.share-buttons{display:none}</style><body>'+articleContent+'</body></html>');printWindow.document.close();printWindow.focus();setTimeout(function(){printWindow.print();printWindow.close();},250);}\nfunction improvedStickyPosition(){var sidebarCards=document.querySelectorAll('.sidebar-card');var originalPositions=[],isFixed=[];sidebarCards.forEach(function(card,index){var rect=card.getBoundingClientRect();originalPositions[index]=rect.top+window.pageYOffset;isFixed[index]=false;var placeholder=document.createElement('div');placeholder.style.cssText='height:'+card.offsetHeight+'px;display:none';placeholder.className='sticky-placeholder';card.parentNode.insertBefore(placeholder,card.nextSibling);});function updateStickyPosition(){var scrollTop=window.pageYOffset||document.documentElement.scrollTop;sidebarCards.forEach(function(card,index){var originalTop=originalPositions[index];var stickyTop=index>0?(document.querySelector('.toc-card').style.position==='fixed'?60+document.querySelector('.toc-card').offsetHeight+8:80):60;var placeholder=card.nextSibling;if(scrollTop>originalTop-stickyTop){if(!isFixed[index]){var colContainer=card.closest('[class*=\"col-\"]');var colRect=colContainer.getBoundingClientRect();var colStyle=window.getComputedStyle(colContainer);var paddingLeft=parseFloat(colStyle.paddingLeft)||0;var paddingRight=parseFloat(colStyle.paddingRight)||0;card.style.cssText='position:fixed;top:'+stickyTop+'px;left:'+(colRect.left+paddingLeft)+'px;width:'+(colRect.width-paddingLeft-paddingRight)+'px;z-index:9999;box-sizing:border-box';card.classList.add('fixed');placeholder.style.display='block';isFixed[index]=true;}}else if(isFixed[index]){card.style.cssText='';card.classList.remove('fixed');placeholder.style.display='none';isFixed[index]=false;}});}window.addEventListener('scroll',updateStickyPosition);window.addEventListener('resize',function(){setTimeout(function(){sidebarCards.forEach(function(card,index){if(isFixed[index]){var colContainer=card.closest('[class*=\"col-\"]');var colRect=colContainer.getBoundingClientRect();var colStyle=window.getComputedStyle(colContainer);var paddingLeft=parseFloat(colStyle.paddingLeft)||0;var paddingRight=parseFloat(colStyle.paddingRight)||0;card.style.left=(colRect.left+paddingLeft)+'px';card.style.width=(colRect.width-paddingLeft-paddingRight)+'px';}else{originalPositions[index]=card.getBoundingClientRect().top+window.pageYOffset;}});},100);});updateStickyPosition();}\ndocument.addEventListener('DOMContentLoaded',function(){setTimeout(improvedStickyPosition,1000);});\n    </script>\n\r\n            </div>\r\n        </form>\r\n\r\n        <div class=\"fixed-icon\">\r\n            <div style=\"width: 120px; position: relative; left: 1rem; top: -12px;\">\r\n            </div>\r\n            <a id=\"webchat\" class=\"wx-con disa\" href=\"http://wpa.qq.com/msgrd?v=3&uin=365833440&site=qq&menu=yes\" target=\"_blank\">\r\n                <img class=\"wechat-ico\" src=\"site/image/icon/qq.svg\" alt=\"OCR助手QQ在線客服\" />\r\n                <div class=\"wx-text\">QQ客服（365833440）</div>\r\n                <label class=\"badge badge-danger\" style=\"background: rgb(255, 34, 122); display: inline-block;\">1</label>\r\n            </a>\r\n\r\n            <a class=\"wx-con\" href=\"https://qm.qq.com/cgi-bin/qm/qr?k=bohNy9GMdgD5bsYrB-fcIjy-s6Ot3zOg&jump_from=webapi&qr=1\" target=\"_blank\">\r\n                <img class=\"wechat-ico\" src=\"site/image/icon/qq.svg\" alt=\"OCR助手QQ用戶交流群\" />\r\n                <div class=\"wx-text\">QQ群（100029010）</div>\r\n                <label class=\"badge badge-danger\" style=\"background: rgb(255, 34, 122); display: inline-block;\">2</label>\r\n            </a>\r\n            <a href=\"mailto:<EMAIL>\" class=\"wx-con\">\r\n                <img class=\"wechat-ico\" src=\"site/image/icon/im.svg\" alt=\"OCR助手郵件聯繫客服\" />\r\n                <div class=\"wx-text\" style=\"width: 240px; text-align: left; left: -15.3rem; padding: 10px 20px;\">\r\n                    <div style=\"font-size: 15px\">郵箱：<EMAIL></div>\r\n                    <hr style=\"background: #c1c1c1; margin: 10px 10px 6px 0;\" />\r\n                    <span style=\"font-size: 13px; color: #c1c1c1\">感謝您的意見和建議！</span>\r\n                </div>\r\n                <label class=\"badge badge-danger\" style=\"background: rgb(255, 34, 122); display: inline-block;\">3</label>\r\n            </a>\r\n        </div>\r\n\r\n        <style>\r\n            .content {\r\n                width: 100%;\r\n                padding: 0 !important;\r\n                margin: 0 auto\r\n            }\r\n\r\n            h3.h6 {\r\n                font-size: 1rem !important;\r\n                font-weight: bold !important;\r\n                line-height: 1.2 !important;\r\n                margin-bottom: .5rem;\r\n                font-family: inherit\r\n            }\r\n        </style>\r\n        <footer class=\"page-footer\">\r\n            <div class=\"container\">\r\n                <div class=\"row foot-cont pt-4 no-gutters\" style=\"border-top: 1px solid #e1e6ed; max-width: 100%\">\r\n                    <div class=\"col-lg-12 hu-an p-0 mb-3 mt-3 text-l color-gray\" style=\"text-align: center;\">\r\n                        OCR文字識別助手&nbsp;©️ 2025 ALL RIGHTS RESERVED. 保留所有權利&nbsp;&nbsp;&nbsp;|&nbsp;&nbsp;&nbsp;\r\n                        <a href=\"PrivacyPolicy.aspx\" style=\"text-decoration: underline;\">私隱協議</a>&nbsp;&nbsp;&nbsp;|&nbsp;&nbsp;&nbsp;\r\n                        <a href=\"UserAgreement.aspx\" style=\"text-decoration: underline;\">用戶協議</a>&nbsp;&nbsp;&nbsp;|&nbsp;&nbsp;&nbsp;\r\n                        <a href=\"Status.aspx\" style=\"text-decoration: underline;\">服務狀態</a>&nbsp;&nbsp;&nbsp;|&nbsp;&nbsp;&nbsp;\r\n                        <a class=\"mb-2 color-gray\" href=\"https://beian.miit.gov.cn/\" target=\"_blank\" style=\"text-decoration: underline;\">鄂ICP備2021012692號</a>\r\n                    </div>\r\n                </div>\r\n            </div>\r\n        </footer>\r\n        <style>\r\n            #translate > .translateSelectLanguage {\r\n                right: 2rem;\r\n                font-size: 1rem;\r\n                width: 150px;\r\n                padding: .3rem;\r\n                margin-right: 20px;\r\n                border: 1px solid #C9C9C9;\r\n                background: #fff;\r\n                color: #555\r\n            }\r\n        </style>\r\n    </div>\r\n    <script>\r\n        document.addEventListener('DOMContentLoaded', function () {\r\n            var setScroll = function (active) {\r\n                forEach(qa('.v4_header_pc,.v4_header_mob'), function (e) {\r\n                    if (active) addClass(e, 'scrollActive'); else removeClass(e, 'scrollActive');\r\n                });\r\n            };\r\n            if (getScrollY() > 60) setScroll(1);\r\n            window.addEventListener('scroll', function () { setScroll(getScrollY() > 60); });\r\n\r\n            forEach(qa('.v4_header_pc'), function (h) {\r\n                h.addEventListener('mouseenter', function () { addClass(h, 'active'); });\r\n                h.addEventListener('mouseleave', function () { removeClass(h, 'active'); });\r\n            });\r\n\r\n            var menuTimer;\r\n            var hideMenu = function (menuId) {\r\n                var m = document.getElementById(menuId);\r\n                if (m) {\r\n                    m.style.cssText = 'visibility:hidden;height:0;display:none';\r\n                    var n = q('.nav-drown-con', m);\r\n                    if (n) { n.style.cssText = 'height:0;opacity:0;transform:translate(0,-100%)'; }\r\n                }\r\n            };\r\n\r\n            forEach(qa('ul.top-nav>li'), function (i) {\r\n                i.addEventListener('mouseenter', function () {\r\n                    clearTimeout(menuTimer);\r\n                    var l = this.querySelector('a'), t = this.querySelector('.triangle'), s = l ? l.getAttribute('data-sub') : null;\r\n                    if (l) addClass(l, 'active'); if (t) addClass(t, 'active');\r\n                    if (s) {\r\n                        var m = document.getElementById(s);\r\n                        if (m) {\r\n                            m.style.cssText = 'display:block;visibility:visible';\r\n                            var siblings = m.parentNode ? toArray(m.parentNode.children) : [];\r\n                            forEach(siblings, function (c) { if (c !== m) c.style.display = 'none'; });\r\n                            var n = q('.nav-drown-con', m);\r\n                            if (n) { n.style.cssText = 'height:100%;opacity:1;transform:translate(0,0)'; }\r\n\r\n                            m.addEventListener('mouseenter', function () { clearTimeout(menuTimer); });\r\n                            m.addEventListener('mouseleave', function () { menuTimer = setTimeout(function () { hideMenu(s); }, 100); });\r\n                        }\r\n                    }\r\n                });\r\n                i.addEventListener('mouseleave', function () {\r\n                    var l = this.querySelector('a'), t = this.querySelector('.triangle'), s = l ? l.getAttribute('data-sub') : null;\r\n                    if (l) removeClass(l, 'active'); if (t) removeClass(t, 'active');\r\n                    if (s) { menuTimer = setTimeout(function () { hideMenu(s); }, 100); }\r\n                });\r\n            });\r\n\r\n            var r = q('.v4_header_mob .right-menu');\r\n            if (r) r.addEventListener('click', function () {\r\n                var arr = [r, q('.v4_header_mob'), q('#nav_products'), q('.mob-nav-content .sidebar-fix')];\r\n                for (var i = 0; i < arr.length; i++) {\r\n                    var e = arr[i];\r\n                    if (e) {\r\n                        var name = (i === 0 ? 'active' : (i === 1 ? 'active' : (i === 2 ? 'active1' : 'fixedActiveBg')));\r\n                        toggleClass(e, name);\r\n                    }\r\n                }\r\n                var f = q('.mob-nav-content .sidebar-fix');\r\n                if (f) {\r\n                    toggleClass(f, 'show');\r\n                    document.body.style.overflow = hasClass(f, 'show') ? 'hidden' : 'scroll';\r\n                }\r\n            });\r\n\r\n            document.addEventListener('click', function (e) {\r\n                e = e || window.event;\r\n                var target = e.target || e.srcElement;\r\n                var m = closest(target, '.sidebar-fix-left .mob-nav-item');\r\n                if (m) {\r\n                    var p = m.parentNode;\r\n                    if (p) forEach(toArray(p.children), function (c) { removeClass(c, 'active'); });\r\n                    addClass(m, 'active');\r\n                    var d = m.getAttribute('data');\r\n                    if (d) forEach(qa('.sidebar-fix-rigth .sub-nav'), function (n, i) { n.style.display = (i == d ? 'block' : 'none'); });\r\n                }\r\n            });\r\n        });\r\n    </script>\r\n</body>\r\n</html>\r\n\r\n\t            <script type=\"text/javascript\">\r\n                    var _hmt = _hmt || [];\r\n                    (function () {\r\n                        var hm = document.createElement('script');hm.async=1;\r\n                        hm.src = 'https://hm.baidu.com/hm.js?5ee048d7caaf0d439cf16309a1687754';\r\n                        var s = document.getElementsByTagName('script')[0];s.parentNode.insertBefore(hm, s);\r\n                    })();\r\n                </script><div id=\"translate\" class=\"ignore\" style=\"display: none\"></div><script src=\"/static/js/translate.js\"></script>"
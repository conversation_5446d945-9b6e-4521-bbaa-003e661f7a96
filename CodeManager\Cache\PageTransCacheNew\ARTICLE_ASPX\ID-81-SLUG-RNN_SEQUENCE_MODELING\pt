﻿"<!DOCTYPE html>\r\n<html xmlns=\"http://www.w3.org/1999/xhtml\" lang=\"pt\">\r\n<head><link rel=\"canonical\" href=\"https://ocr.oldfish.cn/pt/Article.aspx?id=81&slug=rnn-sequence-modeling\" /><link rel=\"alternate\" href=\"https://ocr.oldfish.cn/Article.aspx?id=81&slug=rnn-sequence-modeling\" hreflang=\"x-default\" />\r\n<link rel=\"alternate\" href=\"https://ocr.oldfish.cn/Article.aspx?id=81&slug=rnn-sequence-modeling\" hreflang=\"zh\" />\r\n<link rel=\"alternate\" href=\"https://ocr.oldfish.cn/Article.aspx?id=81&slug=rnn-sequence-modeling\" hreflang=\"zh\" />\r\n<link rel=\"alternate\" href=\"https://ocr.oldfish.cn/zh-Hant/Article.aspx?id=81&slug=rnn-sequence-modeling\" hreflang=\"zh-tw\" />\r\n<link rel=\"alternate\" href=\"https://ocr.oldfish.cn/yue/Article.aspx?id=81&slug=rnn-sequence-modeling\" hreflang=\"zh-yue\" />\r\n<link rel=\"alternate\" href=\"https://ocr.oldfish.cn/en/Article.aspx?id=81&slug=rnn-sequence-modeling\" hreflang=\"en\" />\r\n<link rel=\"alternate\" href=\"https://ocr.oldfish.cn/es/Article.aspx?id=81&slug=rnn-sequence-modeling\" hreflang=\"es\" />\r\n<link rel=\"alternate\" href=\"https://ocr.oldfish.cn/hi/Article.aspx?id=81&slug=rnn-sequence-modeling\" hreflang=\"hi\" />\r\n<link rel=\"alternate\" href=\"https://ocr.oldfish.cn/ar/Article.aspx?id=81&slug=rnn-sequence-modeling\" hreflang=\"ar\" />\r\n<link rel=\"alternate\" href=\"https://ocr.oldfish.cn/pt/Article.aspx?id=81&slug=rnn-sequence-modeling\" hreflang=\"pt\" />\r\n<link rel=\"alternate\" href=\"https://ocr.oldfish.cn/bn/Article.aspx?id=81&slug=rnn-sequence-modeling\" hreflang=\"bn\" />\r\n<link rel=\"alternate\" href=\"https://ocr.oldfish.cn/ru/Article.aspx?id=81&slug=rnn-sequence-modeling\" hreflang=\"ru\" />\r\n<link rel=\"alternate\" href=\"https://ocr.oldfish.cn/ja/Article.aspx?id=81&slug=rnn-sequence-modeling\" hreflang=\"ja\" />\r\n<link rel=\"alternate\" href=\"https://ocr.oldfish.cn/pa/Article.aspx?id=81&slug=rnn-sequence-modeling\" hreflang=\"pa\" />\r\n<link rel=\"alternate\" href=\"https://ocr.oldfish.cn/de/Article.aspx?id=81&slug=rnn-sequence-modeling\" hreflang=\"de\" />\r\n<link rel=\"alternate\" href=\"https://ocr.oldfish.cn/fr/Article.aspx?id=81&slug=rnn-sequence-modeling\" hreflang=\"fr\" />\r\n<link rel=\"alternate\" href=\"https://ocr.oldfish.cn/mr/Article.aspx?id=81&slug=rnn-sequence-modeling\" hreflang=\"mr\" />\r\n<link rel=\"alternate\" href=\"https://ocr.oldfish.cn/te/Article.aspx?id=81&slug=rnn-sequence-modeling\" hreflang=\"te\" />\r\n<link rel=\"alternate\" href=\"https://ocr.oldfish.cn/vi/Article.aspx?id=81&slug=rnn-sequence-modeling\" hreflang=\"vi\" />\r\n<link rel=\"alternate\" href=\"https://ocr.oldfish.cn/ko/Article.aspx?id=81&slug=rnn-sequence-modeling\" hreflang=\"ko\" />\r\n<link rel=\"alternate\" href=\"https://ocr.oldfish.cn/ta/Article.aspx?id=81&slug=rnn-sequence-modeling\" hreflang=\"ta\" />\r\n<link rel=\"alternate\" href=\"https://ocr.oldfish.cn/ur/Article.aspx?id=81&slug=rnn-sequence-modeling\" hreflang=\"ur\" />\r\n<link rel=\"alternate\" href=\"https://ocr.oldfish.cn/tr/Article.aspx?id=81&slug=rnn-sequence-modeling\" hreflang=\"tr\" />\r\n<link rel=\"alternate\" href=\"https://ocr.oldfish.cn/it/Article.aspx?id=81&slug=rnn-sequence-modeling\" hreflang=\"it\" />\r\n<link rel=\"alternate\" href=\"https://ocr.oldfish.cn/gu/Article.aspx?id=81&slug=rnn-sequence-modeling\" hreflang=\"gu\" />\r\n<link rel=\"alternate\" href=\"https://ocr.oldfish.cn/pl/Article.aspx?id=81&slug=rnn-sequence-modeling\" hreflang=\"pl\" />\r\n<link rel=\"alternate\" href=\"https://ocr.oldfish.cn/uk/Article.aspx?id=81&slug=rnn-sequence-modeling\" hreflang=\"uk\" />\r\n<link rel=\"alternate\" href=\"https://ocr.oldfish.cn/ms/Article.aspx?id=81&slug=rnn-sequence-modeling\" hreflang=\"ms\" />\r\n<link rel=\"alternate\" href=\"https://ocr.oldfish.cn/id/Article.aspx?id=81&slug=rnn-sequence-modeling\" hreflang=\"id\" />\r\n<link rel=\"alternate\" href=\"https://ocr.oldfish.cn/ml/Article.aspx?id=81&slug=rnn-sequence-modeling\" hreflang=\"ml\" />\r\n<link rel=\"alternate\" href=\"https://ocr.oldfish.cn/kn/Article.aspx?id=81&slug=rnn-sequence-modeling\" hreflang=\"kn\" />\r\n<link rel=\"alternate\" href=\"https://ocr.oldfish.cn/fa/Article.aspx?id=81&slug=rnn-sequence-modeling\" hreflang=\"fa\" />\r\n<link rel=\"alternate\" href=\"https://ocr.oldfish.cn/nl/Article.aspx?id=81&slug=rnn-sequence-modeling\" hreflang=\"nl\" />\r\n<link rel=\"alternate\" href=\"https://ocr.oldfish.cn/th/Article.aspx?id=81&slug=rnn-sequence-modeling\" hreflang=\"th\" />\r\n<link rel=\"alternate\" href=\"https://ocr.oldfish.cn/sw/Article.aspx?id=81&slug=rnn-sequence-modeling\" hreflang=\"sw\" />\r\n<link rel=\"alternate\" href=\"https://ocr.oldfish.cn/ro/Article.aspx?id=81&slug=rnn-sequence-modeling\" hreflang=\"ro\" />\r\n<link rel=\"alternate\" href=\"https://ocr.oldfish.cn/my/Article.aspx?id=81&slug=rnn-sequence-modeling\" hreflang=\"my\" />\r\n<link rel=\"alternate\" href=\"https://ocr.oldfish.cn/or/Article.aspx?id=81&slug=rnn-sequence-modeling\" hreflang=\"or\" />\r\n<link rel=\"alternate\" href=\"https://ocr.oldfish.cn/he/Article.aspx?id=81&slug=rnn-sequence-modeling\" hreflang=\"he\" />\r\n<link rel=\"alternate\" href=\"https://ocr.oldfish.cn/am/Article.aspx?id=81&slug=rnn-sequence-modeling\" hreflang=\"am\" />\r\n<link rel=\"alternate\" href=\"https://ocr.oldfish.cn/fil/Article.aspx?id=81&slug=rnn-sequence-modeling\" hreflang=\"fil\" />\r\n<link rel=\"alternate\" href=\"https://ocr.oldfish.cn/sv/Article.aspx?id=81&slug=rnn-sequence-modeling\" hreflang=\"sv\" />\r\n<link rel=\"alternate\" href=\"https://ocr.oldfish.cn/el/Article.aspx?id=81&slug=rnn-sequence-modeling\" hreflang=\"el\" />\r\n<link rel=\"alternate\" href=\"https://ocr.oldfish.cn/cs/Article.aspx?id=81&slug=rnn-sequence-modeling\" hreflang=\"cs\" />\r\n<link rel=\"alternate\" href=\"https://ocr.oldfish.cn/hu/Article.aspx?id=81&slug=rnn-sequence-modeling\" hreflang=\"hu\" />\r\n<link rel=\"alternate\" href=\"https://ocr.oldfish.cn/be/Article.aspx?id=81&slug=rnn-sequence-modeling\" hreflang=\"be\" />\r\n<link rel=\"alternate\" href=\"https://ocr.oldfish.cn/si/Article.aspx?id=81&slug=rnn-sequence-modeling\" hreflang=\"si\" />\r\n<link rel=\"alternate\" href=\"https://ocr.oldfish.cn/ne/Article.aspx?id=81&slug=rnn-sequence-modeling\" hreflang=\"ne\" />\r\n<link rel=\"alternate\" href=\"https://ocr.oldfish.cn/km/Article.aspx?id=81&slug=rnn-sequence-modeling\" hreflang=\"km\" />\r\n<link rel=\"alternate\" href=\"https://ocr.oldfish.cn/sk/Article.aspx?id=81&slug=rnn-sequence-modeling\" hreflang=\"sk\" />\r\n<link rel=\"alternate\" href=\"https://ocr.oldfish.cn/bg/Article.aspx?id=81&slug=rnn-sequence-modeling\" hreflang=\"bg\" />\r\n<link rel=\"alternate\" href=\"https://ocr.oldfish.cn/fr-ca/Article.aspx?id=81&slug=rnn-sequence-modeling\" hreflang=\"fr-ca\" />\r\n<link rel=\"alternate\" href=\"https://ocr.oldfish.cn/ha/Article.aspx?id=81&slug=rnn-sequence-modeling\" hreflang=\"ha\" />\r\n<link rel=\"alternate\" href=\"https://ocr.oldfish.cn/yo/Article.aspx?id=81&slug=rnn-sequence-modeling\" hreflang=\"yo\" />\r\n<link rel=\"alternate\" href=\"https://ocr.oldfish.cn/ig/Article.aspx?id=81&slug=rnn-sequence-modeling\" hreflang=\"ig\" />\r\n<link rel=\"alternate\" href=\"https://ocr.oldfish.cn/ku/Article.aspx?id=81&slug=rnn-sequence-modeling\" hreflang=\"ku\" />\r\n<link rel=\"alternate\" href=\"https://ocr.oldfish.cn/rw/Article.aspx?id=81&slug=rnn-sequence-modeling\" hreflang=\"rw\" />\r\n<link rel=\"alternate\" href=\"https://ocr.oldfish.cn/ca/Article.aspx?id=81&slug=rnn-sequence-modeling\" hreflang=\"ca\" />\r\n<link rel=\"alternate\" href=\"https://ocr.oldfish.cn/da/Article.aspx?id=81&slug=rnn-sequence-modeling\" hreflang=\"da\" />\r\n<link rel=\"alternate\" href=\"https://ocr.oldfish.cn/fi/Article.aspx?id=81&slug=rnn-sequence-modeling\" hreflang=\"fi\" />\r\n<link rel=\"alternate\" href=\"https://ocr.oldfish.cn/nb/Article.aspx?id=81&slug=rnn-sequence-modeling\" hreflang=\"nb\" />\r\n<link rel=\"alternate\" href=\"https://ocr.oldfish.cn/hr/Article.aspx?id=81&slug=rnn-sequence-modeling\" hreflang=\"hr\" />\r\n<link rel=\"alternate\" href=\"https://ocr.oldfish.cn/sr-Cyrl/Article.aspx?id=81&slug=rnn-sequence-modeling\" hreflang=\"sr-cyrl\" />\r\n<link rel=\"alternate\" href=\"https://ocr.oldfish.cn/sr-Latn/Article.aspx?id=81&slug=rnn-sequence-modeling\" hreflang=\"sr-latn\" />\r\n<link rel=\"alternate\" href=\"https://ocr.oldfish.cn/sq/Article.aspx?id=81&slug=rnn-sequence-modeling\" hreflang=\"sq\" />\r\n<link rel=\"alternate\" href=\"https://ocr.oldfish.cn/so/Article.aspx?id=81&slug=rnn-sequence-modeling\" hreflang=\"so\" />\r\n<link rel=\"alternate\" href=\"https://ocr.oldfish.cn/zu/Article.aspx?id=81&slug=rnn-sequence-modeling\" hreflang=\"zu\" />\r\n<link rel=\"alternate\" href=\"https://ocr.oldfish.cn/ka/Article.aspx?id=81&slug=rnn-sequence-modeling\" hreflang=\"ka\" />\r\n<meta http-equiv=\"Content-Type\" content=\"text/html; charset=UTF-8\" /><meta http-equiv=\"X-UA-Compatible\" content=\"IE=Edge,chrome=1\" /><meta name=\"renderer\" content=\"webkit\" /><meta name=\"viewport\" content=\"width=device-width, initial-scale=1.0, maximum-scale=5.0\" /><link rel=\"icon\" href=\"/favicon.ico\" type=\"image/x-icon\" /><meta name=\"robots\" content=\"all\" /><link rel=\"preconnect\" href=\"https://scm-file-new-1301864755.cos.ap-beijing.myqcloud.com\" /><link rel=\"preconnect\" href=\"https://cdn.oldfish.cn\" /><link rel=\"dns-prefetch\" href=\"https://hm.baidu.com\" /><link rel=\"dns-prefetch\" href=\"https://cdn.oldfish.cn\" /><link rel=\"preload\" href=\"/site/css/site.css\" as=\"style\" />\r\n<script>function toArray(l){var a=[];for(var i=0;i<(l?l.length:0);i++)a.push(l[i]);return a;}function forEach(l,f){for(var i=0,n=(l?l.length:0);i<n;i++)f(l[i],i);}function hasClass(e,c){if(!e||!c)return false;return(' '+e.className+' ').indexOf(' '+c+' ')>-1;}function addClass(e,c){if(!e||!c||hasClass(e,c))return;e.className=(e.className?e.className+' ':'')+c;}function removeClass(e,c){if(!e||!c)return;var u=' '+e.className+' ';while(u.indexOf(' '+c+' ')>-1){u=u.replace(' '+c+' ',' ');}e.className=u.replace(/^\\s+|\\s+$/g,'');}function toggleClass(e,c){if(!e||!c)return;if(hasClass(e,c))removeClass(e,c);else addClass(e,c);}function matchesSelector(e,s){if(!e||e.nodeType!==1)return false;if(e.matches)return e.matches(s);var p=e.parentNode||document;var m=toArray(p.querySelectorAll(s));for(var i=0;i<m.length;i++)if(m[i]===e)return true;return false;}function closest(e,s){var n=e;while(n&&n.nodeType===1){if(matchesSelector(n,s))return n;n=n.parentNode;}return null;}function safeRemove(e){if(e&&e.parentNode)e.parentNode.removeChild(e);}function qa(s,r){return toArray((r||document).querySelectorAll(s));}function q(s,r){return(r||document).querySelector(s);}function xhrGet(u,o,e){try{var x=new XMLHttpRequest();x.open('GET',u,true);x.onreadystatechange=function(){if(x.readyState===4){if(x.status>=200&&x.status<300)o&&o(x.responseText);else e&&e(x);}};x.send(null);}catch(r){e&&e(r);}}function getScrollY(){return window.pageYOffset||document.documentElement.scrollTop||document.body.scrollTop||0;}function getOrigin(){return window.location.protocol+'//'+window.location.host;}</script>\r\n<script>var isWebP=(function(){try{return document.createElement('canvas').toDataURL('image/webp').indexOf('data:image/webp')===0;}catch(e){return false;}})();document.addEventListener('DOMContentLoaded',function(){var c=document.getElementById('user-login-container');if(c){var u=c.getAttribute('data-url');if(u){xhrGet(u,function(h){c.innerHTML=h;forEach(qa('a',c),function(l){l.style.display='inline-block';l.style.marginLeft='10px';});});}}});</script>\r\n    \n    \n\n    <meta name=\"description\" content=\"Mergulhe na aplicação de RNN, LSTM, GRU em OCR. Análise detalhada dos princípios da modelagem de sequências, soluções para problemas de gradiente e as vantagens dos RNNs bidirecionais.\" />\n    <meta name=\"keywords\" content=\"RNN, LSTM, GRU, Modelagem de sequência, Desaparecimento de gradiente, RNN bidirecional, Mecanismo de atenção, CRNN, OCR, reconhecimento de texto OCR, imagem para texto, tecnologia OCR\" />\n    <meta property=\"og:title\" content=\"【Série OCR de Aprendizado Profundo·4】Redes Neurais Recorrentes e Modelagem de Sequência\" />\n    <meta property=\"og:description\" content=\"Mergulhe na aplicação de RNN, LSTM, GRU em OCR. Análise detalhada dos princípios da modelagem de sequências, soluções para problemas de gradiente e as vantagens dos RNNs bidirecionais.\" />\n    <meta property=\"og:type\" content=\"article\" />\n    <meta property=\"og:url\" content=\"https://ocr.oldfish.cn/Article.aspx?id=81&slug=rnn-sequence-modeling&lang=zh-Hans&_translation_source=true\" />\n    <meta property=\"og:site_name\" content=\"Assistente de reconhecimento de texto OCR\" />\n    <meta name=\"twitter:card\" content=\"summary\" />\n    <meta name=\"twitter:title\" content=\"【Série OCR de Aprendizado Profundo·4】Redes Neurais Recorrentes e Modelagem de Sequência\" />\n    <meta name=\"twitter:description\" content=\"Mergulhe na aplicação de RNN, LSTM, GRU em OCR. Análise detalhada dos princípios da modelagem de sequências, soluções para problemas de gradiente e as vantagens dos RNNs bidirecionais.\" />\n\n    \n    <script type=\"application/ld+json\">\n    {\n        \"@context\": \"https://schema.org\",\n        \"@type\": \"Article\",\n        \"headline\": \"【Deep Learning OCR Series 4] Modelagem de Sequência e Rede Neural Recorrente\",\n        \"description\": \"Mergulhe na aplicação de RNN, LSTM, GRU em OCR. Análise detalhada dos princípios da modelagem de sequências, soluções para problemas de gradiente e as vantagens dos RNNs bidirecionais。\",\n        \"publisher\": {\n            \"@type\": \"Organization\",\n            \"name\": \"Equipe de assistente de reconhecimento de texto OCR\"\n        },\n        \"datePublished\": \"2025-08-19T06:32:05Z\",\n        \"dateModified\": \"2025-08-19T06:32:05Z\",\n        \"mainEntityOfPage\": {\n            \"@type\": \"WebPage\",\n            \"@id\": \"https://ocr.oldfish.cn/Article.aspx?id=81&slug=rnn-sequence-modeling&lang=zh-Hans&_translation_source=true\"\n        }\n    }\n    </script>\n    \n    <style>\n        .reading-progress { position: fixed; top: 0; left: 0; width: 0%; height: 3px; background: linear-gradient(90deg, #1764ff, #0d47a1); z-index: 9999; transition: width 0.2s ease; box-shadow: 0 1px 3px rgba(23, 100, 255, 0.3); }\n        .home-container.root { overflow: visible !important; }\n        .article-detail-container { background: #f5f7fc; min-height: calc(100vh - 60px); padding: 80px 0 60px; position: relative; overflow: visible; }\n        .article-detail-container .row { margin: 0 -12px; align-items: flex-start !important; min-height: 100vh !important; }\n        .article-detail-container .row > [class*=\"col-\"] { padding: 0 12px; box-sizing: border-box; min-width: 0; }\n        .article-detail-container .row > .col-lg-8, .article-detail-container .row > .col-xl-9 { overflow: hidden; }\n        .article-detail-container .row > .col-lg-4, .article-detail-container .row > .col-xl-3 { overflow: visible !important; height: 100% !important; min-height: 100% !important; position: static !important; display: block !important; transform: none !important; will-change: auto !important; contain: none !important; perspective: none !important; filter: none !important; }\n        .article-detail { background: #fff; border-radius: 8px; padding: 1.5rem; margin-bottom: 2rem; box-shadow: 0 2px 10px rgba(0,0,0,0.1); width: 100%; box-sizing: border-box; }\n        .article-title { font-size: 1.7rem; color: #1a1c1f; line-height: 1.3; margin-bottom: 1.5rem; font-weight: 600; }\n        .article-meta { display: flex; align-items: center; flex-wrap: nowrap; gap: 1.2rem; color: #6c757d; font-size: 0.88rem; margin-bottom: 2rem; padding: 1.2rem 1.4rem; background: linear-gradient(135deg, #f8f9fa 0%, #ffffff 100%); border-radius: 12px; border-left: 4px solid #1764ff; box-shadow: 0 2px 8px rgba(0,0,0,0.06); overflow-x: hidden; transition: all 0.3s ease; }\n        .article-meta:hover { box-shadow: 0 4px 16px rgba(0,0,0,0.1); transform: translateY(-1px); }\n        .article-meta .meta-item { display: flex; align-items: center; gap: 0.25rem; font-weight: 500; color: #495057; white-space: nowrap; flex-shrink: 1; transition: color 0.2s ease; cursor: default; }\n        .article-meta .meta-item:hover { color: #1764ff; }\n        .article-meta .meta-item:hover .meta-icon { transform: scale(1.1); filter: drop-shadow(0 2px 6px rgba(13, 71, 161, 0.25)); }\n        .meta-icon { display: inline-block; font-size: 16px; flex-shrink: 0; vertical-align: middle; transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1); margin-right: 4px; opacity: 0.9; }\n        .meta-item:hover .meta-icon { opacity: 1; }\n        .meta-item:hover { color: #1764ff; }\n        .meta-item { position: relative; }\n        .meta-item::after { content: ''; position: absolute; bottom: -2px; left: 0; width: 0; height: 2px; background: linear-gradient(90deg, #1764ff, #00d4ff); transition: width 0.3s ease; }\n        .meta-item:hover::after { width: 100%; }\n        .article-category, .read-time { display: inline-block; padding: 6px 12px; border-radius: 16px; font-size: 0.8rem; font-weight: 600; }\n        .article-category { background: linear-gradient(135deg, #1764ff, #0d47a1); color: white; text-decoration: none; text-transform: uppercase; letter-spacing: 0.5px; transition: all 0.3s ease; }\n        .article-category:hover { background: linear-gradient(135deg, #0d47a1, #1764ff); color: white; text-decoration: none; transform: translateY(-1px); box-shadow: 0 4px 12px rgba(23, 100, 255, 0.3); }\n        .read-time { background: #e3f2fd; color: #1565c0; border: 1px solid #90caf9; }\n        .article-tags { margin: 20px 0 1.5rem; }\n        .article-tags .tag { display: inline-block; background: #1764ff; color: #fff; padding: 4px 12px; border-radius: 12px; font-size: 0.8rem; margin: 0 8px 4px 0; }\n        .article-summary { background: #e3f2fd; border-left: 4px solid #1764ff; padding: 1rem 1.5rem; margin-bottom: 2rem; border-radius: 0 4px 4px 0; }\n        .article-content { line-height: 1.8; font-size: 16px; color: #333; word-wrap: break-word; }\n        .article-content h1, .article-content h2, .article-content h3, .article-content h4, .article-content h5, .article-content h6 { margin: 2rem 0 1rem; font-weight: 600; line-height: 1.25; color: #1a1c1f; }\n        .article-content h1 { font-size: 2rem; border-bottom: 2px solid #e9ecef; padding-bottom: 0.5rem; }\n        .article-content h2 { font-size: 1.5rem; border-bottom: 1px solid #e9ecef; padding-bottom: 0.3rem; }\n        .article-content h3 { font-size: 1.25rem; }\n        .article-content h4 { font-size: 1.1rem; }\n        .article-content h5, .article-content h6 { font-size: 1rem; }\n        .article-content ul, .article-content ol { margin: 1rem 0; padding-left: 2rem; }\n        .article-content ul ul, .article-content ol ol, .article-content ul ol, .article-content ol ul { margin: 0.5rem 0; }\n        .article-content blockquote { margin: 1rem 0; padding: 0.5rem 1rem; border-left: 4px solid #1764ff; background-color: #f8f9fa; color: #6c757d; font-style: italic; }\n        .article-content pre { background-color: #f8f9fa; border: 1px solid #e9ecef; border-radius: 6px; padding: 1rem; margin: 1rem 0; overflow-x: auto; font-family: 'Courier New', Courier, monospace; font-size: 0.9em; line-height: 1.4; }\n        .article-content pre code { background: none; border: none; padding: 0; color: inherit; }\n        .article-content p { margin: 1rem 0; }\n        .article-content p:first-child { margin-top: 0; }\n        .article-content p:last-child { margin-bottom: 0; }\n        .article-footer { margin-top: 4rem; padding: 2.5rem; background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%); border-radius: 12px; border: 1px solid #e9ecef; position: relative; overflow: hidden; }\n        .article-footer::before { content: ''; position: absolute; top: 0; left: 0; right: 0; height: 3px; background: linear-gradient(90deg, #1764ff, #17a2b8, #28a745); }\n        .article-share h6 { color: #495057; font-weight: 600; margin-bottom: 1.5rem; font-size: 16px; }\n        .share-buttons { display: flex; gap: 12px; flex-wrap: wrap; }\n        .share-buttons .btn { padding: 10px 20px; border-radius: 25px; font-weight: 500; font-size: 14px; border: none; transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1); box-shadow: 0 2px 8px rgba(0,0,0,0.1); display: inline-flex; align-items: center; gap: 8px; text-decoration: none; }\n        .btn-weibo { background: linear-gradient(135deg, #e6162d, #c41230); color: white; }\n        .btn-copy { background: #1764ff; color: white; }\n        .btn-print { background: linear-gradient(135deg, #17a2b8, #138496); color: white; }\n        .btn-weibo:hover { background: linear-gradient(135deg, #c41230, #a00e26); box-shadow: 0 4px 16px rgba(230, 22, 45, 0.3); }\n        .btn-copy:hover { background: #0d47a1; box-shadow: 0 4px 16px rgba(23, 100, 255, 0.3); }\n        .btn-print:hover { background: linear-gradient(135deg, #138496, #117a8b); box-shadow: 0 4px 16px rgba(23, 162, 184, 0.3); }\n        .btn-weibo:hover, .btn-copy:hover, .btn-print:hover { transform: translateY(-2px); color: white; }\n        .article-actions { display: flex; justify-content: flex-end; align-items: center; }\n        .related-articles { margin-top: 3rem; }\n        .related-article-item { background: #fff; border-radius: 8px; padding: 1.5rem; margin-bottom: 1rem; box-shadow: 0 2px 8px rgba(0,0,0,0.1); transition: transform 0.3s ease; }\n        .related-article-item:hover { transform: translateY(-2px); }\n        .sidebar-card { background: linear-gradient(135deg, rgba(255,255,255,0.9) 0%, rgba(255,255,255,0.8) 100%); backdrop-filter: blur(20px); border: 1px solid rgba(255,255,255,0.3); border-radius: 16px; padding: 1rem; margin-bottom: 0.5rem; box-shadow: 0 4px 20px rgba(0,0,0,0.08), inset 0 1px 0 rgba(255,255,255,0.2); overflow: visible; box-sizing: border-box; width: 100%; z-index: 10; }\n        .sidebar-card.fixed { box-shadow: 0 8px 32px rgba(0,0,0,0.12); }\n        .sidebar-card::before { content: ''; position: absolute; top: 0; left: 0; right: 0; height: 3px; background: linear-gradient(90deg, #00d4ff, #1764ff, #9c27b0); border-radius: 20px 20px 0 0; }\n        .sidebar-card h5 { color: #1a202c; padding-bottom: 0.8rem; border-bottom: 2px solid rgba(23, 100, 255, 0.2); font-weight: 700; font-size: 1.1rem; }\n        .sidebar { min-height: 100vh; position: relative !important; display: block !important; flex-direction: column !important; gap: 1rem; }\n        .toc-card { flex: 0 1 auto; max-height: 60vh; overflow-y: auto; }\n        .sidebar-card:not(.toc-card) { flex: 1 1 auto; overflow: visible; }\n        .recommended-item { padding: 0.75rem 0; border-bottom: 1px solid rgba(0,0,0,0.05); }\n        .recommended-item:last-child { border-bottom: none; }\n        .recommended-item h6 { margin: 0 0 0.5rem 0; font-size: 0.9rem; line-height: 1.4; }\n        .recommended-item h6 a { color: #1a1c1f; text-decoration: none; transition: color 0.3s ease; }\n        .recommended-item h6 a:hover { color: #1764ff; }\n        .recommended-item .text-muted { font-size: 0.75rem; margin: 0; }\n        .toc-list { list-style: none; padding: 0; margin: 0; max-height: none; overflow-y: visible; overflow-x: hidden; }\n        .toc-list::-webkit-scrollbar { width: 4px; }\n        .toc-list::-webkit-scrollbar-track { background: rgba(0,0,0,0.1); border-radius: 2px; }\n        .toc-list::-webkit-scrollbar-thumb { background: linear-gradient(135deg, #00d4ff, #1764ff); border-radius: 2px; }\n        .toc-list::-webkit-scrollbar-thumb:hover { background: linear-gradient(135deg, #1764ff, #0d47a1); }\n        .toc-list a { color: #4a5568; text-decoration: none; font-size: 13px; font-weight: 500; display: block; padding: 8px 12px; border-radius: 8px; transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1); position: relative; margin-bottom: 4px; border: 1px solid transparent; }\n        .toc-list a::before { content: ''; position: absolute; left: 0; top: 0; bottom: 0; width: 3px; background: linear-gradient(135deg, #00d4ff, #1764ff); border-radius: 0 12px 12px 0; transform: scaleY(0); transition: transform 0.3s ease; }\n        .toc-list a:hover { color: #1764ff; background: linear-gradient(135deg, rgba(0, 212, 255, 0.1), rgba(23, 100, 255, 0.1)); border-color: rgba(23, 100, 255, 0.2); transform: translateX(8px); box-shadow: 0 4px 12px rgba(0, 212, 255, 0.2); }\n        .toc-list a:hover::before, .toc-list a.active::before { transform: scaleY(1); }\n        .toc-list a.active { color: #1764ff; background: linear-gradient(135deg, rgba(0, 212, 255, 0.15), rgba(23, 100, 255, 0.15)); font-weight: 700; border-color: rgba(23, 100, 255, 0.3); transform: translateX(4px); box-shadow: 0 6px 16px rgba(0, 212, 255, 0.25); }\n        .toc-number { color: #1764ff; font-weight: 600; margin-right: 0.5rem; font-size: 0.9em; }\n        .toc-text { flex: 1; }\n        .toc-level-1 { font-weight: 700; font-size: 15px; }\n        .toc-level-2 { font-size: 14px; }\n        .toc-level-3 { font-size: 13px; opacity: 0.9; }\n        .toc-level-4, .toc-level-5, .toc-level-6 { font-size: 12px; opacity: 0.8; }\n        .article-content .formatted-list { margin: 1.5rem 0; padding-left: 0; list-style: none; }\n        .article-content .formatted-list.ordered { counter-reset: list-counter; }\n        .article-content .formatted-list.ordered li { counter-increment: list-counter; position: relative; margin: 1rem 0; padding: 1rem 1rem 1rem 3rem; background: linear-gradient(135deg, rgba(23, 100, 255, 0.03), rgba(0, 212, 255, 0.03)); border-left: 4px solid #1764ff; border-radius: 0 8px 8px 0; transition: all 0.3s ease; }\n        .article-content .formatted-list.ordered li::before { content: counter(list-counter); position: absolute; left: 0.8rem; top: 1rem; background: linear-gradient(135deg, #1764ff, #00d4ff); color: white; width: 1.5rem; height: 1.5rem; border-radius: 50%; display: flex; align-items: center; justify-content: center; font-weight: 600; font-size: 0.9rem; }\n        .article-content .formatted-list.ordered li:hover { background: linear-gradient(135deg, rgba(23, 100, 255, 0.06), rgba(0, 212, 255, 0.06)); transform: translateX(4px); box-shadow: 0 2px 8px rgba(23, 100, 255, 0.15); }\n        .article-content .formatted-list.unordered li { position: relative; margin: 0.8rem 0; padding: 0.8rem 1rem 0.8rem 2.5rem; background: linear-gradient(135deg, rgba(40, 167, 69, 0.03), rgba(25, 135, 84, 0.03)); border-left: 4px solid #28a745; border-radius: 0 6px 6px 0; transition: all 0.3s ease; }\n        .article-content .formatted-list.unordered li::before { content: \"•\"; position: absolute; left: 1rem; top: 0.8rem; color: #28a745; font-size: 1.2rem; font-weight: bold; }\n        .article-content .formatted-list.unordered li:hover { background: linear-gradient(135deg, rgba(40, 167, 69, 0.06), rgba(25, 135, 84, 0.06)); transform: translateX(4px); box-shadow: 0 2px 8px rgba(40, 167, 69, 0.15); }\n        .article-content .list-title { color: #1764ff; font-weight: 600; display: inline-block; margin-bottom: 0.3rem; }\n        .article-content .list-content { color: #2c3e50; line-height: 1.6; display: block; }\n        .article-content .inline-code { background: #f8f9fa; border: 1px solid #e9ecef; border-radius: 4px; padding: 2px 6px; font-family: 'Consolas', 'Monaco', 'Courier New', monospace; font-size: 0.9em; color: #e83e8c; }\n        .article-content .label-item { margin: 0.8rem 0; }\n        .article-content .label { color: #1764ff; font-weight: 600; }\n        .article-content .section-label { color: #2c3e50; font-weight: 600; margin: 1rem 0 0.5rem 0; font-size: 1.1em; border-left: 4px solid #1764ff; padding-left: 0.8rem; }\n        .article-content p, .article-content .content-paragraph { margin: 1.2rem 0; line-height: 1.8; text-align: justify; color: #2c3e50; font-size: 1rem; }\n        .article-content .content-paragraph { text-indent: 0; margin: 1rem 0; }\n        .article-content .indent-paragraph { text-indent: 2em; margin: 1.2rem 0; }\n        .article-content .text-indent-1 { text-indent: 2em; display: block; }\n        .article-content .text-indent-2 { text-indent: 4em; display: block; }\n        .article-content .text-indent-3 { text-indent: 6em; display: block; }\n        .article-content .network-config { background: linear-gradient(135deg, #f8f9fa, #e9ecef); border: 1px solid #dee2e6; border-radius: 6px; padding: 0.8rem 1rem; margin: 0.5rem 0; font-family: 'Consolas', 'Monaco', 'Courier New', monospace; font-size: 0.9rem; box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05); }\n        .article-content .config-label { color: #1764ff; font-weight: 600; }\n        .article-content .config-desc { color: #495057; }\n        .article-content .algorithm-step { background: linear-gradient(135deg, rgba(23, 100, 255, 0.05), rgba(0, 212, 255, 0.05)); border-left: 4px solid #1764ff; padding: 0.8rem 1rem; margin: 0.5rem 0; border-radius: 0 6px 6px 0; transition: all 0.3s ease; }\n        .article-content .step-number { color: #1764ff; font-weight: 700; margin-right: 0.5rem; }\n        .article-content .step-desc { color: #2c3e50; line-height: 1.5; }\n        .article-content table { width: 100%; border-collapse: collapse; margin: 1.5rem 0; background: white; border-radius: 8px; overflow: hidden; box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1); }\n        .article-content table th { background: linear-gradient(135deg, #1764ff, #00d4ff); color: white; padding: 1rem; text-align: left; font-weight: 600; }\n        .article-content table td { padding: 0.8rem 1rem; border-bottom: 1px solid #e9ecef; }\n        .article-content table tr:hover { background: rgba(23, 100, 255, 0.05); }\n        .article-content strong { color: #1764ff; font-weight: 600; }\n        .article-content em { color: #6c757d; font-style: italic; }\n        .article-content a { color: #1764ff; text-decoration: none; border-bottom: 1px solid transparent; transition: all 0.3s ease; }\n        .article-content a:hover { border-bottom-color: #1764ff; color: #0056b3; }\n        .article-content .definition-item { background: linear-gradient(135deg, rgba(40, 167, 69, 0.05), rgba(25, 135, 84, 0.05)); border-left: 4px solid #28a745; padding: 1rem; margin: 1rem 0; border-radius: 0 8px 8px 0; }\n        .article-content .definition-label { color: #28a745; font-weight: 600; display: block; margin-bottom: 0.5rem; }\n        .article-content .definition-content { color: #2c3e50; line-height: 1.6; }\n        .article-content .note-item { background: linear-gradient(135deg, rgba(255, 193, 7, 0.1), rgba(255, 193, 7, 0.05)); border: 1px solid rgba(255, 193, 7, 0.3); border-radius: 8px; padding: 1rem; margin: 1rem 0; position: relative; }\n        .article-content .note-item::before { content: \"💡\"; position: absolute; left: 1rem; top: 1rem; font-size: 1.2em; }\n        .article-content .note-label { color: #856404; font-weight: 600; margin-left: 2rem; }\n        .article-content .note-content { color: #2c3e50; line-height: 1.6; margin-left: 2rem; display: block; margin-top: 0.3rem; }\n        .article-content .math-formula { background: #f8f9fa; border: 1px solid #e9ecef; border-radius: 6px; padding: 1rem; margin: 1rem 0; text-align: center; font-family: 'Times New Roman', serif; font-size: 1.1em; color: #2c3e50; }\n        @media (min-width: 1400px) { .article-content { font-size: 17px; line-height: 1.8; } .article-detail { padding: 2.5rem; } }\n        @media (max-width: 1199px) and (min-width: 992px) { .article-detail { padding: 1.5rem; } .sidebar-card { padding: 1rem; } .toc-list a { font-size: 12px; padding: 6px 8px; } }\n        @media (max-width: 768px) {\n            .article-detail-container { padding: 60px 15px; }\n            .article-content .network-config, .article-content .algorithm-step, .article-content .definition-item, .article-content .note-item { margin: 0.5rem -1rem; border-radius: 0; }\n            .article-content .note-item::before { left: 0.5rem; }\n            .article-content .note-label, .article-content .note-content { margin-left: 1.5rem; }\n            .article-detail { padding: 1.5rem; }\n            .article-title { font-size: 1.6rem; line-height: 1.3; }\n            .article-meta { flex-wrap: wrap; gap: 1rem; padding: 1rem; font-size: 0.8rem; }\n            .article-meta .meta-item { flex-shrink: 1; }\n            .meta-icon { font-size: 14px; margin-right: 3px; }\n            .article-content { font-size: 15px; }\n            .article-content h1 { font-size: 1.6rem; }\n            .article-content h2 { font-size: 1.4rem; }\n            .article-content h3 { font-size: 1.2rem; }\n            .article-content h4 { font-size: 1.1rem; }\n            .article-content pre { padding: 0.75rem; font-size: 0.85em; overflow-x: auto; }\n            .article-content code { font-size: 0.85em; padding: 1px 4px; }\n            .sidebar { margin-top: 2rem; }\n            .sidebar-card { position: static; }\n            .toc-card { max-height: none; overflow-y: visible; }\n            .share-buttons { flex-direction: column; gap: 0.5rem; }\n            .share-buttons .btn { width: 100%; justify-content: center; }\n        }\n        @media print { body * { visibility: hidden; } .article-detail, .article-detail * { visibility: visible; } .article-detail { position: absolute; left: 0; top: 0; width: 100%; background: white; box-shadow: none; border: none; margin: 0; padding: 20px; } .article-meta { border: none; background: none; padding: 0; margin-bottom: 20px; } .article-footer, .reading-progress, .back-to-top { display: none !important; } .article-content { font-size: 12pt; line-height: 1.6; } .article-content h1, .article-content h2, .article-content h3, .article-content h4, .article-content h5, .article-content h6 { page-break-after: avoid; margin-top: 20pt; margin-bottom: 10pt; } .article-content pre, .article-content blockquote { page-break-inside: avoid; } }\n    </style>\n\n<script type=\"application/ld+json\">{\"@context\":\"https://schema.org\",\"@type\":\"BreadcrumbList\",\"itemListElement\":[{\"@type\":\"ListItem\",\"position\":1,\"name\":\"Casa\",\"item\":\"https://ocr.oldfish.cn/default.aspx\"},{\"@type\":\"ListItem\",\"position\":2,\"name\":\"Artigos técnicos\",\"item\":\"https://ocr.oldfish.cn/Articles.aspx\"},{\"@type\":\"ListItem\",\"position\":3,\"name\":\"Detalhes do artigo\",\"item\":\"https://ocr.oldfish.cn/Article.aspx?id=81&slug=rnn-sequence-modeling&lang=zh-Hans&_translation_source=true\"}]}</script>\n<link rel=\"stylesheet\" href=\"/site/css/site.css\" type=\"text/css\" /><title>【Série OCR de Aprendizado Profundo·4】Redes Neurais Recorrentes e Modelagem de Sequência</title><meta http-equiv=\"Content-Language\" content=\"pt\" />\n</head>\r\n<body>\r\n    <div class=\"home-container root\">\r\n        <header class=\"v4_header_pc\">\r\n            <div class=\"header_pc_left\">\r\n                <a href=\"Default.aspx\" title=\"Início | Reconhecimento de texto inteligente AI\" class=\"pc-logo-wrap ml-4 mr-5\">\r\n                    <picture>\r\n                        <source srcset=\"site/image/logo-s.png.webp\" type=\"image/webp\" />\r\n                        <img src=\"site/image/logo-s.png\" class=\"pc-logo\" style=\"height: 100%\" alt=\"Logotipo do site oficial do assistente de reconhecimento de texto OCR - Plataforma de reconhecimento de texto inteligente AI\" />\r\n                    </picture>\r\n                    <span style=\"font-size: 18px; font-weight: bold; color: #333;\">Assistente de reconhecimento de texto OCR</span>\r\n                </a>\r\n                <ul class=\"top-nav\" role=\"navigation\" aria-label=\"Navegação principal\">\r\n                    <li><a class=\"color-default\" href=\"Default.aspx\" title=\"Página inicial do Assistente de reconhecimento de texto OCR\">Casa</a></li>\r\n                    <li><a class=\"color-default\" data-sub=\"product-sub-nav\" href=\"Detail.aspx\" title=\"Introdução à função do produto OCR\">Características do produto:</a><div class=\"triangle\"></div>\r\n                    </li>\r\n                    <li><a class=\"color-default\" data-sub=\"experience-sub-nav\" href=\"Ocr.aspx\" title=\"Experimente os recursos de OCR online\">Experiência online</a><div class=\"triangle\"></div>\r\n                    </li>\r\n                    <li><a class=\"color-default\" data-sub=\"vip-sub-nav\" href=\"Version.aspx\" title=\"Serviço de atualização de associação OCR\">Upgrades de associação</a><div class=\"triangle\"></div>\r\n                    </li>\r\n                    <li><a class=\"color-default\" href=\"Download.aspx\" title=\"Descarregar o OCR Text Recognition Assistant grátis\">Download grátis</a></li>\r\n                    <li><a class=\"color-default\" data-sub=\"articles-sub-nav\" href=\"Articles.aspx\" title=\"Artigos técnicos de OCR e compartilhamento de conhecimento\">Compartilhamento de tecnologia</a><div class=\"triangle\"></div>\r\n                    </li>\r\n                    <li><a class=\"color-default\" data-sub=\"support-sub-nav\" href=\"FAQ.aspx\" title=\"Ajuda de uso de OCR e suporte técnico\">Central de Ajuda</a><div class=\"triangle\"></div>\r\n                    </li>\r\n                </ul>\r\n                <div class=\"product-con\">\r\n                    <section id=\"product-sub-nav\" class=\"pls-nav-dropdown\" style=\"visibility: hidden; height: 0px; display: none;\">\r\n                        <div class=\"d-flex nav-drown-con\" style=\"height: 0px; transition: transform 0.2s ease-in-out 0s,opacity 0.2s ease-in-out 0s; opacity: 0; transform: translate(0px, -100%);\">\r\n                            <div class=\"prdocu-sub-left\">\r\n                                <div class=\"mr-auto\">\r\n                                    <picture>\r\n                                        <source class=\"p-icon\" srcset=\"site/image/icon/p_1.png.webp\" type=\"image/webp\" alt=\"\" aria-hidden=\"true\" />\r\n                                        <img class=\"p-icon\" src=\"site/image/icon/p_1.png\" alt=\"Ícone da função do produto OCR\" aria-hidden=\"true\" loading=\"lazy\" />\r\n                                    </picture>\r\n                                    <div class=\"mt-4\">\r\n                                        <h3 class=\"h6\">Assistente de reconhecimento de texto OCR</h3>\r\n                                        <p class=\"color-gray fn14 mt-3 sub-left-desc\">Melhore a eficiência, reduza custos e crie valor</p>\r\n                                        <p class=\"color-gray fn14 mt-3 sub-left-desc\">&nbsp;</p>\r\n                                        <p class=\"color-gray fn14 mt-3 sub-left-desc\">Reconhecimento inteligente, processamento de alta velocidade e saída precisa</p>\r\n                                        <p class=\"color-gray fn14 mt-3 sub-left-desc\">Do texto às tabelas, das fórmulas às traduções</p>\r\n                                        <p class=\"color-gray fn14 mt-3 sub-left-desc\">Torne cada processamento de texto tão fácil</p>\r\n                                        <a class=\"link block disa fn14 contact-business shake\" href=\"Detail.aspx\">Saiba mais sobre os recursos<span class=\"iconfont4 icon-xiangzuo gt\"></span></a>\r\n                                    </div>\r\n                                </div>\r\n                            </div>\r\n                            <div class=\"bg-white prdocu-sub-right\">\r\n                                <div>\r\n                                    <h2 class=\"text-base-color h6\">Características do produto:</h2>\r\n                                    <div class=\"mt-4 d-flex flex-wrap\">\r\n                                        <a href=\"Detail.aspx\" class=\"pro-item color-default\" title=\"Confira os detalhes das principais funções do OCR Assistant\">\r\n                                            <div class=\"letter-wrap\">\r\n                                                <h3 class=\"h6\">Características principais:</h3>\r\n                                                <span class=\"color-gray fn14\">Saiba mais sobre os principais recursos e benefícios técnicos do OCR Assistant, com uma taxa de reconhecimento de 98%+</span>\r\n                                            </div>\r\n                                        </a>\r\n                                        <a href=\"Version.aspx\" class=\"pro-item color-default\" title=\"Compare as diferenças entre as versões do Assistente de OCR\">\r\n                                            <div class=\"letter-wrap\">\r\n                                                <h3 class=\"h6\">Comparação de versões</h3>\r\n                                                <span class=\"color-gray fn14\">Compare as diferenças funcionais da versão gratuita, versão pessoal, versão profissional e versão final em detalhes</span>\r\n                                            </div>\r\n                                        </a>\r\n                                        <a href=\"FAQ.aspx\" class=\"pro-item color-default\" title=\"Confira as perguntas frequentes do OCR Assistant\">\r\n                                            <div class=\"letter-wrap\">\r\n                                                <h3 class=\"h6\">Perguntas e respostas sobre produtos</h3>\r\n                                                <span class=\"color-gray fn14\">Aprenda rapidamente sobre os recursos do produto, métodos de uso e respostas detalhadas para perguntas frequentes</span>\r\n                                            </div>\r\n                                        </a>\r\n                                        <a href=\"Download.aspx\" class=\"pro-item color-default\" title=\"Descarregar o OCR Text Recognition Assistant grátis\">\r\n                                            <div class=\"letter-wrap\">\r\n                                                <h3 class=\"h6\">Experimente gratuitamente</h3>\r\n                                                <span class=\"color-gray fn14\">Baixe e instale o OCR Assistant agora para experimentar a poderosa função de reconhecimento de texto gratuitamente</span>\r\n                                            </div>\r\n                                        </a>\r\n                                        <div style=\"width: 100%; margin: 15px 0;\">\r\n                                            <h4 class=\"h6\" style=\"color: #007cfa; margin-bottom: 10px;\">Reconhecimento de OCR online</h4>\r\n                                        </div>\r\n                                        <a href=\"Ocr.aspx?type=text_recognize\" class=\"pro-item color-default\" title=\"Experimente o reconhecimento universal de texto online\">\r\n                                            <div class=\"letter-wrap\">\r\n                                                <h3 class=\"h6\">Reconhecimento universal de caracteres</h3>\r\n                                                <span class=\"color-gray fn14\">Extração inteligente de texto multilíngue de alta precisão, suportando reconhecimento de imagem complexa impressa e multicena</span>\r\n                                            </div>\r\n                                        </a>\r\n                                        <a href=\"Ocr.aspx?type=table\" class=\"pro-item color-default\">\r\n                                            <div class=\"letter-wrap\">\r\n                                                <h3 class=\"h6\">Identificação universal da tabela</h3>\r\n                                                <span class=\"color-gray fn14\">Conversão inteligente de imagens de tabela em arquivos Excel, processamento automático de estruturas de tabela complexas e células mescladas</span>\r\n                                            </div>\r\n                                        </a>\r\n                                        <a href=\"Ocr.aspx?type=handwritten_ocr\" class=\"pro-item color-default\">\r\n                                            <div class=\"letter-wrap\">\r\n                                                <h3 class=\"h6\">Reconhecimento de manuscrito</h3>\r\n                                                <span class=\"color-gray fn14\">Reconhecimento inteligente de conteúdo manuscrito em chinês e inglês, notas de suporte em sala de aula, registros médicos e outros cenários</span>\r\n                                            </div>\r\n                                        </a>\r\n                                        <a href=\"Ocr.aspx?type=pdf2word\" class=\"pro-item color-default\">\r\n                                            <div class=\"letter-wrap\">\r\n                                                <h3 class=\"h6\">PDF para Word</h3>\r\n                                                <span class=\"color-gray fn14\">Os documentos PDF são rapidamente convertidos para o formato Word, preservando perfeitamente o layout original e o layout gráfico</span>\r\n                                            </div>\r\n                                        </a>\r\n                                    </div>\r\n                                </div>\r\n                            </div>\r\n                        </div>\r\n                    </section>\r\n                    <section id=\"experience-sub-nav\" class=\"pls-nav-dropdown solution-part\" style=\"visibility: hidden; height: 0px; display: none;\">\r\n                        <div class=\"d-flex nav-drown-con\" style=\"height: 0px; transition: transform 0.2s ease-in-out 0s,opacity 0.2s ease-in-out 0s; opacity: 0; transform: translate(0px, -100%);\">\r\n                            <div class=\"prdocu-sub-left\">\r\n                                <div class=\"mr-auto\">\r\n                                    <picture>\r\n                                        <source srcset=\"site/image/icon/p_4.png.webp\" type=\"image/webp\" />\r\n                                        <img class=\"p-icon\" src=\"site/image/icon/p_4.png\" alt=\"Ícone do Centro de experiência de OCR online\" aria-hidden=\"true\" loading=\"lazy\" />\r\n                                    </picture>\r\n                                    <div class=\"mt-4\">\r\n                                        <h3 class=\"h6\">Assistente de reconhecimento de texto OCR</h3>\r\n                                        <p class=\"color-gray fn14 mt-3 sub-left-desc\">Texto, tabelas, fórmulas, documentos, traduções</p>\r\n                                        <p class=\"color-gray fn14 mt-3 sub-left-desc\">&nbsp;</p>\r\n                                        <p class=\"color-gray fn14 mt-3 sub-left-desc\">Conclua todas as suas necessidades de processamento de texto em três etapas</p>\r\n                                        <p class=\"color-gray fn14 mt-3 sub-left-desc\">Captura de tela → Identificar aplicativos →</p>\r\n                                        <p class=\"color-gray fn14 mt-3 sub-left-desc\">Aumente a eficiência do trabalho em 300%</p>\r\n                                        <a class=\"link block disa fn14 contact-business shake\" href=\"Ocr.aspx\">Experimente agora<span class=\"iconfont4 icon-xiangzuo gt\"></span></a>\r\n                                    </div>\r\n                                </div>\r\n                            </div>\r\n                            <div class=\"bg-white prdocu-sub-right\">\r\n                                <div>\r\n                                    <div class=\"mt-4 d-flex flex-wrap\">\r\n                                        <div style=\"width: 100%; margin-bottom: 15px;\">\r\n                                            <h4 class=\"h6\" style=\"color: #007cfa; margin-bottom: 10px;\">Experiência da função OCR</h4>\r\n                                        </div>\r\n                                        <a href=\"Ocr.aspx?type=index\" class=\"pro-item color-default\">\r\n                                            <div class=\"letter-wrap\">\r\n                                                <h3 class=\"h6\">Funcionalidade completa</h3>\r\n                                                <span class=\"color-gray fn14\">Experimente todos os recursos inteligentes de OCR em um só lugar para encontrar rapidamente a melhor solução para suas necessidades</span>\r\n                                            </div>\r\n                                        </a>\r\n                                        <a href=\"Ocr.aspx?type=text_recognize\" class=\"pro-item color-default\">\r\n                                            <div class=\"letter-wrap\">\r\n                                                <h3 class=\"h6\">Reconhecimento universal de caracteres</h3>\r\n                                                <span class=\"color-gray fn14\">Extração inteligente de texto multilíngue de alta precisão, suportando reconhecimento de imagem complexa impressa e multicena</span>\r\n                                            </div>\r\n                                        </a>\r\n                                        <a href=\"Ocr.aspx?type=table\" class=\"pro-item color-default\">\r\n                                            <div class=\"letter-wrap\">\r\n                                                <h3 class=\"h6\">Identificação universal da tabela</h3>\r\n                                                <span class=\"color-gray fn14\">Conversão inteligente de imagens de tabela em arquivos Excel, processamento automático de estruturas de tabela complexas e células mescladas</span>\r\n                                            </div>\r\n                                        </a>\r\n                                        <a href=\"Ocr.aspx?type=handwritten_ocr\" class=\"pro-item color-default\">\r\n                                            <div class=\"letter-wrap\">\r\n                                                <h3 class=\"h6\">Reconhecimento de manuscrito</h3>\r\n                                                <span class=\"color-gray fn14\">Reconhecimento inteligente de conteúdo manuscrito em chinês e inglês, notas de suporte em sala de aula, registros médicos e outros cenários</span>\r\n                                            </div>\r\n                                        </a>\r\n                                        <a href=\"Ocr.aspx?type=pdf2word\" class=\"pro-item color-default\">\r\n                                            <div class=\"letter-wrap\">\r\n                                                <h3 class=\"h6\">PDF para Word</h3>\r\n                                                <span class=\"color-gray fn14\">Os documentos PDF são rapidamente convertidos para o formato Word, preservando perfeitamente o layout original e o layout gráfico</span>\r\n                                            </div>\r\n                                        </a>\r\n                                        <a href=\"Ocr.aspx?type=pdf2markdown\" class=\"pro-item color-default\">\r\n                                            <div class=\"letter-wrap\">\r\n                                                <h3 class=\"h6\">PDF para Markdown</h3>\r\n                                                <span class=\"color-gray fn14\">Os documentos PDF são convertidos de forma inteligente para o formato MD e os blocos de código e as estruturas de texto são otimizados automaticamente para processamento</span>\r\n                                            </div>\r\n                                        </a>\r\n                                        <div style=\"width: 100%; margin: 15px 0;\">\r\n                                            <h4 class=\"h6\" style=\"color: #007cfa; margin-bottom: 10px;\">Ferramentas de processamento de documentos</h4>\r\n                                        </div>\r\n                                        <a href=\"Ocr.aspx?type=word2pdf\" class=\"pro-item color-default\">\r\n                                            <div class=\"letter-wrap\">\r\n                                                <h3 class=\"h6\">Word para PDF</h3>\r\n                                                <span class=\"color-gray fn14\">Os documentos do Word são convertidos em PDF com um clique, mantendo perfeitamente o formato original, adequado para arquivamento e compartilhamento de documentos oficiais</span>\r\n                                            </div>\r\n                                        </a>\r\n                                        <a href=\"Ocr.aspx?type=word2jpg\" class=\"pro-item color-default\">\r\n                                            <div class=\"letter-wrap\">\r\n                                                <h3 class=\"h6\">Palavra à imagem</h3>\r\n                                                <span class=\"color-gray fn14\">Conversão inteligente de documento do Word para imagem JPG, suporta processamento de várias páginas, fácil de compartilhar nas mídias sociais</span>\r\n                                            </div>\r\n                                        </a>\r\n                                        <a href=\"Ocr.aspx?type=pdf2jpg\" class=\"pro-item color-default\">\r\n                                            <div class=\"letter-wrap\">\r\n                                                <h3 class=\"h6\">PDF para imagem</h3>\r\n                                                <span class=\"color-gray fn14\">Converta documentos PDF em imagens JPG em alta definição, suporte a processamento em lote e resolução personalizada</span>\r\n                                            </div>\r\n                                        </a>\r\n                                        <a href=\"Ocr.aspx?type=image2pdf\" class=\"pro-item color-default\">\r\n                                            <div class=\"letter-wrap\">\r\n                                                <h3 class=\"h6\">Imagem para PDF</h3>\r\n                                                <span class=\"color-gray fn14\">Mescle várias imagens em documentos PDF, suporte à classificação e configuração de página</span>\r\n                                            </div>\r\n                                        </a>\r\n                                        <div style=\"width: 100%; margin: 15px 0;\">\r\n                                            <h4 class=\"h6\" style=\"color: #007cfa; margin-bottom: 10px;\">Ferramentas de desenvolvedor</h4>\r\n                                        </div>\r\n                                        <a href=\"Tool.aspx?type=Json\" class=\"pro-item color-default\">\r\n                                            <div class=\"letter-wrap\">\r\n                                                <h3 class=\"h6\">Formatação JSON</h3>\r\n                                                <span class=\"color-gray fn14\">Embelezar de forma inteligente a estrutura do código JSON, dar suporte à compactação e expansão e facilitar o desenvolvimento e a depuração</span>\r\n                                            </div>\r\n                                        </a>\r\n                                        <a href=\"Tool.aspx?type=Regex\" class=\"pro-item color-default\">\r\n                                            <div class=\"letter-wrap\">\r\n                                                <h3 class=\"h6\">expressão regular</h3>\r\n                                                <span class=\"color-gray fn14\">Verifique os efeitos de correspondência de expressões regulares em tempo real, com uma biblioteca integrada de padrões comuns</span>\r\n                                            </div>\r\n                                        </a>\r\n                                        <a href=\"Tool.aspx?type=Encode\" class=\"pro-item color-default\">\r\n                                            <div class=\"letter-wrap\">\r\n                                                <h3 class=\"h6\">Conversão de codificação de texto</h3>\r\n                                                <span class=\"color-gray fn14\">Ele suporta a conversão de vários formatos de codificação, como Base64, URL e Unicode</span>\r\n                                            </div>\r\n                                        </a>\r\n                                        <a href=\"Tool.aspx?type=Diff\" class=\"pro-item color-default\">\r\n                                            <div class=\"letter-wrap\">\r\n                                                <h3 class=\"h6\">Correspondência e mesclagem de texto</h3>\r\n                                                <span class=\"color-gray fn14\">Destaque as diferenças de texto e suporte à comparação linha por linha e mesclagem inteligente</span>\r\n                                            </div>\r\n                                        </a>\r\n                                        <a href=\"Tool.aspx?type=Color\" class=\"pro-item color-default\">\r\n                                            <div class=\"letter-wrap\">\r\n                                                <h3 class=\"h6\">Ferramenta Cor</h3>\r\n                                                <span class=\"color-gray fn14\">Conversão de cores RGB/HEX, seletor de cores online, uma ferramenta obrigatória para desenvolvimento front-end</span>\r\n                                            </div>\r\n                                        </a>\r\n                                        <a href=\"Tool.aspx?type=Count\" class=\"pro-item color-default\">\r\n                                            <div class=\"letter-wrap\">\r\n                                                <h3 class=\"h6\">Contagem de palavras</h3>\r\n                                                <span class=\"color-gray fn14\">Contagem inteligente de caracteres, vocabulário e parágrafos e otimização automática do layout do texto</span>\r\n                                            </div>\r\n                                        </a>\r\n                                        <a href=\"Tool.aspx?type=Timespan\" class=\"pro-item color-default\">\r\n                                            <div class=\"letter-wrap\">\r\n                                                <h3 class=\"h6\">Conversão de carimbo de data/hora</h3>\r\n                                                <span class=\"color-gray fn14\">A hora é convertida de e para carimbos de data/hora Unix e vários formatos e configurações de fuso horário são suportados</span>\r\n                                            </div>\r\n                                        </a>\r\n                                        <a href=\"Tool.aspx?type=Calc\" class=\"pro-item color-default\">\r\n                                            <div class=\"letter-wrap\">\r\n                                                <h3 class=\"h6\">Ferramenta de calculadora</h3>\r\n                                                <span class=\"color-gray fn14\">Calculadora científica online com suporte para operações básicas e cálculos avançados de funções matemáticas</span>\r\n                                            </div>\r\n                                        </a>\r\n                                    </div>\r\n                                </div>\r\n                            </div>\r\n                        </div>\r\n                    </section>\r\n                    <section id=\"articles-sub-nav\" class=\"pls-nav-dropdown solution-part\" style=\"visibility: hidden; height: 0px; display: none;\">\r\n                        <div class=\"d-flex nav-drown-con\" style=\"height: 0px; transition: transform 0.2s ease-in-out 0s,opacity 0.2s ease-in-out 0s; opacity: 0; transform: translate(0px, -100%);\">\r\n                            <div class=\"prdocu-sub-left\">\r\n                                <div class=\"mr-auto\">\r\n                                    <picture>\r\n                                        <source srcset=\"site/image/icon/p_4.png.webp\" type=\"image/webp\" />\r\n                                        <img class=\"p-icon\" src=\"site/image/icon/p_4.png\" alt=\"Ícone do Centro de Compartilhamento de Tecnologia\" aria-hidden=\"true\" loading=\"lazy\" />\r\n                                    </picture>\r\n                                    <div class=\"mt-4\">\r\n                                        <h3 class=\"h6\">Compartilhamento de tecnologia OCR</h3>\r\n                                        <p class=\"color-gray fn14 mt-3 sub-left-desc\">Tutoriais técnicos, casos de aplicação, recomendações de ferramentas</p>\r\n                                        <p class=\"color-gray fn14 mt-3 sub-left-desc\">&nbsp;</p>\r\n                                        <p class=\"color-gray fn14 mt-3 sub-left-desc\">Um caminho de aprendizagem completo do iniciante ao domínio</p>\r\n                                        <p class=\"color-gray fn14 mt-3 sub-left-desc\">Casos práticos → análise técnica → aplicações de ferramentas</p>\r\n                                        <p class=\"color-gray fn14 mt-3 sub-left-desc\">Capacite seu caminho para a melhoria da tecnologia OCR</p>\r\n                                        <a class=\"link block disa fn14 contact-business shake\" href=\"Articles.aspx\">Procurar artigos<span class=\"iconfont4 icon-xiangzuo gt\"></span></a>\r\n                                    </div>\r\n                                </div>\r\n                            </div>\r\n                            <div class=\"bg-white prdocu-sub-right\">\r\n                                <div>\r\n                                    <h2 class=\"text-base-color h6\">Compartilhamento de tecnologia</h2>\r\n                                    <div class=\"mt-4 d-flex flex-wrap\">\r\n                                        <a href=\"Articles.aspx\" class=\"pro-item color-default\" title=\"Ver todos os artigos técnicos de OCR\">\r\n                                            <div class=\"letter-wrap\">\r\n                                                <h3 class=\"h6\">Todos os artigos</h3>\r\n                                                <span class=\"color-gray fn14\">Navegue por todos os artigos técnicos de OCR cobrindo um corpo completo de conhecimento, do básico ao avançado</span>\r\n                                            </div>\r\n                                        </a>\r\n                                        \r\n                                        <a href=\"Articles.aspx?category=2\" class=\"pro-item color-default\" title=\"Tutoriais técnicos de OCR e guias de introdução\">\r\n                                            <div class=\"letter-wrap\">\r\n                                                <h3 class=\"h6\">Guia Avançado</h3>\r\n                                                <span class=\"color-gray fn14\">De tutoriais técnicos de OCR introdutórios a proficientes, guias de instruções detalhados e orientações práticas</span>\r\n                                            </div>\r\n                                        </a>\r\n                                        \r\n                                        <a href=\"Articles.aspx?category=3\" class=\"pro-item color-default\" title=\"Princípios, algoritmos e aplicações da tecnologia OCR\">\r\n                                            <div class=\"letter-wrap\">\r\n                                                <h3 class=\"h6\">Exploração tecnológica</h3>\r\n                                                <span class=\"color-gray fn14\">Explore as fronteiras da tecnologia OCR, dos princípios às aplicações, e analise profundamente os principais algoritmos</span>\r\n                                            </div>\r\n                                        </a>\r\n                                        \r\n                                        <a href=\"Articles.aspx?category=4\" class=\"pro-item color-default\" title=\"Os últimos desenvolvimentos e tendências de desenvolvimento na indústria de OCR\">\r\n                                            <div class=\"letter-wrap\">\r\n                                                <h3 class=\"h6\">Tendências da indústria</h3>\r\n                                                <span class=\"color-gray fn14\">Insights detalhados sobre tendências de desenvolvimento de tecnologia OCR, análise de mercado, dinâmica do setor e perspectivas futuras</span>\r\n                                            </div>\r\n                                        </a>\r\n                                        \r\n                                        <a href=\"Articles.aspx?category=5\" class=\"pro-item color-default\" title=\"Casos de aplicação da tecnologia OCR em vários setores\">\r\n                                            <div class=\"letter-wrap\">\r\n                                                <h3 class=\"h6\">Casos de uso:</h3>\r\n                                                <span class=\"color-gray fn14\">Casos de aplicação do mundo real, soluções e melhores práticas da tecnologia OCR em vários setores são compartilhados</span>\r\n                                            </div>\r\n                                        </a>\r\n                                        \r\n                                        <a href=\"Articles.aspx?category=6\" class=\"pro-item color-default\" title=\"Avaliações profissionais, análises comparativas e diretrizes recomendadas para o uso de ferramentas de software de OCR\">\r\n                                            <div class=\"letter-wrap\">\r\n                                                <h3 class=\"h6\">Revisão da ferramenta</h3>\r\n                                                <span class=\"color-gray fn14\">Avalie vários softwares e ferramentas de reconhecimento de texto OCR e forneça sugestões detalhadas de comparação e seleção de funções</span>\r\n                                            </div>\r\n                                        </a>\r\n                                        \r\n                                    </div>\r\n                                </div>\r\n                            </div>\r\n                        </div>\r\n                    </section>\r\n                    <section id=\"vip-sub-nav\" class=\"pls-nav-dropdown solution-part\" style=\"visibility: hidden; height: 0px; display: none;\">\r\n                        <div class=\"d-flex nav-drown-con\" style=\"height: 0px; transition: transform 0.2s ease-in-out 0s,opacity 0.2s ease-in-out 0s; opacity: 0; transform: translate(0px, -100%);\">\r\n                            <div class=\"prdocu-sub-left\">\r\n                                <div class=\"mr-auto\">\r\n                                    <picture>\r\n                                        <source class=\"p-icon\" srcset=\"site/image/icon/p_1.png.webp\" type=\"image/webp\" alt=\"\" aria-hidden=\"true\" />\r\n                                        <img class=\"p-icon\" src=\"site/image/icon/p_1.png\" alt=\"Ícone do serviço de upgrade de associação\" aria-hidden=\"true\" loading=\"lazy\" />\r\n                                    </picture>\r\n                                    <div class=\"mt-4\">\r\n                                        <h3 class=\"h6\">Serviço de upgrade de associação</h3>\r\n                                        <p class=\"color-gray fn14 mt-3 sub-left-desc\">Desbloqueie todos os recursos premium e desfrute de serviços exclusivos</p>\r\n                                        <p class=\"color-gray fn14 mt-3 sub-left-desc\">&nbsp;</p>\r\n                                        <p class=\"color-gray fn14 mt-3 sub-left-desc\">Reconhecimento offline, processamento em lote, uso ilimitado</p>\r\n                                        <p class=\"color-gray fn14 mt-3 sub-left-desc\">Pro → Ultimate → Enterprise</p>\r\n                                        <p class=\"color-gray fn14 mt-3 sub-left-desc\">Há algo para atender às suas necessidades</p>\r\n                                        <a class=\"link block disa fn14 contact-business shake\" href=\"Version.aspx\">Ver detalhes<span class=\"iconfont4 icon-xiangzuo gt\"></span></a>\r\n                                    </div>\r\n                                </div>\r\n                            </div>\r\n                            <div class=\"bg-white prdocu-sub-right\">\r\n                                <div>\r\n                                    <h2 class=\"text-base-color h6\" style=\"margin: inherit;\">Upgrades de associação</h2>\r\n                                    <div class=\"mt-4 d-flex flex-wrap\">\r\n                                        <a href=\"Version.aspx\" class=\"pro-item color-default\">\r\n                                            <div class=\"letter-wrap\">\r\n                                                <h3 class=\"h6\">Privilégios de associação</h3>\r\n                                                <span class=\"color-gray fn14\">Saiba mais sobre as diferenças entre as edições e escolha o nível de associação que melhor se adapta a você</span>\r\n                                            </div>\r\n                                        </a>\r\n                                        <a href=\"Upgrade.aspx\" class=\"pro-item color-default\">\r\n                                            <div class=\"letter-wrap\">\r\n                                                <h3 class=\"h6\">Atualize agora</h3>\r\n                                                <span class=\"color-gray fn14\">Atualize rapidamente sua assinatura VIP para desbloquear mais recursos premium e serviços exclusivos</span>\r\n                                            </div>\r\n                                        </a>\r\n                                        <a href=\"User.aspx\" class=\"pro-item color-default\">\r\n                                            <div class=\"letter-wrap\">\r\n                                                <h3 class=\"h6\">Minha Conta</h3>\r\n                                                <span class=\"color-gray fn14\">Gerencie informações da conta, status da assinatura e histórico de uso para personalizar as configurações</span>\r\n                                            </div>\r\n                                        </a>\r\n                                    </div>\r\n                                </div>\r\n                            </div>\r\n                        </div>\r\n                    </section>\r\n                    <section id=\"support-sub-nav\" class=\"pls-nav-dropdown solution-part\" style=\"visibility: hidden; height: 0px; display: none;\">\r\n                        <div class=\"d-flex nav-drown-con\" style=\"height: 0px; transition: transform 0.2s ease-in-out 0s,opacity 0.2s ease-in-out 0s; opacity: 0; transform: translate(0px, -100%);\">\r\n                            <div class=\"prdocu-sub-left\">\r\n                                <div class=\"mr-auto\">\r\n                                    <picture>\r\n                                        <source class=\"p-icon\" srcset=\"site/image/icon/p_4.png.webp\" type=\"image/webp\" alt=\"\" aria-hidden=\"true\" />\r\n                                        <img class=\"p-icon\" src=\"site/image/icon/p_4.png\" alt=\"Ícone de suporte da Central de Ajuda\" aria-hidden=\"true\" loading=\"lazy\" />\r\n                                    </picture>\r\n                                    <div class=\"mt-4\">\r\n                                        <h3 class=\"h6\">Central de Ajuda</h3>\r\n                                        <p class=\"color-gray fn14 mt-3 sub-left-desc\">Atendimento ao cliente profissional, documentação detalhada e resposta rápida</p>\r\n                                        <p class=\"color-gray fn14 mt-3 sub-left-desc\">&nbsp;</p>\r\n                                        <p class=\"color-gray fn14 mt-3 sub-left-desc\">Não entre em pânico quando encontrar problemas</p>\r\n                                        <p class=\"color-gray fn14 mt-3 sub-left-desc\">Problema → encontrar → resolvido</p>\r\n                                        <p class=\"color-gray fn14 mt-3 sub-left-desc\">Torne sua experiência mais tranquila</p>\r\n                                        <a class=\"link block disa fn14 contact-business shake\" href=\"FAQ.aspx\">Obter ajuda<span class=\"iconfont4 icon-xiangzuo gt\"></span></a>\r\n                                    </div>\r\n                                </div>\r\n                            </div>\r\n                            <div class=\"bg-white prdocu-sub-right\">\r\n                                <div>\r\n                                    <h2 class=\"text-base-color h6\" style=\"margin: inherit;\">Central de Ajuda</h2>\r\n                                    <div class=\"mt-4 d-flex flex-wrap\">\r\n                                        <a href=\"FAQ.aspx\" class=\"pro-item color-default\">\r\n                                            <div class=\"letter-wrap\">\r\n                                                <h3 class=\"h6\">Perguntas frequentes</h3>\r\n                                                <span class=\"color-gray fn14\">Responda rapidamente a perguntas comuns dos usuários e forneça guias de uso detalhados e suporte técnico</span>\r\n                                            </div>\r\n                                        </a>\r\n                                        <a href=\"AboutUs.aspx\" class=\"pro-item color-default\">\r\n                                            <div class=\"letter-wrap\">\r\n                                                <h3 class=\"h6\">Quem somos</h3>\r\n                                                <span class=\"color-gray fn14\">Saiba mais sobre o histórico de desenvolvimento, as principais funções e os conceitos de serviço do assistente de reconhecimento de texto OCR</span>\r\n                                            </div>\r\n                                        </a>\r\n                                        <a href=\"UserAgreement.aspx\" class=\"pro-item color-default\">\r\n                                            <div class=\"letter-wrap\">\r\n                                                <h3 class=\"h6\">Contrato do Usuário</h3>\r\n                                                <span class=\"color-gray fn14\">Termos de serviço detalhados e direitos e obrigações do usuário</span>\r\n                                            </div>\r\n                                        </a>\r\n                                        <a href=\"PrivacyPolicy.aspx\" class=\"pro-item color-default\">\r\n                                            <div class=\"letter-wrap\">\r\n                                                <h3 class=\"h6\">Acordo de Privacidade</h3>\r\n                                                <span class=\"color-gray fn14\">Política de proteção de informações pessoais e medidas de segurança de dados</span>\r\n                                            </div>\r\n                                        </a>\r\n                                        <a href=\"Status.aspx\" class=\"pro-item color-default\">\r\n                                            <div class=\"letter-wrap\">\r\n                                                <h3 class=\"h6\">Status do sistema</h3>\r\n                                                <span class=\"color-gray fn14\">Monitore o status de operação dos nós de identificação global em tempo real e visualize os dados de desempenho do sistema</span>\r\n                                            </div>\r\n                                        </a>\r\n                                        <a href=\"javascript:void(0);\" class=\"pro-item color-default\" onclick=\"if(window.openCustomerService) window.openCustomerService(); else alert('Clique no ícone da janela flutuante à direita para entrar em contato com o atendimento ao cliente');\">\r\n                                            <div class=\"letter-wrap\">\r\n                                                <h3 class=\"h6\">Entre em contato com o atendimento ao cliente</h3>\r\n                                                <span class=\"color-gray fn14\">Suporte de atendimento ao cliente online para responder rapidamente às suas perguntas e necessidades</span>\r\n                                            </div>\r\n                                        </a>\r\n                                    </div>\r\n                                </div>\r\n                            </div>\r\n                        </div>\r\n                    </section>\r\n                </div>\r\n            </div>\r\n            <div class=\"header_pc_right\" style=\"display: flex; align-items: center; justify-content: flex-end;\">\r\n                <div id=\"translate\"></div>\r\n                <div id=\"user-login-container\" data-url=\"User.ashx?op=userinfo\" style=\"white-space: nowrap; padding-right: 20px;\"></div>\r\n            </div>\r\n        </header>\r\n        <header class=\"v4_header_mob\">\r\n            <div class=\"container\">\r\n                <div class=\"row no-gutters justify-content-between align-items-center\">\r\n                    <a href=\"Default.aspx\" title=\"Início | Reconhecimento de texto inteligente AI\" class=\"mob-logo-wrap\">\r\n                        <picture>\r\n                            <source srcset=\"site/image/logo-s.png.webp\" type=\"image/webp\" />\r\n                            <img src=\"site/image/logo-s.png\" class=\"pc-logo\" style=\"height: 32px; width: 32px\" alt=\"Logotipo móvel do assistente de reconhecimento de texto OCR\" />\r\n                        </picture>\r\n                        <span style=\"font-size: 18px; font-weight: bold; color: #333;\">Assistente de reconhecimento de texto OCR</span>\r\n                    </a>\r\n                    <div class=\"right-menu\" role=\"button\" tabindex=\"0\" aria-label=\"Abra o menu de navegação\" data-toggle=\"collapse\" data-target=\"#mob-header-collapse\" aria-expanded=\"false\" aria-controls=\"mob-header-collapse\">\r\n                        <nav-products id=\"nav_products\">\r\n                            <nav-button class=\"submail-mdi-nav-btn\" style=\"width: 60px\">\r\n                                <label id=\"mdi-submail-nav-btn\">\r\n                                    <span class=\"mdi-nav-products-1\">\r\n                                        <span class=\"nav-products-icon\"></span>\r\n                                    </span>\r\n                                    <span class=\"mdi-nav-products-2\">\r\n                                        <span class=\"nav-products-icon\"></span>\r\n                                    </span>\r\n                                    <span class=\"mdi-nav-products-3\">\r\n                                        <span class=\"nav-products-icon\"></span>\r\n                                    </span>\r\n                                </label>\r\n                            </nav-button>\r\n                        </nav-products>\r\n                    </div>\r\n                </div>\r\n            </div>\r\n            <div class=\"mob-nav-content\">\r\n                <div class=\"sidebar-fix\">\r\n                    <div class=\"container\">\r\n                        <div class=\"row\">\r\n                            <div class=\"col-4 bg-white sidebar-fix-left\" style=\"border-right: 1px solid #e1e6ed\">\r\n                                <div class=\"mob-nav-item active\" data=\"0\">\r\n                                    <div class=\"nav-header\"><a href=\"Default.aspx\" class=\"nav-header-letter px-3\"><span>Casa</span></a></div>\r\n                                </div>\r\n                                <div class=\"mob-nav-item\" data=\"1\">\r\n                                    <div class=\"nav-header\"><a href=\"Detail.aspx\" class=\"nav-header-letter px-3\"><span>função</span></a></div>\r\n                                </div>\r\n                                <div class=\"mob-nav-item\" data=\"2\">\r\n                                    <div class=\"nav-header\"><a href=\"Ocr.aspx\" class=\"nav-header-letter px-3\"><span>experiência</span></a></div>\r\n                                </div>\r\n                                <div class=\"mob-nav-item\" data=\"3\">\r\n                                    <div class=\"nav-header\"><a href=\"Version.aspx\" class=\"nav-header-letter px-3\"><span>membro</span></a></div>\r\n                                </div>\r\n                                <div class=\"mob-nav-item\" data=\"4\">\r\n                                    <div class=\"nav-header\"><a href=\"Download.aspx\" class=\"nav-header-letter px-3\"><span>Baixar</span></a></div>\r\n                                </div>\r\n                                <div class=\"mob-nav-item\" data=\"5\">\r\n                                    <div class=\"nav-header\"><a href=\"Articles.aspx\" class=\"nav-header-letter px-3\"><span>Compartilhar</span></a></div>\r\n                                </div>\r\n                                <div class=\"mob-nav-item\" data=\"6\">\r\n                                    <div class=\"nav-header\"><a href=\"FAQ.aspx\" class=\"nav-header-letter px-3\"><span>Ajuda</span></a></div>\r\n                                </div>\r\n                            </div>\r\n                            <div class=\"col-8 bg-white sidebar-fix-rigth\">\r\n                                <div class=\"sub-nav\">\r\n                                    <div class=\"sub-nav-item py-2 pb-3 px-4\">\r\n                                        <h2 class=\"h5\" style=\"margin: inherit;\">Ferramentas de produtividade eficientes</h2>\r\n                                        <p class=\"color-gray fn14 mt-3 sub-left-desc\">Reconhecimento inteligente, processamento de alta velocidade e saída precisa</p>\r\n                                        <p class=\"color-gray fn14 mt-3 sub-left-desc\">&nbsp;</p>\r\n                                        <p class=\"color-gray fn14 mt-3 sub-left-desc\">Reconheça uma página inteira de documentos em 3 segundos</p>\r\n                                        <p class=\"color-gray fn14 mt-3 sub-left-desc\">98%+ precisão de reconhecimento</p>\r\n                                        <p class=\"color-gray fn14 mt-3 sub-left-desc\">Processamento multilíngue em tempo real sem demora</p>\r\n                                        <a class=\"link block disa fn14 contact-business shake\" href=\"Download.aspx\">Baixe a experiência agora<span class=\"iconfont4 icon-xiangzuo gt\"></span></a>\r\n                                    </div>\r\n                                </div>\r\n                                <div class=\"sub-nav\">\r\n                                    <div class=\"sub-nav-item py-3 px-4\">\r\n                                        <h2 class=\"h5\" style=\"margin: inherit;\">Características do produto:</h2>\r\n                                        <p class=\"color-gray fn14 mt-3 sub-left-desc\">Identificação inteligente de IA, solução completa</p>\r\n                                    </div>\r\n                                    <div class=\"sub-nav-item py-3 px-4\"><a href=\"Detail.aspx\">Introdução da função</a></div>\r\n                                    <div class=\"sub-nav-item py-3 px-4\"><a href=\"Download.aspx\">Download de software</a></div>\r\n                                    <div class=\"sub-nav-item py-3 px-4\"><a href=\"Version.aspx\">Comparação de versões</a></div>\r\n                                    <div class=\"sub-nav-item py-3 px-4\"><a href=\"Ocr.aspx\">Experiência online</a></div>\r\n                                    <div class=\"sub-nav-item py-3 px-4\"><a href=\"Status.aspx\">Status do sistema</a></div>\r\n                                </div>\r\n                                <div class=\"sub-nav\">\r\n                                    <div class=\"sub-nav-item py-3 px-4\">\r\n                                        <h2 class=\"h5\" style=\"margin: inherit;\">Experiência online</h2>\r\n                                        <p class=\"color-gray fn14 mt-3 sub-left-desc\">Experiência online gratuita com a função OCR</p>\r\n                                    </div>\r\n                                    <div class=\"sub-nav-item py-3 px-4\"><a href=\"Ocr.aspx\">Funcionalidade completa</a></div>\r\n                                    <div class=\"sub-nav-item py-3 px-4\"><a href=\"Ocr.aspx?type=text_recognize\">Reconhecimento de palavras</a></div>\r\n                                    <div class=\"sub-nav-item py-3 px-4\"><a href=\"Ocr.aspx?type=table\">Identificação da tabela</a></div>\r\n                                    <div class=\"sub-nav-item py-3 px-4\"><a href=\"Ocr.aspx?type=pdf2word\">PDF para Word</a></div>\r\n                                </div>\r\n                                <div class=\"sub-nav\">\r\n                                    <div class=\"sub-nav-item py-3 px-4\">\r\n                                        <h2 class=\"h5\" style=\"margin: inherit;\">Upgrades de associação</h2>\r\n                                        <p class=\"color-gray fn14 mt-3 sub-left-desc\">Desbloqueie todos os recursos e desfrute de serviços exclusivos</p>\r\n                                    </div>\r\n                                    <div class=\"sub-nav-item py-3 px-4\"><a href=\"Version.aspx\">Benefícios da associação</a></div>\r\n                                    <div class=\"sub-nav-item py-3 px-4\"><a href=\"Upgrade.aspx\">Ative imediatamente</a></div>\r\n                                </div>\r\n                                <div class=\"sub-nav\">\r\n                                    <div class=\"sub-nav-item py-3 px-4\">\r\n                                        <h2 class=\"h5\" style=\"margin: inherit;\">Download de software</h2>\r\n                                        <p class=\"color-gray fn14 mt-3 sub-left-desc\">Baixe o software profissional de OCR gratuitamente</p>\r\n                                    </div>\r\n                                    <div class=\"sub-nav-item py-3 px-4\"><a href=\"Download.aspx\">Faça o download agora</a></div>\r\n                                    <div class=\"sub-nav-item py-3 px-4\"><a href=\"Version.aspx\">Comparação de versões</a></div>\r\n                                </div>\r\n                                <div class=\"sub-nav\">\r\n                                    <div class=\"sub-nav-item py-3 px-4\">\r\n                                        <h2 class=\"h5\" style=\"margin: inherit;\">Compartilhamento de tecnologia</h2>\r\n                                        <p class=\"color-gray fn14 mt-3 sub-left-desc\">Artigos técnicos de OCR e compartilhamento de conhecimento</p>\r\n                                    </div>\r\n                                    <div class=\"sub-nav-item py-3 px-4\"><a href=\"Articles.aspx\">Todos os artigos</a></div>\r\n                                    \r\n                                    <div class=\"sub-nav-item py-3 px-4\"><a href=\"Articles.aspx?category=2\">Guia Avançado</a></div>\r\n                                    \r\n                                    <div class=\"sub-nav-item py-3 px-4\"><a href=\"Articles.aspx?category=3\">Exploração tecnológica</a></div>\r\n                                    \r\n                                    <div class=\"sub-nav-item py-3 px-4\"><a href=\"Articles.aspx?category=4\">Tendências da indústria</a></div>\r\n                                    \r\n                                    <div class=\"sub-nav-item py-3 px-4\"><a href=\"Articles.aspx?category=5\">Casos de uso:</a></div>\r\n                                    \r\n                                    <div class=\"sub-nav-item py-3 px-4\"><a href=\"Articles.aspx?category=6\">Revisão da ferramenta</a></div>\r\n                                    \r\n                                </div>\r\n                                <div class=\"sub-nav\">\r\n                                    <div class=\"sub-nav-item py-3 px-4\">\r\n                                        <h2 class=\"h5\" style=\"margin: inherit;\">Central de Ajuda</h2>\r\n                                        <p class=\"color-gray fn14 mt-3 sub-left-desc\">Atendimento ao cliente profissional, serviço íntimo</p>\r\n                                    </div>\r\n                                    <div class=\"sub-nav-item py-3 px-4\"><a href=\"FAQ.aspx\">Usar ajuda</a></div>\r\n                                    <div class=\"sub-nav-item py-3 px-4\"><a href=\"AboutUs.aspx\">Quem somos</a></div>\r\n                                    <div class=\"sub-nav-item py-3 px-4\"><a href=\"mailto:<EMAIL>\">Entre em contato com o atendimento ao cliente</a></div>\r\n                                    <div class=\"sub-nav-item py-3 px-4\"><a href=\"UserAgreement.aspx\">Termos de Serviço</a></div>\r\n                                </div>\r\n                            </div>\r\n                        </div>\r\n                    </div>\r\n                </div>\r\n            </div>\r\n        </header>\r\n        <form method=\"post\" action=\"./Article.aspx?id=81&amp;slug=rnn-sequence-modeling&amp;lang=zh-Hans&amp;_translation_source=true\" id=\"aspnetForm\">\r\n<div class=\"aspNetHidden\">\r\n<input type=\"hidden\" name=\"__VIEWSTATE\" id=\"__VIEWSTATE\" value=\"Q9uUeUlUJtaRG68B9ZsvqxMA0nWMrG97wrg6XH6pF/PALpTNvdWvXETFldXabtYfiX/3jguNbdKAEx4O02Rz9ETQIoNHodxI3/t/Hx/oGKM=\" />\r\n</div>\r\n\r\n<div class=\"aspNetHidden\">\r\n\r\n\t<input type=\"hidden\" name=\"__VIEWSTATEGENERATOR\" id=\"__VIEWSTATEGENERATOR\" value=\"2173C2F0\" />\r\n</div>\r\n            <div>\r\n                \n\n<div class=\"reading-progress\" id=\"readingProgress\"></div>\n<div class=\"article-detail-container\">\n<div class=\"container\">\n<div class=\"row\">\n<div class=\"col-lg-8 col-xl-9 col-md-12\">\n                    \n                    <article class=\"article-detail\" id=\"articleDetail\" data-article-id=\"81\">\n                        <header class=\"article-header\">\n                            <h1 class=\"article-title\">【Série OCR de Aprendizado Profundo·4】Redes Neurais Recorrentes e Modelagem de Sequência</h1>\n                            <div class=\"article-meta\">\n                                <div class=\"meta-item\">\n                                    <span class=\"meta-icon\">📅</span>\n                                    <span>Horário da postagem: 2025-08-19</span>\n                                </div>\n                                <div class=\"meta-item\">\n                                    <span class=\"meta-icon\">👁️</span>\n                                    <span>Leitura:<span class=\"view-count\">1200</span></span>\n                                </div>\n                                <div class=\"meta-item\">\n                                    <span class=\"meta-icon\">⏱️</span>\n                                    <span>Aprox. 50 minutos (9819 palavras)</span>\n                                </div>\n                                \n                                <div class=\"meta-item\">\n                                    <span class=\"meta-icon\">📁</span>\n                                    <span>Categoria: Guias Avançados</span>\n                                </div>\n                                \n                            </div>\n                            <div class=\"article-summary\"><p>Mergulhe na aplicação de RNN, LSTM, GRU em OCR. Análise detalhada dos princípios da modelagem de sequências, soluções para problemas de gradiente e as vantagens dos RNNs bidirecionais.</p></div>\n                        </header>\n                        <div class=\"article-content\" id=\"articleContent\">## Introdução\r\n\r\nA Rede Neural Recorrente (RNN) é uma arquitetura de rede neural em aprendizado profundo especializada no processamento de dados de sequência. Em tarefas de OCR, o reconhecimento de texto é essencialmente um problema de conversão de sequência para sequência: converter uma sequência de recursos de imagem em uma sequência de caracteres de texto. Este artigo se aprofundará em como funciona o RNN, suas principais variantes e suas aplicações específicas em OCR, fornecendo aos leitores uma base teórica abrangente e orientação prática.\r\n\r\n## Fundamentos da RNN\r\n\r\n### Limitações das redes neurais tradicionais\r\n\r\nAs redes neurais feedforward tradicionais têm limitações fundamentais no processamento de dados de sequência. Essas redes pressupõem que os dados de entrada são independentes e homodistribuídos e não podem capturar as dependências temporais entre os elementos na sequência.\r\n\r\n**Problemas de rede feedforward**:\r\n- Comprimento fixo de entrada e saída: sequências de comprimento variável não podem ser tratadas\r\n- Falta de capacidade de memória: Incapacidade de usar informações históricas\r\n- Dificuldade no compartilhamento de parâmetros: O mesmo padrão precisa ser aprendido repetidamente em locais diferentes\r\n- Sensibilidade posicional: Alterar a ordem das entradas pode levar a saídas completamente diferentes\r\n\r\nEssas limitações são particularmente perceptíveis em tarefas de OCR. As sequências de texto são altamente dependentes do contexto e os resultados de reconhecimento do caractere anterior geralmente ajudam a determinar a probabilidade de caracteres subsequentes. Por exemplo, ao identificar a palavra inglesa \"the\", se \"th\" já estiver reconhecido, o próximo caractere provavelmente será \"e\".\r\n\r\n### A ideia central da RNN\r\n\r\nO RNN resolve o problema da modelagem de sequências introduzindo junções de loop. A ideia central é adicionar um mecanismo de \"memória\" à rede, para que a rede possa armazenar e utilizar informações de momentos anteriores.\r\n\r\n**Representação Matemática do RNN**:\r\nNo momento t, o estado oculto da RNN h_t determinado pelo x_t de entrada atual e pelo estado oculto do momento anterior h_{t-1}:\r\n\r\nh_t = f(W_hh * h_{t-1} + W_xh * x_t + b_h)\r\n\r\nNisso:\r\n- W_hh é a matriz de peso do estado oculto para o estado oculto\r\n- W_xh é a matriz de peso inserida no estado oculto  \r\n- b_h é um vetor de viés\r\n- f é a função de ativação (geralmente tanh ou ReLU)\r\n\r\nO y_t de saída é calculado a partir do estado oculto atual:\r\ny_t = W_hy * h_t + b_y\r\n\r\n**Vantagens dos RNNs**:\r\n- Compartilhamento de parâmetros: os mesmos pesos são compartilhados em todas as etapas de tempo\r\n- Processamento de sequência de comprimento variável: Pode lidar com sequências de entrada de comprimento arbitrário\r\n- Capacidade de memória: estados ocultos atuam como \"memórias\" da rede\r\n- Entrada e saída flexíveis: suporta os modos um-para-um, um-para-muitos, muitos-para-um, muitos-para-muitos e muito mais\r\n\r\n### Visualização expandida do RNN\r\n\r\nPara entender melhor como funcionam as RNNs, podemos expandi-las na dimensão temporal. O RNN expandido parece uma rede feedforward profunda, mas todos os timesteps compartilham os mesmos parâmetros.\r\n\r\n** O significado do tempo se desdobrando **:\r\n- Fluxo de informações fácil de entender: é possível ver claramente como as informações são passadas entre as etapas de tempo\r\n- Cálculo de gradiente: Os gradientes são calculados por meio do algoritmo Time Backpropagation (BPTT)\r\n- Considerações de paralelização: Embora as RNNs sejam inerentemente sequenciais, certas operações podem ser paralelizadas\r\n\r\n**Descrição matemática do processo de desdobramento**:\r\nPara sequências de comprimento T, o RNN se expande da seguinte forma:\r\nh_1 = f(W_xh * x_1 + b_h)\r\nh_2 = f(W_hh * h_1 + W_xh * x_2 + b_h)\r\nh_3 = f(W_hh * h_2 + W_xh * x_3 + b_h)\r\n...\r\nh_T = f(W_hh * h_{T-1} + W_xh * x_T + b_h)\r\n\r\nEste formulário desdobrado mostra claramente como as informações são passadas entre as etapas de tempo e como os parâmetros são compartilhados em todas as etapas de tempo.\r\n\r\n## Desaparecimento do gradiente e problema de explosão\r\n\r\n### A raiz do problema\r\n\r\nAo treinar RNNs, usamos o algoritmo Backpropagation Through Time (BPTT). O algoritmo precisa calcular o gradiente da função de perda para cada parâmetro de passo de tempo.\r\n\r\n**Lei da Cadeia para Cálculo de Gradiente**:\r\nQuando a sequência é longa, o gradiente precisa ser retropropagado por meio de várias etapas de tempo. De acordo com a regra da cadeia, um gradiente conterá várias multiplicações da matriz de peso:\r\n\r\n∂L/∂W = Σ_t (∂L/∂y_t) * (∂y_t/∂h_t) * (∂h_t/∂W)\r\n\r\nonde ∂h_t/∂W envolve o produto de todos os estados intermediários do momento t ao momento 1.\r\n\r\n**Análise Matemática do Desaparecimento do Gradiente**:\r\nConsidere a propagação de gradientes entre etapas de tempo:\r\n∂h_t/∂h_{t-1} = diag(f_prime(W_hh * h_{t-1} + W_xh * x_t + b_h)) * W_hh\r\n\r\nQuando o comprimento da sequência é T, o gradiente contém T-1 tal termo de produto. Se o autovalor máximo do W_hh for menor que 1, a multiplicação contínua da matriz causará decaimento exponencial do gradiente.\r\n\r\n**Análise matemática de explosões de gradiente**:\r\nPor outro lado, quando o autovalor máximo do W_hh é maior que 1, o gradiente aumenta exponencialmente:\r\n|| ∂h_t/∂h_1|| ≈ || W_hh|| ^{t-1}\r\n\r\nIsso leva a um treinamento instável e atualizações excessivas de parâmetros.\r\n\r\n### Explicação detalhada da solução\r\n\r\nRecorte de gradiente:\r\nO recorte de gradiente é a maneira mais direta de resolver explosões de gradiente. Quando a norma de gradiente excede um limite definido, o gradiente é dimensionado para o tamanho do limite. Este método é simples e eficaz, mas requer uma seleção cuidadosa de limites. Um limite muito pequeno limitará a capacidade de aprendizado e um limite muito grande não impedirá efetivamente a explosão do gradiente.\r\n\r\n**Estratégia de inicialização de peso**:\r\nA inicialização de peso adequada pode aliviar problemas de gradiente:\r\n- Inicialização do Xavier: A variação de peso é 1/n, em que n é a dimensão de entrada\r\n- Inicialização He: A variação de peso é 2/n, o que é adequado para funções de ativação de ReLU\r\n- Inicialização ortogonal: inicializa a matriz de peso como uma matriz ortogonal\r\n\r\n**Seleção de funções de ativação**:\r\nDiferentes funções de ativação têm efeitos diferentes na propagação do gradiente:\r\n- tanh: faixa de saída [-1,1], valor máximo de gradiente de 1\r\n- ReLU: pode aliviar o desaparecimento do gradiente, mas pode causar morte neuronal\r\n- ReLU com vazamento: Resolve o problema de morte neuronal do ReLU\r\n\r\n**Melhorias arquitetônicas**:\r\nA solução mais fundamental foi melhorar a arquitetura RNN, o que levou ao surgimento do LSTM e do GRU. Essas arquiteturas abordam gradientes por meio de mecanismos de bloqueio e projetos especializados de fluxo de informações.\r\n\r\n## LSTM: Rede de memória de longo prazo\r\n\r\n### Motivação de design para LSTM\r\n\r\nLSTM (Long Short-Term Memory) é uma variante RNN proposta por Hochreiter e Schmidhuber em 1997, projetada especificamente para resolver o problema de desaparecimento de gradiente e dificuldades de aprendizagem dependentes de longa distância.\r\n\r\n**Principais inovações da LSTM**:\r\n- Estado da célula: Serve como uma \"rodovia\" para informações, permitindo que as informações fluam diretamente entre as etapas de tempo\r\n- Mecanismo de gating: controle preciso sobre o fluxo de entrada, retenção e saída de informações\r\n- Mecanismos de memória dissociada: distingue entre memória de curto prazo (estado oculto) e memória de longo prazo (estado celular)\r\n\r\n**Como o LSTM resolve problemas de gradiente**:\r\nO LSTM atualiza o estado da célula por meio de operações aditivas em vez de multiplicativas, o que permite que os gradientes fluam mais facilmente para etapas de tempo anteriores. Fórmula atualizada para o estado da célula:\r\n\r\nC_t = f_t ⊙ C_{t-1} + i_t ⊙ C_tilde_t\r\n\r\nA adição em nível de elemento é usada aqui, evitando a multiplicação contínua de matrizes em RNNs tradicionais.\r\n\r\n### Explicação detalhada da arquitetura LSTM\r\n\r\nO LSTM contém três unidades de passagem e um estado de célula:\r\n\r\n**1. Esqueça o portão**:\r\nO portão do esquecimento decide quais informações descartar do estado da célula:\r\nf_t = σ(W_f · [h_{t-1}, x_t] + b_f)\r\n\r\nA saída da porta do esquecimento é um valor entre 0 e 1, com 0 sendo \"completamente esquecido\" e 1 sendo \"completamente retido\". Esta porta permite que o LSTM esqueça seletivamente informações históricas sem importância.\r\n\r\n**2. Porta de entrada**:\r\nA porta de entrada determina quais novas informações são armazenadas no estado da célula:\r\ni_t = σ(W_i · [h_{t-1}, x_t] + b_i)\r\nC_tilde_t = tanh(W_C · [h_{t-1}, x_t] + b_C)\r\n\r\nA porta de entrada consiste em duas partes: a camada sigmóide determina quais valores atualizar e a camada tanh cria vetores de valor candidatos.\r\n\r\n**3. Atualização do status da célula**:\r\nCombine as saídas da porta de esquecimento e da porta de entrada para atualizar o estado da célula:\r\nC_t = f_t ⊙ C_{t-1} + i_t ⊙ C_tilde_t\r\n\r\nEssa fórmula está no centro do LSTM: retenção seletiva e atualização de informações por meio de operações de multiplicação e adição em nível de elemento.\r\n\r\n**4. Porta de saída**:\r\nA porta de saída determina quais partes da célula são emitidas:\r\no_t = σ(W_o · [h_{t-1}, x_t] + b_o)\r\nh_t = o_t ⊙ tanh(C_t)\r\n\r\nA porta de saída controla quais partes do estado da célula afetam a saída atual.\r\n\r\n### Variantes LSTM\r\n\r\n**Olho mágico LSTM**:\r\nCom base no LSTM padrão, o Peephole LSTM permite que a unidade de gating visualize o estado da célula:\r\nf_t = σ(W_f · [C_{t-1}, h_{t-1}, x_t] + b_f)\r\ni_t = σ(W_i · [C_{t-1}, h_{t-1}, x_t] + b_i)\r\no_t = σ(W_o · [C_t, h_ {t-1}, x_t] + b_o)\r\n\r\n**LSTM acoplado**:\r\nAcople a porta de esquecimento com a porta de entrada para garantir que a quantidade de informações esquecidas seja igual à quantidade de informações inseridas:\r\nf_t = σ(W_f · [h_{t-1}, x_t] + b_f)\r\ni_t = 1 - f_t\r\n\r\nEsse design reduz o número de parâmetros, mantendo a funcionalidade principal do LSTM.\r\n\r\n## GRU: Unidade de loop fechado\r\n\r\n### Projeto simplificado do GRU\r\n\r\nGRU (Gated Recurrent Unit) é uma versão simplificada do LSTM proposto por Cho et al. em 2014. O GRU simplifica as três portas do LSTM para duas portas e mescla o estado celular e o estado oculto.\r\n\r\n**Filosofia de design da GRU**:\r\n- Estrutura simplificada: Reduz o número de portas e reduz a complexidade dos cálculos\r\n- Manter o desempenho: simplifique enquanto mantém o desempenho comparável ao LSTM\r\n- Fácil de implementar: A construção mais simples permite fácil implementação e comissionamento\r\n\r\n### Mecanismo de gating do GRU\r\n\r\n**1. Porta de reinicialização**:\r\nr_t = σ(W_r · [h_{t-1}, x_t] + b_r)\r\n\r\nA porta de reinicialização determina como combinar a nova entrada com a memória anterior. Quando a porta de reinicialização se aproxima de 0, o modelo ignora o estado oculto anterior.\r\n\r\n**2. Portão de atualização**:\r\nz_t = σ(W_z · [h_{t-1}, x_t] + b_z)\r\n\r\nO portão de atualização determina a quantidade de informações passadas a serem mantidas e a quantidade de informações novas a serem adicionadas. Ele controla o esquecimento e a entrada, semelhante à combinação de esquecimento e portas de entrada no LSTM.\r\n\r\n**3. Status oculto do candidato**:\r\nh_tilde_t = tanh(W_h · [r_t ⊙ h_{t-1}, x_t] + b_h)\r\n\r\nOs estados ocultos candidatos usam a porta de reinicialização para controlar os efeitos do estado oculto anterior.\r\n\r\n**4. Estado oculto final**:\r\nh_t = (1 - z_t) ⊙ h_{t-1} + z_t ⊙ h_tilde_t\r\n\r\nO estado oculto final é uma média ponderada do estado oculto anterior e do estado oculto candidato.\r\n\r\n### Comparação detalhada entre GRU e LSTM\r\n\r\n**Comparação do número de parâmetros**:\r\n- LSTM: 4 matrizes de peso (porta de esquecimento, porta de entrada, valor candidato, porta de saída)\r\n- GRU: 3 matrizes de peso (porta de reinicialização, porta de atualização, valor candidato)\r\n- O número de parâmetros do GRU é de aproximadamente 75% do LSTM\r\n\r\n**Comparação de complexidade computacional**:\r\n- LSTM: Requer cálculo de 4 saídas de porta e atualizações de estado da célula\r\n- GRU: Basta calcular a saída de 2 portas e atualizações de status ocultas\r\n- GRU é normalmente 20-30% mais rápido que LSTM\r\n\r\n**Comparação de desempenho**:\r\n- Na maioria das tarefas, GRU e LSTM têm desempenho comparável\r\n- LSTM pode ser um pouco melhor do que GRU em algumas tarefas de sequência longa\r\n- GRU é uma escolha melhor nos casos em que os recursos de computação são limitados\r\n\r\n## RNNs bidirecionais\r\n\r\n### A necessidade de processamento bidirecional\r\n\r\nEm muitas tarefas de modelagem de sequência, a saída do momento presente depende não apenas de informações passadas, mas também futuras. Isso é especialmente importante em tarefas de OCR, onde o reconhecimento de caracteres geralmente requer considerar o contexto de toda a palavra ou frase.\r\n\r\n**Limitações de RNNs unidirecionais**:\r\n- Apenas informações históricas podem ser usadas, nenhum contexto futuro pode ser obtido\r\n- Desempenho limitado em determinadas tarefas, especialmente aquelas que exigem informações globais\r\n- Reconhecimento limitado de caracteres ambíguos\r\n\r\n**Vantagens do processamento bidirecional**:\r\n- Informações contextuais completas: aproveite as informações passadas e futuras\r\n- Melhor desambiguação: Desambiguação com informações contextuais\r\n- Precisão de reconhecimento aprimorada: melhor desempenho na maioria das tarefas de anotação de sequência\r\n\r\n### Arquitetura LSTM bidirecional\r\n\r\nO LSTM bidirecional consiste em duas camadas LSTM:\r\n- Forward LSTM: Processe sequências da esquerda para a direita\r\n- LSTM para trás: Processe sequências da direita para a esquerda\r\n\r\n**Representação Matemática**:\r\nh_forward_t = LSTM_forward(x_t, h_forward_{t-1})\r\nh_backward_t = LSTM_backward(x_t, h_backward_{t+1})\r\nh_t = [h_forward_t; h_backward_t] # Costurando estados ocultos para frente e para trás\r\n\r\n**Processo de treinamento**:\r\n1. O LSTM encaminha sequências de processos em ordem normal\r\n2. O LSTM reverso processa as sequências em ordem inversa\r\n3. Em cada etapa de tempo, conecte os estados ocultos em ambas as direções\r\n4. Use o estado emendado para previsão\r\n\r\n**Vantagens e desvantagens**:\r\nVantagem:\r\n- Informações contextuais completas\r\n- Melhor desempenho\r\n- Tratamento de simetria\r\n\r\nPosição inferior:\r\n- Dobre a complexidade dos cálculos\r\n- Não pode ser processado em tempo real (requer sequência completa)\r\n- Aumento dos requisitos de memória\r\n\r\n## Aplicativos de modelagem de sequência em OCR\r\n\r\n### Explicação detalhada do reconhecimento de linha de texto\r\n\r\nEm sistemas OCR, o reconhecimento de linha de texto é uma aplicação típica da modelagem de sequência. Este processo envolve a conversão de uma sequência de feições de imagem em uma sequência de caracteres.\r\n\r\n**Modelagem de Problemas**:\r\n- Entrada: Sequência de recursos de imagem X = {x_1, x_2, ..., x_T}\r\n- Saída: Sequência de caracteres Y = {y_1, y_2, ..., y_S}\r\n- Desafio: O comprimento da sequência de entrada T e o comprimento da sequência de saída S geralmente não são iguais\r\n\r\n**Aplicação da arquitetura CRNN no reconhecimento de linha de texto**:\r\nCRNN (Rede Neural Recorrente Convolucional) é uma das arquiteturas de maior sucesso em OCR:\r\n\r\n1. **Camada de extração de recursos CNN**:\r\n   - Extraia recursos de imagem usando redes neurais convolucionais\r\n   - Converta recursos de imagem 2D em sequências de recursos 1D\r\n   - Manter a continuidade das informações de tempo\r\n\r\n2. **Camada de modelagem de sequência RNN**:\r\n   - Modele sequências de recursos usando LSTMs bidirecionais\r\n   - Capture dependências contextuais entre caracteres\r\n   - Distribuição de probabilidade de caracteres de saída para cada intervalo de tempo\r\n\r\n3. **Camada de alinhamento CTC**:\r\n   - Soluciona incompatibilidades de comprimento de sequência de entrada/saída\r\n   - Não são necessárias dimensões de alinhamento no nível do caractere\r\n   - Treinamento de ponta a ponta\r\n\r\n**Conversão de extração de recursos em sequência**:\r\nO mapa de recursos extraído pela CNN precisa ser convertido em uma forma de sequência que a RNN possa processar:\r\n- Segmente o mapa de recursos em colunas, com cada coluna como um intervalo de tempo\r\n- Manter a cronologia da informação espacial\r\n- Certifique-se de que o comprimento da sequência de recursos seja proporcional à largura da imagem\r\n\r\n### Aplicação do mecanismo de atenção em OCR\r\n\r\nOs RNNs tradicionais ainda têm gargalos de informação ao lidar com sequências longas. A introdução de mecanismos de atenção aumenta ainda mais as capacidades da modelagem de sequências.\r\n\r\n**Princípios dos Mecanismos de Atenção**:\r\nO mecanismo de atenção permite que o modelo se concentre em diferentes partes da sequência de entrada ao gerar cada saída:\r\n- Resolvido o gargalo de informações de vetores codificados de comprimento fixo\r\n- Fornece explicabilidade das decisões do modelo\r\n- Processamento aprimorado de sequências longas\r\n\r\n**Aplicações específicas em OCR**:\r\n\r\n1. **Atenção no nível do personagem**:\r\n   - Concentre-se em áreas relevantes da imagem ao identificar cada personagem\r\n   - Ajuste os pesos de atenção em tempo real\r\n   - Melhore a robustez para fundos complexos\r\n\r\n2. **Atenção em nível de palavra**:\r\n   - Considere informações contextuais no nível do vocabulário\r\n   - Aproveite o conhecimento do modelo de linguagem\r\n   - Melhorar a precisão do reconhecimento de palavras inteiras\r\n\r\n3. **Atenção em várias escalas**:\r\n   - Aplicação de mecanismos de atenção em diferentes resoluções\r\n   - Lidar com texto de tamanhos diferentes\r\n   - Melhorar a adaptabilidade às mudanças de escala\r\n\r\n**Representação matemática do mecanismo de atenção**:\r\nPara a sequência de saída do codificador H = {h_1, h_2, ..., h_T} e o estado do decodificador s_t:\r\n\r\ne_{t,i} = a(s_t, h_i) # Pontuação de atenção\r\nα_{t,i} = softmax(e_{t,i}) # Peso de atenção\r\nc_t = Σ_i α_{t,i} * h_i # vetor de contexto\r\n\r\n## Estratégias de treinamento e otimização\r\n\r\n### Estratégia de treinamento sequência a sequência\r\n\r\n**Forçar o professor**:\r\nDurante a fase de treinamento, use a sequência de destino real como entrada do decodificador:\r\n- Prós: velocidade de treinamento rápida, convergência estável\r\n- Contras: Fases de treinamento e inferência inconsistentes, levando ao acúmulo de erros\r\n\r\n**Amostragem Programada**:\r\nFaça a transição gradual da força do professor para o uso das próprias previsões do modelo durante o treinamento:\r\n- Use rótulos reais no estágio inicial e previsões de modelo nos estágios posteriores\r\n- Reduzir as diferenças de treinamento e raciocínio\r\n- Melhorar a robustez do modelo\r\n\r\n**Aprendizagem Curricular**:\r\nComece com amostras simples e aumente gradualmente a complexidade das amostras:\r\n- Sequências curtas a longas: treine textos curtos primeiro, depois textos longos\r\n- Imagens claras a desfocadas: aumente gradualmente a complexidade da imagem\r\n- Fontes simples a complexas: do impresso à caligrafia\r\n\r\n### Técnicas de Regularização\r\n\r\n**Aplicação de Dropout em RNN**:\r\nA aplicação do dropout no RNN requer atenção especial:\r\n- Não aplique dropout em conexões de loop\r\n- O dropout pode ser aplicado nas camadas de entrada e saída\r\n- Abandono variacional: use a mesma máscara de abandono em todas as etapas de tempo\r\n\r\n**Perda de peso**:\r\nA regularização L2 evita o sobreajuste:\r\nPerda = CrossEntropy + λ * || W|| ²\r\n\r\nonde λ é o coeficiente de regularização, que precisa ser otimizado pelo conjunto de validação.\r\n\r\n**Corte de gradiente**:\r\nUma maneira eficaz de evitar explosões de gradiente. Quando a norma de gradiente exceder o limite, dimensione o gradiente proporcionalmente para manter a direção do gradiente inalterada.\r\n\r\n**Parada precoce**:\r\nMonitore o desempenho do conjunto de validação e interrompa o treinamento quando o desempenho não estiver mais melhorando:\r\n- Evite o sobreajuste\r\n- Economize recursos de computação\r\n- Selecione o modelo ideal\r\n\r\n### Ajuste de hiperparâmetros\r\n\r\n**Programação da taxa de aprendizado**:\r\n- Taxa de aprendizado inicial: Normalmente definida em 0,001-0,01\r\n- Decadência da taxa de aprendizado: decaimento exponencial ou decaimento da escada\r\n- Taxa de aprendizado adaptável: use otimizadores como Adam, RMSprop, etc.\r\n\r\n**Seleção do tamanho do lote**:\r\n- Pequenos lotes: Melhor desempenho de generalização, mas maior tempo de treinamento\r\n- Alto volume: o treinamento é rápido, mas pode afetar a generalização\r\n- Tamanhos de lote entre 16-128 são geralmente selecionados\r\n\r\n**Processamento de comprimento de sequência**:\r\n- Comprimento fixo: trunque ou preencha sequências para comprimentos fixos\r\n- Comprimento dinâmico: use preenchimento e mascaramento para lidar com sequências de comprimento variável\r\n- Estratégia de ensacamento: agrupe sequências de comprimento semelhante\r\n\r\n## Avaliação e análise de desempenho\r\n\r\n### Avalie as métricas\r\n\r\n**Precisão no nível do personagem**:\r\nAccuracy_char = (Número de caracteres reconhecidos corretamente) / (Total de caracteres)\r\n\r\nEsse é o indicador de avaliação mais básico e reflete diretamente os recursos de reconhecimento de caracteres do modelo.\r\n\r\n**Precisão de nível serial**:\r\nAccuracy_seq = (Número de sequências reconhecidas corretamente) / (Número total de sequências)\r\n\r\nEste indicador é mais rigoroso e apenas uma sequência completamente correta é considerada correta.\r\n\r\n**Distância de edição (distância de Levenshtein)**:\r\nMeça a diferença entre as séries previstas e verdadeiras:\r\n- O número mínimo de operações de inserção, remoção e substituição\r\n- Distância de edição padronizada: distância de edição / comprimento da sequência\r\n- Pontuação BLEU: comumente usada em tradução automática e também pode ser usada para avaliação de OCR\r\n\r\n### Análise de erros\r\n\r\n**Tipos de erros comuns**:\r\n1. **Confusão de caracteres**: Identificação incorreta de caracteres semelhantes\r\n   - O número 0 e a letra O\r\n   - Número 1 e letra l\r\n   - Letras M e N\r\n\r\n2. **Erro de sequência**: Erro na ordem dos caracteres\r\n   - As posições dos caracteres são invertidas\r\n   - Duplicação ou omissão de caracteres\r\n\r\n3. **Erro de comprimento**: Erro na previsão do comprimento da sequência\r\n   - Muito longo: Caracteres inexistentes inseridos\r\n   - Muito curto: Personagens que estão presentes estão faltando\r\n\r\n**Método de análise**:\r\n1. **Matriz de confusão**: Analisa padrões de erro no nível do caractere\r\n2. **Visualização de atenção**: Entenda as preocupações do modelo\r\n3. **Análise de gradiente**: Verifique o fluxo do gradiente\r\n4. **Análise de ativação**: Observe os padrões de ativação nas camadas da rede\r\n\r\n### Diagnóstico de modelo\r\n\r\n**Detecção de sobreajuste**:\r\n- As perdas de treinamento continuam diminuindo, as perdas de validação aumentam\r\n- A precisão do treinamento é muito maior do que a precisão da validação\r\n- Solução: Aumentar a regularidade e reduzir a complexidade do modelo\r\n\r\n**Detecção de Underfit**:\r\n- As perdas de treinamento e validação são altas\r\n- O modelo não tem um bom desempenho no conjunto de treinamento\r\n- Solução: aumentar a complexidade do modelo e ajustar a taxa de aprendizado\r\n\r\n**Diagnóstico de Problema de Gradiente**:\r\n- Perda de gradiente: O valor do gradiente é muito pequeno, aprendizado lento\r\n- Explosão de gradiente: Valores excessivos de gradiente levam a um treinamento instável\r\n- Solução: Usando LSTM/GRU, corte de gradiente\r\n\r\n## Casos de aplicação do mundo real\r\n\r\n### Sistema de reconhecimento de caracteres manuscritos\r\n\r\n**Cenários de aplicação**:\r\n- Digitalize notas manuscritas: converta notas em papel em documentos eletrônicos\r\n- Preenchimento automático de formulários: reconhece automaticamente o conteúdo do formulário manuscrito\r\n- Identificação de documentos históricos: digitalize livros antigos e documentos históricos\r\n\r\n**Características técnicas**:\r\n- Grandes variações de caracteres: o texto manuscrito tem um alto grau de personalização\r\n- Processamento contínuo da caneta: as conexões entre os caracteres precisam ser tratadas\r\n- Contexto importante: utilize modelos de linguagem para melhorar o reconhecimento\r\n\r\n**Arquitetura do sistema**:\r\n1. **Módulo de pré-tratamento**:\r\n   - Redução de ruído e aprimoramento de imagem\r\n   - Correção de inclinação\r\n   - Divisão de linha de texto\r\n\r\n2. **Módulo de extração de recursos**:\r\n   - CNN extrai recursos visuais\r\n   - Fusão de recursos em várias escalas\r\n   - Serialização de recursos\r\n\r\n3. **Módulo de modelagem de sequência**:\r\n   - Modelagem LSTM bidirecional\r\n   - Mecanismos de atenção\r\n   - Codificação contextual\r\n\r\n4. **Módulo de decodificação**:\r\n   - Decodificação CTC ou decodificação de atenção\r\n   - Pós-processamento do modelo de linguagem\r\n   - Avaliação de confiança\r\n\r\n### Sistema de reconhecimento de documentos impressos\r\n\r\n**Cenários de aplicação**:\r\n- Digitalização de documentos: Convertendo documentos em papel em formatos editáveis\r\n- Reconhecimento de contas: processe automaticamente faturas, recibos e outras contas\r\n- Reconhecimento de sinalização: identifique sinais de trânsito, sinais de lojas e muito mais\r\n\r\n**Características técnicas**:\r\n- Fonte normal: mais regular do que texto manuscrito\r\n- Regras de tipografia: as informações de layout podem ser utilizadas\r\n- Requisitos de alta precisão: As aplicações comerciais têm requisitos rígidos de precisão\r\n\r\n**Estratégia de Otimização**:\r\n1. **Treinamento de várias fontes**: Usa dados de treinamento de várias fontes\r\n2. ** Aprimoramento de dados **: Gire, dimensione, adição de ruído\r\n3. **Otimização de pós-processamento**: verificação ortográfica, correção gramatical\r\n4. **Avaliação de confiança**: Fornece uma pontuação de confiabilidade para os resultados do reconhecimento\r\n\r\n### Sistema de reconhecimento de texto de cena\r\n\r\n**Cenários de aplicação**:\r\n- Reconhecimento de texto do Street View: reconhecimento de texto no Google Street View\r\n- Reconhecimento de rótulos de produtos: Identificação automática de produtos de supermercado\r\n- Reconhecimento de sinais de trânsito: aplicações de sistemas de transporte inteligentes\r\n\r\n**Desafios técnicos**:\r\n- Fundos complexos: o texto é incorporado em cenas naturais complexas\r\n- Deformação severa: Deformação em perspectiva, deformação por flexão\r\n- Requisitos em tempo real: os aplicativos móveis precisam ser responsivos\r\n\r\n**Solução**:\r\n1. **Extração robusta de recursos**: Usa redes CNN mais profundas\r\n2. **Processamento em várias escalas**: Manipule texto de tamanhos diferentes\r\n3. **Correção de geometria**: Corrige automaticamente deformações geométricas\r\n4. **Compressão de Modelos**: Otimize o modelo para dispositivos móveis\r\n\r\n## Resumo\r\n\r\nAs redes neurais recorrentes fornecem uma ferramenta poderosa para modelagem de sequência em OCR. De RNNs básicos a LSTMs e GRUs aprimorados a mecanismos de processamento e atenção bidirecionais, o desenvolvimento dessas tecnologias melhorou muito o desempenho dos sistemas de OCR.\r\n\r\n**Principais conclusões**:\r\n- RNNs implementam modelagem de sequência por meio de junções de loop, mas há um problema de desaparecimento de gradiente\r\n- LSTM e GRU resolvem o problema de aprendizagem dependente de longa distância por meio de mecanismos de gating\r\n- RNNs bidirecionais são capazes de aproveitar informações contextuais completas\r\n- Os mecanismos de atenção aumentam ainda mais a capacidade de modelagem de sequência\r\n- Estratégias de treinamento e técnicas de regularização apropriadas são cruciais para o desempenho do modelo\r\n\r\n**Direções de desenvolvimento futuro**:\r\n- Integração com arquiteturas Transformer\r\n- Abordagem mais eficiente para modelagem de sequência\r\n- Aprendizado multimodal de ponta a ponta\r\n- Equilíbrio entre tempo real e precisão\r\n\r\nÀ medida que a tecnologia continua a evoluir, as técnicas de modelagem de sequência ainda estão evoluindo. A experiência e a tecnologia acumuladas pelas RNNs e suas variantes no campo do OCR estabeleceram uma base sólida para entender e projetar métodos de modelagem de sequência mais avançados.</div>\n                        \n                        <div class=\"article-tags\">\n                            <h6>Etiqueta:</h6>\n                            <div class=\"tags-list\">\n                                \n                                <span class=\"tag\">RNN</span>\n                                \n                                <span class=\"tag\">LSTM</span>\n                                \n                                <span class=\"tag\">GRU</span>\n                                \n                                <span class=\"tag\">Modelagem de sequência</span>\n                                \n                                <span class=\"tag\">O gradiente desaparece</span>\n                                \n                                <span class=\"tag\">RNN bidirecional</span>\n                                \n                                <span class=\"tag\">Mecanismo de atenção</span>\n                                \n                                <span class=\"tag\">CRNN</span>\n                                \n                                <span class=\"tag\">OCR</span>\n                                \n                            </div>\n                        </div>\n                        \n                    </article>\n                    \n                    <footer class=\"article-footer\" id=\"articleFooter\">\n                        <div class=\"article-share\">\n                            <h6 style=\"color: #1a1c1f; margin-bottom: 1rem;\">Compartilhe e opere:</h6>\n                            <div class=\"share-buttons\">\n                                <a href=\"javascript:void(0)\" onclick=\"shareToWeibo()\" class=\"btn btn-weibo\">📱 Weibo compartilhou</a>\n                                <a href=\"javascript:void(0)\" onclick=\"copyUrl()\" class=\"btn btn-copy\">🔗 Copiar link</a>\n                                <button type=\"button\" class=\"btn btn-print\" onclick=\"printArticle()\">🖨️ Imprimir o artigo</button>\n                            </div>\n                        </div>\n                    </footer>\n                    \n                </div>\n                <div class=\"col-lg-4 col-xl-3 col-md-12\">\n                    <div class=\"sidebar\">\n                        <div class=\"sidebar-card toc-card\" id=\"tocCard\" style=\"display: none;\"><h5>Índice</h5><div id=\"tableOfContents\" class=\"toc-list\"></div></div>\n                        <div class=\"sidebar-card\">\n                            <h5>Leitura recomendada</h5>\n                            <div id=\"recommendedArticles\">\n                                \n                                <div class=\"recommended-item\"><h6><a href=\"Article.aspx?slug=document-intelligence-future-outlook\">【Série de Processamento Inteligente de Documentos·20】Perspectivas de desenvolvimento da tecnologia de processamento inteligente de documentos</a></h6><p class=\"text-muted small\">2025-08-19 • 1203 Próxima leitura</p></div>\n                                \n                                <div class=\"recommended-item\"><h6><a href=\"Article.aspx?slug=document-intelligence-quality-assurance\">【Série de Processamento Inteligente de Documentos·19】Sistema de Garantia de Qualidade de Processamento Inteligente de Documentos</a></h6><p class=\"text-muted small\">2025-08-19 • 1200 Próxima leitura</p></div>\n                                \n                                <div class=\"recommended-item\"><h6><a href=\"Article.aspx?slug=large-scale-document-processing-optimization\">【Série de processamento inteligente de documentos·18】 Otimização do desempenho do processamento de documentos em larga escala</a></h6><p class=\"text-muted small\">2025-08-19 • 1201 Próxima leitura</p></div>\n                                \n                            </div>\n                        </div>\n                    </div>\n</div>\n</div>\n</div>\n</div>\n<script>\nvar articleId,viewCountKey,now=new Date().getTime();\ndocument.addEventListener('DOMContentLoaded',function(){var articleDetail=document.getElementById('articleDetail');if(articleDetail&&articleDetail.querySelector('.article-content')){formatContent();initReadingProgress();optimizeImages();updateViewCount();}});\nfunction updateViewCount(){articleId=getArticleId();if(!articleId)return;viewCountKey='article_view_'+articleId;var lastViewTime=localStorage.getItem(viewCountKey);if(lastViewTime&&(now-parseInt(lastViewTime))<3600000)return;var xhr=new XMLHttpRequest();xhr.open('POST','/Article.ashx',true);xhr.setRequestHeader('Content-Type','application/x-www-form-urlencoded');xhr.onreadystatechange=function(){if(xhr.readyState===4&&xhr.status===200){try{var response=JSON.parse(xhr.responseText);if(response.success){updateViewCountDisplay(response.viewCount);localStorage.setItem(viewCountKey,now.toString());}}catch(e){}}};xhr.send('action=updateviewcount&articleId='+encodeURIComponent(articleId));}\nfunction getArticleId(){var articleDetail=document.getElementById('articleDetail');if(articleDetail){var id=articleDetail.getAttribute('data-article-id');if(id)return id;}var urlParams=new URLSearchParams(window.location.search);return urlParams.get('id');}\nfunction updateViewCountDisplay(newCount){document.querySelectorAll('.view-count').forEach(function(element){element.textContent=newCount;});}\nfunction decodeHtmlEntities(text){var textArea=document.createElement('textarea');textArea.innerHTML=text;return textArea.value;}\nfunction formatContent(){var contentDiv=document.getElementById('articleContent');if(!contentDiv)return;var content=contentDiv.textContent||contentDiv.innerText;if(!content)return;content=decodeHtmlEntities(content);content=formatMarkdownContent(content);content=formatIndentation(content);content=formatLists(content);content=formatParagraphs(content);content=formatLinks(content);contentDiv.innerHTML=content;initTableOfContents();}\nfunction formatIndentation(content){var lines=content.split('\\n'),result=[];for(var i=0;i<lines.length;i++){var line=lines[i];if(line.trim().match(/^<[^>]+>/)){result.push(line);continue;}line=line.replace(/^\\t+/g,function(match){return '    '.repeat(match.length);});if(line.match(/^  +[^\\s]/)){var indentMatch=line.match(/^( +)(.*)$/);if(indentMatch){var indentLevel=Math.floor(indentMatch[1].length/2);line='<span class=\"text-indent-'+indentLevel+'\">'+indentMatch[2]+'</span>';}}result.push(line);}return result.join('\\n');}\nfunction formatMarkdownContent(content){content=content.replace(/^\\s*##### (.+)$/gm,'<h5>$1</h5>').replace(/^\\s*#### (.+)$/gm,'<h4>$1</h4>').replace(/^\\s*### (.+)$/gm,'<h3>$1</h3>').replace(/^\\s*## (.+)$/gm,'<h2>$1</h2>').replace(/^\\s*# (.+)$/gm,'<h1>$1</h1>').replace(/^> (.+)$/gm,'<blockquote><p>$1</p></blockquote>').replace(/\\*\\*([^*]+?)\\*\\*/g,'<strong>$1</strong>').replace(/\\*([^*\\n]+?)\\*/g,'<em>$1</em>').replace(/`([^`]+?)`/g,'<code class=\"inline-code\">$1</code>').replace(/^(\\*\\*[^*]+?\\*\\*)：/gm,'<div class=\"label-item\"><span class=\"label\">$1</span></div>').replace(/^([^：\\n]+?)：$/gm,'<div class=\"section-label\">$1：</div>').replace(/^(\\*\\*[^*]+?\\*\\*)：(.+)$/gm,'<div class=\"definition-item\"><span class=\"definition-label\">$1：</span><span class=\"definition-content\">$2</span></div>').replace(/^(nota|nota|nota):(.+)$/gm,'<div class=\"note-item\"><span class=\"note-label\">$1：</span><span class=\"note-content\">$2</span></div>');content=formatLists(content);content=formatMathSymbols(content);content=formatParagraphs(content);return content;}\nfunction formatLists(content){var lines=content.split('\\n'),result=[],inOrderedList=false,inUnorderedList=false;for(var i=0;i<lines.length;i++){var line=lines[i],trimmed=line.trim();var orderedMatch=line.match(/^(\\d+)\\.\\s*(.+)$/);var unorderedMatch=line.match(/^[-*+]\\s*(.+)$/);if(orderedMatch){if(!inOrderedList){if(inUnorderedList){result.push('</ul>');inUnorderedList=false;}result.push('<ol class=\"formatted-list ordered\">');inOrderedList=true;}var listContent=orderedMatch[2].replace(/^\\*\\*([^*]+?)\\*\\*：(.+)$/,'<strong class=\"list-title\">$1：</strong><span class=\"list-content\">$2</span>');result.push('<li>'+listContent+'</li>');}else if(unorderedMatch){if(!inUnorderedList){if(inOrderedList){result.push('</ol>');inOrderedList=false;}result.push('<ul class=\"formatted-list unordered\">');inUnorderedList=true;}var listContent=unorderedMatch[1].replace(/^\\*\\*([^*]+?)\\*\\*：(.+)$/,'<strong class=\"list-title\">$1：</strong><span class=\"list-content\">$2</span>');result.push('<li>'+listContent+'</li>');}else{if(trimmed===''){if(i+1<lines.length){var nextLine=lines[i+1].trim();if(!nextLine.match(/^\\d+\\.\\s/)&&!nextLine.match(/^[-*+]\\s/)){if(inOrderedList){result.push('</ol>');inOrderedList=false;}if(inUnorderedList){result.push('</ul>');inUnorderedList=false;}}}result.push(line);}else{if(inOrderedList){result.push('</ol>');inOrderedList=false;}if(inUnorderedList){result.push('</ul>');inUnorderedList=false;}result.push(line);}}}if(inOrderedList)result.push('</ol>');if(inUnorderedList)result.push('</ul>');return result.join('\\n');}\nfunction formatMathSymbols(content){var mathSymbols={'α':'&alpha;','β':'&beta;','γ':'&gamma;','δ':'&delta;','ε':'&epsilon;','ζ':'&zeta;','η':'&eta;','θ':'&theta;','λ':'&lambda;','μ':'&mu;','π':'&pi;','σ':'&sigma;','∑':'&sum;','∏':'&prod;','∫':'&int;','∂':'&part;','∇':'&nabla;','∞':'&infin;','≤':'&le;','≥':'&ge;','≠':'&ne;','≈':'&asymp;','±':'&plusmn;','×':'&times;','÷':'&divide;','→':'&rarr;','←':'&larr;','↑':'&uarr;','↓':'&darr;','⇒':'&rArr;','⇐':'&lArr;'};var subMap={'₀':'0','₁':'1','₂':'2','₃':'3','₄':'4','₅':'5','₆':'6','₇':'7','₈':'8','₉':'9','ₖ':'k','ₜ':'t','ₛ':'s','₊':'+','₋':'-'};var supMap={'⁰':'0','¹':'1','²':'2','³':'3','⁴':'4','⁵':'5','⁶':'6','⁷':'7','⁸':'8','⁹':'9','ᵏ':'k','ᵗ':'t','ˢ':'s','⁺':'+','⁻':'-'};for(var symbol in mathSymbols){content=content.replace(new RegExp(escapeRegExp(symbol),'g'),mathSymbols[symbol]);}content=content.replace(/([a-zA-Z])₍([0-9]+)₎/g,'$1<sub>$2</sub>').replace(/([a-zA-Z])⁽([0-9]+)⁾/g,'$1<sup>$2</sup>').replace(/([a-zA-Z])([₀₁₂₃₄₅₆₇₈₉ₖₜₛ₊₋]+)/g,function(match,base,subscript){var converted=subscript.replace(/[₀₁₂₃₄₅₆₇₈₉ₖₜₛ₊₋]/g,function(char){return subMap[char]||char;});return base+'<sub>'+converted+'</sub>';}).replace(/([a-zA-Z])([⁰¹²³⁴⁵⁶⁷⁸⁹ᵏᵗˢ⁺⁻]+)/g,function(match,base,superscript){var converted=superscript.replace(/[⁰¹²³⁴⁵⁶⁷⁸⁹ᵏᵗˢ⁺⁻]/g,function(char){return supMap[char]||char;});return base+'<sup>'+converted+'</sup>';}).replace(/\\b([A-Za-z])_\\{([^}]+)\\}/g,'$1<sub>$2</sub>').replace(/\\b([A-Za-z])\\^\\{([^}]+)\\}/g,'$1<sup>$2</sup>');return content;}\nfunction formatParagraphs(content){var lines=content.split('\\n'),result=[],inParagraph=false,currentParagraph=[];for(var i=0;i<lines.length;i++){var originalLine=lines[i],line=originalLine.trim();if(line.match(/^<[^>]+>/)||line===''){if(inParagraph&&currentParagraph.length>0){var paragraphContent=currentParagraph.join(' ').trim();if(paragraphContent)result.push('<p class=\"content-paragraph indent-paragraph\">'+paragraphContent+'</p>');currentParagraph=[];inParagraph=false;}result.push(originalLine);continue;}if(line.match(/^#{1,6}\\s/)||line.match(/^\\d+\\.\\s/)||line.match(/^[-*+]\\s/)||line.match(/^>/)){if(inParagraph&&currentParagraph.length>0){var paragraphContent=currentParagraph.join(' ').trim();if(paragraphContent)result.push('<p class=\"content-paragraph indent-paragraph\">'+paragraphContent+'</p>');currentParagraph=[];inParagraph=false;}result.push(originalLine);continue;}if(line){currentParagraph.push(line);inParagraph=true;}}if(inParagraph&&currentParagraph.length>0){var paragraphContent=currentParagraph.join(' ').trim();if(paragraphContent)result.push('<p class=\"content-paragraph indent-paragraph\">'+paragraphContent+'</p>');}return result.join('\\n');}\nfunction escapeRegExp(string){return string.replace(/[.*+?^${}()|[\\]\\\\]/g,'\\\\$&');}\nfunction formatLinks(content){return content.replace(/<a\\s+href=\"(https?:\\/\\/[^\"]+)\"([^>]*)>/gi,function(match,url,attrs){return !attrs.includes('target=')?'<a href=\"'+url+'\"'+attrs+' target=\"_blank\" rel=\"noopener noreferrer\">':match;});}\nfunction escapeHtml(text){var div=document.createElement('div');div.textContent=text;return div.innerHTML;}\nfunction addTocClickEvents(){document.querySelectorAll('#tableOfContents a').forEach(function(link){link.addEventListener('click',function(e){e.preventDefault();var targetElement=document.getElementById(this.getAttribute('data-target'));if(targetElement){targetElement.scrollIntoView({behavior:'smooth',block:'start'});updateActiveTocItem(this);}});});}\nfunction addScrollHighlight(headings){var throttleTimer=null;function updateActiveHeading(){var windowHeight=window.innerHeight,activeHeading=null;for(var i=headings.length-1;i>=0;i--){var rect=headings[i].getBoundingClientRect();if(rect.top<=windowHeight*0.3){activeHeading=headings[i];break;}}if(activeHeading){var activeLink=document.querySelector('#tableOfContents a[data-target=\"'+activeHeading.id+'\"]');if(activeLink)updateActiveTocItem(activeLink);}}window.addEventListener('scroll',function(){if(throttleTimer)clearTimeout(throttleTimer);throttleTimer=setTimeout(updateActiveHeading,100);});updateActiveHeading();}\nfunction updateActiveTocItem(activeLink){document.querySelectorAll('#tableOfContents a').forEach(function(link){link.classList.remove('active');});if(activeLink){activeLink.classList.add('active');scrollTocToActiveItem(activeLink);}}\nfunction scrollTocToActiveItem(activeLink){var tocCard=document.getElementById('tocCard');if(!activeLink||!tocCard)return;var cardRect=tocCard.getBoundingClientRect();var activeRect=activeLink.getBoundingClientRect();var relativeTop=activeRect.top-cardRect.top;var relativeBottom=activeRect.bottom-cardRect.bottom;var cardStyle=window.getComputedStyle(tocCard);var paddingTop=parseFloat(cardStyle.paddingTop)||0;var titleHeight=tocCard.querySelector('h5')?tocCard.querySelector('h5').offsetHeight:0;var contentTop=paddingTop+titleHeight;if(relativeTop<contentTop){tocCard.scrollTop+=(relativeTop-contentTop-10);}else if(relativeBottom>0){tocCard.scrollTop+=(relativeBottom+10);}}\nfunction optimizeImages(){document.querySelectorAll('.article-content img').forEach(function(img){img.loading='lazy';img.onerror=function(){this.style.display='none';};if(!img.alt)img.alt='Artigo com fotos';});}\nfunction initReadingProgress(){var progressBar=document.getElementById('readingProgress');function updateProgress(){var scrollTop=window.pageYOffset||document.documentElement.scrollTop;var docHeight=document.documentElement.scrollHeight-window.innerHeight;progressBar.style.width=Math.min((scrollTop/docHeight)*100,100)+'%';}window.addEventListener('scroll',updateProgress);updateProgress();}\nfunction initTableOfContents(){var content=document.querySelector('.article-content');if(!content)return;var headings=content.querySelectorAll('h1, h2, h3, h4, h5, h6');if(headings.length<=1)return;var tocCard=document.getElementById('tocCard');var tocList=document.getElementById('tableOfContents');var html='',counters=[0,0,0,0,0,0];headings.forEach(function(heading,index){var id='heading-'+index;heading.id=id;var level=parseInt(heading.tagName.substring(1));var indent=(level-1)*15;counters[level-1]++;for(var i=level;i<counters.length;i++)counters[i]=0;var numberText='';for(var i=0;i<level;i++){if(counters[i]>0)numberText+=(numberText?'.':'')+counters[i];}numberText+='.';html+='<a href=\"#'+id+'\" class=\"toc-item toc-level-'+level+'\" style=\"padding-left: '+indent+'px;\" data-target=\"'+id+'\"><span class=\"toc-number\">'+numberText+'</span> <span class=\"toc-text\">'+heading.textContent+'</span></a>';});tocList.innerHTML=html;tocCard.style.display='block';addTocClickEvents();addScrollHighlight(headings);tocList.addEventListener('click',function(e){if(e.target.tagName==='A'){e.preventDefault();var targetElement=document.getElementById(e.target.getAttribute('data-target'));if(targetElement){targetElement.scrollIntoView({behavior:'smooth',block:'start'});targetElement.style.transition='all 0.3s ease';targetElement.style.backgroundColor='#e3f2fd';targetElement.style.padding='10px';targetElement.style.borderRadius='6px';setTimeout(function(){targetElement.style.backgroundColor='';targetElement.style.padding='';targetElement.style.borderRadius='';},2000);}}});}\nfunction shareToWeibo(){window.open('https://service.weibo.com/share/share.php?url='+encodeURIComponent(window.location.href)+'&title='+encodeURIComponent(document.title),'_blank');}\nfunction copyUrl(){navigator.clipboard.writeText(window.location.href).then(function(){alert('O link foi copiado para a área de transferência');}).catch(function(){var textArea=document.createElement('textarea');textArea.value=window.location.href;textArea.style.cssText='position:fixed;left:-999999px;top:-999999px';document.body.appendChild(textArea);textArea.focus();textArea.select();try{alert(document.execCommand('copy')?'O link foi copiado para a área de transferência':'Se a cópia falhar, copie o link manualmente');}catch(err){alert('Se a cópia falhar, copie o link manualmente');}document.body.removeChild(textArea);});}\nfunction printArticle(){var printWindow=window.open('','_blank');var articleContent=document.querySelector('.article-detail').innerHTML;var articleTitle=document.querySelector('.article-title').textContent;printWindow.document.write('<!DOCTYPE html><html lang=\"pt\"><title>'+articleTitle+'</title><style>body{font-family:-apple-system,BlinkMacSystemFont,\"Segoe UI\",Roboto,\"Helvetica Neue\",Arial,sans-serif;line-height:1.6;color:#333;max-width:800px;margin:0 auto;padding:20px}h1,h2,h3,h4,h5,h6{color:#1a1c1f;margin-top:1.5em;margin-bottom:0.5em}h1{font-size:2em;border-bottom:2px solid #e9ecef;padding-bottom:0.3em}h2{font-size:1.5em;border-bottom:1px solid #e9ecef;padding-bottom:0.3em}h3{font-size:1.25em}p{margin-bottom:1em}pre{background:#f8f9fa;border:1px solid #e9ecef;border-radius:4px;padding:1em;overflow-x:auto;font-family:Consolas,Monaco,\"Courier New\",monospace}code{background:#f1f3f4;padding:2px 4px;border-radius:3px;font-family:Consolas,Monaco,\"Courier New\",monospace}blockquote{border-left:4px solid #1764ff;background:#f8f9fa;padding:1em;margin:1em 0;border-radius:0 4px 4px 0}img{max-width:100%;height:auto}table{width:100%;border-collapse:collapse;margin:1em 0}th,td{border:1px solid #ddd;padding:8px;text-align:left}th{background-color:#f2f2f2}.article-meta,.article-footer,.share-buttons{display:none}</style><body>'+articleContent+'</body></html>');printWindow.document.close();printWindow.focus();setTimeout(function(){printWindow.print();printWindow.close();},250);}\nfunction improvedStickyPosition(){var sidebarCards=document.querySelectorAll('.sidebar-card');var originalPositions=[],isFixed=[];sidebarCards.forEach(function(card,index){var rect=card.getBoundingClientRect();originalPositions[index]=rect.top+window.pageYOffset;isFixed[index]=false;var placeholder=document.createElement('div');placeholder.style.cssText='height:'+card.offsetHeight+'px;display:none';placeholder.className='sticky-placeholder';card.parentNode.insertBefore(placeholder,card.nextSibling);});function updateStickyPosition(){var scrollTop=window.pageYOffset||document.documentElement.scrollTop;sidebarCards.forEach(function(card,index){var originalTop=originalPositions[index];var stickyTop=index>0?(document.querySelector('.toc-card').style.position==='fixed'?60+document.querySelector('.toc-card').offsetHeight+8:80):60;var placeholder=card.nextSibling;if(scrollTop>originalTop-stickyTop){if(!isFixed[index]){var colContainer=card.closest('[class*=\"col-\"]');var colRect=colContainer.getBoundingClientRect();var colStyle=window.getComputedStyle(colContainer);var paddingLeft=parseFloat(colStyle.paddingLeft)||0;var paddingRight=parseFloat(colStyle.paddingRight)||0;card.style.cssText='position:fixed;top:'+stickyTop+'px;left:'+(colRect.left+paddingLeft)+'px;width:'+(colRect.width-paddingLeft-paddingRight)+'px;z-index:9999;box-sizing:border-box';card.classList.add('fixed');placeholder.style.display='block';isFixed[index]=true;}}else if(isFixed[index]){card.style.cssText='';card.classList.remove('fixed');placeholder.style.display='none';isFixed[index]=false;}});}window.addEventListener('scroll',updateStickyPosition);window.addEventListener('resize',function(){setTimeout(function(){sidebarCards.forEach(function(card,index){if(isFixed[index]){var colContainer=card.closest('[class*=\"col-\"]');var colRect=colContainer.getBoundingClientRect();var colStyle=window.getComputedStyle(colContainer);var paddingLeft=parseFloat(colStyle.paddingLeft)||0;var paddingRight=parseFloat(colStyle.paddingRight)||0;card.style.left=(colRect.left+paddingLeft)+'px';card.style.width=(colRect.width-paddingLeft-paddingRight)+'px';}else{originalPositions[index]=card.getBoundingClientRect().top+window.pageYOffset;}});},100);});updateStickyPosition();}\ndocument.addEventListener('DOMContentLoaded',function(){setTimeout(improvedStickyPosition,1000);});\n    </script>\n\r\n            </div>\r\n        </form>\r\n\r\n        <div class=\"fixed-icon\">\r\n            <div style=\"width: 120px; position: relative; left: 1rem; top: -12px;\">\r\n            </div>\r\n            <a id=\"webchat\" class=\"wx-con disa\" href=\"http://wpa.qq.com/msgrd?v=3&uin=365833440&site=qq&menu=yes\" target=\"_blank\">\r\n                <img class=\"wechat-ico\" src=\"site/image/icon/qq.svg\" alt=\"Assistente de OCR QQ atendimento ao cliente online\" />\r\n                <div class=\"wx-text\">Atendimento ao Cliente QQ (365833440)</div>\r\n                <label class=\"badge badge-danger\" style=\"background: rgb(255, 34, 122); display: inline-block;\">1</label>\r\n            </a>\r\n\r\n            <a class=\"wx-con\" href=\"https://qm.qq.com/cgi-bin/qm/qr?k=bohNy9GMdgD5bsYrB-fcIjy-s6Ot3zOg&jump_from=webapi&qr=1\" target=\"_blank\">\r\n                <img class=\"wechat-ico\" src=\"site/image/icon/qq.svg\" alt=\"Grupo de comunicação do usuário do assistente de OCR QQ\" />\r\n                <div class=\"wx-text\">Grupo QQ (100029010)</div>\r\n                <label class=\"badge badge-danger\" style=\"background: rgb(255, 34, 122); display: inline-block;\">2</label>\r\n            </a>\r\n            <a href=\"mailto:<EMAIL>\" class=\"wx-con\">\r\n                <img class=\"wechat-ico\" src=\"site/image/icon/im.svg\" alt=\"Assistente de OCR entre em contato com o atendimento ao cliente por e-mail\" />\r\n                <div class=\"wx-text\" style=\"width: 240px; text-align: left; left: -15.3rem; padding: 10px 20px;\">\r\n                    <div style=\"font-size: 15px\">E-mail: <EMAIL></div>\r\n                    <hr style=\"background: #c1c1c1; margin: 10px 10px 6px 0;\" />\r\n                    <span style=\"font-size: 13px; color: #c1c1c1\">Obrigado por seus comentários e sugestões!</span>\r\n                </div>\r\n                <label class=\"badge badge-danger\" style=\"background: rgb(255, 34, 122); display: inline-block;\">3</label>\r\n            </a>\r\n        </div>\r\n\r\n        <style>\r\n            .content {\r\n                width: 100%;\r\n                padding: 0 !important;\r\n                margin: 0 auto\r\n            }\r\n\r\n            h3.h6 {\r\n                font-size: 1rem !important;\r\n                font-weight: bold !important;\r\n                line-height: 1.2 !important;\r\n                margin-bottom: .5rem;\r\n                font-family: inherit\r\n            }\r\n        </style>\r\n        <footer class=\"page-footer\">\r\n            <div class=\"container\">\r\n                <div class=\"row foot-cont pt-4 no-gutters\" style=\"border-top: 1px solid #e1e6ed; max-width: 100%\">\r\n                    <div class=\"col-lg-12 hu-an p-0 mb-3 mt-3 text-l color-gray\" style=\"text-align: center;\">\r\n                        Assistente de reconhecimento de texto OCR&nbsp;©️ 2025 ALL RIGHTS RESERVED. Todos os direitos reservados&nbsp;&nbsp;&nbsp;|&nbsp;&nbsp;&nbsp;\r\n                        <a href=\"PrivacyPolicy.aspx\" style=\"text-decoration: underline;\">Acordo de Privacidade</a>&nbsp;&nbsp;&nbsp;|&nbsp;&nbsp;&nbsp;\r\n                        <a href=\"UserAgreement.aspx\" style=\"text-decoration: underline;\">Contrato do Usuário</a>&nbsp;&nbsp;&nbsp;|&nbsp;&nbsp;&nbsp;\r\n                        <a href=\"Status.aspx\" style=\"text-decoration: underline;\">Status do serviço</a>&nbsp;&nbsp;&nbsp;|&nbsp;&nbsp;&nbsp;\r\n                        <a class=\"mb-2 color-gray\" href=\"https://beian.miit.gov.cn/\" target=\"_blank\" style=\"text-decoration: underline;\">E Preparação do PIC n.º 2021012692</a>\r\n                    </div>\r\n                </div>\r\n            </div>\r\n        </footer>\r\n        <style>\r\n            #translate > .translateSelectLanguage {\r\n                right: 2rem;\r\n                font-size: 1rem;\r\n                width: 150px;\r\n                padding: .3rem;\r\n                margin-right: 20px;\r\n                border: 1px solid #C9C9C9;\r\n                background: #fff;\r\n                color: #555\r\n            }\r\n        </style>\r\n    </div>\r\n    <script>\r\n        document.addEventListener('DOMContentLoaded', function () {\r\n            var setScroll = function (active) {\r\n                forEach(qa('.v4_header_pc,.v4_header_mob'), function (e) {\r\n                    if (active) addClass(e, 'scrollActive'); else removeClass(e, 'scrollActive');\r\n                });\r\n            };\r\n            if (getScrollY() > 60) setScroll(1);\r\n            window.addEventListener('scroll', function () { setScroll(getScrollY() > 60); });\r\n\r\n            forEach(qa('.v4_header_pc'), function (h) {\r\n                h.addEventListener('mouseenter', function () { addClass(h, 'active'); });\r\n                h.addEventListener('mouseleave', function () { removeClass(h, 'active'); });\r\n            });\r\n\r\n            var menuTimer;\r\n            var hideMenu = function (menuId) {\r\n                var m = document.getElementById(menuId);\r\n                if (m) {\r\n                    m.style.cssText = 'visibility:hidden;height:0;display:none';\r\n                    var n = q('.nav-drown-con', m);\r\n                    if (n) { n.style.cssText = 'height:0;opacity:0;transform:translate(0,-100%)'; }\r\n                }\r\n            };\r\n\r\n            forEach(qa('ul.top-nav>li'), function (i) {\r\n                i.addEventListener('mouseenter', function () {\r\n                    clearTimeout(menuTimer);\r\n                    var l = this.querySelector('a'), t = this.querySelector('.triangle'), s = l ? l.getAttribute('data-sub') : null;\r\n                    if (l) addClass(l, 'active'); if (t) addClass(t, 'active');\r\n                    if (s) {\r\n                        var m = document.getElementById(s);\r\n                        if (m) {\r\n                            m.style.cssText = 'display:block;visibility:visible';\r\n                            var siblings = m.parentNode ? toArray(m.parentNode.children) : [];\r\n                            forEach(siblings, function (c) { if (c !== m) c.style.display = 'none'; });\r\n                            var n = q('.nav-drown-con', m);\r\n                            if (n) { n.style.cssText = 'height:100%;opacity:1;transform:translate(0,0)'; }\r\n\r\n                            m.addEventListener('mouseenter', function () { clearTimeout(menuTimer); });\r\n                            m.addEventListener('mouseleave', function () { menuTimer = setTimeout(function () { hideMenu(s); }, 100); });\r\n                        }\r\n                    }\r\n                });\r\n                i.addEventListener('mouseleave', function () {\r\n                    var l = this.querySelector('a'), t = this.querySelector('.triangle'), s = l ? l.getAttribute('data-sub') : null;\r\n                    if (l) removeClass(l, 'active'); if (t) removeClass(t, 'active');\r\n                    if (s) { menuTimer = setTimeout(function () { hideMenu(s); }, 100); }\r\n                });\r\n            });\r\n\r\n            var r = q('.v4_header_mob .right-menu');\r\n            if (r) r.addEventListener('click', function () {\r\n                var arr = [r, q('.v4_header_mob'), q('#nav_products'), q('.mob-nav-content .sidebar-fix')];\r\n                for (var i = 0; i < arr.length; i++) {\r\n                    var e = arr[i];\r\n                    if (e) {\r\n                        var name = (i === 0 ? 'active' : (i === 1 ? 'active' : (i === 2 ? 'active1' : 'fixedActiveBg')));\r\n                        toggleClass(e, name);\r\n                    }\r\n                }\r\n                var f = q('.mob-nav-content .sidebar-fix');\r\n                if (f) {\r\n                    toggleClass(f, 'show');\r\n                    document.body.style.overflow = hasClass(f, 'show') ? 'hidden' : 'scroll';\r\n                }\r\n            });\r\n\r\n            document.addEventListener('click', function (e) {\r\n                e = e || window.event;\r\n                var target = e.target || e.srcElement;\r\n                var m = closest(target, '.sidebar-fix-left .mob-nav-item');\r\n                if (m) {\r\n                    var p = m.parentNode;\r\n                    if (p) forEach(toArray(p.children), function (c) { removeClass(c, 'active'); });\r\n                    addClass(m, 'active');\r\n                    var d = m.getAttribute('data');\r\n                    if (d) forEach(qa('.sidebar-fix-rigth .sub-nav'), function (n, i) { n.style.display = (i == d ? 'block' : 'none'); });\r\n                }\r\n            });\r\n        });\r\n    </script>\r\n</body>\r\n</html>\r\n\r\n\t            <script type=\"text/javascript\">\r\n                    var _hmt = _hmt || [];\r\n                    (function () {\r\n                        var hm = document.createElement('script');hm.async=1;\r\n                        hm.src = 'https://hm.baidu.com/hm.js?5ee048d7caaf0d439cf16309a1687754';\r\n                        var s = document.getElementsByTagName('script')[0];s.parentNode.insertBefore(hm, s);\r\n                    })();\r\n                </script><div id=\"translate\" class=\"ignore\" style=\"display: none\"></div><script src=\"/static/js/translate.js\"></script>"
﻿"<!DOCTYPE html>\r\n<html xmlns=\"http://www.w3.org/1999/xhtml\" lang=\"sr-Latn\">\r\n<head><link rel=\"canonical\" href=\"https://ocr.oldfish.cn/sr-Latn/Article.aspx?id=81&slug=rnn-sequence-modeling\" /><link rel=\"alternate\" href=\"https://ocr.oldfish.cn/Article.aspx?id=81&slug=rnn-sequence-modeling\" hreflang=\"x-default\" />\r\n<link rel=\"alternate\" href=\"https://ocr.oldfish.cn/Article.aspx?id=81&slug=rnn-sequence-modeling\" hreflang=\"zh\" />\r\n<link rel=\"alternate\" href=\"https://ocr.oldfish.cn/Article.aspx?id=81&slug=rnn-sequence-modeling\" hreflang=\"zh\" />\r\n<link rel=\"alternate\" href=\"https://ocr.oldfish.cn/zh-Hant/Article.aspx?id=81&slug=rnn-sequence-modeling\" hreflang=\"zh-tw\" />\r\n<link rel=\"alternate\" href=\"https://ocr.oldfish.cn/yue/Article.aspx?id=81&slug=rnn-sequence-modeling\" hreflang=\"zh-yue\" />\r\n<link rel=\"alternate\" href=\"https://ocr.oldfish.cn/en/Article.aspx?id=81&slug=rnn-sequence-modeling\" hreflang=\"en\" />\r\n<link rel=\"alternate\" href=\"https://ocr.oldfish.cn/es/Article.aspx?id=81&slug=rnn-sequence-modeling\" hreflang=\"es\" />\r\n<link rel=\"alternate\" href=\"https://ocr.oldfish.cn/hi/Article.aspx?id=81&slug=rnn-sequence-modeling\" hreflang=\"hi\" />\r\n<link rel=\"alternate\" href=\"https://ocr.oldfish.cn/ar/Article.aspx?id=81&slug=rnn-sequence-modeling\" hreflang=\"ar\" />\r\n<link rel=\"alternate\" href=\"https://ocr.oldfish.cn/pt/Article.aspx?id=81&slug=rnn-sequence-modeling\" hreflang=\"pt\" />\r\n<link rel=\"alternate\" href=\"https://ocr.oldfish.cn/bn/Article.aspx?id=81&slug=rnn-sequence-modeling\" hreflang=\"bn\" />\r\n<link rel=\"alternate\" href=\"https://ocr.oldfish.cn/ru/Article.aspx?id=81&slug=rnn-sequence-modeling\" hreflang=\"ru\" />\r\n<link rel=\"alternate\" href=\"https://ocr.oldfish.cn/ja/Article.aspx?id=81&slug=rnn-sequence-modeling\" hreflang=\"ja\" />\r\n<link rel=\"alternate\" href=\"https://ocr.oldfish.cn/pa/Article.aspx?id=81&slug=rnn-sequence-modeling\" hreflang=\"pa\" />\r\n<link rel=\"alternate\" href=\"https://ocr.oldfish.cn/de/Article.aspx?id=81&slug=rnn-sequence-modeling\" hreflang=\"de\" />\r\n<link rel=\"alternate\" href=\"https://ocr.oldfish.cn/fr/Article.aspx?id=81&slug=rnn-sequence-modeling\" hreflang=\"fr\" />\r\n<link rel=\"alternate\" href=\"https://ocr.oldfish.cn/mr/Article.aspx?id=81&slug=rnn-sequence-modeling\" hreflang=\"mr\" />\r\n<link rel=\"alternate\" href=\"https://ocr.oldfish.cn/te/Article.aspx?id=81&slug=rnn-sequence-modeling\" hreflang=\"te\" />\r\n<link rel=\"alternate\" href=\"https://ocr.oldfish.cn/vi/Article.aspx?id=81&slug=rnn-sequence-modeling\" hreflang=\"vi\" />\r\n<link rel=\"alternate\" href=\"https://ocr.oldfish.cn/ko/Article.aspx?id=81&slug=rnn-sequence-modeling\" hreflang=\"ko\" />\r\n<link rel=\"alternate\" href=\"https://ocr.oldfish.cn/ta/Article.aspx?id=81&slug=rnn-sequence-modeling\" hreflang=\"ta\" />\r\n<link rel=\"alternate\" href=\"https://ocr.oldfish.cn/ur/Article.aspx?id=81&slug=rnn-sequence-modeling\" hreflang=\"ur\" />\r\n<link rel=\"alternate\" href=\"https://ocr.oldfish.cn/tr/Article.aspx?id=81&slug=rnn-sequence-modeling\" hreflang=\"tr\" />\r\n<link rel=\"alternate\" href=\"https://ocr.oldfish.cn/it/Article.aspx?id=81&slug=rnn-sequence-modeling\" hreflang=\"it\" />\r\n<link rel=\"alternate\" href=\"https://ocr.oldfish.cn/gu/Article.aspx?id=81&slug=rnn-sequence-modeling\" hreflang=\"gu\" />\r\n<link rel=\"alternate\" href=\"https://ocr.oldfish.cn/pl/Article.aspx?id=81&slug=rnn-sequence-modeling\" hreflang=\"pl\" />\r\n<link rel=\"alternate\" href=\"https://ocr.oldfish.cn/uk/Article.aspx?id=81&slug=rnn-sequence-modeling\" hreflang=\"uk\" />\r\n<link rel=\"alternate\" href=\"https://ocr.oldfish.cn/ms/Article.aspx?id=81&slug=rnn-sequence-modeling\" hreflang=\"ms\" />\r\n<link rel=\"alternate\" href=\"https://ocr.oldfish.cn/id/Article.aspx?id=81&slug=rnn-sequence-modeling\" hreflang=\"id\" />\r\n<link rel=\"alternate\" href=\"https://ocr.oldfish.cn/ml/Article.aspx?id=81&slug=rnn-sequence-modeling\" hreflang=\"ml\" />\r\n<link rel=\"alternate\" href=\"https://ocr.oldfish.cn/kn/Article.aspx?id=81&slug=rnn-sequence-modeling\" hreflang=\"kn\" />\r\n<link rel=\"alternate\" href=\"https://ocr.oldfish.cn/fa/Article.aspx?id=81&slug=rnn-sequence-modeling\" hreflang=\"fa\" />\r\n<link rel=\"alternate\" href=\"https://ocr.oldfish.cn/nl/Article.aspx?id=81&slug=rnn-sequence-modeling\" hreflang=\"nl\" />\r\n<link rel=\"alternate\" href=\"https://ocr.oldfish.cn/th/Article.aspx?id=81&slug=rnn-sequence-modeling\" hreflang=\"th\" />\r\n<link rel=\"alternate\" href=\"https://ocr.oldfish.cn/sw/Article.aspx?id=81&slug=rnn-sequence-modeling\" hreflang=\"sw\" />\r\n<link rel=\"alternate\" href=\"https://ocr.oldfish.cn/ro/Article.aspx?id=81&slug=rnn-sequence-modeling\" hreflang=\"ro\" />\r\n<link rel=\"alternate\" href=\"https://ocr.oldfish.cn/my/Article.aspx?id=81&slug=rnn-sequence-modeling\" hreflang=\"my\" />\r\n<link rel=\"alternate\" href=\"https://ocr.oldfish.cn/or/Article.aspx?id=81&slug=rnn-sequence-modeling\" hreflang=\"or\" />\r\n<link rel=\"alternate\" href=\"https://ocr.oldfish.cn/he/Article.aspx?id=81&slug=rnn-sequence-modeling\" hreflang=\"he\" />\r\n<link rel=\"alternate\" href=\"https://ocr.oldfish.cn/am/Article.aspx?id=81&slug=rnn-sequence-modeling\" hreflang=\"am\" />\r\n<link rel=\"alternate\" href=\"https://ocr.oldfish.cn/fil/Article.aspx?id=81&slug=rnn-sequence-modeling\" hreflang=\"fil\" />\r\n<link rel=\"alternate\" href=\"https://ocr.oldfish.cn/sv/Article.aspx?id=81&slug=rnn-sequence-modeling\" hreflang=\"sv\" />\r\n<link rel=\"alternate\" href=\"https://ocr.oldfish.cn/el/Article.aspx?id=81&slug=rnn-sequence-modeling\" hreflang=\"el\" />\r\n<link rel=\"alternate\" href=\"https://ocr.oldfish.cn/cs/Article.aspx?id=81&slug=rnn-sequence-modeling\" hreflang=\"cs\" />\r\n<link rel=\"alternate\" href=\"https://ocr.oldfish.cn/hu/Article.aspx?id=81&slug=rnn-sequence-modeling\" hreflang=\"hu\" />\r\n<link rel=\"alternate\" href=\"https://ocr.oldfish.cn/be/Article.aspx?id=81&slug=rnn-sequence-modeling\" hreflang=\"be\" />\r\n<link rel=\"alternate\" href=\"https://ocr.oldfish.cn/si/Article.aspx?id=81&slug=rnn-sequence-modeling\" hreflang=\"si\" />\r\n<link rel=\"alternate\" href=\"https://ocr.oldfish.cn/ne/Article.aspx?id=81&slug=rnn-sequence-modeling\" hreflang=\"ne\" />\r\n<link rel=\"alternate\" href=\"https://ocr.oldfish.cn/km/Article.aspx?id=81&slug=rnn-sequence-modeling\" hreflang=\"km\" />\r\n<link rel=\"alternate\" href=\"https://ocr.oldfish.cn/sk/Article.aspx?id=81&slug=rnn-sequence-modeling\" hreflang=\"sk\" />\r\n<link rel=\"alternate\" href=\"https://ocr.oldfish.cn/bg/Article.aspx?id=81&slug=rnn-sequence-modeling\" hreflang=\"bg\" />\r\n<link rel=\"alternate\" href=\"https://ocr.oldfish.cn/fr-ca/Article.aspx?id=81&slug=rnn-sequence-modeling\" hreflang=\"fr-ca\" />\r\n<link rel=\"alternate\" href=\"https://ocr.oldfish.cn/ha/Article.aspx?id=81&slug=rnn-sequence-modeling\" hreflang=\"ha\" />\r\n<link rel=\"alternate\" href=\"https://ocr.oldfish.cn/yo/Article.aspx?id=81&slug=rnn-sequence-modeling\" hreflang=\"yo\" />\r\n<link rel=\"alternate\" href=\"https://ocr.oldfish.cn/ig/Article.aspx?id=81&slug=rnn-sequence-modeling\" hreflang=\"ig\" />\r\n<link rel=\"alternate\" href=\"https://ocr.oldfish.cn/ku/Article.aspx?id=81&slug=rnn-sequence-modeling\" hreflang=\"ku\" />\r\n<link rel=\"alternate\" href=\"https://ocr.oldfish.cn/rw/Article.aspx?id=81&slug=rnn-sequence-modeling\" hreflang=\"rw\" />\r\n<link rel=\"alternate\" href=\"https://ocr.oldfish.cn/ca/Article.aspx?id=81&slug=rnn-sequence-modeling\" hreflang=\"ca\" />\r\n<link rel=\"alternate\" href=\"https://ocr.oldfish.cn/da/Article.aspx?id=81&slug=rnn-sequence-modeling\" hreflang=\"da\" />\r\n<link rel=\"alternate\" href=\"https://ocr.oldfish.cn/fi/Article.aspx?id=81&slug=rnn-sequence-modeling\" hreflang=\"fi\" />\r\n<link rel=\"alternate\" href=\"https://ocr.oldfish.cn/nb/Article.aspx?id=81&slug=rnn-sequence-modeling\" hreflang=\"nb\" />\r\n<link rel=\"alternate\" href=\"https://ocr.oldfish.cn/hr/Article.aspx?id=81&slug=rnn-sequence-modeling\" hreflang=\"hr\" />\r\n<link rel=\"alternate\" href=\"https://ocr.oldfish.cn/sr-Cyrl/Article.aspx?id=81&slug=rnn-sequence-modeling\" hreflang=\"sr-cyrl\" />\r\n<link rel=\"alternate\" href=\"https://ocr.oldfish.cn/sr-Latn/Article.aspx?id=81&slug=rnn-sequence-modeling\" hreflang=\"sr-latn\" />\r\n<link rel=\"alternate\" href=\"https://ocr.oldfish.cn/sq/Article.aspx?id=81&slug=rnn-sequence-modeling\" hreflang=\"sq\" />\r\n<link rel=\"alternate\" href=\"https://ocr.oldfish.cn/so/Article.aspx?id=81&slug=rnn-sequence-modeling\" hreflang=\"so\" />\r\n<link rel=\"alternate\" href=\"https://ocr.oldfish.cn/zu/Article.aspx?id=81&slug=rnn-sequence-modeling\" hreflang=\"zu\" />\r\n<link rel=\"alternate\" href=\"https://ocr.oldfish.cn/ka/Article.aspx?id=81&slug=rnn-sequence-modeling\" hreflang=\"ka\" />\r\n<meta http-equiv=\"Content-Type\" content=\"text/html; charset=UTF-8\" /><meta http-equiv=\"X-UA-Compatible\" content=\"IE=Edge,chrome=1\" /><meta name=\"renderer\" content=\"webkit\" /><meta name=\"viewport\" content=\"width=device-width, initial-scale=1.0, maximum-scale=5.0\" /><link rel=\"icon\" href=\"/favicon.ico\" type=\"image/x-icon\" /><meta name=\"robots\" content=\"all\" /><link rel=\"preconnect\" href=\"https://scm-file-new-1301864755.cos.ap-beijing.myqcloud.com\" /><link rel=\"preconnect\" href=\"https://cdn.oldfish.cn\" /><link rel=\"dns-prefetch\" href=\"https://hm.baidu.com\" /><link rel=\"dns-prefetch\" href=\"https://cdn.oldfish.cn\" /><link rel=\"preload\" href=\"/site/css/site.css\" as=\"style\" />\r\n<script>function toArray(l){var a=[];for(var i=0;i<(l?l.length:0);i++)a.push(l[i]);return a;}function forEach(l,f){for(var i=0,n=(l?l.length:0);i<n;i++)f(l[i],i);}function hasClass(e,c){if(!e||!c)return false;return(' '+e.className+' ').indexOf(' '+c+' ')>-1;}function addClass(e,c){if(!e||!c||hasClass(e,c))return;e.className=(e.className?e.className+' ':'')+c;}function removeClass(e,c){if(!e||!c)return;var u=' '+e.className+' ';while(u.indexOf(' '+c+' ')>-1){u=u.replace(' '+c+' ',' ');}e.className=u.replace(/^\\s+|\\s+$/g,'');}function toggleClass(e,c){if(!e||!c)return;if(hasClass(e,c))removeClass(e,c);else addClass(e,c);}function matchesSelector(e,s){if(!e||e.nodeType!==1)return false;if(e.matches)return e.matches(s);var p=e.parentNode||document;var m=toArray(p.querySelectorAll(s));for(var i=0;i<m.length;i++)if(m[i]===e)return true;return false;}function closest(e,s){var n=e;while(n&&n.nodeType===1){if(matchesSelector(n,s))return n;n=n.parentNode;}return null;}function safeRemove(e){if(e&&e.parentNode)e.parentNode.removeChild(e);}function qa(s,r){return toArray((r||document).querySelectorAll(s));}function q(s,r){return(r||document).querySelector(s);}function xhrGet(u,o,e){try{var x=new XMLHttpRequest();x.open('GET',u,true);x.onreadystatechange=function(){if(x.readyState===4){if(x.status>=200&&x.status<300)o&&o(x.responseText);else e&&e(x);}};x.send(null);}catch(r){e&&e(r);}}function getScrollY(){return window.pageYOffset||document.documentElement.scrollTop||document.body.scrollTop||0;}function getOrigin(){return window.location.protocol+'//'+window.location.host;}</script>\r\n<script>var isWebP=(function(){try{return document.createElement('canvas').toDataURL('image/webp').indexOf('data:image/webp')===0;}catch(e){return false;}})();document.addEventListener('DOMContentLoaded',function(){var c=document.getElementById('user-login-container');if(c){var u=c.getAttribute('data-url');if(u){xhrGet(u,function(h){c.innerHTML=h;forEach(qa('a',c),function(l){l.style.display='inline-block';l.style.marginLeft='10px';});});}}});</script>\r\n    \n    \n\n    <meta name=\"description\" content=\"Zaronite u primenu RNN, LSTM, GRU u OCR-u. Detaljna analiza principa modeliranja sekvenci, rešenja problema gradijenta i prednosti dvosmernih RNN.\" />\n    <meta name=\"keywords\" content=\"RNN, LSTM, GRU, Modeliranje sekvenci, Gradijent nestajanje, Dvosmerni RNN, Mehanizam pažnje, CRNN, OCR, OCR prepoznavanje teksta, slika u tekst, OCR tehnologija\" />\n    <meta property=\"og:title\" content=\"【Deep Learning OCR serija · 4】 Rekurentne neuronske mreže i modeliranje sekvenci\" />\n    <meta property=\"og:description\" content=\"Zaronite u primenu RNN, LSTM, GRU u OCR-u. Detaljna analiza principa modeliranja sekvenci, rešenja problema gradijenta i prednosti dvosmernih RNN.\" />\n    <meta property=\"og:type\" content=\"article\" />\n    <meta property=\"og:url\" content=\"https://ocr.oldfish.cn/Article.aspx?id=81&slug=rnn-sequence-modeling&lang=zh-Hans&_translation_source=true\" />\n    <meta property=\"og:site_name\" content=\"OCR pomoćnik za prepoznavanje teksta\" />\n    <meta name=\"twitter:card\" content=\"summary\" />\n    <meta name=\"twitter:title\" content=\"【Deep Learning OCR serija · 4】 Rekurentne neuronske mreže i modeliranje sekvenci\" />\n    <meta name=\"twitter:description\" content=\"Zaronite u primenu RNN, LSTM, GRU u OCR-u. Detaljna analiza principa modeliranja sekvenci, rešenja problema gradijenta i prednosti dvosmernih RNN.\" />\n\n    \n    <script type=\"application/ld+json\">\n    {\n        \"@context\": \"https://schema.org\",\n        \"@type\": \"Article\",\n        \"headline\": \"【Duboko učenje OCR serija KSNUMKS] Rekurentna neuronska mreža i modeliranje sekvenci\",\n        \"description\": \"Zaronite u primenu RNN, LSTM, GRU u OCR-u. Detaljna analiza principa modeliranja sekvenci, rešenja problema gradijenta, i prednosti dvosmernih RNNs。\",\n        \"publisher\": {\n            \"@type\": \"Organization\",\n            \"name\": \"OCR tim za prepoznavanje teksta\"\n        },\n        \"datePublished\": \"2025-08-19T06:32:05Z\",\n        \"dateModified\": \"2025-08-19T06:32:05Z\",\n        \"mainEntityOfPage\": {\n            \"@type\": \"WebPage\",\n            \"@id\": \"https://ocr.oldfish.cn/Article.aspx?id=81&slug=rnn-sequence-modeling&lang=zh-Hans&_translation_source=true\"\n        }\n    }\n    </script>\n    \n    <style>\n        .reading-progress { position: fixed; top: 0; left: 0; width: 0%; height: 3px; background: linear-gradient(90deg, #1764ff, #0d47a1); z-index: 9999; transition: width 0.2s ease; box-shadow: 0 1px 3px rgba(23, 100, 255, 0.3); }\n        .home-container.root { overflow: visible !important; }\n        .article-detail-container { background: #f5f7fc; min-height: calc(100vh - 60px); padding: 80px 0 60px; position: relative; overflow: visible; }\n        .article-detail-container .row { margin: 0 -12px; align-items: flex-start !important; min-height: 100vh !important; }\n        .article-detail-container .row > [class*=\"col-\"] { padding: 0 12px; box-sizing: border-box; min-width: 0; }\n        .article-detail-container .row > .col-lg-8, .article-detail-container .row > .col-xl-9 { overflow: hidden; }\n        .article-detail-container .row > .col-lg-4, .article-detail-container .row > .col-xl-3 { overflow: visible !important; height: 100% !important; min-height: 100% !important; position: static !important; display: block !important; transform: none !important; will-change: auto !important; contain: none !important; perspective: none !important; filter: none !important; }\n        .article-detail { background: #fff; border-radius: 8px; padding: 1.5rem; margin-bottom: 2rem; box-shadow: 0 2px 10px rgba(0,0,0,0.1); width: 100%; box-sizing: border-box; }\n        .article-title { font-size: 1.7rem; color: #1a1c1f; line-height: 1.3; margin-bottom: 1.5rem; font-weight: 600; }\n        .article-meta { display: flex; align-items: center; flex-wrap: nowrap; gap: 1.2rem; color: #6c757d; font-size: 0.88rem; margin-bottom: 2rem; padding: 1.2rem 1.4rem; background: linear-gradient(135deg, #f8f9fa 0%, #ffffff 100%); border-radius: 12px; border-left: 4px solid #1764ff; box-shadow: 0 2px 8px rgba(0,0,0,0.06); overflow-x: hidden; transition: all 0.3s ease; }\n        .article-meta:hover { box-shadow: 0 4px 16px rgba(0,0,0,0.1); transform: translateY(-1px); }\n        .article-meta .meta-item { display: flex; align-items: center; gap: 0.25rem; font-weight: 500; color: #495057; white-space: nowrap; flex-shrink: 1; transition: color 0.2s ease; cursor: default; }\n        .article-meta .meta-item:hover { color: #1764ff; }\n        .article-meta .meta-item:hover .meta-icon { transform: scale(1.1); filter: drop-shadow(0 2px 6px rgba(13, 71, 161, 0.25)); }\n        .meta-icon { display: inline-block; font-size: 16px; flex-shrink: 0; vertical-align: middle; transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1); margin-right: 4px; opacity: 0.9; }\n        .meta-item:hover .meta-icon { opacity: 1; }\n        .meta-item:hover { color: #1764ff; }\n        .meta-item { position: relative; }\n        .meta-item::after { content: ''; position: absolute; bottom: -2px; left: 0; width: 0; height: 2px; background: linear-gradient(90deg, #1764ff, #00d4ff); transition: width 0.3s ease; }\n        .meta-item:hover::after { width: 100%; }\n        .article-category, .read-time { display: inline-block; padding: 6px 12px; border-radius: 16px; font-size: 0.8rem; font-weight: 600; }\n        .article-category { background: linear-gradient(135deg, #1764ff, #0d47a1); color: white; text-decoration: none; text-transform: uppercase; letter-spacing: 0.5px; transition: all 0.3s ease; }\n        .article-category:hover { background: linear-gradient(135deg, #0d47a1, #1764ff); color: white; text-decoration: none; transform: translateY(-1px); box-shadow: 0 4px 12px rgba(23, 100, 255, 0.3); }\n        .read-time { background: #e3f2fd; color: #1565c0; border: 1px solid #90caf9; }\n        .article-tags { margin: 20px 0 1.5rem; }\n        .article-tags .tag { display: inline-block; background: #1764ff; color: #fff; padding: 4px 12px; border-radius: 12px; font-size: 0.8rem; margin: 0 8px 4px 0; }\n        .article-summary { background: #e3f2fd; border-left: 4px solid #1764ff; padding: 1rem 1.5rem; margin-bottom: 2rem; border-radius: 0 4px 4px 0; }\n        .article-content { line-height: 1.8; font-size: 16px; color: #333; word-wrap: break-word; }\n        .article-content h1, .article-content h2, .article-content h3, .article-content h4, .article-content h5, .article-content h6 { margin: 2rem 0 1rem; font-weight: 600; line-height: 1.25; color: #1a1c1f; }\n        .article-content h1 { font-size: 2rem; border-bottom: 2px solid #e9ecef; padding-bottom: 0.5rem; }\n        .article-content h2 { font-size: 1.5rem; border-bottom: 1px solid #e9ecef; padding-bottom: 0.3rem; }\n        .article-content h3 { font-size: 1.25rem; }\n        .article-content h4 { font-size: 1.1rem; }\n        .article-content h5, .article-content h6 { font-size: 1rem; }\n        .article-content ul, .article-content ol { margin: 1rem 0; padding-left: 2rem; }\n        .article-content ul ul, .article-content ol ol, .article-content ul ol, .article-content ol ul { margin: 0.5rem 0; }\n        .article-content blockquote { margin: 1rem 0; padding: 0.5rem 1rem; border-left: 4px solid #1764ff; background-color: #f8f9fa; color: #6c757d; font-style: italic; }\n        .article-content pre { background-color: #f8f9fa; border: 1px solid #e9ecef; border-radius: 6px; padding: 1rem; margin: 1rem 0; overflow-x: auto; font-family: 'Courier New', Courier, monospace; font-size: 0.9em; line-height: 1.4; }\n        .article-content pre code { background: none; border: none; padding: 0; color: inherit; }\n        .article-content p { margin: 1rem 0; }\n        .article-content p:first-child { margin-top: 0; }\n        .article-content p:last-child { margin-bottom: 0; }\n        .article-footer { margin-top: 4rem; padding: 2.5rem; background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%); border-radius: 12px; border: 1px solid #e9ecef; position: relative; overflow: hidden; }\n        .article-footer::before { content: ''; position: absolute; top: 0; left: 0; right: 0; height: 3px; background: linear-gradient(90deg, #1764ff, #17a2b8, #28a745); }\n        .article-share h6 { color: #495057; font-weight: 600; margin-bottom: 1.5rem; font-size: 16px; }\n        .share-buttons { display: flex; gap: 12px; flex-wrap: wrap; }\n        .share-buttons .btn { padding: 10px 20px; border-radius: 25px; font-weight: 500; font-size: 14px; border: none; transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1); box-shadow: 0 2px 8px rgba(0,0,0,0.1); display: inline-flex; align-items: center; gap: 8px; text-decoration: none; }\n        .btn-weibo { background: linear-gradient(135deg, #e6162d, #c41230); color: white; }\n        .btn-copy { background: #1764ff; color: white; }\n        .btn-print { background: linear-gradient(135deg, #17a2b8, #138496); color: white; }\n        .btn-weibo:hover { background: linear-gradient(135deg, #c41230, #a00e26); box-shadow: 0 4px 16px rgba(230, 22, 45, 0.3); }\n        .btn-copy:hover { background: #0d47a1; box-shadow: 0 4px 16px rgba(23, 100, 255, 0.3); }\n        .btn-print:hover { background: linear-gradient(135deg, #138496, #117a8b); box-shadow: 0 4px 16px rgba(23, 162, 184, 0.3); }\n        .btn-weibo:hover, .btn-copy:hover, .btn-print:hover { transform: translateY(-2px); color: white; }\n        .article-actions { display: flex; justify-content: flex-end; align-items: center; }\n        .related-articles { margin-top: 3rem; }\n        .related-article-item { background: #fff; border-radius: 8px; padding: 1.5rem; margin-bottom: 1rem; box-shadow: 0 2px 8px rgba(0,0,0,0.1); transition: transform 0.3s ease; }\n        .related-article-item:hover { transform: translateY(-2px); }\n        .sidebar-card { background: linear-gradient(135deg, rgba(255,255,255,0.9) 0%, rgba(255,255,255,0.8) 100%); backdrop-filter: blur(20px); border: 1px solid rgba(255,255,255,0.3); border-radius: 16px; padding: 1rem; margin-bottom: 0.5rem; box-shadow: 0 4px 20px rgba(0,0,0,0.08), inset 0 1px 0 rgba(255,255,255,0.2); overflow: visible; box-sizing: border-box; width: 100%; z-index: 10; }\n        .sidebar-card.fixed { box-shadow: 0 8px 32px rgba(0,0,0,0.12); }\n        .sidebar-card::before { content: ''; position: absolute; top: 0; left: 0; right: 0; height: 3px; background: linear-gradient(90deg, #00d4ff, #1764ff, #9c27b0); border-radius: 20px 20px 0 0; }\n        .sidebar-card h5 { color: #1a202c; padding-bottom: 0.8rem; border-bottom: 2px solid rgba(23, 100, 255, 0.2); font-weight: 700; font-size: 1.1rem; }\n        .sidebar { min-height: 100vh; position: relative !important; display: block !important; flex-direction: column !important; gap: 1rem; }\n        .toc-card { flex: 0 1 auto; max-height: 60vh; overflow-y: auto; }\n        .sidebar-card:not(.toc-card) { flex: 1 1 auto; overflow: visible; }\n        .recommended-item { padding: 0.75rem 0; border-bottom: 1px solid rgba(0,0,0,0.05); }\n        .recommended-item:last-child { border-bottom: none; }\n        .recommended-item h6 { margin: 0 0 0.5rem 0; font-size: 0.9rem; line-height: 1.4; }\n        .recommended-item h6 a { color: #1a1c1f; text-decoration: none; transition: color 0.3s ease; }\n        .recommended-item h6 a:hover { color: #1764ff; }\n        .recommended-item .text-muted { font-size: 0.75rem; margin: 0; }\n        .toc-list { list-style: none; padding: 0; margin: 0; max-height: none; overflow-y: visible; overflow-x: hidden; }\n        .toc-list::-webkit-scrollbar { width: 4px; }\n        .toc-list::-webkit-scrollbar-track { background: rgba(0,0,0,0.1); border-radius: 2px; }\n        .toc-list::-webkit-scrollbar-thumb { background: linear-gradient(135deg, #00d4ff, #1764ff); border-radius: 2px; }\n        .toc-list::-webkit-scrollbar-thumb:hover { background: linear-gradient(135deg, #1764ff, #0d47a1); }\n        .toc-list a { color: #4a5568; text-decoration: none; font-size: 13px; font-weight: 500; display: block; padding: 8px 12px; border-radius: 8px; transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1); position: relative; margin-bottom: 4px; border: 1px solid transparent; }\n        .toc-list a::before { content: ''; position: absolute; left: 0; top: 0; bottom: 0; width: 3px; background: linear-gradient(135deg, #00d4ff, #1764ff); border-radius: 0 12px 12px 0; transform: scaleY(0); transition: transform 0.3s ease; }\n        .toc-list a:hover { color: #1764ff; background: linear-gradient(135deg, rgba(0, 212, 255, 0.1), rgba(23, 100, 255, 0.1)); border-color: rgba(23, 100, 255, 0.2); transform: translateX(8px); box-shadow: 0 4px 12px rgba(0, 212, 255, 0.2); }\n        .toc-list a:hover::before, .toc-list a.active::before { transform: scaleY(1); }\n        .toc-list a.active { color: #1764ff; background: linear-gradient(135deg, rgba(0, 212, 255, 0.15), rgba(23, 100, 255, 0.15)); font-weight: 700; border-color: rgba(23, 100, 255, 0.3); transform: translateX(4px); box-shadow: 0 6px 16px rgba(0, 212, 255, 0.25); }\n        .toc-number { color: #1764ff; font-weight: 600; margin-right: 0.5rem; font-size: 0.9em; }\n        .toc-text { flex: 1; }\n        .toc-level-1 { font-weight: 700; font-size: 15px; }\n        .toc-level-2 { font-size: 14px; }\n        .toc-level-3 { font-size: 13px; opacity: 0.9; }\n        .toc-level-4, .toc-level-5, .toc-level-6 { font-size: 12px; opacity: 0.8; }\n        .article-content .formatted-list { margin: 1.5rem 0; padding-left: 0; list-style: none; }\n        .article-content .formatted-list.ordered { counter-reset: list-counter; }\n        .article-content .formatted-list.ordered li { counter-increment: list-counter; position: relative; margin: 1rem 0; padding: 1rem 1rem 1rem 3rem; background: linear-gradient(135deg, rgba(23, 100, 255, 0.03), rgba(0, 212, 255, 0.03)); border-left: 4px solid #1764ff; border-radius: 0 8px 8px 0; transition: all 0.3s ease; }\n        .article-content .formatted-list.ordered li::before { content: counter(list-counter); position: absolute; left: 0.8rem; top: 1rem; background: linear-gradient(135deg, #1764ff, #00d4ff); color: white; width: 1.5rem; height: 1.5rem; border-radius: 50%; display: flex; align-items: center; justify-content: center; font-weight: 600; font-size: 0.9rem; }\n        .article-content .formatted-list.ordered li:hover { background: linear-gradient(135deg, rgba(23, 100, 255, 0.06), rgba(0, 212, 255, 0.06)); transform: translateX(4px); box-shadow: 0 2px 8px rgba(23, 100, 255, 0.15); }\n        .article-content .formatted-list.unordered li { position: relative; margin: 0.8rem 0; padding: 0.8rem 1rem 0.8rem 2.5rem; background: linear-gradient(135deg, rgba(40, 167, 69, 0.03), rgba(25, 135, 84, 0.03)); border-left: 4px solid #28a745; border-radius: 0 6px 6px 0; transition: all 0.3s ease; }\n        .article-content .formatted-list.unordered li::before { content: \"•\"; position: absolute; left: 1rem; top: 0.8rem; color: #28a745; font-size: 1.2rem; font-weight: bold; }\n        .article-content .formatted-list.unordered li:hover { background: linear-gradient(135deg, rgba(40, 167, 69, 0.06), rgba(25, 135, 84, 0.06)); transform: translateX(4px); box-shadow: 0 2px 8px rgba(40, 167, 69, 0.15); }\n        .article-content .list-title { color: #1764ff; font-weight: 600; display: inline-block; margin-bottom: 0.3rem; }\n        .article-content .list-content { color: #2c3e50; line-height: 1.6; display: block; }\n        .article-content .inline-code { background: #f8f9fa; border: 1px solid #e9ecef; border-radius: 4px; padding: 2px 6px; font-family: 'Consolas', 'Monaco', 'Courier New', monospace; font-size: 0.9em; color: #e83e8c; }\n        .article-content .label-item { margin: 0.8rem 0; }\n        .article-content .label { color: #1764ff; font-weight: 600; }\n        .article-content .section-label { color: #2c3e50; font-weight: 600; margin: 1rem 0 0.5rem 0; font-size: 1.1em; border-left: 4px solid #1764ff; padding-left: 0.8rem; }\n        .article-content p, .article-content .content-paragraph { margin: 1.2rem 0; line-height: 1.8; text-align: justify; color: #2c3e50; font-size: 1rem; }\n        .article-content .content-paragraph { text-indent: 0; margin: 1rem 0; }\n        .article-content .indent-paragraph { text-indent: 2em; margin: 1.2rem 0; }\n        .article-content .text-indent-1 { text-indent: 2em; display: block; }\n        .article-content .text-indent-2 { text-indent: 4em; display: block; }\n        .article-content .text-indent-3 { text-indent: 6em; display: block; }\n        .article-content .network-config { background: linear-gradient(135deg, #f8f9fa, #e9ecef); border: 1px solid #dee2e6; border-radius: 6px; padding: 0.8rem 1rem; margin: 0.5rem 0; font-family: 'Consolas', 'Monaco', 'Courier New', monospace; font-size: 0.9rem; box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05); }\n        .article-content .config-label { color: #1764ff; font-weight: 600; }\n        .article-content .config-desc { color: #495057; }\n        .article-content .algorithm-step { background: linear-gradient(135deg, rgba(23, 100, 255, 0.05), rgba(0, 212, 255, 0.05)); border-left: 4px solid #1764ff; padding: 0.8rem 1rem; margin: 0.5rem 0; border-radius: 0 6px 6px 0; transition: all 0.3s ease; }\n        .article-content .step-number { color: #1764ff; font-weight: 700; margin-right: 0.5rem; }\n        .article-content .step-desc { color: #2c3e50; line-height: 1.5; }\n        .article-content table { width: 100%; border-collapse: collapse; margin: 1.5rem 0; background: white; border-radius: 8px; overflow: hidden; box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1); }\n        .article-content table th { background: linear-gradient(135deg, #1764ff, #00d4ff); color: white; padding: 1rem; text-align: left; font-weight: 600; }\n        .article-content table td { padding: 0.8rem 1rem; border-bottom: 1px solid #e9ecef; }\n        .article-content table tr:hover { background: rgba(23, 100, 255, 0.05); }\n        .article-content strong { color: #1764ff; font-weight: 600; }\n        .article-content em { color: #6c757d; font-style: italic; }\n        .article-content a { color: #1764ff; text-decoration: none; border-bottom: 1px solid transparent; transition: all 0.3s ease; }\n        .article-content a:hover { border-bottom-color: #1764ff; color: #0056b3; }\n        .article-content .definition-item { background: linear-gradient(135deg, rgba(40, 167, 69, 0.05), rgba(25, 135, 84, 0.05)); border-left: 4px solid #28a745; padding: 1rem; margin: 1rem 0; border-radius: 0 8px 8px 0; }\n        .article-content .definition-label { color: #28a745; font-weight: 600; display: block; margin-bottom: 0.5rem; }\n        .article-content .definition-content { color: #2c3e50; line-height: 1.6; }\n        .article-content .note-item { background: linear-gradient(135deg, rgba(255, 193, 7, 0.1), rgba(255, 193, 7, 0.05)); border: 1px solid rgba(255, 193, 7, 0.3); border-radius: 8px; padding: 1rem; margin: 1rem 0; position: relative; }\n        .article-content .note-item::before { content: \"💡\"; position: absolute; left: 1rem; top: 1rem; font-size: 1.2em; }\n        .article-content .note-label { color: #856404; font-weight: 600; margin-left: 2rem; }\n        .article-content .note-content { color: #2c3e50; line-height: 1.6; margin-left: 2rem; display: block; margin-top: 0.3rem; }\n        .article-content .math-formula { background: #f8f9fa; border: 1px solid #e9ecef; border-radius: 6px; padding: 1rem; margin: 1rem 0; text-align: center; font-family: 'Times New Roman', serif; font-size: 1.1em; color: #2c3e50; }\n        @media (min-width: 1400px) { .article-content { font-size: 17px; line-height: 1.8; } .article-detail { padding: 2.5rem; } }\n        @media (max-width: 1199px) and (min-width: 992px) { .article-detail { padding: 1.5rem; } .sidebar-card { padding: 1rem; } .toc-list a { font-size: 12px; padding: 6px 8px; } }\n        @media (max-width: 768px) {\n            .article-detail-container { padding: 60px 15px; }\n            .article-content .network-config, .article-content .algorithm-step, .article-content .definition-item, .article-content .note-item { margin: 0.5rem -1rem; border-radius: 0; }\n            .article-content .note-item::before { left: 0.5rem; }\n            .article-content .note-label, .article-content .note-content { margin-left: 1.5rem; }\n            .article-detail { padding: 1.5rem; }\n            .article-title { font-size: 1.6rem; line-height: 1.3; }\n            .article-meta { flex-wrap: wrap; gap: 1rem; padding: 1rem; font-size: 0.8rem; }\n            .article-meta .meta-item { flex-shrink: 1; }\n            .meta-icon { font-size: 14px; margin-right: 3px; }\n            .article-content { font-size: 15px; }\n            .article-content h1 { font-size: 1.6rem; }\n            .article-content h2 { font-size: 1.4rem; }\n            .article-content h3 { font-size: 1.2rem; }\n            .article-content h4 { font-size: 1.1rem; }\n            .article-content pre { padding: 0.75rem; font-size: 0.85em; overflow-x: auto; }\n            .article-content code { font-size: 0.85em; padding: 1px 4px; }\n            .sidebar { margin-top: 2rem; }\n            .sidebar-card { position: static; }\n            .toc-card { max-height: none; overflow-y: visible; }\n            .share-buttons { flex-direction: column; gap: 0.5rem; }\n            .share-buttons .btn { width: 100%; justify-content: center; }\n        }\n        @media print { body * { visibility: hidden; } .article-detail, .article-detail * { visibility: visible; } .article-detail { position: absolute; left: 0; top: 0; width: 100%; background: white; box-shadow: none; border: none; margin: 0; padding: 20px; } .article-meta { border: none; background: none; padding: 0; margin-bottom: 20px; } .article-footer, .reading-progress, .back-to-top { display: none !important; } .article-content { font-size: 12pt; line-height: 1.6; } .article-content h1, .article-content h2, .article-content h3, .article-content h4, .article-content h5, .article-content h6 { page-break-after: avoid; margin-top: 20pt; margin-bottom: 10pt; } .article-content pre, .article-content blockquote { page-break-inside: avoid; } }\n    </style>\n\n<script type=\"application/ld+json\">{\"@context\":\"https://schema.org\",\"@type\":\"BreadcrumbList\",\"itemListElement\":[{\"@type\":\"ListItem\",\"position\":1,\"name\":\"Kuжi\",\"item\":\"https://ocr.oldfish.cn/default.aspx\"},{\"@type\":\"ListItem\",\"position\":2,\"name\":\"Tehnički članci\",\"item\":\"https://ocr.oldfish.cn/Articles.aspx\"},{\"@type\":\"ListItem\",\"position\":3,\"name\":\"Detalji o članku\",\"item\":\"https://ocr.oldfish.cn/Article.aspx?id=81&slug=rnn-sequence-modeling&lang=zh-Hans&_translation_source=true\"}]}</script>\n<link rel=\"stylesheet\" href=\"/site/css/site.css\" type=\"text/css\" /><title>【Deep Learning OCR serija · 4】 Rekurentne neuronske mreže i modeliranje sekvenci</title><meta http-equiv=\"Content-Language\" content=\"sr-Latn\" />\n</head>\r\n<body>\r\n    <div class=\"home-container root\">\r\n        <header class=\"v4_header_pc\">\r\n            <div class=\"header_pc_left\">\r\n                <a href=\"Default.aspx\" title=\"Kuća | AI inteligentno prepoznavanje teksta\" class=\"pc-logo-wrap ml-4 mr-5\">\r\n                    <picture>\r\n                        <source srcset=\"site/image/logo-s.png.webp\" type=\"image/webp\" />\r\n                        <img src=\"site/image/logo-s.png\" class=\"pc-logo\" style=\"height: 100%\" alt=\"OCR Asistent za prepoznavanje teksta Zvanični sajt Logo - AI Inteligentna platforma za prepoznavanje teksta\" />\r\n                    </picture>\r\n                    <span style=\"font-size: 18px; font-weight: bold; color: #333;\">OCR pomoćnik za prepoznavanje teksta</span>\r\n                </a>\r\n                <ul class=\"top-nav\" role=\"navigation\" aria-label=\"Glavna navigacija\">\r\n                    <li><a class=\"color-default\" href=\"Default.aspx\" title=\"OCR Asistent za prepoznavanje teksta početna stranica\">Kuжi</a></li>\r\n                    <li><a class=\"color-default\" data-sub=\"product-sub-nav\" href=\"Detail.aspx\" title=\"Uvod u funkciju OCR proizvoda\">Karakteristike proizvoda:</a><div class=\"triangle\"></div>\r\n                    </li>\r\n                    <li><a class=\"color-default\" data-sub=\"experience-sub-nav\" href=\"Ocr.aspx\" title=\"Doživite OCR funkcije na mreži\">Online iskustvo</a><div class=\"triangle\"></div>\r\n                    </li>\r\n                    <li><a class=\"color-default\" data-sub=\"vip-sub-nav\" href=\"Version.aspx\" title=\"OCR usluga nadogradnje članstva\">Nadogradnje članstva</a><div class=\"triangle\"></div>\r\n                    </li>\r\n                    <li><a class=\"color-default\" href=\"Download.aspx\" title=\"Preuzmite OCR Tekt Recognition Assistant besplatno\">Besplatno skidanje</a></li>\r\n                    <li><a class=\"color-default\" data-sub=\"articles-sub-nav\" href=\"Articles.aspx\" title=\"OCR tehnički članci i razmena znanja\">Deljenje tehnologije</a><div class=\"triangle\"></div>\r\n                    </li>\r\n                    <li><a class=\"color-default\" data-sub=\"support-sub-nav\" href=\"FAQ.aspx\" title=\"OCR upotreba pomoć i tehnička podrška\">Centar za pomoć</a><div class=\"triangle\"></div>\r\n                    </li>\r\n                </ul>\r\n                <div class=\"product-con\">\r\n                    <section id=\"product-sub-nav\" class=\"pls-nav-dropdown\" style=\"visibility: hidden; height: 0px; display: none;\">\r\n                        <div class=\"d-flex nav-drown-con\" style=\"height: 0px; transition: transform 0.2s ease-in-out 0s,opacity 0.2s ease-in-out 0s; opacity: 0; transform: translate(0px, -100%);\">\r\n                            <div class=\"prdocu-sub-left\">\r\n                                <div class=\"mr-auto\">\r\n                                    <picture>\r\n                                        <source class=\"p-icon\" srcset=\"site/image/icon/p_1.png.webp\" type=\"image/webp\" alt=\"\" aria-hidden=\"true\" />\r\n                                        <img class=\"p-icon\" src=\"site/image/icon/p_1.png\" alt=\"OCR funkcija proizvoda ikona\" aria-hidden=\"true\" loading=\"lazy\" />\r\n                                    </picture>\r\n                                    <div class=\"mt-4\">\r\n                                        <h3 class=\"h6\">OCR pomoćnik za prepoznavanje teksta</h3>\r\n                                        <p class=\"color-gray fn14 mt-3 sub-left-desc\">Poboljšati efikasnost, smanjiti troškove, i stvoriti vrednost</p>\r\n                                        <p class=\"color-gray fn14 mt-3 sub-left-desc\">&nbsp;</p>\r\n                                        <p class=\"color-gray fn14 mt-3 sub-left-desc\">Inteligentno prepoznavanje, obrada velike brzine i precizan izlaz</p>\r\n                                        <p class=\"color-gray fn14 mt-3 sub-left-desc\">Od teksta do tabela, od formula do prevoda</p>\r\n                                        <p class=\"color-gray fn14 mt-3 sub-left-desc\">Učinite svaku obradu teksta tako lako</p>\r\n                                        <a class=\"link block disa fn14 contact-business shake\" href=\"Detail.aspx\">Saznajte više o karakteristikama<span class=\"iconfont4 icon-xiangzuo gt\"></span></a>\r\n                                    </div>\r\n                                </div>\r\n                            </div>\r\n                            <div class=\"bg-white prdocu-sub-right\">\r\n                                <div>\r\n                                    <h2 class=\"text-base-color h6\">Karakteristike proizvoda:</h2>\r\n                                    <div class=\"mt-4 d-flex flex-wrap\">\r\n                                        <a href=\"Detail.aspx\" class=\"pro-item color-default\" title=\"Pogledajte detalje osnovnih funkcija OCR Assistant\">\r\n                                            <div class=\"letter-wrap\">\r\n                                                <h3 class=\"h6\">Osnovne karakteristike:</h3>\r\n                                                <span class=\"color-gray fn14\">Saznajte više o osnovnim karakteristikama i tehničkim prednostima OCR Assistant-a, sa stopom prepoznavanja KSNUMKS% +</span>\r\n                                            </div>\r\n                                        </a>\r\n                                        <a href=\"Version.aspx\" class=\"pro-item color-default\" title=\"Uporedite razlike između verzija OCR Assistant\">\r\n                                            <div class=\"letter-wrap\">\r\n                                                <h3 class=\"h6\">Poređenje verzija</h3>\r\n                                                <span class=\"color-gray fn14\">Uporedite funkcionalne razlike besplatne verzije, lične verzije, profesionalne verzije i krajnje verzije u detalje</span>\r\n                                            </div>\r\n                                        </a>\r\n                                        <a href=\"FAQ.aspx\" class=\"pro-item color-default\" title=\"Pogledajte FAK OCR pomoćnika\">\r\n                                            <div class=\"letter-wrap\">\r\n                                                <h3 class=\"h6\">Pitanja i odgovori o proizvodu</h3>\r\n                                                <span class=\"color-gray fn14\">Brzo saznajte o karakteristikama proizvoda, metodama korišćenja i detaljnim odgovorima na često postavljana pitanja</span>\r\n                                            </div>\r\n                                        </a>\r\n                                        <a href=\"Download.aspx\" class=\"pro-item color-default\" title=\"Preuzmite OCR Tekt Recognition Assistant besplatno\">\r\n                                            <div class=\"letter-wrap\">\r\n                                                <h3 class=\"h6\">Isprobajte besplatno</h3>\r\n                                                <span class=\"color-gray fn14\">Preuzmite i instalirajte OCR Assistant sada da biste besplatno iskusili moćnu funkciju prepoznavanja teksta</span>\r\n                                            </div>\r\n                                        </a>\r\n                                        <div style=\"width: 100%; margin: 15px 0;\">\r\n                                            <h4 class=\"h6\" style=\"color: #007cfa; margin-bottom: 10px;\">Onlajn OCR prepoznavanje</h4>\r\n                                        </div>\r\n                                        <a href=\"Ocr.aspx?type=text_recognize\" class=\"pro-item color-default\" title=\"Doživite univerzalno prepoznavanje teksta na mreži\">\r\n                                            <div class=\"letter-wrap\">\r\n                                                <h3 class=\"h6\">Univerzalno prepoznavanje karaktera</h3>\r\n                                                <span class=\"color-gray fn14\">Inteligentna vađenje višejezičnog teksta visoke preciznosti, podržavajući štampano i multi-scensko prepoznavanje složenih slika</span>\r\n                                            </div>\r\n                                        </a>\r\n                                        <a href=\"Ocr.aspx?type=table\" class=\"pro-item color-default\">\r\n                                            <div class=\"letter-wrap\">\r\n                                                <h3 class=\"h6\">Univerzalna Tabela Identifikacija</h3>\r\n                                                <span class=\"color-gray fn14\">Inteligentna konverzija tabelarnih slika u Ekcel datoteke, automatska obrada složenih struktura tabela i spojenih ćelija</span>\r\n                                            </div>\r\n                                        </a>\r\n                                        <a href=\"Ocr.aspx?type=handwritten_ocr\" class=\"pro-item color-default\">\r\n                                            <div class=\"letter-wrap\">\r\n                                                <h3 class=\"h6\">Prepoznavanje rukopisa</h3>\r\n                                                <span class=\"color-gray fn14\">Inteligentno prepoznavanje kineskog i engleskog rukopisnog sadržaja, podrška beleškama u učionici, medicinskoj dokumentaciji i drugim scenarijima</span>\r\n                                            </div>\r\n                                        </a>\r\n                                        <a href=\"Ocr.aspx?type=pdf2word\" class=\"pro-item color-default\">\r\n                                            <div class=\"letter-wrap\">\r\n                                                <h3 class=\"h6\">PDF u Vord</h3>\r\n                                                <span class=\"color-gray fn14\">PDF dokumenti se brzo pretvaraju u Vord format, savršeno čuvajući originalni izgled i grafički izgled</span>\r\n                                            </div>\r\n                                        </a>\r\n                                    </div>\r\n                                </div>\r\n                            </div>\r\n                        </div>\r\n                    </section>\r\n                    <section id=\"experience-sub-nav\" class=\"pls-nav-dropdown solution-part\" style=\"visibility: hidden; height: 0px; display: none;\">\r\n                        <div class=\"d-flex nav-drown-con\" style=\"height: 0px; transition: transform 0.2s ease-in-out 0s,opacity 0.2s ease-in-out 0s; opacity: 0; transform: translate(0px, -100%);\">\r\n                            <div class=\"prdocu-sub-left\">\r\n                                <div class=\"mr-auto\">\r\n                                    <picture>\r\n                                        <source srcset=\"site/image/icon/p_4.png.webp\" type=\"image/webp\" />\r\n                                        <img class=\"p-icon\" src=\"site/image/icon/p_4.png\" alt=\"Ikona Online OCR Experience Center\" aria-hidden=\"true\" loading=\"lazy\" />\r\n                                    </picture>\r\n                                    <div class=\"mt-4\">\r\n                                        <h3 class=\"h6\">OCR pomoćnik za prepoznavanje teksta</h3>\r\n                                        <p class=\"color-gray fn14 mt-3 sub-left-desc\">Tekst, tabele, formule, dokumenti, prevodi</p>\r\n                                        <p class=\"color-gray fn14 mt-3 sub-left-desc\">&nbsp;</p>\r\n                                        <p class=\"color-gray fn14 mt-3 sub-left-desc\">Popunite sve svoje potrebe za obradom teksta u tri koraka</p>\r\n                                        <p class=\"color-gray fn14 mt-3 sub-left-desc\">Snimak ekrana → Identifikujte → aplikacije</p>\r\n                                        <p class=\"color-gray fn14 mt-3 sub-left-desc\">Povećajte efikasnost rada za 300%</p>\r\n                                        <a class=\"link block disa fn14 contact-business shake\" href=\"Ocr.aspx\">Probajte sada<span class=\"iconfont4 icon-xiangzuo gt\"></span></a>\r\n                                    </div>\r\n                                </div>\r\n                            </div>\r\n                            <div class=\"bg-white prdocu-sub-right\">\r\n                                <div>\r\n                                    <div class=\"mt-4 d-flex flex-wrap\">\r\n                                        <div style=\"width: 100%; margin-bottom: 15px;\">\r\n                                            <h4 class=\"h6\" style=\"color: #007cfa; margin-bottom: 10px;\">OCR funkcija iskustvo</h4>\r\n                                        </div>\r\n                                        <a href=\"Ocr.aspx?type=index\" class=\"pro-item color-default\">\r\n                                            <div class=\"letter-wrap\">\r\n                                                <h3 class=\"h6\">Potpuna funkcionalnost</h3>\r\n                                                <span class=\"color-gray fn14\">Doživite sve pametne funkcije OCR-a na jednom mestu kako biste brzo pronašli najbolje rešenje za vaše potrebe</span>\r\n                                            </div>\r\n                                        </a>\r\n                                        <a href=\"Ocr.aspx?type=text_recognize\" class=\"pro-item color-default\">\r\n                                            <div class=\"letter-wrap\">\r\n                                                <h3 class=\"h6\">Univerzalno prepoznavanje karaktera</h3>\r\n                                                <span class=\"color-gray fn14\">Inteligentna vađenje višejezičnog teksta visoke preciznosti, podržavajući štampano i multi-scensko prepoznavanje složenih slika</span>\r\n                                            </div>\r\n                                        </a>\r\n                                        <a href=\"Ocr.aspx?type=table\" class=\"pro-item color-default\">\r\n                                            <div class=\"letter-wrap\">\r\n                                                <h3 class=\"h6\">Univerzalna Tabela Identifikacija</h3>\r\n                                                <span class=\"color-gray fn14\">Inteligentna konverzija tabelarnih slika u Ekcel datoteke, automatska obrada složenih struktura tabela i spojenih ćelija</span>\r\n                                            </div>\r\n                                        </a>\r\n                                        <a href=\"Ocr.aspx?type=handwritten_ocr\" class=\"pro-item color-default\">\r\n                                            <div class=\"letter-wrap\">\r\n                                                <h3 class=\"h6\">Prepoznavanje rukopisa</h3>\r\n                                                <span class=\"color-gray fn14\">Inteligentno prepoznavanje kineskog i engleskog rukopisnog sadržaja, podrška beleškama u učionici, medicinskoj dokumentaciji i drugim scenarijima</span>\r\n                                            </div>\r\n                                        </a>\r\n                                        <a href=\"Ocr.aspx?type=pdf2word\" class=\"pro-item color-default\">\r\n                                            <div class=\"letter-wrap\">\r\n                                                <h3 class=\"h6\">PDF u Vord</h3>\r\n                                                <span class=\"color-gray fn14\">PDF dokumenti se brzo pretvaraju u Vord format, savršeno čuvajući originalni izgled i grafički izgled</span>\r\n                                            </div>\r\n                                        </a>\r\n                                        <a href=\"Ocr.aspx?type=pdf2markdown\" class=\"pro-item color-default\">\r\n                                            <div class=\"letter-wrap\">\r\n                                                <h3 class=\"h6\">PDF u Markdovn</h3>\r\n                                                <span class=\"color-gray fn14\">PDF dokumenti su inteligentno konvertovani u MD formatu, a blokovi koda i tekstualne strukture se automatski optimizuju za obradu</span>\r\n                                            </div>\r\n                                        </a>\r\n                                        <div style=\"width: 100%; margin: 15px 0;\">\r\n                                            <h4 class=\"h6\" style=\"color: #007cfa; margin-bottom: 10px;\">Alati za obradu dokumenata</h4>\r\n                                        </div>\r\n                                        <a href=\"Ocr.aspx?type=word2pdf\" class=\"pro-item color-default\">\r\n                                            <div class=\"letter-wrap\">\r\n                                                <h3 class=\"h6\">Reč u PDF</h3>\r\n                                                <span class=\"color-gray fn14\">Vord dokumenti se pretvaraju u PDF jednim klikom, savršeno zadržavajući originalni format, pogodan za arhiviranje i zvaničnu deljenje dokumenata</span>\r\n                                            </div>\r\n                                        </a>\r\n                                        <a href=\"Ocr.aspx?type=word2jpg\" class=\"pro-item color-default\">\r\n                                            <div class=\"letter-wrap\">\r\n                                                <h3 class=\"h6\">Od reči do slike</h3>\r\n                                                <span class=\"color-gray fn14\">Reč dokument inteligentna konverzija u JPG sliku, podržava obradu više stranica, lako se deli na društvenim medijima</span>\r\n                                            </div>\r\n                                        </a>\r\n                                        <a href=\"Ocr.aspx?type=pdf2jpg\" class=\"pro-item color-default\">\r\n                                            <div class=\"letter-wrap\">\r\n                                                <h3 class=\"h6\">PDF u sliku</h3>\r\n                                                <span class=\"color-gray fn14\">Pretvoriti PDF dokumenata u JPG slike u visokoj definiciji, podrška batch obradu i prilagođenu rezoluciju</span>\r\n                                            </div>\r\n                                        </a>\r\n                                        <a href=\"Ocr.aspx?type=image2pdf\" class=\"pro-item color-default\">\r\n                                            <div class=\"letter-wrap\">\r\n                                                <h3 class=\"h6\">Slika u PDF</h3>\r\n                                                <span class=\"color-gray fn14\">Spojite više slika u PDF dokumente, podržite sortiranje i podešavanje stranice</span>\r\n                                            </div>\r\n                                        </a>\r\n                                        <div style=\"width: 100%; margin: 15px 0;\">\r\n                                            <h4 class=\"h6\" style=\"color: #007cfa; margin-bottom: 10px;\">Alati za programere</h4>\r\n                                        </div>\r\n                                        <a href=\"Tool.aspx?type=Json\" class=\"pro-item color-default\">\r\n                                            <div class=\"letter-wrap\">\r\n                                                <h3 class=\"h6\">JSON formatiranje</h3>\r\n                                                <span class=\"color-gray fn14\">Inteligentno ulepšati strukturu JSON koda, podržati kompresiju i proširenje i olakšati razvoj i otklanjanje grešaka</span>\r\n                                            </div>\r\n                                        </a>\r\n                                        <a href=\"Tool.aspx?type=Regex\" class=\"pro-item color-default\">\r\n                                            <div class=\"letter-wrap\">\r\n                                                <h3 class=\"h6\">regularni izraz</h3>\r\n                                                <span class=\"color-gray fn14\">Proverite efekte podudaranja regularnog izraza u realnom vremenu, sa ugrađenom bibliotekom zajedničkih obrazaca</span>\r\n                                            </div>\r\n                                        </a>\r\n                                        <a href=\"Tool.aspx?type=Encode\" class=\"pro-item color-default\">\r\n                                            <div class=\"letter-wrap\">\r\n                                                <h3 class=\"h6\">Konverzija kodiranja teksta</h3>\r\n                                                <span class=\"color-gray fn14\">Podržava konverziju više formata kodiranja kao što su Base64, URL i Unicode</span>\r\n                                            </div>\r\n                                        </a>\r\n                                        <a href=\"Tool.aspx?type=Diff\" class=\"pro-item color-default\">\r\n                                            <div class=\"letter-wrap\">\r\n                                                <h3 class=\"h6\">Usklađivanje i spajanje teksta</h3>\r\n                                                <span class=\"color-gray fn14\">Istaknite razlike u tekstu i podržite poređenje linija po liniju i inteligentno spajanje</span>\r\n                                            </div>\r\n                                        </a>\r\n                                        <a href=\"Tool.aspx?type=Color\" class=\"pro-item color-default\">\r\n                                            <div class=\"letter-wrap\">\r\n                                                <h3 class=\"h6\">Boja alat</h3>\r\n                                                <span class=\"color-gray fn14\">RGB / HEKS konverzija boja, online birač boja, neophodan alat za razvoj front-enda</span>\r\n                                            </div>\r\n                                        </a>\r\n                                        <a href=\"Tool.aspx?type=Count\" class=\"pro-item color-default\">\r\n                                            <div class=\"letter-wrap\">\r\n                                                <h3 class=\"h6\">Broj reči</h3>\r\n                                                <span class=\"color-gray fn14\">Inteligentno brojanje znakova, vokabulara i paragrafa i automatsko optimizovanje rasporeda teksta</span>\r\n                                            </div>\r\n                                        </a>\r\n                                        <a href=\"Tool.aspx?type=Timespan\" class=\"pro-item color-default\">\r\n                                            <div class=\"letter-wrap\">\r\n                                                <h3 class=\"h6\">Konverzija vremenskih oznaka</h3>\r\n                                                <span class=\"color-gray fn14\">Vreme se pretvara u i iz Unik vremenskih oznaka, a podržani su višestruki formati i podešavanja vremenske zone</span>\r\n                                            </div>\r\n                                        </a>\r\n                                        <a href=\"Tool.aspx?type=Calc\" class=\"pro-item color-default\">\r\n                                            <div class=\"letter-wrap\">\r\n                                                <h3 class=\"h6\">Kalkulator alat</h3>\r\n                                                <span class=\"color-gray fn14\">Online naučni kalkulator sa podrškom za osnovne operacije i napredne matematičke funkcije proračuna</span>\r\n                                            </div>\r\n                                        </a>\r\n                                    </div>\r\n                                </div>\r\n                            </div>\r\n                        </div>\r\n                    </section>\r\n                    <section id=\"articles-sub-nav\" class=\"pls-nav-dropdown solution-part\" style=\"visibility: hidden; height: 0px; display: none;\">\r\n                        <div class=\"d-flex nav-drown-con\" style=\"height: 0px; transition: transform 0.2s ease-in-out 0s,opacity 0.2s ease-in-out 0s; opacity: 0; transform: translate(0px, -100%);\">\r\n                            <div class=\"prdocu-sub-left\">\r\n                                <div class=\"mr-auto\">\r\n                                    <picture>\r\n                                        <source srcset=\"site/image/icon/p_4.png.webp\" type=\"image/webp\" />\r\n                                        <img class=\"p-icon\" src=\"site/image/icon/p_4.png\" alt=\"Ikona Centra za deljenje tehnologije\" aria-hidden=\"true\" loading=\"lazy\" />\r\n                                    </picture>\r\n                                    <div class=\"mt-4\">\r\n                                        <h3 class=\"h6\">OCR deljenje tehnologije</h3>\r\n                                        <p class=\"color-gray fn14 mt-3 sub-left-desc\">Tehnički tutoriali, slučajevi primene, preporuke alata</p>\r\n                                        <p class=\"color-gray fn14 mt-3 sub-left-desc\">&nbsp;</p>\r\n                                        <p class=\"color-gray fn14 mt-3 sub-left-desc\">Kompletan put učenja od početnika do majstorstva</p>\r\n                                        <p class=\"color-gray fn14 mt-3 sub-left-desc\">Praktični slučajevi → tehničku analizu → aplikacije alata</p>\r\n                                        <p class=\"color-gray fn14 mt-3 sub-left-desc\">Osnažite svoj put ka poboljšanju OCR tehnologije</p>\r\n                                        <a class=\"link block disa fn14 contact-business shake\" href=\"Articles.aspx\">Pregledaj članke<span class=\"iconfont4 icon-xiangzuo gt\"></span></a>\r\n                                    </div>\r\n                                </div>\r\n                            </div>\r\n                            <div class=\"bg-white prdocu-sub-right\">\r\n                                <div>\r\n                                    <h2 class=\"text-base-color h6\">Deljenje tehnologije</h2>\r\n                                    <div class=\"mt-4 d-flex flex-wrap\">\r\n                                        <a href=\"Articles.aspx\" class=\"pro-item color-default\" title=\"Pogledajte sve OCR tehničke članke\">\r\n                                            <div class=\"letter-wrap\">\r\n                                                <h3 class=\"h6\">Svi članci</h3>\r\n                                                <span class=\"color-gray fn14\">Pregledajte sve OCR tehničke članke koji pokrivaju kompletan korpus znanja od osnovnih do naprednih</span>\r\n                                            </div>\r\n                                        </a>\r\n                                        \r\n                                        <a href=\"Articles.aspx?category=2\" class=\"pro-item color-default\" title=\"OCR tehnički tutorijali i vodiči za početak rada\">\r\n                                            <div class=\"letter-wrap\">\r\n                                                <h3 class=\"h6\">Napredni vodič</h3>\r\n                                                <span class=\"color-gray fn14\">Od uvodnih do stručnih OCR tehničkih tutorijala, detaljnih vodiča i praktičnih uputa</span>\r\n                                            </div>\r\n                                        </a>\r\n                                        \r\n                                        <a href=\"Articles.aspx?category=3\" class=\"pro-item color-default\" title=\"Principi OCR tehnologije, algoritmi i aplikacije\">\r\n                                            <div class=\"letter-wrap\">\r\n                                                <h3 class=\"h6\">Tehnološka istraživanja</h3>\r\n                                                <span class=\"color-gray fn14\">Istražite granice OCR tehnologije, od principa do aplikacija, i duboko analizirajte osnovne algoritme</span>\r\n                                            </div>\r\n                                        </a>\r\n                                        \r\n                                        <a href=\"Articles.aspx?category=4\" class=\"pro-item color-default\" title=\"Najnovija dostignuća i trendovi razvoja u OCR industriji\">\r\n                                            <div class=\"letter-wrap\">\r\n                                                <h3 class=\"h6\">Trendovi u industriji</h3>\r\n                                                <span class=\"color-gray fn14\">Detaljan uvid u trendove razvoja OCR tehnologije, analizu tržišta, dinamiku industrije i buduće izglede</span>\r\n                                            </div>\r\n                                        </a>\r\n                                        \r\n                                        <a href=\"Articles.aspx?category=5\" class=\"pro-item color-default\" title=\"Slučajevi primene OCR tehnologije u različitim industrijama\">\r\n                                            <div class=\"letter-wrap\">\r\n                                                <h3 class=\"h6\">Slučajevi upotrebe:</h3>\r\n                                                <span class=\"color-gray fn14\">Dele se stvarni slučajevi primene, rešenja i najbolje prakse OCR tehnologije u različitim industrijama</span>\r\n                                            </div>\r\n                                        </a>\r\n                                        \r\n                                        <a href=\"Articles.aspx?category=6\" class=\"pro-item color-default\" title=\"Stručni pregledi, komparativna analiza, i preporučene smernice za korišćenje OCR softverskih alata\">\r\n                                            <div class=\"letter-wrap\">\r\n                                                <h3 class=\"h6\">Pregled alata</h3>\r\n                                                <span class=\"color-gray fn14\">Procenite različite OCR softver i alate za prepoznavanje teksta i pružite detaljne predloge za poređenje funkcija i izbor</span>\r\n                                            </div>\r\n                                        </a>\r\n                                        \r\n                                    </div>\r\n                                </div>\r\n                            </div>\r\n                        </div>\r\n                    </section>\r\n                    <section id=\"vip-sub-nav\" class=\"pls-nav-dropdown solution-part\" style=\"visibility: hidden; height: 0px; display: none;\">\r\n                        <div class=\"d-flex nav-drown-con\" style=\"height: 0px; transition: transform 0.2s ease-in-out 0s,opacity 0.2s ease-in-out 0s; opacity: 0; transform: translate(0px, -100%);\">\r\n                            <div class=\"prdocu-sub-left\">\r\n                                <div class=\"mr-auto\">\r\n                                    <picture>\r\n                                        <source class=\"p-icon\" srcset=\"site/image/icon/p_1.png.webp\" type=\"image/webp\" alt=\"\" aria-hidden=\"true\" />\r\n                                        <img class=\"p-icon\" src=\"site/image/icon/p_1.png\" alt=\"Ikona usluge nadogradnje članstva\" aria-hidden=\"true\" loading=\"lazy\" />\r\n                                    </picture>\r\n                                    <div class=\"mt-4\">\r\n                                        <h3 class=\"h6\">Usluga nadogradnje članstva</h3>\r\n                                        <p class=\"color-gray fn14 mt-3 sub-left-desc\">Otključajte sve premium funkcije i uživajte u ekskluzivnim uslugama</p>\r\n                                        <p class=\"color-gray fn14 mt-3 sub-left-desc\">&nbsp;</p>\r\n                                        <p class=\"color-gray fn14 mt-3 sub-left-desc\">Offline prepoznavanje, batch obrada, neograničena upotreba</p>\r\n                                        <p class=\"color-gray fn14 mt-3 sub-left-desc\">Pro → Ultimate → preduzeća</p>\r\n                                        <p class=\"color-gray fn14 mt-3 sub-left-desc\">Postoji nešto što odgovara vašim potrebama</p>\r\n                                        <a class=\"link block disa fn14 contact-business shake\" href=\"Version.aspx\">Pogledaj detalje<span class=\"iconfont4 icon-xiangzuo gt\"></span></a>\r\n                                    </div>\r\n                                </div>\r\n                            </div>\r\n                            <div class=\"bg-white prdocu-sub-right\">\r\n                                <div>\r\n                                    <h2 class=\"text-base-color h6\" style=\"margin: inherit;\">Nadogradnje članstva</h2>\r\n                                    <div class=\"mt-4 d-flex flex-wrap\">\r\n                                        <a href=\"Version.aspx\" class=\"pro-item color-default\">\r\n                                            <div class=\"letter-wrap\">\r\n                                                <h3 class=\"h6\">Privilegije članstva</h3>\r\n                                                <span class=\"color-gray fn14\">Saznajte više o razlikama između izdanja i izaberite nivo članstva koji vam najviše odgovara</span>\r\n                                            </div>\r\n                                        </a>\r\n                                        <a href=\"Upgrade.aspx\" class=\"pro-item color-default\">\r\n                                            <div class=\"letter-wrap\">\r\n                                                <h3 class=\"h6\">Nadogradite sada</h3>\r\n                                                <span class=\"color-gray fn14\">Brzo nadogradite svoje VIP članstvo da biste otključali više premium funkcija i ekskluzivnih usluga</span>\r\n                                            </div>\r\n                                        </a>\r\n                                        <a href=\"User.aspx\" class=\"pro-item color-default\">\r\n                                            <div class=\"letter-wrap\">\r\n                                                <h3 class=\"h6\">Moj nalog</h3>\r\n                                                <span class=\"color-gray fn14\">Upravljajte informacijama o nalogu, statusu pretplate i istoriji korišćenja za personalizaciju postavki</span>\r\n                                            </div>\r\n                                        </a>\r\n                                    </div>\r\n                                </div>\r\n                            </div>\r\n                        </div>\r\n                    </section>\r\n                    <section id=\"support-sub-nav\" class=\"pls-nav-dropdown solution-part\" style=\"visibility: hidden; height: 0px; display: none;\">\r\n                        <div class=\"d-flex nav-drown-con\" style=\"height: 0px; transition: transform 0.2s ease-in-out 0s,opacity 0.2s ease-in-out 0s; opacity: 0; transform: translate(0px, -100%);\">\r\n                            <div class=\"prdocu-sub-left\">\r\n                                <div class=\"mr-auto\">\r\n                                    <picture>\r\n                                        <source class=\"p-icon\" srcset=\"site/image/icon/p_4.png.webp\" type=\"image/webp\" alt=\"\" aria-hidden=\"true\" />\r\n                                        <img class=\"p-icon\" src=\"site/image/icon/p_4.png\" alt=\"Ikona podrške centra za pomoć\" aria-hidden=\"true\" loading=\"lazy\" />\r\n                                    </picture>\r\n                                    <div class=\"mt-4\">\r\n                                        <h3 class=\"h6\">Centar za pomoć</h3>\r\n                                        <p class=\"color-gray fn14 mt-3 sub-left-desc\">Profesionalna služba za korisnike, detaljna dokumentacija i brz odgovor</p>\r\n                                        <p class=\"color-gray fn14 mt-3 sub-left-desc\">&nbsp;</p>\r\n                                        <p class=\"color-gray fn14 mt-3 sub-left-desc\">Ne paničite kada naiđete na probleme</p>\r\n                                        <p class=\"color-gray fn14 mt-3 sub-left-desc\">Problem → Pronađite → rešen</p>\r\n                                        <p class=\"color-gray fn14 mt-3 sub-left-desc\">Učinite svoje iskustvo lakšim</p>\r\n                                        <a class=\"link block disa fn14 contact-business shake\" href=\"FAQ.aspx\">Potražite pomoć<span class=\"iconfont4 icon-xiangzuo gt\"></span></a>\r\n                                    </div>\r\n                                </div>\r\n                            </div>\r\n                            <div class=\"bg-white prdocu-sub-right\">\r\n                                <div>\r\n                                    <h2 class=\"text-base-color h6\" style=\"margin: inherit;\">Centar za pomoć</h2>\r\n                                    <div class=\"mt-4 d-flex flex-wrap\">\r\n                                        <a href=\"FAQ.aspx\" class=\"pro-item color-default\">\r\n                                            <div class=\"letter-wrap\">\r\n                                                <h3 class=\"h6\">Najčešća pitanja</h3>\r\n                                                <span class=\"color-gray fn14\">Brzo odgovorite na uobičajena pitanja korisnika i pružite detaljne vodiče za upotrebu i tehničku podršku</span>\r\n                                            </div>\r\n                                        </a>\r\n                                        <a href=\"AboutUs.aspx\" class=\"pro-item color-default\">\r\n                                            <div class=\"letter-wrap\">\r\n                                                <h3 class=\"h6\">O nama</h3>\r\n                                                <span class=\"color-gray fn14\">Saznajte više o istoriji razvoja, osnovnim funkcijama i konceptima usluga OCR pomoćnika za prepoznavanje teksta</span>\r\n                                            </div>\r\n                                        </a>\r\n                                        <a href=\"UserAgreement.aspx\" class=\"pro-item color-default\">\r\n                                            <div class=\"letter-wrap\">\r\n                                                <h3 class=\"h6\">Korisnički ugovor</h3>\r\n                                                <span class=\"color-gray fn14\">Detaljni uslovi korišćenja i prava i obaveze korisnika</span>\r\n                                            </div>\r\n                                        </a>\r\n                                        <a href=\"PrivacyPolicy.aspx\" class=\"pro-item color-default\">\r\n                                            <div class=\"letter-wrap\">\r\n                                                <h3 class=\"h6\">Ugovor o privatnosti</h3>\r\n                                                <span class=\"color-gray fn14\">Politika zaštite ličnih podataka i mere bezbednosti podataka</span>\r\n                                            </div>\r\n                                        </a>\r\n                                        <a href=\"Status.aspx\" class=\"pro-item color-default\">\r\n                                            <div class=\"letter-wrap\">\r\n                                                <h3 class=\"h6\">Status sistema</h3>\r\n                                                <span class=\"color-gray fn14\">Pratite status rada globalnih identifikacionih čvorova u realnom vremenu i pregledajte podatke o performansama sistema</span>\r\n                                            </div>\r\n                                        </a>\r\n                                        <a href=\"javascript:void(0);\" class=\"pro-item color-default\" onclick=\"if(window.openCustomerService) window.openCustomerService(); else alert('Kliknite na ikonu plutajućeg prozora sa desne strane da biste kontaktirali korisnički servis');\">\r\n                                            <div class=\"letter-wrap\">\r\n                                                <h3 class=\"h6\">Kontaktirajte korisnički servis</h3>\r\n                                                <span class=\"color-gray fn14\">Online korisnički servis podrška da brzo odgovori na vaša pitanja i potrebe</span>\r\n                                            </div>\r\n                                        </a>\r\n                                    </div>\r\n                                </div>\r\n                            </div>\r\n                        </div>\r\n                    </section>\r\n                </div>\r\n            </div>\r\n            <div class=\"header_pc_right\" style=\"display: flex; align-items: center; justify-content: flex-end;\">\r\n                <div id=\"translate\"></div>\r\n                <div id=\"user-login-container\" data-url=\"User.ashx?op=userinfo\" style=\"white-space: nowrap; padding-right: 20px;\"></div>\r\n            </div>\r\n        </header>\r\n        <header class=\"v4_header_mob\">\r\n            <div class=\"container\">\r\n                <div class=\"row no-gutters justify-content-between align-items-center\">\r\n                    <a href=\"Default.aspx\" title=\"Kuća | AI inteligentno prepoznavanje teksta\" class=\"mob-logo-wrap\">\r\n                        <picture>\r\n                            <source srcset=\"site/image/logo-s.png.webp\" type=\"image/webp\" />\r\n                            <img src=\"site/image/logo-s.png\" class=\"pc-logo\" style=\"height: 32px; width: 32px\" alt=\"OCR prepoznavanje teksta pomoćnik mobilni logo\" />\r\n                        </picture>\r\n                        <span style=\"font-size: 18px; font-weight: bold; color: #333;\">OCR pomoćnik za prepoznavanje teksta</span>\r\n                    </a>\r\n                    <div class=\"right-menu\" role=\"button\" tabindex=\"0\" aria-label=\"Otvorite navigacioni meni\" data-toggle=\"collapse\" data-target=\"#mob-header-collapse\" aria-expanded=\"false\" aria-controls=\"mob-header-collapse\">\r\n                        <nav-products id=\"nav_products\">\r\n                            <nav-button class=\"submail-mdi-nav-btn\" style=\"width: 60px\">\r\n                                <label id=\"mdi-submail-nav-btn\">\r\n                                    <span class=\"mdi-nav-products-1\">\r\n                                        <span class=\"nav-products-icon\"></span>\r\n                                    </span>\r\n                                    <span class=\"mdi-nav-products-2\">\r\n                                        <span class=\"nav-products-icon\"></span>\r\n                                    </span>\r\n                                    <span class=\"mdi-nav-products-3\">\r\n                                        <span class=\"nav-products-icon\"></span>\r\n                                    </span>\r\n                                </label>\r\n                            </nav-button>\r\n                        </nav-products>\r\n                    </div>\r\n                </div>\r\n            </div>\r\n            <div class=\"mob-nav-content\">\r\n                <div class=\"sidebar-fix\">\r\n                    <div class=\"container\">\r\n                        <div class=\"row\">\r\n                            <div class=\"col-4 bg-white sidebar-fix-left\" style=\"border-right: 1px solid #e1e6ed\">\r\n                                <div class=\"mob-nav-item active\" data=\"0\">\r\n                                    <div class=\"nav-header\"><a href=\"Default.aspx\" class=\"nav-header-letter px-3\"><span>Kuжi</span></a></div>\r\n                                </div>\r\n                                <div class=\"mob-nav-item\" data=\"1\">\r\n                                    <div class=\"nav-header\"><a href=\"Detail.aspx\" class=\"nav-header-letter px-3\"><span>Funkciju</span></a></div>\r\n                                </div>\r\n                                <div class=\"mob-nav-item\" data=\"2\">\r\n                                    <div class=\"nav-header\"><a href=\"Ocr.aspx\" class=\"nav-header-letter px-3\"><span>Iskustvo</span></a></div>\r\n                                </div>\r\n                                <div class=\"mob-nav-item\" data=\"3\">\r\n                                    <div class=\"nav-header\"><a href=\"Version.aspx\" class=\"nav-header-letter px-3\"><span>Član</span></a></div>\r\n                                </div>\r\n                                <div class=\"mob-nav-item\" data=\"4\">\r\n                                    <div class=\"nav-header\"><a href=\"Download.aspx\" class=\"nav-header-letter px-3\"><span>Preuzimanje</span></a></div>\r\n                                </div>\r\n                                <div class=\"mob-nav-item\" data=\"5\">\r\n                                    <div class=\"nav-header\"><a href=\"Articles.aspx\" class=\"nav-header-letter px-3\"><span>Delite</span></a></div>\r\n                                </div>\r\n                                <div class=\"mob-nav-item\" data=\"6\">\r\n                                    <div class=\"nav-header\"><a href=\"FAQ.aspx\" class=\"nav-header-letter px-3\"><span>Pomoć</span></a></div>\r\n                                </div>\r\n                            </div>\r\n                            <div class=\"col-8 bg-white sidebar-fix-rigth\">\r\n                                <div class=\"sub-nav\">\r\n                                    <div class=\"sub-nav-item py-2 pb-3 px-4\">\r\n                                        <h2 class=\"h5\" style=\"margin: inherit;\">Efikasni alati za produktivnost</h2>\r\n                                        <p class=\"color-gray fn14 mt-3 sub-left-desc\">Inteligentno prepoznavanje, obrada velike brzine i precizan izlaz</p>\r\n                                        <p class=\"color-gray fn14 mt-3 sub-left-desc\">&nbsp;</p>\r\n                                        <p class=\"color-gray fn14 mt-3 sub-left-desc\">Prepoznajte celu stranicu dokumenata u KSNUMKS sekundi</p>\r\n                                        <p class=\"color-gray fn14 mt-3 sub-left-desc\">98%+ tačnost prepoznavanja</p>\r\n                                        <p class=\"color-gray fn14 mt-3 sub-left-desc\">Višejezična obrada u realnom vremenu bez odlaganja</p>\r\n                                        <a class=\"link block disa fn14 contact-business shake\" href=\"Download.aspx\">Preuzmite iskustvo sada<span class=\"iconfont4 icon-xiangzuo gt\"></span></a>\r\n                                    </div>\r\n                                </div>\r\n                                <div class=\"sub-nav\">\r\n                                    <div class=\"sub-nav-item py-3 px-4\">\r\n                                        <h2 class=\"h5\" style=\"margin: inherit;\">Karakteristike proizvoda:</h2>\r\n                                        <p class=\"color-gray fn14 mt-3 sub-left-desc\">AI inteligentna identifikacija, rešenje na jednom mestu</p>\r\n                                    </div>\r\n                                    <div class=\"sub-nav-item py-3 px-4\"><a href=\"Detail.aspx\">Uvod u funkciju</a></div>\r\n                                    <div class=\"sub-nav-item py-3 px-4\"><a href=\"Download.aspx\">Preuzimanje softvera</a></div>\r\n                                    <div class=\"sub-nav-item py-3 px-4\"><a href=\"Version.aspx\">Poređenje verzija</a></div>\r\n                                    <div class=\"sub-nav-item py-3 px-4\"><a href=\"Ocr.aspx\">Online iskustvo</a></div>\r\n                                    <div class=\"sub-nav-item py-3 px-4\"><a href=\"Status.aspx\">Status sistema</a></div>\r\n                                </div>\r\n                                <div class=\"sub-nav\">\r\n                                    <div class=\"sub-nav-item py-3 px-4\">\r\n                                        <h2 class=\"h5\" style=\"margin: inherit;\">Online iskustvo</h2>\r\n                                        <p class=\"color-gray fn14 mt-3 sub-left-desc\">Besplatno online OCR funkcija iskustvo</p>\r\n                                    </div>\r\n                                    <div class=\"sub-nav-item py-3 px-4\"><a href=\"Ocr.aspx\">Potpuna funkcionalnost</a></div>\r\n                                    <div class=\"sub-nav-item py-3 px-4\"><a href=\"Ocr.aspx?type=text_recognize\">Prepoznavanje reči</a></div>\r\n                                    <div class=\"sub-nav-item py-3 px-4\"><a href=\"Ocr.aspx?type=table\">Identifikacija tabele</a></div>\r\n                                    <div class=\"sub-nav-item py-3 px-4\"><a href=\"Ocr.aspx?type=pdf2word\">PDF u Vord</a></div>\r\n                                </div>\r\n                                <div class=\"sub-nav\">\r\n                                    <div class=\"sub-nav-item py-3 px-4\">\r\n                                        <h2 class=\"h5\" style=\"margin: inherit;\">Nadogradnje članstva</h2>\r\n                                        <p class=\"color-gray fn14 mt-3 sub-left-desc\">Otključajte sve funkcije i uživajte u ekskluzivnim uslugama</p>\r\n                                    </div>\r\n                                    <div class=\"sub-nav-item py-3 px-4\"><a href=\"Version.aspx\">Pogodnosti za članstvo</a></div>\r\n                                    <div class=\"sub-nav-item py-3 px-4\"><a href=\"Upgrade.aspx\">Aktivirajte odmah</a></div>\r\n                                </div>\r\n                                <div class=\"sub-nav\">\r\n                                    <div class=\"sub-nav-item py-3 px-4\">\r\n                                        <h2 class=\"h5\" style=\"margin: inherit;\">Preuzimanje softvera</h2>\r\n                                        <p class=\"color-gray fn14 mt-3 sub-left-desc\">Preuzmite profesionalni OCR softver besplatno</p>\r\n                                    </div>\r\n                                    <div class=\"sub-nav-item py-3 px-4\"><a href=\"Download.aspx\">Preuzmi odmah</a></div>\r\n                                    <div class=\"sub-nav-item py-3 px-4\"><a href=\"Version.aspx\">Poređenje verzija</a></div>\r\n                                </div>\r\n                                <div class=\"sub-nav\">\r\n                                    <div class=\"sub-nav-item py-3 px-4\">\r\n                                        <h2 class=\"h5\" style=\"margin: inherit;\">Deljenje tehnologije</h2>\r\n                                        <p class=\"color-gray fn14 mt-3 sub-left-desc\">OCR tehnički članci i razmena znanja</p>\r\n                                    </div>\r\n                                    <div class=\"sub-nav-item py-3 px-4\"><a href=\"Articles.aspx\">Svi članci</a></div>\r\n                                    \r\n                                    <div class=\"sub-nav-item py-3 px-4\"><a href=\"Articles.aspx?category=2\">Napredni vodič</a></div>\r\n                                    \r\n                                    <div class=\"sub-nav-item py-3 px-4\"><a href=\"Articles.aspx?category=3\">Tehnološka istraživanja</a></div>\r\n                                    \r\n                                    <div class=\"sub-nav-item py-3 px-4\"><a href=\"Articles.aspx?category=4\">Trendovi u industriji</a></div>\r\n                                    \r\n                                    <div class=\"sub-nav-item py-3 px-4\"><a href=\"Articles.aspx?category=5\">Slučajevi upotrebe:</a></div>\r\n                                    \r\n                                    <div class=\"sub-nav-item py-3 px-4\"><a href=\"Articles.aspx?category=6\">Pregled alata</a></div>\r\n                                    \r\n                                </div>\r\n                                <div class=\"sub-nav\">\r\n                                    <div class=\"sub-nav-item py-3 px-4\">\r\n                                        <h2 class=\"h5\" style=\"margin: inherit;\">Centar za pomoć</h2>\r\n                                        <p class=\"color-gray fn14 mt-3 sub-left-desc\">Profesionalna služba za korisnike, intimna usluga</p>\r\n                                    </div>\r\n                                    <div class=\"sub-nav-item py-3 px-4\"><a href=\"FAQ.aspx\">Koristite pomoć</a></div>\r\n                                    <div class=\"sub-nav-item py-3 px-4\"><a href=\"AboutUs.aspx\">O nama</a></div>\r\n                                    <div class=\"sub-nav-item py-3 px-4\"><a href=\"mailto:<EMAIL>\">Kontaktirajte korisnički servis</a></div>\r\n                                    <div class=\"sub-nav-item py-3 px-4\"><a href=\"UserAgreement.aspx\">Uslovi korišćenja usluge</a></div>\r\n                                </div>\r\n                            </div>\r\n                        </div>\r\n                    </div>\r\n                </div>\r\n            </div>\r\n        </header>\r\n        <form method=\"post\" action=\"./Article.aspx?id=81&amp;slug=rnn-sequence-modeling&amp;lang=zh-Hans&amp;_translation_source=true\" id=\"aspnetForm\">\r\n<div class=\"aspNetHidden\">\r\n<input type=\"hidden\" name=\"__VIEWSTATE\" id=\"__VIEWSTATE\" value=\"Q9uUeUlUJtaRG68B9ZsvqxMA0nWMrG97wrg6XH6pF/PALpTNvdWvXETFldXabtYfiX/3jguNbdKAEx4O02Rz9ETQIoNHodxI3/t/Hx/oGKM=\" />\r\n</div>\r\n\r\n<div class=\"aspNetHidden\">\r\n\r\n\t<input type=\"hidden\" name=\"__VIEWSTATEGENERATOR\" id=\"__VIEWSTATEGENERATOR\" value=\"2173C2F0\" />\r\n</div>\r\n            <div>\r\n                \n\n<div class=\"reading-progress\" id=\"readingProgress\"></div>\n<div class=\"article-detail-container\">\n<div class=\"container\">\n<div class=\"row\">\n<div class=\"col-lg-8 col-xl-9 col-md-12\">\n                    \n                    <article class=\"article-detail\" id=\"articleDetail\" data-article-id=\"81\">\n                        <header class=\"article-header\">\n                            <h1 class=\"article-title\">【Deep Learning OCR serija · 4】 Rekurentne neuronske mreže i modeliranje sekvenci</h1>\n                            <div class=\"article-meta\">\n                                <div class=\"meta-item\">\n                                    <span class=\"meta-icon\">📅</span>\n                                    <span>Vreme: 2025-08-19</span>\n                                </div>\n                                <div class=\"meta-item\">\n                                    <span class=\"meta-icon\">👁️</span>\n                                    <span>Иitao:<span class=\"view-count\">1200</span></span>\n                                </div>\n                                <div class=\"meta-item\">\n                                    <span class=\"meta-icon\">⏱️</span>\n                                    <span>Pribl. 50 minuta (9819 reči)</span>\n                                </div>\n                                \n                                <div class=\"meta-item\">\n                                    <span class=\"meta-icon\">📁</span>\n                                    <span>Kategorija: Napredni vodiči</span>\n                                </div>\n                                \n                            </div>\n                            <div class=\"article-summary\"><p>Zaronite u primenu RNN, LSTM, GRU u OCR-u. Detaljna analiza principa modeliranja sekvenci, rešenja problema gradijenta i prednosti dvosmernih RNN.</p></div>\n                        </header>\n                        <div class=\"article-content\" id=\"articleContent\">## Uvod\r\n\r\nRekurentna neuronska mreža (RNN) je arhitektura neuronske mreže u dubokom učenju koja je specijalizovana za obradu podataka o sekvencama. U OCR zadacima, prepoznavanje teksta je u suštini problem konverzije sekvence u sekvencu: pretvaranje niza karakteristika slike u sekvencu tekstualnih znakova. Ovaj članak će se pozabaviti načinom na koji RNN funkcioniše, njegovim glavnim varijantama i njegovim specifičnim primenama u OCR-u, pružajući čitaocima sveobuhvatnu teorijsku osnovu i praktične smernice.\r\n\r\n## RNN Osnove\r\n\r\n### Ograničenja tradicionalnih neuronskih mreža\r\n\r\nTradicionalne neuronske mreže imaju fundamentalna ograničenja u obradi podataka o sekvenci. Ove mreže pretpostavljaju da su ulazni podaci nezavisni i homoraspoređeni, i ne mogu da obuhvate vremenske zavisnosti između elemenata u nizu.\r\n\r\n** Feedforvard Mrežni problemi **:\r\n- Fiksni ulaz i izlaz dužina: Promenljiva dužina sekvence ne može da se rukuje\r\n- Nedostatak sposobnosti pamćenja: Nemogućnost korišćenja istorijskih informacija\r\n- Teškoće u deljenju parametara: Isti obrazac treba da se nauči više puta na različitim lokacijama\r\n- Poziciona osetljivost: Promena redosleda ulaza može dovesti do potpuno različitih izlaza\r\n\r\nOva ograničenja su posebno uočljiva u OCR zadacima. Tekstualne sekvence su veoma zavisne od konteksta, a rezultati prepoznavanja prethodnog karaktera često pomažu u određivanju verovatnoće narednih znakova. Na primer, kada identifikujete englesku reč \"the\", ako je \"th\" već prepoznato, onda će sledeći znak verovatno biti \"e\".\r\n\r\n### Osnovna ideja RNN-a\r\n\r\nRNN rešava problem modeliranja sekvenci uvođenjem spajanja petlje. Osnovna ideja je da se mreži doda mehanizam \"memorije\", tako da mreža može da skladišti i koristi informacije iz prethodnih trenutaka.\r\n\r\n** Matematička reprezentacija RNN **:\r\nU trenutku t, skriveno stanje RNN-a h_t određeno trenutnim ulaznim x_t i skrivenim stanjem prethodnog trenutka h_{t-1}:\r\n\r\nh_t = f(W_hh * h_{t-1} + W_xh * x_t + b_h)\r\n\r\nU tome:\r\n- W_hh je matrica težine od skrivenog stanja do skrivenog stanja\r\n- W_xh je matrica težine ušla u skriveno stanje  \r\n- b_h je vektor pristrasnosti\r\n- f je aktivaciona funkcija (obično tanh ili ReLU)\r\n\r\nIzlazni y_t se izračunava iz trenutnog skrivenog stanja:\r\ny_t = W_hy * h_t + b_y\r\n\r\n** Prednosti RNN **:\r\n- Deljenje parametara: Iste težine se dele u svim vremenskim koracima\r\n- Promenljiva dužina Sekvenca obrada: Može da obradi ulazne sekvence proizvoljne dužine\r\n- Sposobnost memorije: Skrivena stanja deluju kao \"sećanja\" mreže\r\n- Fleksibilan ulaz i izlaz: Podržava jedan-na-jedan, jedan-na-više, mnogo-na-jedan, mani-na-više režima i još mnogo toga\r\n\r\n### Prošireni pogled na RNN\r\n\r\nDa bismo bolje razumeli kako RNN funkcionišu, možemo ih proširiti u vremenskoj dimenziji. Prošireni RNN izgleda kao duboka mreža za napredovanje, ali svi vremenski koraci dele iste parametre.\r\n\r\n** Značaj vremena odvijanja **:\r\n- Lako razumljiv protok informacija: Moguće je jasno videti kako se informacije prenose između vremenskih koraka\r\n- Izračunavanje gradijenta: Gradijenti se izračunavaju kroz algoritam Time Backpropagation (BPTT)\r\n- Razmatranja paralelizacije: Dok su RNN-ovi inherentno sekvencijalni, određene operacije mogu biti paralelizovane\r\n\r\n**Matematički opis procesa odvijanja **:\r\nZa sekvence dužine T, RNN se širi na sledeći način:\r\nh_1 = f(W_xh * x_1 + b_h)\r\nh_2 = f(W_hh * h_1 + W_xh * x_2 + b_h)\r\nh_3 = f(W_hh * h_2 + W_xh * x_3 + b_h)\r\n...\r\nh_T = f(W_hh * h_{T-1} + W_xh * x_T + b_h)\r\n\r\nOvaj razvijeni obrazac jasno pokazuje kako se informacije prenose između vremenskih koraka i kako se parametri dele u svim vremenskim koracima.\r\n\r\n## Gradijent nestanak i eksplozija problem\r\n\r\n### Koren problema\r\n\r\nPrilikom obuke RNN-a koristimo algoritam Backpropagation Through Time (BPTT). Algoritam treba da izračuna gradijent funkcije gubitka za svaki timestep parametar.\r\n\r\n** Zakon lanca za izračunavanje gradijenta **:\r\nKada je sekvenca duga, gradijent treba da se unazad propagira kroz više vremenskih koraka. Prema pravilu lanca, gradijent će sadržavati višestruko množenje matrice težine:\r\n\r\n∂L/∂W = Σ_t (∂L/∂y_t) * (∂y_t/∂h_t) * (∂h_t/∂W)\r\n\r\ngde ∂h_t/∂W uključuje proizvod svih intermedijarnih stanja od momenta t do momenta 1.\r\n\r\n** Matematička analiza gradijenta nestanka **:\r\nRazmotrite širenje gradijenata između vremenskih koraka:\r\n∂h_t/∂h_{t-1} = diag(f_prime(W_hh * h_{t-1} + W_xh * x_t + b_h)) * W_hh\r\n\r\nKada je dužina sekvence T, gradijent sadrži T-1 takav termin proizvoda. Ako je maksimalna svojstvena vrednost W_hh manja od 1, kontinuirano množenje matrice će izazvati eksponencijalni raspad gradijenta.\r\n\r\n** Matematička analiza gradijentnih eksplozija **:\r\nNasuprot tome, kada je maksimalna svojstvena vrednost W_hh veća od 1, gradijent se povećava eksponencijalno:\r\n|| ∂h_t/∂h_1|| ≈ || W_hh|| ^{t-1}\r\n\r\nTo dovodi do nestabilne obuke i prekomernog ažuriranja parametara.\r\n\r\n### Detaljno objašnjenje rešenja\r\n\r\nGradijent Clipping:\r\nGradijent clipping je najdirektniji način za rešavanje gradijentnih eksplozija. Kada gradijent norma prelazi postavljeni prag, gradijent se skalira na veličinu praga. Ovaj metod je jednostavan i efikasan, ali zahteva pažljiv odabir pragova. Prag koji je premali će ograničiti sposobnost učenja, a prag koji je prevelik neće efikasno sprečiti eksploziju gradijenta.\r\n\r\n** Strategija inicijalizacije težine **:\r\nPravilna inicijalizacija težine može ublažiti probleme sa gradijentom:\r\n- Ksavier inicijalizacija: Težina varijansa je 1 / n, gde n je ulazna dimenzija\r\n- On inicijalizacija: Varijansa težine je 2/n, što je pogodno za funkcije aktivacije ReLU\r\n- Ortogonalna inicijalizacija: Inicijalizuje matricu težine kao ortogonalnu matricu\r\n\r\n** Izbor funkcija aktivacije **:\r\nRazličite funkcije aktivacije imaju različite efekte na širenje gradijenta:\r\n- TANH: izlazni opseg [-1,1], gradijent maksimalna vrednost 1\r\n- ReLU: može ublažiti nestanak gradijenta, ali može izazvati smrt neurona\r\n- Leaki ReLU: Rešava problem neuronske smrti ReLU\r\n\r\n** Arhitektonska poboljšanja **:\r\nNajosnovnije rešenje bilo je poboljšanje RNN arhitekture, što je dovelo do pojave LSTM i GRU. Ove arhitekture se bave gradijentima kroz mehanizme za zatvaranje i specijalizovane dizajne protoka informacija.\r\n\r\n## LSTM: Mreža duge kratkoročne memorije\r\n\r\n### Motivacija za dizajn za LSTM\r\n\r\nLSTM (Long Short-Term Memory) je RNN varijanta koju su predložili Hochreiter i Schmidhuber 1997. godine, posebno dizajnirana da reši problem gradijenta nestajanja i poteškoća u učenju na daljinu.\r\n\r\n** LSTM-ove osnovne inovacije **:\r\n- Stanje ćelije: Služi kao \"autoput\" za informacije, omogućavajući informacijama da teku direktno između vremenskih koraka\r\n- Mehanizam za zatvaranje: Precizna kontrola nad prilivom, zadržavanjem i izlazom informacija\r\n- Disocirani memorijski mehanizmi: razlikovati kratkoročno pamćenje (skriveno stanje) i dugoročno pamćenje (ćelijsko stanje)\r\n\r\n** Kako LSTM rešava gradijent probleme **:\r\nLSTM ažurira stanje ćelije putem aditivnih, a ne multiplikativnih operacija, što omogućava gradijentima da lakše teku u ranijim vremenskim koracima. Ažurirana formula za stanje ćelije:\r\n\r\nC_t = f_t ⊙ C_{t-1} + i_t ⊙ C_tilde_t\r\n\r\nOvde se koristi sabiranje na nivou elementa, izbegavajući kontinuirano množenje matrice u tradicionalnim RNN-ovima.\r\n\r\n### Detaljno objašnjenje LSTM arhitekture\r\n\r\nLSTM sadrži tri jedinice za zatvaranje i stanje ćelije:\r\n\r\n**1. Zaboravi kapiju**:\r\nKapija zaborava odlučuje koje informacije treba odbaciti iz stanja ćelije:\r\nf_t = σ(W_f · [h_{t-1}, x_t] + b_f)\r\n\r\nIzlaz vrata zaborava je vrednost između 0 i 1, pri čemu je 0 \"potpuno zaboravljen\" i 1 je \"potpuno zadržan\". Ova kapija omogućava LSTM-u da selektivno zaboravi nevažne istorijske informacije.\r\n\r\n**2. Ulazna kapija **:\r\nUlazna kapija određuje koje nove informacije se čuvaju u stanju ćelije:\r\ni_t = σ(W_i · [h_{t-1}, x_t] + b_i)\r\nC_tilde_t = tanh(W_C · [h_{t-1}, x_t] + b_C)\r\n\r\nUlazna kapija se sastoji od dva dela: sigmoidni sloj određuje koje vrednosti treba ažurirati, a tanh sloj stvara vektore vrednosti kandidata.\r\n\r\n**3. Ažuriranje statusa ćelije **:\r\nKombinujte izlaze vrata zaboravljanja i ulazne kapije da biste ažurirali stanje ćelije:\r\nC_t = f_t ⊙ C_{t-1} + i_t ⊙ C_tilde_t\r\n\r\nOva formula je u srcu LSTM-a: selektivno zadržavanje i ažuriranje informacija kroz operacije množenja i sabiranja na nivou elemenata.\r\n\r\n**4. Izlazna kapija **:\r\nIzlazna kapija određuje koji delovi ćelije su izlazni:\r\no_t = σ(W_o · [h_{t-1}, x_t] + b_o)\r\nh_t = o_t ⊙ tanh(C_t)\r\n\r\nIzlazna kapija kontroliše koji delovi stanja ćelije utiču na trenutni izlaz.\r\n\r\n### LSTM varijante\r\n\r\n**Peephole LSTM**:\r\nOslanjajući se na standardni LSTM, Peephole LSTM omogućava jedinici za zatvaranje da vidi stanje ćelije:\r\nf_t = σ(W_f · [C_{t-1}, h_{t-1}, x_t] + b_f)\r\ni_t = σ(W_i · [C_{t-1}, h_{t-1}, x_t] + b_i)\r\no_t = σ(W_o · [C_t, h_{t-1}, x_t] + b_o)\r\n\r\n** Spojeni LSTM **:\r\nUparite kapiju zaboravljanja sa ulaznom kapijom kako biste osigurali da je količina zaboravljenih informacija jednaka količini unesenih informacija:\r\nf_t = σ(W_f · [h_{t-1}, x_t] + b_f)\r\ni_t = 1 - f_t\r\n\r\nOvaj dizajn smanjuje broj parametara uz održavanje osnovne funkcionalnosti LSTM-a.\r\n\r\n## GRU: Jedinica sa vratom petlje\r\n\r\n### Pojednostavljeni dizajn GRU\r\n\r\nGRU (Gated Rekurentna jedinica) je pojednostavljena verzija LSTM-a koju je predložio Cho et al. u 2014. godini. GRU pojednostavljuje tri kapije LSTM-a na dve kapije i spaja ćelijsko stanje i skriveno stanje.\r\n\r\n**GRU je filozofija dizajna **:\r\n- Pojednostavljena struktura: Smanjuje broj vrata i smanjuje složenost proračuna\r\n- Održavanje performansi: Pojednostavite uz održavanje LSTM-uporedivih performansi\r\n- Jednostavan za implementaciju: Jednostavnija konstrukcija omogućava jednostavnu implementaciju i puštanje u rad\r\n\r\n### Mehanizam za zatvaranje GRU\r\n\r\n**1. Resetuj kapiju**:\r\nr_t = σ(W_r · [h_{t-1}, x_t] + b_r)\r\n\r\nVrata za resetovanje određuje kako kombinovati novi ulaz sa prethodnom memorijom. Kada se vrata za resetovanje približi 0, model ignoriše prethodno skriveno stanje.\r\n\r\n**2. Ažuriranje kapije **:\r\nz_t = σ(W_z · [h_{t-1}, x_t] + b_z)\r\n\r\nVrata za ažuriranje određuju koliko prošlih informacija treba zadržati i koliko novih informacija dodati. Kontroliše i zaboravljanje i unos, slično kombinaciji zaborava i ulaznih vrata u LSTM-u.\r\n\r\n**3. Status skrivenog kandidata **:\r\nh_tilde_t = tanh(W_h · [r_t ⊙ h_{t-1}, x_t] + b_h)\r\n\r\nSkrivena stanja kandidata koriste kapiju za resetovanje da kontrolišu efekte prethodnog skrivenog stanja.\r\n\r\n**4. Konačno skriveno stanje **:\r\nh_t = (1 - z_t) ⊙ h_{t-1} + z_t ⊙ h_tilde_t\r\n\r\nKonačna skrivena država je ponderisani prosek prethodnog skrivenog stanja i kandidata skrivenog stanja.\r\n\r\n### GRU vs LSTM Detaljno poređenje\r\n\r\n** Poređenje broja parametara **:\r\n- LSTM: 4 matrice težine (zaboravljanje kapije, ulazna vrata, vrednost kandidata, izlazna vrata)\r\n- GRU: 3 matrice težine (resetovanje kapije, ažuriranje kapije, vrednost kandidata)\r\n- Broj parametara GRU je oko 75% LSTM\r\n\r\n** Poređenje računarske složenosti **:\r\n- LSTM: Zahteva izračunavanje 4 izlaza kapije i ažuriranja stanja ćelija\r\n- GRU: Jednostavno izračunajte izlaz KSNUMKS kapije i skrivenih ažuriranja statusa\r\n- GRU je obično 20-30% brži od LSTM-a\r\n\r\n** Poređenje performansi **:\r\n- Na većini zadataka, GRU i LSTM obavljaju uporedivo\r\n- LSTM je možda malo bolji od GRU na nekim zadacima dugog niza\r\n- GRU je bolji izbor u slučajevima kada su računarski resursi ograničeni\r\n\r\n## Dvosmerni RNNs\r\n\r\n### Neophodnost dvosmerne obrade\r\n\r\nU mnogim zadacima modeliranja sekvenci, izlaz sadašnjeg trenutka se oslanja ne samo na prošlost, već i na buduće informacije. Ovo je posebno važno u OCR zadacima, gde prepoznavanje karaktera često zahteva razmatranje konteksta cele reči ili rečenice.\r\n\r\n** Ograničenja jednosmernih RNN **:\r\n- Mogu se koristiti samo istorijske informacije, ne može se dobiti budući kontekst\r\n- Ograničene performanse u određenim zadacima, posebno onima koji zahtevaju globalne informacije\r\n- Ograničeno prepoznavanje dvosmislenih znakova\r\n\r\n** Prednosti dvosmerne obrade **:\r\n- Kompletne kontekstualne informacije: Iskoristite i prošle i buduće informacije\r\n- Bolja višeznačnost: Višeznačnost sa kontekstualnim informacijama\r\n- Poboljšana tačnost prepoznavanja: Izvodi bolje na većini zadataka napomena sekvence\r\n\r\n### Dvosmerna LSTM arhitektura\r\n\r\nDvosmerni LSTM se sastoji od dva LSTM sloja:\r\n- Napred LSTM: Procesne sekvence s leva na desno\r\n- Backward LSTM: Procesne sekvence s desna na levo\r\n\r\n**Matematička reprezentacija**:\r\nh_forward_t = LSTM_forward(x_t, h_forward_{t-1})\r\nh_backward_t = LSTM_backward(x_t, h_backward_{t+1})\r\nh_t = [h_forward_t; h_backward_t] # Šivenje napred i nazad skrivenih stanja\r\n\r\n** Proces obuke **:\r\n1. Napred LSTM obrađuje sekvence u normalnom redosledu\r\n2. Unazad LSTM obrađuje sekvence obrnutim redosledom\r\n3. U svakom koraku vremena, povežite skrivena stanja u oba smera\r\n4. Koristite spojeno stanje za predviđanje\r\n\r\n** Prednosti i mane **:\r\nPrednost:\r\n- Potpune kontekstualne informacije\r\n- Bolje performanse\r\n- Tretman simetrije\r\n\r\nInferiorni položaj:\r\n- Udvostručite složenost proračuna\r\n- Ne može se obraditi u realnom vremenu (zahteva punu sekvencu)\r\n- Povećani zahtevi za memoriju\r\n\r\n## Aplikacije za modeliranje sekvenci u OCR-u\r\n\r\n### Detaljno objašnjenje prepoznavanja tekstualne linije\r\n\r\nU OCR sistemima, prepoznavanje tekstualne linije je tipična primena modeliranja sekvenci. Ovaj proces uključuje pretvaranje niza karakteristika slike u niz znakova.\r\n\r\n** Modeliranje problema **:\r\n- Ulaz: Sekvenca karakteristika slike X = {x_1, x_2, ..., x_T}\r\n- Izlaz: Redosled znakova I = {y_1, y_2, ..., y_S}\r\n- Izazov: Dužina ulazne sekvence T i dužina izlazne sekvence S često nisu jednake\r\n\r\n** Primena CRNN arhitekture u prepoznavanju tekstualnih linija **:\r\nCRNN (Convolutional Rekurentna neuronska mreža) je jedna od najuspešnijih arhitektura u OCR-u:\r\n\r\n1. ** CNN sloj za ekstrakciju karakteristika **:\r\n   - Ekstrakt slike funkcije koristeći konvolucijske neuronske mreže\r\n   - Pretvoriti 2D slike funkcije u 1D funkcija sekvence\r\n   - Održavajte kontinuitet informacija o vremenu\r\n\r\n2. ** RNN sloj za modeliranje sekvence **:\r\n   - Model karakteristika sekvence koristeći dvosmerne LSTM\r\n   - Uhvatite kontekstualne zavisnosti između likova\r\n   - Izlazna distribucija verovatnoće karaktera za svaki vremenski korak\r\n\r\n3. ** CTC sloj poravnanja **:\r\n   - Adrese neusklađenosti dužine ulaza / izlaza sekvence\r\n   - Nisu potrebne dimenzije poravnanja na nivou karaktera\r\n   - End-to-end obuka\r\n\r\n** Konverzija ekstrakcije funkcija u redosledu **:\r\nMapa karakteristika koju je izdvojio CNN treba da se pretvori u oblik sekvence koji RNN može da obradi:\r\n- Segmentirajte mapu karakteristika u kolone, sa svakom kolonom kao vremenskim korakom\r\n- Održavati hronologiju prostornih informacija\r\n- Uverite se da je dužina sekvence karakteristika proporcionalna širini slike\r\n\r\n### Primena mehanizma pažnje u OCR-u\r\n\r\nTradicionalni RNN-ovi i dalje imaju uska grla u informacijama kada se radi o dugim sekvencama. Uvođenje mehanizama pažnje dodatno poboljšava mogućnosti modeliranja sekvenci.\r\n\r\n** Principi mehanizama pažnje **:\r\nMehanizam pažnje omogućava modelu da se fokusira na različite delove ulazne sekvence prilikom generisanja svakog izlaza:\r\n- Rešiti informacije usko grlo fiksne dužine kodiranih vektora\r\n- Obezbeđuje objašnjivost modela odluka\r\n- Poboljšana obrada dugih sekvenci\r\n\r\n** Specifične aplikacije u OCR **:\r\n\r\n1. ** Pažnja na nivou karaktera **:\r\n   - Fokusirajte se na relevantne oblasti slike prilikom identifikacije svakog karaktera\r\n   - Podesite težinu pažnje u letu\r\n   - Poboljšati robusnost složenih pozadina\r\n\r\n2. ** Pažnja na nivou reči **:\r\n   - Razmotrite kontekstualne informacije na nivou vokabulara\r\n   - Iskoristiti znanje jezičkog modela\r\n   - Poboljšati tačnost prepoznavanja cele reči\r\n\r\n3. ** Multi-skala pažnje **:\r\n   - Primena mehanizama pažnje u različitim rezolucijama\r\n   - Rukovanje tekstom različitih veličina\r\n   - Poboljšati prilagodljivost promenama skale\r\n\r\n** Matematička reprezentacija mehanizma pažnje **:\r\nZa izlaznu sekvencu enkodera H = {h_1, h_2, ..., h_T} i stanje dekodera s_t:\r\n\r\ne_{t,i} = a(s_t, h_i) # Rezultat pažnje\r\nα_{t,i} = softmax(e_{t,i}) # Težina pažnje\r\nc_t = Σ_i α_{t,i} * h_i # vektor konteksta\r\n\r\n## Strategije obuke i optimizacija\r\n\r\n### Strategija obuke od sekvence do sekvence\r\n\r\n** Učitelj Forcing **:\r\nTokom faze obuke, koristite stvarnu ciljnu sekvencu kao ulaz dekodera:\r\n- Prednosti: brza brzina treninga, stabilna konvergencija\r\n- Protiv: Nedosledne faze obuke i zaključivanja, što je dovelo do akumulacije grešaka\r\n\r\n**Planirano uzorkovanje**:\r\nPostepeno prelazak sa prisiljavanja nastavnika na korišćenje sopstvenih predviđanja modela tokom treninga:\r\n- Koristite stvarne oznake u početnoj fazi i predviđanja modela u kasnijim fazama\r\n- Smanjite razlike u obuci i rasuđivanju\r\n- Poboljšati robusnost modela\r\n\r\n** Nastavni plan i program Učenje **:\r\nPočnite sa jednostavnim uzorcima i postepeno povećavajte složenost uzoraka:\r\n- Kratke do duge sekvence: Prvo trenirajte kratke tekstove, a zatim duge tekstove\r\n- Jasno do zamućene slike: Postepeno povećavaju složenost slike\r\n- Jednostavan do složenih fontova: od štampanog do rukopisa\r\n\r\n### Tehnike regularizacije\r\n\r\n** Primena napuštanja u RNN **:\r\nPrimena napuštanja u RNN zahteva posebnu pažnju:\r\n- Ne primenjujte ispadanje na petlje konekcije\r\n- Dropout može da se primeni na ulaznom i izlaznom sloju\r\n- Varijacioni ispadanje: Koristite istu masku za napuštanje u svim koracima\r\n\r\n** Propadanje težine **:\r\nL2 regularizacija sprečava preterano uklapanje:\r\nGubitak = Unakrsna Entropija + λ * || W|| ²\r\n\r\ngde je λ koeficijent regularizacije, koji treba da bude optimizovan skupom validacije.\r\n\r\n** Gradijent obrezivanje **:\r\nEfikasan način da se spreči gradijent eksplozije. Kada gradijent norma prelazi prag, skala gradijent proporcionalno da zadrži smer gradijenta nepromenjen.\r\n\r\n** Rano zaustavljanje **:\r\nPratite validaciju, postavite performanse i zaustavite obuku kada se performanse više ne poboljšavaju:\r\n- Sprečite preterano opremanje\r\n- Sačuvajte računarske resurse\r\n- Izaberite optimalni model\r\n\r\n### Podešavanje hiperparametara\r\n\r\n** Planiranje brzine učenja **:\r\n- Početna stopa učenja: Obično postavljena na 0.001-0.01\r\n- Propadanje stope učenja: eksponencijalno propadanje ili propadanje merdevina\r\n- Adaptivna stopa učenja: Koristite optimizatore kao što su Adam, RMSprop, itd\r\n\r\n** Izbor veličine serije **:\r\n- Male serije: Bolje performanse generalizacije, ali duže vreme obuke\r\n- Veliki obim: Obuka je brza, ali može uticati na generalizaciju\r\n- Veličine serije između 16-128 se obično biraju\r\n\r\n** Sekvenca Dužina Obrada **:\r\n- Fiksna dužina: Skratiti ili popuniti sekvence na fiksne dužine\r\n- Dinamička dužina: Koristite oblaganje i maskiranje za rukovanje sekvence promenljive dužine\r\n- Strategija pakovanja: Grupne sekvence slične dužine\r\n\r\n## Procena i analiza učinka\r\n\r\n### Procenite metriku\r\n\r\n** Tačnost na nivou karaktera **:\r\nAccuracy_char = (Broj znakova ispravno prepoznati) / (Ukupno znakova)\r\n\r\nOvo je najosnovniji indikator evaluacije i direktno odražava mogućnosti prepoznavanja karaktera modela.\r\n\r\n** Tačnost serijskog nivoa **:\r\nAccuracy_seq = (Broj pravilno prepoznatih sekvenci) / (Ukupan broj sekvenci)\r\n\r\nOvaj indikator je rigorozniji, a samo potpuno ispravna sekvenca se smatra tačnom.\r\n\r\n** Uređivanje udaljenost (Levenshtein udaljenost) **:\r\nIzmerite razliku između predviđenih i istinitih serija:\r\n- Minimalni broj operacija umetanja, uklanjanja i zamene\r\n- Standardizovana montaža rastojanje: uređivanje rastojanje / dužina sekvence\r\n- BLEU rezultat: Obično se koristi u mašinskom prevođenju i može se koristiti za OCR procenu\r\n\r\n### Analiza grešaka\r\n\r\n** Uobičajeni tipovi grešaka **:\r\n1. ** Konfuzija karaktera **: Pogrešna identifikacija sličnih znakova\r\n   - Broj 0 i slovo O\r\n   - Broj 1 i slovo l\r\n   - Slova M i N\r\n\r\n2. ** Greška sekvence **: Greška u redosledu znakova\r\n   - Pozicije likova su obrnute\r\n   - Dupliranje ili izostavljanje znakova\r\n\r\n3. ** Greška dužine **: Greška u predviđanju dužine sekvence\r\n   - Predugo: Umetnuti nepostojeći likovi\r\n   - Prekratak: Nedostaju likovi koji su prisutni\r\n\r\n** Metoda analize **:\r\n1. ** Matrica konfuzije **: Analizira obrasce grešaka na nivou karaktera\r\n2. ** Vizualizacija pažnje **: Razumeti zabrinutost modela\r\n3. ** Gradient Analiza **: Proverite protok gradijenta\r\n4. ** Aktivacija Analiza **: Posmatrajte obrasce aktivacije preko slojeva mreže\r\n\r\n### Model Dijagnostika\r\n\r\n** Overfit Detection **:\r\n- Gubici obuke nastavljaju da opadaju, gubici validacije rastu\r\n- Tačnost treninga je mnogo veća od tačnosti validacije\r\n- Rešenje: Povećajte regularnost i smanjite složenost modela\r\n\r\n** Underfit Detekcija **:\r\n- I gubici obuke i validacije su visoki\r\n- Model ne radi dobro na setu treninga\r\n- Rešenje: Povećajte složenost modela i prilagodite brzinu učenja\r\n\r\n** Dijagnoza problema gradijenta **:\r\n- Gradient Loss: Gradijent vrednost je premala, sporo učenje\r\n- Gradijent eksplozija: Prekomerne vrednosti gradijenta dovode do nestabilne obuke\r\n- Rešenje: Korišćenje LSTM / GRU, gradijent obrezivanje\r\n\r\n## Slučajevi primene u stvarnom svetu\r\n\r\n### Sistem za prepoznavanje rukom pisanih znakova\r\n\r\n** Scenariji primene **:\r\n- Digitalizovati rukom pisane beleške: Pretvoriti papirne beleške u elektronskim dokumentima\r\n- Automatsko popunjavanje obrazaca: Automatski prepoznaje sadržaj rukom pisanog obrasca\r\n- Identifikacija istorijskog dokumenta: Digitalizujte drevne knjige i istorijske dokumente\r\n\r\n** Tehničke karakteristike **:\r\n- Velike varijacije karaktera: Rukom pisani tekst ima visok stepen personalizacije\r\n- Kontinuirana obrada olovke: Veze između znakova treba da se rukuje\r\n- Kontekst-važno: Koristite jezičke modele za poboljšanje prepoznavanja\r\n\r\n** Arhitektura sistema **:\r\n1. ** Modul za prethodnu obradu **:\r\n   - Uklanjanje šuma i poboljšanje slike\r\n   - Korekcija nagiba\r\n   - Razdvajanje linija teksta\r\n\r\n2. ** Funkcija Modul za ekstrakciju **:\r\n   - CNN izvlači vizuelne karakteristike\r\n   - Multi-skala funkcija fuzija\r\n   - Funkcija serijalizacija\r\n\r\n3. ** Modul za modeliranje sekvence **:\r\n   - Dvosmerno LSTM modeliranje\r\n   - Mehanizmi pažnje\r\n   - Kontekstualno kodiranje\r\n\r\n4. ** Modul za dekodiranje **:\r\n   - CTC dekodiranje ili dekodiranje pažnje\r\n   - Jezički model post-obrada\r\n   - Procena poverenja\r\n\r\n### Sistem za prepoznavanje štampanih dokumenata\r\n\r\n** Scenariji primene **:\r\n- Digitalizacija dokumenata: Pretvaranje papirnih dokumenata u formate koji se mogu uređivati\r\n- Priznavanje računa: Automatski obrađuju fakture, potvrde i druge račune\r\n- Prepoznavanje signalizacije: Identifikujte putokaze, znakove prodavnica i još mnogo toga\r\n\r\n** Tehničke karakteristike **:\r\n- Redovni font: Pravilniji od rukom pisanog teksta\r\n- Pravila tipografije: Informacije o rasporedu mogu se koristiti\r\n- Zahtevi visoke tačnosti: Komercijalne aplikacije imaju stroge zahteve tačnosti\r\n\r\n** Strategija optimizacije **:\r\nKSNUMKS. ** Multi-Font Trening **: Koristi podatke o obuci iz više fontova\r\n2. ** Poboljšanje podataka **: Rotiranje, skala, dodavanje buke\r\n3. ** Optimizacija posle obrade **: provera pravopisa, korekcija gramatike\r\n4. ** Procena poverenja **: Obezbeđuje rezultat pouzdanosti za rezultate prepoznavanja\r\n\r\n### Sistem za prepoznavanje teksta scene\r\n\r\n** Scenariji primene **:\r\n- Street Viev Prepoznavanje teksta: Prepoznavanje teksta u Google Street Viev-u\r\n- Prepoznavanje etiketa proizvoda: Automatska identifikacija proizvoda supermarketa\r\n- Prepoznavanje saobraćajnih znakova: Primena inteligentnih transportnih sistema\r\n\r\n** Tehnički izazovi **:\r\n- Složene pozadine: Tekst je ugrađen u složenim prirodnim scenama\r\n- Teška deformacija: Perspektivna deformacija, deformacija savijanja\r\n- Zahtevi u realnom vremenu: Aplikacije za mobilne uređaje moraju da reaguju\r\n\r\n**Rešenje**:\r\n1. ** Robusna ekstrakcija karakteristika **: Koristi dublje CNN mreže\r\n2. ** Multi-Scale Obrada **: Rukovanje tekstom različitih veličina\r\n3. ** Geometrija Korekcija **: Automatski ispravlja geometrijske deformacije\r\n4. ** Kompresija modela **: Optimizujte model za mobilne uređaje\r\n\r\n## Rezime\r\n\r\nRekurentne neuronske mreže pružaju moćan alat za modeliranje sekvenci u OCR-u. Od osnovnih RNN-ova do poboljšanih LSM-ova i GRU do dvosmerne obrade i mehanizama pažnje, razvoj ovih tehnologija je znatno poboljšao performanse OCR sistema.\r\n\r\n** Ključni Takeavais **:\r\n- RNN-ovi implementiraju modeliranje sekvenci kroz spajanje petlje, ali postoji problem sa nestankom gradijenta\r\n- LSTM i GRU rešavaju problem učenja zavisnog od daljine putem mehanizama za zatvaranje\r\n- Dvosmerni RNN-ovi su u stanju da iskoriste pune kontekstualne informacije\r\n- Mehanizmi pažnje dodatno poboljšavaju sposobnost modeliranja sekvenci\r\n- Odgovarajuće strategije obuke i tehnike regularizacije su od ključnog značaja za performanse modela\r\n\r\n** Budući pravci razvoja **:\r\n- Integracija sa Transformer arhitekturama\r\n- Efikasniji pristup modeliranju sekvenci\r\n- End-to-end multimodalno učenje\r\n- Balans u realnom vremenu i tačnosti\r\n\r\nKako tehnologija nastavlja da se razvija, tehnike modeliranja sekvenci se i dalje razvijaju. Iskustvo i tehnologija akumulirana od strane RNN-a i njihovih varijanti u oblasti OCR-a postavili su čvrste temelje za razumevanje i dizajniranje naprednijih metoda modeliranja sekvenci.</div>\n                        \n                        <div class=\"article-tags\">\n                            <h6>Oznaku:</h6>\n                            <div class=\"tags-list\">\n                                \n                                <span class=\"tag\">RNN</span>\n                                \n                                <span class=\"tag\">LSTM</span>\n                                \n                                <span class=\"tag\">GRU</span>\n                                \n                                <span class=\"tag\">Modeliranje sekvenci</span>\n                                \n                                <span class=\"tag\">Gradijent nestaje</span>\n                                \n                                <span class=\"tag\">Dvosmerni RNN</span>\n                                \n                                <span class=\"tag\">Mehanizam pažnje</span>\n                                \n                                <span class=\"tag\">CRNN</span>\n                                \n                                <span class=\"tag\">OCR</span>\n                                \n                            </div>\n                        </div>\n                        \n                    </article>\n                    \n                    <footer class=\"article-footer\" id=\"articleFooter\">\n                        <div class=\"article-share\">\n                            <h6 style=\"color: #1a1c1f; margin-bottom: 1rem;\">Delite i radite:</h6>\n                            <div class=\"share-buttons\">\n                                <a href=\"javascript:void(0)\" onclick=\"shareToWeibo()\" class=\"btn btn-weibo\">📱 Veibo deli</a>\n                                <a href=\"javascript:void(0)\" onclick=\"copyUrl()\" class=\"btn btn-copy\">🔗 Kopiraj vezu</a>\n                                <button type=\"button\" class=\"btn btn-print\" onclick=\"printArticle()\">🖨️ Odštampaj članak</button>\n                            </div>\n                        </div>\n                    </footer>\n                    \n                </div>\n                <div class=\"col-lg-4 col-xl-3 col-md-12\">\n                    <div class=\"sidebar\">\n                        <div class=\"sidebar-card toc-card\" id=\"tocCard\" style=\"display: none;\"><h5>Sadržaj</h5><div id=\"tableOfContents\" class=\"toc-list\"></div></div>\n                        <div class=\"sidebar-card\">\n                            <h5>Preporučena literatura</h5>\n                            <div id=\"recommendedArticles\">\n                                \n                                <div class=\"recommended-item\"><h6><a href=\"Article.aspx?slug=document-intelligence-future-outlook\">【Dokument Inteligentna obrada serije · 20】 Razvojne perspektive tehnologije inteligentne obrade dokumenata</a></h6><p class=\"text-muted small\">2025-08-19 • 1203 Sledeće čitanje</p></div>\n                                \n                                <div class=\"recommended-item\"><h6><a href=\"Article.aspx?slug=document-intelligence-quality-assurance\">【Dokument Inteligentna obrada serije · 19】 Dokument Inteligentna obrada Sistem za osiguranje kvaliteta</a></h6><p class=\"text-muted small\">2025-08-19 • 1200 Sledeće čitanje</p></div>\n                                \n                                <div class=\"recommended-item\"><h6><a href=\"Article.aspx?slug=large-scale-document-processing-optimization\">【Dokument Inteligentna obrada serija · 18】 Optimizacija performansi obrade dokumenata velikih razmera</a></h6><p class=\"text-muted small\">2025-08-19 • 1201 Sledeće čitanje</p></div>\n                                \n                            </div>\n                        </div>\n                    </div>\n</div>\n</div>\n</div>\n</div>\n<script>\nvar articleId,viewCountKey,now=new Date().getTime();\ndocument.addEventListener('DOMContentLoaded',function(){var articleDetail=document.getElementById('articleDetail');if(articleDetail&&articleDetail.querySelector('.article-content')){formatContent();initReadingProgress();optimizeImages();updateViewCount();}});\nfunction updateViewCount(){articleId=getArticleId();if(!articleId)return;viewCountKey='article_view_'+articleId;var lastViewTime=localStorage.getItem(viewCountKey);if(lastViewTime&&(now-parseInt(lastViewTime))<3600000)return;var xhr=new XMLHttpRequest();xhr.open('POST','/Article.ashx',true);xhr.setRequestHeader('Content-Type','application/x-www-form-urlencoded');xhr.onreadystatechange=function(){if(xhr.readyState===4&&xhr.status===200){try{var response=JSON.parse(xhr.responseText);if(response.success){updateViewCountDisplay(response.viewCount);localStorage.setItem(viewCountKey,now.toString());}}catch(e){}}};xhr.send('action=updateviewcount&articleId='+encodeURIComponent(articleId));}\nfunction getArticleId(){var articleDetail=document.getElementById('articleDetail');if(articleDetail){var id=articleDetail.getAttribute('data-article-id');if(id)return id;}var urlParams=new URLSearchParams(window.location.search);return urlParams.get('id');}\nfunction updateViewCountDisplay(newCount){document.querySelectorAll('.view-count').forEach(function(element){element.textContent=newCount;});}\nfunction decodeHtmlEntities(text){var textArea=document.createElement('textarea');textArea.innerHTML=text;return textArea.value;}\nfunction formatContent(){var contentDiv=document.getElementById('articleContent');if(!contentDiv)return;var content=contentDiv.textContent||contentDiv.innerText;if(!content)return;content=decodeHtmlEntities(content);content=formatMarkdownContent(content);content=formatIndentation(content);content=formatLists(content);content=formatParagraphs(content);content=formatLinks(content);contentDiv.innerHTML=content;initTableOfContents();}\nfunction formatIndentation(content){var lines=content.split('\\n'),result=[];for(var i=0;i<lines.length;i++){var line=lines[i];if(line.trim().match(/^<[^>]+>/)){result.push(line);continue;}line=line.replace(/^\\t+/g,function(match){return '    '.repeat(match.length);});if(line.match(/^  +[^\\s]/)){var indentMatch=line.match(/^( +)(.*)$/);if(indentMatch){var indentLevel=Math.floor(indentMatch[1].length/2);line='<span class=\"text-indent-'+indentLevel+'\">'+indentMatch[2]+'</span>';}}result.push(line);}return result.join('\\n');}\nfunction formatMarkdownContent(content){content=content.replace(/^\\s*##### (.+)$/gm,'<h5>$1</h5>').replace(/^\\s*#### (.+)$/gm,'<h4>$1</h4>').replace(/^\\s*### (.+)$/gm,'<h3>$1</h3>').replace(/^\\s*## (.+)$/gm,'<h2>$1</h2>').replace(/^\\s*# (.+)$/gm,'<h1>$1</h1>').replace(/^> (.+)$/gm,'<blockquote><p>$1</p></blockquote>').replace(/\\*\\*([^*]+?)\\*\\*/g,'<strong>$1</strong>').replace(/\\*([^*\\n]+?)\\*/g,'<em>$1</em>').replace(/`([^`]+?)`/g,'<code class=\"inline-code\">$1</code>').replace(/^(\\*\\*[^*]+?\\*\\*)：/gm,'<div class=\"label-item\"><span class=\"label\">$1</span></div>').replace(/^([^：\\n]+?)：$/gm,'<div class=\"section-label\">$1：</div>').replace(/^(\\*\\*[^*]+?\\*\\*)：(.+)$/gm,'<div class=\"definition-item\"><span class=\"definition-label\">$1：</span><span class=\"definition-content\">$2</span></div>').replace(/^(note|note|note):(.+)$/gm,'<div class=\"note-item\"><span class=\"note-label\">$1：</span><span class=\"note-content\">$2</span></div>');content=formatLists(content);content=formatMathSymbols(content);content=formatParagraphs(content);return content;}\nfunction formatLists(content){var lines=content.split('\\n'),result=[],inOrderedList=false,inUnorderedList=false;for(var i=0;i<lines.length;i++){var line=lines[i],trimmed=line.trim();var orderedMatch=line.match(/^(\\d+)\\.\\s*(.+)$/);var unorderedMatch=line.match(/^[-*+]\\s*(.+)$/);if(orderedMatch){if(!inOrderedList){if(inUnorderedList){result.push('</ul>');inUnorderedList=false;}result.push('<ol class=\"formatted-list ordered\">');inOrderedList=true;}var listContent=orderedMatch[2].replace(/^\\*\\*([^*]+?)\\*\\*：(.+)$/,'<strong class=\"list-title\">$1：</strong><span class=\"list-content\">$2</span>');result.push('<li>'+listContent+'</li>');}else if(unorderedMatch){if(!inUnorderedList){if(inOrderedList){result.push('</ol>');inOrderedList=false;}result.push('<ul class=\"formatted-list unordered\">');inUnorderedList=true;}var listContent=unorderedMatch[1].replace(/^\\*\\*([^*]+?)\\*\\*：(.+)$/,'<strong class=\"list-title\">$1：</strong><span class=\"list-content\">$2</span>');result.push('<li>'+listContent+'</li>');}else{if(trimmed===''){if(i+1<lines.length){var nextLine=lines[i+1].trim();if(!nextLine.match(/^\\d+\\.\\s/)&&!nextLine.match(/^[-*+]\\s/)){if(inOrderedList){result.push('</ol>');inOrderedList=false;}if(inUnorderedList){result.push('</ul>');inUnorderedList=false;}}}result.push(line);}else{if(inOrderedList){result.push('</ol>');inOrderedList=false;}if(inUnorderedList){result.push('</ul>');inUnorderedList=false;}result.push(line);}}}if(inOrderedList)result.push('</ol>');if(inUnorderedList)result.push('</ul>');return result.join('\\n');}\nfunction formatMathSymbols(content){var mathSymbols={'α':'&alpha;','β':'&beta;','γ':'&gamma;','δ':'&delta;','ε':'&epsilon;','ζ':'&zeta;','η':'&eta;','θ':'&theta;','λ':'&lambda;','μ':'&mu;','π':'&pi;','σ':'&sigma;','∑':'&sum;','∏':'&prod;','∫':'&int;','∂':'&part;','∇':'&nabla;','∞':'&infin;','≤':'&le;','≥':'&ge;','≠':'&ne;','≈':'&asymp;','±':'&plusmn;','×':'&times;','÷':'&divide;','→':'&rarr;','←':'&larr;','↑':'&uarr;','↓':'&darr;','⇒':'&rArr;','⇐':'&lArr;'};var subMap={'₀':'0','₁':'1','₂':'2','₃':'3','₄':'4','₅':'5','₆':'6','₇':'7','₈':'8','₉':'9','ₖ':'k','ₜ':'t','ₛ':'s','₊':'+','₋':'-'};var supMap={'⁰':'0','¹':'1','²':'2','³':'3','⁴':'4','⁵':'5','⁶':'6','⁷':'7','⁸':'8','⁹':'9','ᵏ':'k','ᵗ':'t','ˢ':'s','⁺':'+','⁻':'-'};for(var symbol in mathSymbols){content=content.replace(new RegExp(escapeRegExp(symbol),'g'),mathSymbols[symbol]);}content=content.replace(/([a-zA-Z])₍([0-9]+)₎/g,'$1<sub>$2</sub>').replace(/([a-zA-Z])⁽([0-9]+)⁾/g,'$1<sup>$2</sup>').replace(/([a-zA-Z])([₀₁₂₃₄₅₆₇₈₉ₖₜₛ₊₋]+)/g,function(match,base,subscript){var converted=subscript.replace(/[₀₁₂₃₄₅₆₇₈₉ₖₜₛ₊₋]/g,function(char){return subMap[char]||char;});return base+'<sub>'+converted+'</sub>';}).replace(/([a-zA-Z])([⁰¹²³⁴⁵⁶⁷⁸⁹ᵏᵗˢ⁺⁻]+)/g,function(match,base,superscript){var converted=superscript.replace(/[⁰¹²³⁴⁵⁶⁷⁸⁹ᵏᵗˢ⁺⁻]/g,function(char){return supMap[char]||char;});return base+'<sup>'+converted+'</sup>';}).replace(/\\b([A-Za-z])_\\{([^}]+)\\}/g,'$1<sub>$2</sub>').replace(/\\b([A-Za-z])\\^\\{([^}]+)\\}/g,'$1<sup>$2</sup>');return content;}\nfunction formatParagraphs(content){var lines=content.split('\\n'),result=[],inParagraph=false,currentParagraph=[];for(var i=0;i<lines.length;i++){var originalLine=lines[i],line=originalLine.trim();if(line.match(/^<[^>]+>/)||line===''){if(inParagraph&&currentParagraph.length>0){var paragraphContent=currentParagraph.join(' ').trim();if(paragraphContent)result.push('<p class=\"content-paragraph indent-paragraph\">'+paragraphContent+'</p>');currentParagraph=[];inParagraph=false;}result.push(originalLine);continue;}if(line.match(/^#{1,6}\\s/)||line.match(/^\\d+\\.\\s/)||line.match(/^[-*+]\\s/)||line.match(/^>/)){if(inParagraph&&currentParagraph.length>0){var paragraphContent=currentParagraph.join(' ').trim();if(paragraphContent)result.push('<p class=\"content-paragraph indent-paragraph\">'+paragraphContent+'</p>');currentParagraph=[];inParagraph=false;}result.push(originalLine);continue;}if(line){currentParagraph.push(line);inParagraph=true;}}if(inParagraph&&currentParagraph.length>0){var paragraphContent=currentParagraph.join(' ').trim();if(paragraphContent)result.push('<p class=\"content-paragraph indent-paragraph\">'+paragraphContent+'</p>');}return result.join('\\n');}\nfunction escapeRegExp(string){return string.replace(/[.*+?^${}()|[\\]\\\\]/g,'\\\\$&');}\nfunction formatLinks(content){return content.replace(/<a\\s+href=\"(https?:\\/\\/[^\"]+)\"([^>]*)>/gi,function(match,url,attrs){return !attrs.includes('target=')?'<a href=\"'+url+'\"'+attrs+' target=\"_blank\" rel=\"noopener noreferrer\">':match;});}\nfunction escapeHtml(text){var div=document.createElement('div');div.textContent=text;return div.innerHTML;}\nfunction addTocClickEvents(){document.querySelectorAll('#tableOfContents a').forEach(function(link){link.addEventListener('click',function(e){e.preventDefault();var targetElement=document.getElementById(this.getAttribute('data-target'));if(targetElement){targetElement.scrollIntoView({behavior:'smooth',block:'start'});updateActiveTocItem(this);}});});}\nfunction addScrollHighlight(headings){var throttleTimer=null;function updateActiveHeading(){var windowHeight=window.innerHeight,activeHeading=null;for(var i=headings.length-1;i>=0;i--){var rect=headings[i].getBoundingClientRect();if(rect.top<=windowHeight*0.3){activeHeading=headings[i];break;}}if(activeHeading){var activeLink=document.querySelector('#tableOfContents a[data-target=\"'+activeHeading.id+'\"]');if(activeLink)updateActiveTocItem(activeLink);}}window.addEventListener('scroll',function(){if(throttleTimer)clearTimeout(throttleTimer);throttleTimer=setTimeout(updateActiveHeading,100);});updateActiveHeading();}\nfunction updateActiveTocItem(activeLink){document.querySelectorAll('#tableOfContents a').forEach(function(link){link.classList.remove('active');});if(activeLink){activeLink.classList.add('active');scrollTocToActiveItem(activeLink);}}\nfunction scrollTocToActiveItem(activeLink){var tocCard=document.getElementById('tocCard');if(!activeLink||!tocCard)return;var cardRect=tocCard.getBoundingClientRect();var activeRect=activeLink.getBoundingClientRect();var relativeTop=activeRect.top-cardRect.top;var relativeBottom=activeRect.bottom-cardRect.bottom;var cardStyle=window.getComputedStyle(tocCard);var paddingTop=parseFloat(cardStyle.paddingTop)||0;var titleHeight=tocCard.querySelector('h5')?tocCard.querySelector('h5').offsetHeight:0;var contentTop=paddingTop+titleHeight;if(relativeTop<contentTop){tocCard.scrollTop+=(relativeTop-contentTop-10);}else if(relativeBottom>0){tocCard.scrollTop+=(relativeBottom+10);}}\nfunction optimizeImages(){document.querySelectorAll('.article-content img').forEach(function(img){img.loading='lazy';img.onerror=function(){this.style.display='none';};if(!img.alt)img.alt='Članak sa slikama';});}\nfunction initReadingProgress(){var progressBar=document.getElementById('readingProgress');function updateProgress(){var scrollTop=window.pageYOffset||document.documentElement.scrollTop;var docHeight=document.documentElement.scrollHeight-window.innerHeight;progressBar.style.width=Math.min((scrollTop/docHeight)*100,100)+'%';}window.addEventListener('scroll',updateProgress);updateProgress();}\nfunction initTableOfContents(){var content=document.querySelector('.article-content');if(!content)return;var headings=content.querySelectorAll('h1, h2, h3, h4, h5, h6');if(headings.length<=1)return;var tocCard=document.getElementById('tocCard');var tocList=document.getElementById('tableOfContents');var html='',counters=[0,0,0,0,0,0];headings.forEach(function(heading,index){var id='heading-'+index;heading.id=id;var level=parseInt(heading.tagName.substring(1));var indent=(level-1)*15;counters[level-1]++;for(var i=level;i<counters.length;i++)counters[i]=0;var numberText='';for(var i=0;i<level;i++){if(counters[i]>0)numberText+=(numberText?'.':'')+counters[i];}numberText+='.';html+='<a href=\"#'+id+'\" class=\"toc-item toc-level-'+level+'\" style=\"padding-left: '+indent+'px;\" data-target=\"'+id+'\"><span class=\"toc-number\">'+numberText+'</span> <span class=\"toc-text\">'+heading.textContent+'</span></a>';});tocList.innerHTML=html;tocCard.style.display='block';addTocClickEvents();addScrollHighlight(headings);tocList.addEventListener('click',function(e){if(e.target.tagName==='A'){e.preventDefault();var targetElement=document.getElementById(e.target.getAttribute('data-target'));if(targetElement){targetElement.scrollIntoView({behavior:'smooth',block:'start'});targetElement.style.transition='all 0.3s ease';targetElement.style.backgroundColor='#e3f2fd';targetElement.style.padding='10px';targetElement.style.borderRadius='6px';setTimeout(function(){targetElement.style.backgroundColor='';targetElement.style.padding='';targetElement.style.borderRadius='';},2000);}}});}\nfunction shareToWeibo(){window.open('https://service.weibo.com/share/share.php?url='+encodeURIComponent(window.location.href)+'&title='+encodeURIComponent(document.title),'_blank');}\nfunction copyUrl(){navigator.clipboard.writeText(window.location.href).then(function(){alert('Link je kopiran u ostavu');}).catch(function(){var textArea=document.createElement('textarea');textArea.value=window.location.href;textArea.style.cssText='position:fixed;left:-999999px;top:-999999px';document.body.appendChild(textArea);textArea.focus();textArea.select();try{alert(document.execCommand('copy')?'Link je kopiran u ostavu':'Ako kopija ne uspe, kopirajte vezu ručno');}catch(err){alert('Ako kopija ne uspe, kopirajte vezu ručno');}document.body.removeChild(textArea);});}\nfunction printArticle(){var printWindow=window.open('','_blank');var articleContent=document.querySelector('.article-detail').innerHTML;var articleTitle=document.querySelector('.article-title').textContent;printWindow.document.write('<!DOCTYPE html><html lang=\"sr-Latn\"><title>'+articleTitle+'</title><style>body{font-family:-apple-system,BlinkMacSystemFont,\"Segoe UI\",Roboto,\"Helvetica Neue\",Arial,sans-serif;line-height:1.6;color:#333;max-width:800px;margin:0 auto;padding:20px}h1,h2,h3,h4,h5,h6{color:#1a1c1f;margin-top:1.5em;margin-bottom:0.5em}h1{font-size:2em;border-bottom:2px solid #e9ecef;padding-bottom:0.3em}h2{font-size:1.5em;border-bottom:1px solid #e9ecef;padding-bottom:0.3em}h3{font-size:1.25em}p{margin-bottom:1em}pre{background:#f8f9fa;border:1px solid #e9ecef;border-radius:4px;padding:1em;overflow-x:auto;font-family:Consolas,Monaco,\"Courier New\",monospace}code{background:#f1f3f4;padding:2px 4px;border-radius:3px;font-family:Consolas,Monaco,\"Courier New\",monospace}blockquote{border-left:4px solid #1764ff;background:#f8f9fa;padding:1em;margin:1em 0;border-radius:0 4px 4px 0}img{max-width:100%;height:auto}table{width:100%;border-collapse:collapse;margin:1em 0}th,td{border:1px solid #ddd;padding:8px;text-align:left}th{background-color:#f2f2f2}.article-meta,.article-footer,.share-buttons{display:none}</style><body>'+articleContent+'</body></html>');printWindow.document.close();printWindow.focus();setTimeout(function(){printWindow.print();printWindow.close();},250);}\nfunction improvedStickyPosition(){var sidebarCards=document.querySelectorAll('.sidebar-card');var originalPositions=[],isFixed=[];sidebarCards.forEach(function(card,index){var rect=card.getBoundingClientRect();originalPositions[index]=rect.top+window.pageYOffset;isFixed[index]=false;var placeholder=document.createElement('div');placeholder.style.cssText='height:'+card.offsetHeight+'px;display:none';placeholder.className='sticky-placeholder';card.parentNode.insertBefore(placeholder,card.nextSibling);});function updateStickyPosition(){var scrollTop=window.pageYOffset||document.documentElement.scrollTop;sidebarCards.forEach(function(card,index){var originalTop=originalPositions[index];var stickyTop=index>0?(document.querySelector('.toc-card').style.position==='fixed'?60+document.querySelector('.toc-card').offsetHeight+8:80):60;var placeholder=card.nextSibling;if(scrollTop>originalTop-stickyTop){if(!isFixed[index]){var colContainer=card.closest('[class*=\"col-\"]');var colRect=colContainer.getBoundingClientRect();var colStyle=window.getComputedStyle(colContainer);var paddingLeft=parseFloat(colStyle.paddingLeft)||0;var paddingRight=parseFloat(colStyle.paddingRight)||0;card.style.cssText='position:fixed;top:'+stickyTop+'px;left:'+(colRect.left+paddingLeft)+'px;width:'+(colRect.width-paddingLeft-paddingRight)+'px;z-index:9999;box-sizing:border-box';card.classList.add('fixed');placeholder.style.display='block';isFixed[index]=true;}}else if(isFixed[index]){card.style.cssText='';card.classList.remove('fixed');placeholder.style.display='none';isFixed[index]=false;}});}window.addEventListener('scroll',updateStickyPosition);window.addEventListener('resize',function(){setTimeout(function(){sidebarCards.forEach(function(card,index){if(isFixed[index]){var colContainer=card.closest('[class*=\"col-\"]');var colRect=colContainer.getBoundingClientRect();var colStyle=window.getComputedStyle(colContainer);var paddingLeft=parseFloat(colStyle.paddingLeft)||0;var paddingRight=parseFloat(colStyle.paddingRight)||0;card.style.left=(colRect.left+paddingLeft)+'px';card.style.width=(colRect.width-paddingLeft-paddingRight)+'px';}else{originalPositions[index]=card.getBoundingClientRect().top+window.pageYOffset;}});},100);});updateStickyPosition();}\ndocument.addEventListener('DOMContentLoaded',function(){setTimeout(improvedStickyPosition,1000);});\n    </script>\n\r\n            </div>\r\n        </form>\r\n\r\n        <div class=\"fixed-icon\">\r\n            <div style=\"width: 120px; position: relative; left: 1rem; top: -12px;\">\r\n            </div>\r\n            <a id=\"webchat\" class=\"wx-con disa\" href=\"http://wpa.qq.com/msgrd?v=3&uin=365833440&site=qq&menu=yes\" target=\"_blank\">\r\n                <img class=\"wechat-ico\" src=\"site/image/icon/qq.svg\" alt=\"OCR pomoćnik KK online korisnički servis\" />\r\n                <div class=\"wx-text\">KK korisnički servis (365833440)</div>\r\n                <label class=\"badge badge-danger\" style=\"background: rgb(255, 34, 122); display: inline-block;\">1</label>\r\n            </a>\r\n\r\n            <a class=\"wx-con\" href=\"https://qm.qq.com/cgi-bin/qm/qr?k=bohNy9GMdgD5bsYrB-fcIjy-s6Ot3zOg&jump_from=webapi&qr=1\" target=\"_blank\">\r\n                <img class=\"wechat-ico\" src=\"site/image/icon/qq.svg\" alt=\"OCR pomoćnik KK korisnik komunikacija grupa\" />\r\n                <div class=\"wx-text\">KK Grupa (100029010)</div>\r\n                <label class=\"badge badge-danger\" style=\"background: rgb(255, 34, 122); display: inline-block;\">2</label>\r\n            </a>\r\n            <a href=\"mailto:<EMAIL>\" class=\"wx-con\">\r\n                <img class=\"wechat-ico\" src=\"site/image/icon/im.svg\" alt=\"OCR pomoćnik kontaktirajte korisničku službu putem e-maila\" />\r\n                <div class=\"wx-text\" style=\"width: 240px; text-align: left; left: -15.3rem; padding: 10px 20px;\">\r\n                    <div style=\"font-size: 15px\">E-mail: <EMAIL></div>\r\n                    <hr style=\"background: #c1c1c1; margin: 10px 10px 6px 0;\" />\r\n                    <span style=\"font-size: 13px; color: #c1c1c1\">Hvala vam na komentarima i sugestijama!</span>\r\n                </div>\r\n                <label class=\"badge badge-danger\" style=\"background: rgb(255, 34, 122); display: inline-block;\">3</label>\r\n            </a>\r\n        </div>\r\n\r\n        <style>\r\n            .content {\r\n                width: 100%;\r\n                padding: 0 !important;\r\n                margin: 0 auto\r\n            }\r\n\r\n            h3.h6 {\r\n                font-size: 1rem !important;\r\n                font-weight: bold !important;\r\n                line-height: 1.2 !important;\r\n                margin-bottom: .5rem;\r\n                font-family: inherit\r\n            }\r\n        </style>\r\n        <footer class=\"page-footer\">\r\n            <div class=\"container\">\r\n                <div class=\"row foot-cont pt-4 no-gutters\" style=\"border-top: 1px solid #e1e6ed; max-width: 100%\">\r\n                    <div class=\"col-lg-12 hu-an p-0 mb-3 mt-3 text-l color-gray\" style=\"text-align: center;\">\r\n                        OCR pomoćnik za prepoznavanje teksta&nbsp;©️ 2025 ALL RIGHTS RESERVED. Sva prava zadržana&nbsp;&nbsp;&nbsp;|&nbsp;&nbsp;&nbsp;\r\n                        <a href=\"PrivacyPolicy.aspx\" style=\"text-decoration: underline;\">Ugovor o privatnosti</a>&nbsp;&nbsp;&nbsp;|&nbsp;&nbsp;&nbsp;\r\n                        <a href=\"UserAgreement.aspx\" style=\"text-decoration: underline;\">Korisnički ugovor</a>&nbsp;&nbsp;&nbsp;|&nbsp;&nbsp;&nbsp;\r\n                        <a href=\"Status.aspx\" style=\"text-decoration: underline;\">Status usluge</a>&nbsp;&nbsp;&nbsp;|&nbsp;&nbsp;&nbsp;\r\n                        <a class=\"mb-2 color-gray\" href=\"https://beian.miit.gov.cn/\" target=\"_blank\" style=\"text-decoration: underline;\">E ICP Priprema br. 2021012692</a>\r\n                    </div>\r\n                </div>\r\n            </div>\r\n        </footer>\r\n        <style>\r\n            #translate > .translateSelectLanguage {\r\n                right: 2rem;\r\n                font-size: 1rem;\r\n                width: 150px;\r\n                padding: .3rem;\r\n                margin-right: 20px;\r\n                border: 1px solid #C9C9C9;\r\n                background: #fff;\r\n                color: #555\r\n            }\r\n        </style>\r\n    </div>\r\n    <script>\r\n        document.addEventListener('DOMContentLoaded', function () {\r\n            var setScroll = function (active) {\r\n                forEach(qa('.v4_header_pc,.v4_header_mob'), function (e) {\r\n                    if (active) addClass(e, 'scrollActive'); else removeClass(e, 'scrollActive');\r\n                });\r\n            };\r\n            if (getScrollY() > 60) setScroll(1);\r\n            window.addEventListener('scroll', function () { setScroll(getScrollY() > 60); });\r\n\r\n            forEach(qa('.v4_header_pc'), function (h) {\r\n                h.addEventListener('mouseenter', function () { addClass(h, 'active'); });\r\n                h.addEventListener('mouseleave', function () { removeClass(h, 'active'); });\r\n            });\r\n\r\n            var menuTimer;\r\n            var hideMenu = function (menuId) {\r\n                var m = document.getElementById(menuId);\r\n                if (m) {\r\n                    m.style.cssText = 'visibility:hidden;height:0;display:none';\r\n                    var n = q('.nav-drown-con', m);\r\n                    if (n) { n.style.cssText = 'height:0;opacity:0;transform:translate(0,-100%)'; }\r\n                }\r\n            };\r\n\r\n            forEach(qa('ul.top-nav>li'), function (i) {\r\n                i.addEventListener('mouseenter', function () {\r\n                    clearTimeout(menuTimer);\r\n                    var l = this.querySelector('a'), t = this.querySelector('.triangle'), s = l ? l.getAttribute('data-sub') : null;\r\n                    if (l) addClass(l, 'active'); if (t) addClass(t, 'active');\r\n                    if (s) {\r\n                        var m = document.getElementById(s);\r\n                        if (m) {\r\n                            m.style.cssText = 'display:block;visibility:visible';\r\n                            var siblings = m.parentNode ? toArray(m.parentNode.children) : [];\r\n                            forEach(siblings, function (c) { if (c !== m) c.style.display = 'none'; });\r\n                            var n = q('.nav-drown-con', m);\r\n                            if (n) { n.style.cssText = 'height:100%;opacity:1;transform:translate(0,0)'; }\r\n\r\n                            m.addEventListener('mouseenter', function () { clearTimeout(menuTimer); });\r\n                            m.addEventListener('mouseleave', function () { menuTimer = setTimeout(function () { hideMenu(s); }, 100); });\r\n                        }\r\n                    }\r\n                });\r\n                i.addEventListener('mouseleave', function () {\r\n                    var l = this.querySelector('a'), t = this.querySelector('.triangle'), s = l ? l.getAttribute('data-sub') : null;\r\n                    if (l) removeClass(l, 'active'); if (t) removeClass(t, 'active');\r\n                    if (s) { menuTimer = setTimeout(function () { hideMenu(s); }, 100); }\r\n                });\r\n            });\r\n\r\n            var r = q('.v4_header_mob .right-menu');\r\n            if (r) r.addEventListener('click', function () {\r\n                var arr = [r, q('.v4_header_mob'), q('#nav_products'), q('.mob-nav-content .sidebar-fix')];\r\n                for (var i = 0; i < arr.length; i++) {\r\n                    var e = arr[i];\r\n                    if (e) {\r\n                        var name = (i === 0 ? 'active' : (i === 1 ? 'active' : (i === 2 ? 'active1' : 'fixedActiveBg')));\r\n                        toggleClass(e, name);\r\n                    }\r\n                }\r\n                var f = q('.mob-nav-content .sidebar-fix');\r\n                if (f) {\r\n                    toggleClass(f, 'show');\r\n                    document.body.style.overflow = hasClass(f, 'show') ? 'hidden' : 'scroll';\r\n                }\r\n            });\r\n\r\n            document.addEventListener('click', function (e) {\r\n                e = e || window.event;\r\n                var target = e.target || e.srcElement;\r\n                var m = closest(target, '.sidebar-fix-left .mob-nav-item');\r\n                if (m) {\r\n                    var p = m.parentNode;\r\n                    if (p) forEach(toArray(p.children), function (c) { removeClass(c, 'active'); });\r\n                    addClass(m, 'active');\r\n                    var d = m.getAttribute('data');\r\n                    if (d) forEach(qa('.sidebar-fix-rigth .sub-nav'), function (n, i) { n.style.display = (i == d ? 'block' : 'none'); });\r\n                }\r\n            });\r\n        });\r\n    </script>\r\n</body>\r\n</html>\r\n\r\n\t            <script type=\"text/javascript\">\r\n                    var _hmt = _hmt || [];\r\n                    (function () {\r\n                        var hm = document.createElement('script');hm.async=1;\r\n                        hm.src = 'https://hm.baidu.com/hm.js?5ee048d7caaf0d439cf16309a1687754';\r\n                        var s = document.getElementsByTagName('script')[0];s.parentNode.insertBefore(hm, s);\r\n                    })();\r\n                </script><div id=\"translate\" class=\"ignore\" style=\"display: none\"></div><script src=\"/static/js/translate.js\"></script>"
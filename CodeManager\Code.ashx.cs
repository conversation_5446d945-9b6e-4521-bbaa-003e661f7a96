using CommonLib;
using CommonLib.UserConfig;
using ImageLib;
using Microsoft.AspNet.SignalR;
using Newtonsoft.Json;
using Account.Web.Common.Translate;
using System;
using System.Collections.Generic;
using System.IO;
using System.IO.Compression;
using System.Linq;
using System.Text;
using System.Text.RegularExpressions;
using System.Threading;
using System.Threading.Tasks;
using System.Web;

namespace Account.Web
{
    /// <summary>
    ///     Code 的摘要说明
    /// </summary>
    public class Code1 : IHttpAsyncHandler
    {

        private const string OpParam = "op";
        private const string DefaultContentType = "text/plain";

        private const string StrOcrErrorStr = "{\"ocrType\":0,\"processName\":\"温馨提示\",\"result\":{\"spiltText\":\"[resultStr]\",\"autoText\":\"[resultStr]\"},\"id\":1}";
        // 登录按钮样式
        private const string primaryButtonStyle = "display:inline-block; box-sizing:border-box; min-width:80px; height:33px; line-height:33px; font-size:14px; border-radius:4px; margin:0; text-align:center; background-color:#0d6efd; color:#fff; border:none; margin-right:10px; font-weight:normal; text-decoration:none; padding:0 15px;";
        // 注销按钮样式
        private const string logoutStyle = "display:inline-block; box-sizing:border-box; font-size:14px; margin:0; text-align:center; color:#212529; border:none; text-decoration:none; height:33px; line-height:33px;";

        public async Task ProcessRequestAsync(HttpContext context)
        {
            var action = context.Request.Params[OpParam];
            if (string.IsNullOrEmpty(action))
            {
                return;
            }

            // 添加Connection: keep-alive到响应头
            context.Response.AddHeader("Connection", "keep-alive");
            context.Response.AddHeader("Keep-Alive", "timeout=3600");
            context.Response.ContentType = DefaultContentType;

            // 添加CORS头支持跨域访问（如果需要直接访问）
            context.Response.AddHeader("Access-Control-Allow-Origin", "*");
            context.Response.AddHeader("Access-Control-Allow-Methods", "GET, POST, OPTIONS");
            context.Response.AddHeader("Access-Control-Allow-Headers", "Content-Type");

            var result = "";
            UserTypeEnum userType = UserTypeEnum.体验版;
            CancellationTokenSource cts = new CancellationTokenSource();
            context.Response.ClientDisconnectedToken.Register(() =>
            {
                cts.Cancel();
            });
            switch (action)
            {
                //客户端配置相关
                case "health":
                    result = "true";
                    break;
                case "enableSignalR":
                    {
                        if (Global.IsSignalRReady)
                        {
                            var clientType = context.Request.GetValue("clientType");
                            if (Equals(clientType, "Web"))
                            {
                                result = "true";
                            }
                        }
                    }
                    break;
                case "sitemap":
                    {
                        var files = Directory.GetFiles(AppDomain.CurrentDomain.BaseDirectory, "*", SearchOption.AllDirectories);
                        var lstUrl = new List<string>();
                        var sb = new StringBuilder();
                        sb.AppendLine("<?xml version=\"1.0\" encoding=\"UTF-8\"?><urlset xmlns=\"http://www.sitemaps.org/schemas/sitemap/0.9\" xmlns:xhtml=\"http://www.w3.org/1999/xhtml\">");
                        var lstExt = new List<string>() { ".aspx", ".html" };
                        foreach (var file in files)
                        {
                            var strFile = file.Replace(AppDomain.CurrentDomain.BaseDirectory, "").Replace("\\", "/").ToLower();
                            if (strFile.StartsWith("obj/") || strFile.StartsWith("bin/") || strFile.Contains("pay")
                                || strFile.StartsWith("file/") || strFile.StartsWith("math/") || strFile.StartsWith("voice/")
                                || strFile.Contains("article") || strFile.Contains("ocr.aspx") || strFile.Contains("tool.aspx"))
                            {
                                continue;
                            }
                            if (lstExt.Exists(p => strFile.EndsWith(p)) && !LanguagePathModule.IsBackGroundPage(strFile))
                            {
                                lstUrl.Add(strFile);
                            }
                        }
                        var baseUrl = context.Request.Url.ToString().Replace(context.Request.Url.PathAndQuery, "");
                        var lstRawUrl = new List<string>() { baseUrl
                            ,baseUrl+"/articles.aspx?page=1"
                            ,baseUrl+"/articles.aspx?page=2"
                            ,baseUrl+"/articles.aspx?page=3"
                            ,baseUrl+"/articles.aspx?page=4"
                            ,baseUrl+"/articles.aspx?page=5"
                            ,baseUrl+"/articles.aspx?page=6"
                            ,baseUrl+"/articles.aspx?page=7"
                        };
                        foreach (var item in lstRawUrl)
                        {
                            var html = WebClientSyncExt.GetHtml(item);
                            while (html.Contains("<a href="))
                            {
                                html = html.Substring(html.IndexOf("<a href=") + 9);
                                var url = html.Substring(0, html.IndexOf("\"")).TrimStart('/').ToLower();
                                if (!lstUrl.Contains(url) && lstExt.Any(p => url.Contains(p)))
                                {
                                    lstUrl.Add(url);
                                }
                            }
                        }
                        lstUrl.RemoveAll(p => p.Contains("google"));
                        lstUrl.Insert(0, "");

                        foreach (var url in lstUrl)
                        {
                            sb.AppendLine("<url>");
                            sb.AppendLine(string.Format("<loc>{0}</loc>", LanguageConfiguration.HostUrl + url));
                            //sb.Append(LanguageService.GenerateSiteMapLanguageLinks(item));
                            sb.AppendLine("</url>");
                        }
                        sb.AppendLine("</urlset>");
                        result = sb.ToString();
                    }
                    break;
                case "trans":
                    {
                        var strSource = context.Request.Form["source"];
                        var strTrans = context.Request.Form["trans"];
                        var strLanguage = context.Request.Form["lang"];
                        var strKey = LanguageConfiguration.UserTranslationKey;
                        if (!string.IsNullOrEmpty(strLanguage) && !string.IsNullOrEmpty(strSource) && !string.IsNullOrEmpty(strTrans))
                        {
                            CommonTranslate.SetTranslate(strLanguage, HttpUtility.UrlDecode(strSource), HttpUtility.UrlDecode(strTrans), strKey);
                            result = CodeProcessHelper.ServerConfigCache.Get(strKey);
                        }
                    }
                    break;
                case "transWeb":
                    {
                        var strBody = string.Empty;
                        using (var reader = new StreamReader(context.Request.InputStream, Encoding.UTF8))
                        {
                            strBody = reader.ReadToEnd();
                        }
                        if (!string.IsNullOrEmpty(strBody))
                        {
                            try
                            {
                                var content = JsonConvert.DeserializeObject<TransPage>(strBody);
                                if (content != null && !string.IsNullOrEmpty(content.lang) && !string.IsNullOrEmpty(content.trans) && !string.IsNullOrEmpty(content.url))
                                {
                                    var url = new Uri(content.url);
                                    string path = url.LocalPath;

                                    // 尝试提取语言代码
                                    string lang = null;
                                    string actualPath = null;
                                    string oriLanuagePath = null;

                                    if (UrlService.TryExtractLanguage(path, out lang, out oriLanuagePath, out actualPath))
                                        url = CommonTranslate.GetCahcePath(new Uri(content.url.Replace(path, "") + "/" + actualPath));

                                    if (url.LocalPath.Contains(".aspx") || url.LocalPath.Contains(".html"))
                                    {
                                        lang = CommonTranslate.GetCurrentLang(content.lang);
                                        LogHelper.Log.Error("oldUrl:" + content.url + ",lang:" + lang + ",newUrl:" + url);
                                        var strTrans = HttpUtility.UrlDecode(content.trans);
                                        strTrans = strTrans.Replace(" lang=\"zh-CN\"", "");
                                        var htmlDesc = CommonHelper.SubString(strTrans, "<html", ">");
                                        if (!htmlDesc.Contains("lang"))
                                        {
                                            if (string.IsNullOrEmpty(htmlDesc))
                                                strTrans = strTrans.Replace("<html>", "<html lang=\"" + lang + "\">");
                                            else
                                                strTrans = strTrans.Replace(htmlDesc, htmlDesc + " lang=\"" + lang + "\"");
                                        }
                                        else
                                        {
                                            //todo 不包含HTML标签的
                                            if (!htmlDesc.Contains(lang))
                                                return;
                                        }
                                        if (!strTrans.Contains("<!DOCTYPE html>"))
                                        {
                                            strTrans = "<!DOCTYPE html>" + strTrans;
                                        }
                                    }
                                }
                            }
                            catch (Exception oe)
                            {
                                LogHelper.Log.Error("SaveCache异常", oe);
                            }
                        }
                    }
                    break;
                case "config":
                    {
                        var type = context.Request.GetValue("type");
                        if (!string.IsNullOrEmpty(type))
                            result = CodeProcessHelper.ServerConfigCache.Get(type);

                        if (Equals("TransCache", type))
                        {
                            var lang = CommonTranslate.GetCurrentLang(context.Request);
                            var dicResult = new Dictionary<string, Dictionary<string, string>>();
                            try
                            {
                                if (!string.IsNullOrEmpty(lang) && !Equals(lang, LanguageConfiguration.DefaultLanguage))
                                {
                                    var lstTrans = JsonConvert.DeserializeObject<Dictionary<string, Dictionary<string, string>>>(result);
                                    foreach (var item in lstTrans)
                                    {
                                        if (item.Value.ContainsKey(lang))
                                        {
                                            var dicLang = new Dictionary<string, string>
                                            {
                                                { lang, item.Value[lang] }
                                            };
                                            dicResult.Add(item.Key, dicLang);
                                        }
                                    }
                                }
                            }
                            catch { }
                            result = JsonConvert.SerializeObject(dicResult);
                        }
                    }
                    break;
                case "configs":
                    {
                        var type = context.Request.GetValue("type");
                        var lstResult = new List<string>();
                        var lstKeys = type.Split(new string[] { "," }, StringSplitOptions.RemoveEmptyEntries);
                        foreach (var key in lstKeys)
                        {
                            lstResult.Add(CodeProcessHelper.ServerConfigCache.Get(key));
                        }
                        result = JsonConvert.SerializeObject(lstResult);
                    }
                    break;
                case "sconfig":
                    {
                        var type = context.Request.GetValue("type");
                        var value = HttpUtility.UrlDecode(context.Request.GetValue("value"));
                        if (!string.IsNullOrEmpty(type))
                        {
                            CodeProcessHelper.ServerConfigCache.Set(type, value);
                            result = CodeProcessHelper.ServerConfigCache.Get(type);
                        }
                    }
                    break;
                case "path":
                    result = AppDomain.CurrentDomain.RelativeSearchPath;
                    break;
                case "tipconfig":
                    {
                        var lang = CommonTranslate.GetCurrentLang(context.Request);
                        if (string.IsNullOrEmpty(lang) || Equals(lang, LanguageConfiguration.DefaultLanguage))
                        {
                            result = JsonConvert.SerializeObject(TipMsgConfigurationSectionHandler.TipMsgSetting?.LstMsg);
                        }
                        else
                        {
                            try
                            {
                                var lstTrans = JsonConvert.DeserializeObject<Dictionary<string, Dictionary<string, string>>>(CodeProcessHelper.ServerConfigCache.Get(LanguageConfiguration.UserTranslationKey));
                                var lstTip = new List<UserMsgEntity>();
                                foreach (var item in TipMsgConfigurationSectionHandler.TipMsgSetting?.LstMsg)
                                {
                                    var msg = CommonHelper.DeepCopy(item);
                                    if (!string.IsNullOrEmpty(item.Title) && lstTrans.ContainsKey(item.Title) && lstTrans[item.Title].ContainsKey(lang))
                                    {
                                        msg.Title = lstTrans[item.Title][lang];
                                    }
                                    if (!string.IsNullOrEmpty(item.Content) && lstTrans.ContainsKey(item.Content) && lstTrans[item.Content].ContainsKey(lang))
                                    {
                                        msg.Content = lstTrans[item.Content][lang];
                                    }
                                    lstTip.Add(msg);
                                }
                                result = JsonConvert.SerializeObject(lstTip);
                            }
                            catch { }
                        }
                    }
                    break;
                case "time":
                    result = ServerTime.DateTime.Ticks.ToString("F0");
                    break;
                case "waitocr":
                    {
                        var strServer = HttpUtility.UrlDecode(context.Request.QueryString["server"]);
                        var timeOut = BoxUtil.GetInt32FromObject(context.Request.QueryString["timeout"], 10);
                        var version = BoxUtil.GetInt64FromObject(context.Request.QueryString["version"]);
                        var msgId = context.Request.QueryString["msgId"];
                        result = await OcrProcessHelper.WaitOcr(msgId, version, strServer, timeOut, OcrProcessHelper.ProcessByWeb, cts);
                    }
                    break;
                //获取服务端最新的用户类型信息
                case "syncusertype":
                    {
                        result = JsonConvert.SerializeObject(UserTypeHelper.GetAllUserType());
                    }
                    break;
                case "reportbeginprocess":
                    {
                        var strId = context.Request.QueryString["id"];
                        var strServer = HttpUtility.UrlDecode(context.Request.QueryString["server"]);
                        var dtAdd = BoxUtil.GetInt64FromObject(context.Request.QueryString["start"]);
                        var dtReceived = BoxUtil.GetInt64FromObject(context.Request.QueryString["receive"]);
                        var dtServerPush = BoxUtil.GetInt64FromObject(context.Request.QueryString["push"]);
                        var dtOcrStartTransfer = BoxUtil.GetInt64FromObject(context.Request.QueryString["current"]);
                        var version = BoxUtil.GetInt64FromObject(context.Request.QueryString["version"]);
                        var msgId = context.Request.QueryString["msgId"];
                        OcrProcessHelper.ReportToProcess(msgId, strId, strServer, dtReceived, dtServerPush, dtOcrStartTransfer, dtAdd, version, OcrProcessHelper.ProcessByWeb);
                        result = "success";
                    }
                    break;
                case "reportocrlog":
                    {
                        result = "success";
                    }
                    break;
                case "ocrresult":
                    {
                        var content = "";
                        try
                        {
                            using (var memoryStream = new MemoryStream())
                            {
                                context.Request.InputStream.CopyTo(memoryStream);
                                content = DecompressGzip(memoryStream.ToArray());
                            }
                        }
                        catch (Exception oe)
                        {
                            LogHelper.Log.Error("StreamReader Error", oe);
                        }
                        if (!string.IsNullOrEmpty(content))
                        {
                            var strServer = HttpUtility.UrlDecode(context.Request.QueryString["server"]);
                            var version = BoxUtil.GetInt64FromObject(context.Request.QueryString["version"]);
                            var msgId = context.Request.QueryString["msgId"];
                            OcrProcessHelper.SaveOcrResult(msgId, version, strServer, OcrProcessHelper.ProcessByWeb, content);
                            result = "true";
                        }
                    }
                    break;
                case "ocrresultcompensate":
                    {
                        // 失败返还次数
                        var account = HttpUtility.UrlDecode(context.Request.Form["account"]);
                        var token = HttpUtility.UrlDecode(context.Request.Form["token"]);
                        var id = HttpUtility.UrlDecode(context.Request.Form["id"]);
                        CodeProcessHelper.Compensate(account, token, id);
                        result = "true";
                    }
                    break;
                case "fileStaus":
                    {
                        var taskId = context.Request.GetValue("taskId");
                        var ocrType = BoxUtil.GetInt32FromObject(context.Request.GetValue("ocrType"), 0);

                        if (!string.IsNullOrEmpty(taskId))
                        {
                            var ocr = await CodeProcessHelper.SendToFileStatusProcessPool(taskId, ocrType, cts.Token);
                            if (ocr != null)
                            {
                                switch (ocr.state)
                                {
                                    case OcrProcessState.待处理:
                                        ocr.desc = "排队中，请稍候…";
                                        break;
                                    case OcrProcessState.处理中:
                                        ocr.desc = "处理中，请稍候…";
                                        break;
                                    case OcrProcessState.处理成功:
                                        ocr.desc = "处理完毕，可以下载了！";
                                        break;
                                    case OcrProcessState.处理失败:
                                        ocr.desc = "处理异常，请稍后重试！";
                                        break;
                                    case OcrProcessState.处理超时:
                                        ocr.desc = "处理超时，请稍后重试！";
                                        break;
                                    case OcrProcessState.未知状态:
                                        break;
                                    case OcrProcessState.并发限制:
                                        break;
                                    case OcrProcessState.类型不支持:
                                        break;
                                    case OcrProcessState.可预览:
                                        ocr.desc = "下载文件准备中，可以先预览…";
                                        break;
                                    default:
                                        break;
                                }
                                if (!string.IsNullOrEmpty(ocr.desc))
                                    ocr.desc = ocr.desc.GetTrans(context.Request.GetValue("lang"));
                            }
                            if (ocr == null)
                            {
                                ocr = new ProcessStateEntity()
                                {
                                    taskId = taskId,
                                    state = OcrProcessState.处理中
                                };
                            }
                            result = JsonConvert.SerializeObject(ocr);
                        }
                    }
                    break;
                case "waitfilestate":
                    {
                        var timeOut = BoxUtil.GetInt32FromObject(context.Request.QueryString["timeout"], 10);
                        var strServer = HttpUtility.UrlDecode(context.Request.QueryString["server"]);
                        var version = BoxUtil.GetInt64FromObject(context.Request.QueryString["version"]);
                        var msgId = context.Request.QueryString["msgId"];
                        result = await OcrProcessHelper.WaitFile(msgId, version, strServer, timeOut, OcrProcessHelper.ProcessByWeb, cts);
                    }
                    break;
                case "filestateresult":
                    {
                        var content = "";
                        try
                        {
                            using (var memoryStream = new MemoryStream())
                            {
                                context.Request.InputStream.CopyTo(memoryStream);
                                content = DecompressGzip(memoryStream.ToArray());
                            }
                        }
                        catch (Exception oe)
                        {
                            LogHelper.Log.Error("StreamReader Error", oe);
                        }
                        var strServer = HttpUtility.UrlDecode(context.Request.QueryString["server"]);
                        var version = BoxUtil.GetInt64FromObject(context.Request.QueryString["version"]);
                        var msgId = context.Request.QueryString["msgId"];
                        OcrProcessHelper.SaveFileResult(msgId, version, strServer, OcrProcessHelper.ProcessByWeb, content);
                    }
                    break;
                case "siteinfo":
                    #region SiteInfo

                    //result += CustomImageHelper.ReportToday();
                    result += Environment.NewLine + "服务器名称：" + context.Server.MachineName;
                    //服务器名称  
                    result += Environment.NewLine + "服务器IP地址：" + context.Request.ServerVariables["LOCAL_ADDR"];
                    //服务器IP地址  
                    result += Environment.NewLine + "HTTP访问端口：" + context.Request.ServerVariables["SERVER_PORT"];
                    //HTTP访问端口"
                    result += Environment.NewLine + ".NET版本：" + ".NET CLR" + Environment.Version.Major + "." +
                              Environment.Version.Minor + "." + Environment.Version.Build + "." + Environment.Version.Revision;
                    //.NET解释引擎版本  
                    result += Environment.NewLine + "服务器操作系统版本：" + Environment.OSVersion;
                    //服务器操作系统版本  
                    result += Environment.NewLine + "服务器IIS版本：" + context.Request.ServerVariables["SERVER_SOFTWARE"];
                    //服务器IIS版本  
                    result += Environment.NewLine + "服务器域名：" + context.Request.ServerVariables["SERVER_NAME"];
                    //服务器域名  
                    result += Environment.NewLine + "虚拟目录的绝对路径：" + context.Request.ServerVariables["APPL_RHYSICAL_PATH"];
                    //虚拟目录的绝对路径  
                    result += Environment.NewLine + "执行文件的绝对路径：" + context.Request.ServerVariables["PATH_TRANSLATED"];
                    ////执行文件的绝对路径  
                    //result += Environment.NewLine + "虚拟目录Session总数：" + context.Session.Contents.Count.ToString();
                    ////虚拟目录Session总数  
                    //result += Environment.NewLine + "虚拟目录Application总数：" + context.Application.Contents.Count.ToString();
                    //虚拟目录Application总数  
                    result += Environment.NewLine + "域名主机：" + context.Request.ServerVariables["HTTP_HOST"];
                    //域名主机  
                    result += Environment.NewLine + "服务器区域语言：" + context.Request.ServerVariables["HTTP_ACCEPT_LANGUAGE"];
                    //服务器区域语言  
                    result += Environment.NewLine + "用户信息：" + context.Request.ServerVariables["HTTP_USER_AGENT"];
                    result += Environment.NewLine + "CPU个数：" + Environment.GetEnvironmentVariable("NUMBER_OF_PROCESSORS");
                    //CPU个数  
                    result += Environment.NewLine + "CPU类型：" + Environment.GetEnvironmentVariable("PROCESSOR_IDENTIFIER");
                    //CPU类型  
                    result += Environment.NewLine + "请求来源地址：" + context.Request.Headers["X-Real-IP"];

                    #endregion
                    break;
                case "lastlog":
                    int availableWorker, availableIo, maxWorker = 0, maxIo = 0;

                    ThreadPool.GetAvailableThreads(out availableWorker, out availableIo);
                    ThreadPool.GetMaxThreads(out maxWorker, out maxIo);

                    result = string.Format("Worker:{0}/{1} IO:{2}/{3}{4}OffSet:{5}ms{4}"
                        , availableWorker, maxWorker, availableIo, maxIo, Environment.NewLine, new TimeSpan(ServerTime.OffSet).TotalMilliseconds);
                    //+ CommonHelper.GetLastLog();
                    //result = string.Format("STime:{0},Ticks:{1}{2}LTime:{3},Ticks:{4}{2}OffSet:{5},{6}ms{2}"
                    //    , ServerTime.DateTime.ToString("yyyy-MM-dd HH:mm:ss"), ServerTime.DateTime.Ticks, Environment.NewLine
                    //    , DateTime.Now.ToString("yyyy-MM-dd HH:mm:ss"), DateTime.Now.Ticks, ServerTime.offSet, (DateTime.Now - ServerTime.DateTime).TotalMilliseconds)
                    //    + result;
                    break;
                case "serverGroup":
                    result = JsonConvert.SerializeObject(
                        ObjectItemConfigurationSectionHandler.ObjectItemTypeSetting?.LstSettings?
                            .FirstOrDefault(p => Equals(p.Name, "ServerGroup"))?
                            .LstItem?.Where(p => p.Enable) ?? new List<ObjectTypeItem>());
                    break;
                case "localGroup":
                    result = JsonConvert.SerializeObject(
                        ObjectItemConfigurationSectionHandler.ObjectItemTypeSetting?.LstSettings?
                            .FirstOrDefault(p => Equals(p.Name, "LocalGroup"))?
                            .LstItem?.Where(p => p.Enable) ?? new List<ObjectTypeItem>());
                    break;
                case "localImage":
                    result = JsonConvert.SerializeObject(
                        ObjectItemConfigurationSectionHandler.ObjectItemTypeSetting?.LstSettings?
                            .FirstOrDefault(p => Equals(p.Name, "LocalImage"))?
                            .LstItem?.Where(p => p.Enable) ?? new List<ObjectTypeItem>());
                    break;
                #region 对外识别接口
                case "imgUpload":
                    if (context.Request.Files.Count > 0)
                    {
                        try
                        {
                            var file = context.Request.Files[0];
                            using (var binaryReader = new BinaryReader(file.InputStream))
                            {
                                var contentLength = file.ContentLength / 1024;//KB
                                //LogHelper.Log.Info("上传文件类型：" + fileExt + "，大小" + contentLength + "KB");
                                var byts = binaryReader.ReadBytes(file.ContentLength);
                                result = ImageHelper.GetUploadResult(byts);
                            }
                        }
                        catch (Exception oe)
                        {
                            LogHelper.Log.Error("上传图片出错！", oe);
                        }
                    }
                    break;
                case "imgCrop":
                    if (context.Request.Files.Count > 0)
                    {
                        try
                        {
                            var file = context.Request.Files[0];
                            using (var binaryReader = new BinaryReader(file.InputStream))
                            {
                                var contentLength = file.ContentLength / 1024;//KB
                                //LogHelper.Log.Info("上传文件类型：" + fileExt + "，大小" + contentLength + "KB");
                                var byts = binaryReader.ReadBytes(file.ContentLength);
                                result = ImageHelper.GetProcessResult(byts);
                            }
                        }
                        catch (Exception oe)
                        {
                            LogHelper.Log.Error("上传图片出错！", oe);
                        }
                    }
                    break;
                case "line":
                case "code":
                case "codeFile":
                    {
                        var account = context.Request.GetValue("app");
                        var validateVersion = Equals(account, "api") || CommonHelper.IsValidateVersion(context.Request);
                        if (!validateVersion)
                        {
                            result = StrOcrErrorStr.Replace("[resultStr]", ConfigHelper.MinVersionStr);
                        }
                        if (validateVersion)
                        {
                            long todayCount = 0;
                            long todayTokenCount = 0;
                            var strMsg = string.Empty;

                            var token = context.Request.GetValue("token");
                            var uid = context.Request.GetValue("uid");

                            if (!string.IsNullOrEmpty(uid))
                            {
                                if (string.IsNullOrEmpty(token))
                                {
                                    token = uid;
                                }

                                if (string.IsNullOrEmpty(account))
                                {
                                    account = UserLoginCache.TestUser + ":" + uid;
                                }
                            }

                            var strIndex = Guid.NewGuid().ToString().Replace("-", "");

                            if (ValidateRequest(context, account, token, strIndex, ref userType, ref todayCount, ref todayTokenCount, ref strMsg))
                            {
                                var strBase64 = "";
                                var url = "";
                                int contentLength = 0;
                                var fileExt = context.Request.QueryString["ext"];
                                if (context.Request.Files.Count > 0)
                                {
                                    try
                                    {
                                        var file = context.Request.Files[0];
                                        if (string.IsNullOrEmpty(fileExt) && !string.IsNullOrEmpty(file.FileName))
                                            fileExt = Path.GetExtension(file.FileName).TrimStart('.').ToLower();
                                        contentLength = file.ContentLength / 1024;
                                        //LogHelper.Log.Info("上传文件类型：" + fileExt + "，大小" + contentLength + "KB");
                                        if (fileExt?.Equals("txt") == true)
                                        {
                                            using (var binaryReader = new BinaryReader(file.InputStream))
                                            {
                                                var bytes = binaryReader.ReadBytes(file.ContentLength);
                                                strBase64 = Encoding.UTF8.GetString(bytes);
                                            }
                                        }
                                        else
                                        {
                                            //大于300K，保存到本地。否则直接处理
                                            if (contentLength > 30)
                                            {
                                                var user = context.Request.GetValue("app");
                                                var fileName = string.Format("{5}\\{0}\\{1}\\{2}\\{3}.{4}"
                                                    , ServerTime.LocalTime.ToString("yyyy")
                                                    , ServerTime.LocalTime.ToString("MM")
                                                    , ServerTime.LocalTime.ToString("dd")
                                                    , Guid.NewGuid().ToString().Replace("-", "")
                                                    , fileExt
                                                    , string.IsNullOrEmpty(user) ? "tmp" : user
                                                    );
                                                var fileFullName = string.Format("{0}\\{1}", ConfigHelper.StrTmpImagePath, fileName);

                                                if (!Directory.Exists(Path.GetDirectoryName(fileFullName)))
                                                {
                                                    Directory.CreateDirectory(Path.GetDirectoryName(fileFullName));
                                                }

                                                file.SaveAs(fileFullName);
                                                url = string.Format(ConfigHelper.FileHostUrl + fileName.Replace("\\", "/"));
                                            }
                                            else
                                            {
                                                using (var binaryReader = new BinaryReader(file.InputStream))
                                                {
                                                    var bytes = binaryReader.ReadBytes(file.ContentLength);
                                                    strBase64 = Convert.ToBase64String(bytes);
                                                }
                                            }
                                        }
                                    }
                                    catch (Exception oe)
                                    {
                                        LogHelper.Log.Error("获取StrBase64出错！", oe);
                                    }
                                }
                                else
                                {
                                    url = context.Request.Form["url"];
                                    strBase64 = context.Request.Form["code"];
                                }

                                if (!string.IsNullOrEmpty(strBase64) || !string.IsNullOrEmpty(url))
                                {
                                    var ocrType = (OcrType)BoxUtil.GetInt32FromObject(context.Request.QueryString["type"], 0);
                                    var fromLeftToRight = Equals(context.Request.QueryString["left"], "1");
                                    var fromTopToDown = Equals(context.Request.QueryString["top"], "1");
                                    var autoDirection = Equals(context.Request.QueryString["autodirection"], "1");
                                    //排版方向（1：竖版，2：横版）
                                    var layout = Equals(context.Request.QueryString["layout"], "1");
                                    var processId = BoxUtil.GetInt32NullableFromObject(context.Request.QueryString["pid"]);
                                    var from = (TransLanguageTypeEnum)BoxUtil.GetInt32FromObject(context.Request.QueryString["from"], 0);
                                    var to = (TransLanguageTypeEnum)BoxUtil.GetInt32FromObject(context.Request.QueryString["to"], 0);
                                    var dtUser = BoxUtil.GetInt64FromObject(context.Request.Headers["tick"], ServerTime.LocalTime.Ticks);
                                    List<int> groupList = new List<int>();
                                    try
                                    {
                                        groupList = JsonConvert.DeserializeObject<List<int>>("[" + HttpUtility.UrlDecode(context.Request.QueryString["group"] ?? "") + "]") ?? new List<int>();
                                    }
                                    catch { }

                                    groupList = groupList ?? new List<int>();
                                    if (groupList.Count <= 0)
                                    {
                                        groupList.Add(0);
                                    }
                                    else
                                    {
                                        if (groupList.Count > 1)
                                        {
                                            groupList = groupList.Distinct().ToList();
                                            if (groupList.Count > 1)
                                            {
                                                if (groupList.Contains(0))
                                                    groupList.Remove(0);

                                                if (groupList.Count > 3)
                                                {
                                                    groupList = new List<int>(groupList.Take(3));
                                                }
                                            }
                                        }
                                    }

                                    if (Equals(ocrType, OcrType.翻译) && Equals(from, to))
                                    {
                                        if (Equals(from, TransLanguageTypeEnum.自动))
                                            to = TransLanguageTypeEnum.英文;
                                        else
                                            to = TransLanguageTypeEnum.中文;
                                    }

                                    var isAutoFull2Half = Equals(context.Request.QueryString["half"], "1");
                                    var isAutoSpace = Equals(context.Request.QueryString["space"], "1");
                                    var isAutoSymbol = Equals(context.Request.QueryString["symbol"], "1");
                                    var isAutoDuplicateSymbol = Equals(context.Request.QueryString["duplicate"], "1");
                                    var isSupportVertical = BoxUtil.GetBoolNullableFromObject(context.Request.QueryString["vertical"]);
                                    var clientTicks = BoxUtil.GetInt64FromObject(context.Request.QueryString["ticks"], ServerTime.DateTime.Ticks);

                                    if (Equals(action, "line"))
                                    {
                                        if (!string.IsNullOrEmpty(strBase64))
                                        {
                                            result = WebClientSyncExt.GetHtml("http://127.0.0.1:8888/Code.do?type=line&img=" + HttpUtility.HtmlEncode(strBase64), 30);
                                        }
                                    }
                                    else
                                    {
                                        result = await CodeProcessHelper.SendToProcessPool(cts.Token, account, token, strIndex, strBase64, url, userType, ocrType, groupList
                                            , from, to, contentLength, processId
                                            , fromLeftToRight, fromTopToDown, autoDirection, fileExt, CodeProcessHelper.GetTimeOut(fileExt), dtUser
                                            , isAutoFull2Half, isAutoSpace, isAutoSymbol, isAutoDuplicateSymbol
                                            , isSupportVertical
                                            , clientTicks);
                                    }
                                }
                            }
                            else
                            {
                                if (Equals(account, "api"))
                                {
                                    result = strMsg;
                                }
                                else
                                {
                                    result = UserTypeHelper.GetLimitInfo(userType, !strMsg.Contains("限额"), todayCount, todayTokenCount);
                                    if (!string.IsNullOrEmpty(strMsg))
                                    {
                                        result = strMsg + "\n" + result;
                                    }
                                }
                                result = StrOcrErrorStr.Replace("[resultStr]", result);
                            }
                        }
                    }
                    break;
                case "htmlfile":
                    {
                        var json = context.Request.QueryString["param"];
                        if (!string.IsNullOrEmpty(json))
                        {
                            var lang = context.Request.GetValue("lang");
                            json = HttpUtility.UrlDecode(json);
                            result = CodeProcessHelper.GetFileResultHtml(json)
                                .Replace(StrFileView, StrFileView.GetTrans(lang))
                                .Replace(StrFileDownload, StrFileDownload.GetTrans(lang))
                                .Replace("【lang】", lang);
                            context.Response.ContentType = "text/html";
                        }
                    }
                    break;
                case "mathfile":
                    {
                        var json = context.Request.QueryString["param"];
                        if (!string.IsNullOrEmpty(json))
                        {
                            json = HttpUtility.UrlDecode(json);
                            result = CodeProcessHelper.GetMathResultHtml(json);
                            context.Response.ContentType = "text/html";
                        }
                    }
                    break;
                case "idcode":
                    {
                        var codeId = context.Request.Form["id"];
                        if (codeId != null)
                        {
                            result = CodeProcessHelper.GetOtherOcrResultById(codeId);
                        }
                    }
                    break;
                case "ocrprocessinfo":
                    {
                        var codeId = context.Request.QueryString["id"];
                        if (codeId != null)
                        {
                            result = JsonConvert.SerializeObject(CodeProcessHelper.GetProcessInfo(codeId));
                        }
                    }
                    break;
                case "report":
                    //siteFlag = context.Request.Form["flag"];
                    var reportContent = context.Request.Form["content"];
                    if (!string.IsNullOrEmpty(reportContent) || context.Request.Files != null && context.Request.Files.Count > 0)
                    {
                        var reportUser = context.Request.Headers["app"];
                        var reportPath = string.Format("{2}\\report\\{0}\\{1}\\"
                                        , string.IsNullOrEmpty(reportUser) ? "tmp" : reportUser
                                        , ServerTime.LocalTime.ToString("yyyy-MM-dd")
                                        , ConfigHelper.StrTmpImagePath
                                        );
                        var reportGuid = Guid.NewGuid().ToString().Replace("-", "");
                        if (!Directory.Exists(reportPath))
                        {
                            Directory.CreateDirectory(reportPath);
                        }
                        try
                        {
                            for (int i = 0; i < context.Request.Files.Count; i++)
                            {
                                var file = context.Request.Files[i];
                                var fileName = string.Format("{0}\\{1}.{2}"
                                    , reportPath
                                    , reportGuid + "-" + (i + 1)
                                    , "png"
                                    );
                                file.SaveAs(fileName);
                            }
                        }
                        catch (Exception oe)
                        {
                            LogHelper.Log.Error("保存反馈图片文件出错！", oe);
                        }

                        try
                        {
                            if (!string.IsNullOrEmpty(reportContent))
                            {
                                var fileName = string.Format("{0}\\{1}.{2}"
                                    , reportPath
                                    , reportGuid
                                    , "txt"
                                    );
                                File.WriteAllText(fileName, reportContent, Encoding.UTF8);
                                reportContent = reportContent.Substring(0, reportContent.Length > 50 ? 50 : reportContent.Length);
                            }
                        }
                        catch (Exception oe)
                        {
                            LogHelper.Log.Error("保存反馈文本出错！", oe);
                        }
                    }
                    result = "true";
                    break;
                case "savefile":
                    {
                        if (context.Request.Files.Count > 0)
                        {
                            var file = context.Request.Files[0];
                            try
                            {
                                var fileName = string.Format("Result\\{0}\\{1}"
                                    , ServerTime.LocalTime.ToString("yyyy-MM-dd")
                                    , file.FileName
                                    );
                                var fileFullName = string.Format("{0}\\{1}", ConfigHelper.FilePath, fileName);

                                if (!Directory.Exists(Path.GetDirectoryName(fileFullName)))
                                {
                                    Directory.CreateDirectory(Path.GetDirectoryName(fileFullName));
                                }

                                if (!File.Exists(fileFullName))
                                {
                                    file.SaveAs(fileFullName);
                                }
                                result = string.Format(ConfigHelper.FileHostUrl + fileName.Replace("\\", "/"));
                            }
                            catch (Exception oe)
                            {
                                LogHelper.Log.Error("保存文件出错！", oe);
                            }
                        }
                    }
                    break;
                #endregion

                #region Report
                case "serverstate":
                    {
                        var msg = context.Request.Form["info"];
                        if (!string.IsNullOrEmpty(msg))
                        {
                            var content = JsonConvert.DeserializeObject<ServerStateInfo>(msg);
                            CodeProcessHelper.AddServerState(content);
                            result = "ok";
                        }
                        else
                        {
                            result = "info为空";
                        }
                    }
                    break;
                case "reportError":
                    {
                        var strServer = HttpUtility.UrlDecode(context.Request.QueryString["server"]);
                        var msg = HttpUtility.UrlDecode(context.Request.QueryString["msg"]);
                        LogHelper.Log.Warn(string.Format("【{0}】:{1}", strServer, msg));
                    }
                    break;
                case "testMsg":
                    {
                        string clientId = context.Request["clientId"];
                        var img = new CusImageEntity()
                        {
                            StrIndex = Guid.NewGuid().ToString(),
                            DtExpired = ServerTime.DateTime.AddMinutes(10).Ticks,
                            Account = "***********",
                            ImgUrl = "https://download.ydstatic.com/ead/zhiyun/guanwang_cdn_2019/images/p-ocr/cookbook100K.jpg",
                            UserType = UserTypeEnum.旗舰版,
                            FileExt = "png"
                        };

                        // 使用OcrTime对象记录时间信息
                        img.OcrTime.ServerReceivedUserRequest = ServerTime.DateTime.Ticks;
                        img.OcrTime.UserStartRequest = ServerTime.DateTime.Ticks;
                        img.OcrTime.ServerAlloted = ServerTime.DateTime.Ticks;

                        var hubContext = GlobalHost.ConnectionManager.GetHubContext<OcrHub>();
                        hubContext.Clients.Client(clientId).receiveMessage("waitocr", JsonConvert.SerializeObject(img));
                    }
                    break;
                #endregion
                case "getServers":
                    {
                        context.Response.ContentType = "application/json";
                        var lstCode = CodeProcessHelper.GetServerStateInfos().OrderByDescending(p => p.DtUpdate).ToList();
                        var servers = new List<object>();

                        foreach (var stateInfo in lstCode)
                        {
                            var serverData = new
                            {
                                desc = stateInfo.Desc ?? "",
                                createTime = stateInfo.Server != null && stateInfo.Server.CreateTime.Year > 2000
                                    ? stateInfo.Server.CreateTime.ToString("yyyy-MM-dd") : "",
                                maskedIp = CommonLib.BoxUtil.MaskIPAddress(stateInfo.Ip),
                                hasHost = !string.IsNullOrEmpty(stateInfo.Host),
                                mode = stateInfo.Server?.Mode ?? "",
                                os = stateInfo.Server?.OS ?? "",
                                is64Bit = stateInfo.Server?.Is64BitOperatingSystem ?? false,
                                statsSummary = stateInfo.StatsSummary?.Replace(Environment.NewLine, " | ") ?? "",
                                lastUpdate = stateInfo.DtUpdate.ToString("MM-dd HH:mm"),
                                version = stateInfo.Version.ToString("MM-dd HH:mm")
                            };

                            servers.Add(serverData);
                        }

                        result = JsonConvert.SerializeObject(servers);
                    }
                    break;

                // 待翻译页面管理接口
                case "getPendingPages":
                    {
                        try
                        {
                            var pendingPages = PendingTranslationManager.GetPendingPages();
                            result = JsonConvert.SerializeObject(new
                            {
                                success = true,
                                data = pendingPages.Select(p => new
                                {
                                    pagePath = p.PagePath,
                                    physicalPath = p.PhysicalPath,
                                    firstRequestAt = p.FirstRequestAt,
                                    lastRequestAt = p.LastRequestAt,
                                    requestCount = p.RequestCount
                                }).ToArray(),
                                totalCount = pendingPages.Count
                            });
                        }
                        catch (Exception ex)
                        {
                            LogHelper.Log.Error("获取待翻译页面失败", ex);
                            result = JsonConvert.SerializeObject(new
                            {
                                success = false,
                                message = ex.Message
                            });
                        }
                    }
                    break;

                case "clearPendingPages":
                    {
                        try
                        {
                            PendingTranslationManager.ClearAllPendingPages();
                            result = JsonConvert.SerializeObject(new
                            {
                                success = true,
                                message = "待翻译页面已清理"
                            });
                        }
                        catch (Exception ex)
                        {
                            LogHelper.Log.Error("清理待翻译页面失败", ex);
                            result = JsonConvert.SerializeObject(new
                            {
                                success = false,
                                message = ex.Message
                            });
                        }
                    }
                    break;

                case "getServerPendingPages":
                    {
                        try
                        {
                            var serverUrl = context.Request.Form["serverUrl"] ?? context.Request.QueryString["serverUrl"];

                            if (string.IsNullOrEmpty(serverUrl))
                            {
                                result = JsonConvert.SerializeObject(new
                                {
                                    success = false,
                                    message = "服务端URL不能为空"
                                });
                                break;
                            }

                            // 构建请求URL
                            var requestUrl = $"{serverUrl.TrimEnd('/')}/code.ashx?op=getPendingPages";

                            LogHelper.Log.Info($"请求服务端待翻译页面: {requestUrl}");

                            // 使用WebClientSyncExt发起请求
                            var response = WebClientSyncExt.GetHtml(requestUrl, "", "", null, 30);

                            if (!string.IsNullOrEmpty(response))
                            {
                                // 直接返回服务端的响应
                                result = response;
                            }
                            else
                            {
                                result = JsonConvert.SerializeObject(new
                                {
                                    success = false,
                                    message = "服务端返回空响应"
                                });
                            }
                        }
                        catch (Exception ex)
                        {
                            LogHelper.Log.Error("获取服务端待翻译页面失败", ex);
                            result = JsonConvert.SerializeObject(new
                            {
                                success = false,
                                message = $"请求服务端失败: {ex.Message}"
                            });
                        }
                    }
                    break;

                default:
                    break;
            }
            if (string.IsNullOrEmpty(result) && !action.Equals("order") && !action.Equals("code"))
            {
                result = "no";
            }
            context.Response.Write(result ?? "");
        }

        private const string StrFileView = "文件预览";
        private const string StrFileDownload = "下载地址";

        string DecompressGzip(byte[] compressedData)
        {
            if (compressedData == null || compressedData.Length == 0)
            {
                return string.Empty;
            }
            var result = string.Empty;
            try
            {
                // 检查是否为GZIP格式（0x1F 0x8B是GZIP头部标识）
                if (compressedData.Length >= 2 && compressedData[0] == 0x1F && compressedData[1] == 0x8B)
                {
                    using (var memoryStream = new MemoryStream(compressedData))
                    using (var zipStream = new GZipStream(memoryStream, CompressionMode.Decompress))
                    using (var resultStream = new MemoryStream())
                    {
                        zipStream.CopyTo(resultStream);
                        result = Encoding.UTF8.GetString(resultStream.GetBuffer(), 0, (int)resultStream.Length);
                    }
                }
                else
                {
                    // 直接作为UTF-8字符串解码
                    result = Encoding.UTF8.GetString(compressedData);
                }
            }
            catch (Exception oe)
            {
                LogHelper.Log.Error("DecompressGzipToString Error", oe);
                try
                {
                    result = Encoding.UTF8.GetString(compressedData);
                }
                catch (Exception ex)
                {
                    LogHelper.Log.Error("DecompressGzipToString UTF8.GetString Error", ex);
                }
            }
            finally
            {
                compressedData = null;
            }
            return result;
        }

        bool IHttpHandler.IsReusable => false;

        private bool ValidateRequest(HttpContext context, string account, string token, string strIndex, ref UserTypeEnum userType, ref long todayCount, ref long todayTokenCount, ref string strMsg)
        {
            var result = false;
            if (!string.IsNullOrEmpty(token))
            {
                UserType userTypeInfo = null;
                if (account.Equals("api", StringComparison.InvariantCultureIgnoreCase))
                {
                    if (!string.IsNullOrEmpty(token))
                        result = RdsCacheHelper.ApiUserRecordCache.ApiValidate(token, strIndex, ref todayTokenCount, ref strMsg);
                    else
                        strMsg = "Token不存在";
                }
                else
                {
                    var validate = UserTypeHelper.ValidateUserAndToken(account, token, ref userTypeInfo);
                    if (validate)
                    {
                        userType = userTypeInfo.Type;
                        result = RdsCacheHelper.LimitHelper.CheckIsAble(userTypeInfo.Type, account, token, strIndex, true
                            , userTypeInfo.PerTimeSpan, userTypeInfo.PerTimeSpanExecCount, userTypeInfo.LimitPerDayCount,
                            ref todayCount, ref todayTokenCount, ref strMsg);
                        Console.WriteLine($"{token}:{result} todayCount:{todayCount},TokenCount:{todayTokenCount}");
                        if (!result)
                        {
                            var mac = context.Request.Headers["mac"];
                            if (!string.IsNullOrEmpty(mac))
                            {
                                mac = HttpUtility.UrlDecode(mac);
                            }

                            var version = context.Request.Headers["ver"];
                            ConfigHelper._Log.Info(string.Format("Account:{0},Mac:{1},Version:{2} 被限制访问！", account, mac,
                                version));
                        }
                    }
                    else
                    {
                        LogHelper.Log.Info(string.Format("掉线日志 Account:{0},Token:{1}", account, token));
                        strMsg = "账号已掉线，请重新登录后重试！\n或联系客服协助(右键菜单:关于我们->在线客服)！";
                    }
                }
            }
            else
            {
                strMsg = "请升级到最新版后重试(右键:关于我们->检查更新)！\n或联系客服协助(右键:关于我们->在线客服)！";
            }
            return result;
        }

        IAsyncResult IHttpAsyncHandler.BeginProcessRequest(HttpContext context, AsyncCallback cb, object extraData)
        {
            var task = ProcessRequestAsync(context);
            task.ContinueWith(t => cb?.Invoke(t));
            return task;
        }

        void IHttpHandler.ProcessRequest(HttpContext context)
        {

        }

        void IHttpAsyncHandler.EndProcessRequest(IAsyncResult result)
        {
            if (result is Task task)
            {
                task.Dispose(); // 清理资源
            }
        }


    }

    internal class TransPage
    {
        public string url { get; set; }

        public string trans { get; set; }

        public string lang { get; set; }
    }
}
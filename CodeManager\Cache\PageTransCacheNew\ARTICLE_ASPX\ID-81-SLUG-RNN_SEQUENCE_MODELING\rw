﻿"<!DOCTYPE html>\r\n<html xmlns=\"http://www.w3.org/1999/xhtml\" lang=\"rw\">\r\n<head><link rel=\"canonical\" href=\"https://ocr.oldfish.cn/rw/Article.aspx?id=81&slug=rnn-sequence-modeling\" /><link rel=\"alternate\" href=\"https://ocr.oldfish.cn/Article.aspx?id=81&slug=rnn-sequence-modeling\" hreflang=\"x-default\" />\r\n<link rel=\"alternate\" href=\"https://ocr.oldfish.cn/Article.aspx?id=81&slug=rnn-sequence-modeling\" hreflang=\"zh\" />\r\n<link rel=\"alternate\" href=\"https://ocr.oldfish.cn/Article.aspx?id=81&slug=rnn-sequence-modeling\" hreflang=\"zh\" />\r\n<link rel=\"alternate\" href=\"https://ocr.oldfish.cn/zh-Hant/Article.aspx?id=81&slug=rnn-sequence-modeling\" hreflang=\"zh-tw\" />\r\n<link rel=\"alternate\" href=\"https://ocr.oldfish.cn/yue/Article.aspx?id=81&slug=rnn-sequence-modeling\" hreflang=\"zh-yue\" />\r\n<link rel=\"alternate\" href=\"https://ocr.oldfish.cn/en/Article.aspx?id=81&slug=rnn-sequence-modeling\" hreflang=\"en\" />\r\n<link rel=\"alternate\" href=\"https://ocr.oldfish.cn/es/Article.aspx?id=81&slug=rnn-sequence-modeling\" hreflang=\"es\" />\r\n<link rel=\"alternate\" href=\"https://ocr.oldfish.cn/hi/Article.aspx?id=81&slug=rnn-sequence-modeling\" hreflang=\"hi\" />\r\n<link rel=\"alternate\" href=\"https://ocr.oldfish.cn/ar/Article.aspx?id=81&slug=rnn-sequence-modeling\" hreflang=\"ar\" />\r\n<link rel=\"alternate\" href=\"https://ocr.oldfish.cn/pt/Article.aspx?id=81&slug=rnn-sequence-modeling\" hreflang=\"pt\" />\r\n<link rel=\"alternate\" href=\"https://ocr.oldfish.cn/bn/Article.aspx?id=81&slug=rnn-sequence-modeling\" hreflang=\"bn\" />\r\n<link rel=\"alternate\" href=\"https://ocr.oldfish.cn/ru/Article.aspx?id=81&slug=rnn-sequence-modeling\" hreflang=\"ru\" />\r\n<link rel=\"alternate\" href=\"https://ocr.oldfish.cn/ja/Article.aspx?id=81&slug=rnn-sequence-modeling\" hreflang=\"ja\" />\r\n<link rel=\"alternate\" href=\"https://ocr.oldfish.cn/pa/Article.aspx?id=81&slug=rnn-sequence-modeling\" hreflang=\"pa\" />\r\n<link rel=\"alternate\" href=\"https://ocr.oldfish.cn/de/Article.aspx?id=81&slug=rnn-sequence-modeling\" hreflang=\"de\" />\r\n<link rel=\"alternate\" href=\"https://ocr.oldfish.cn/fr/Article.aspx?id=81&slug=rnn-sequence-modeling\" hreflang=\"fr\" />\r\n<link rel=\"alternate\" href=\"https://ocr.oldfish.cn/mr/Article.aspx?id=81&slug=rnn-sequence-modeling\" hreflang=\"mr\" />\r\n<link rel=\"alternate\" href=\"https://ocr.oldfish.cn/te/Article.aspx?id=81&slug=rnn-sequence-modeling\" hreflang=\"te\" />\r\n<link rel=\"alternate\" href=\"https://ocr.oldfish.cn/vi/Article.aspx?id=81&slug=rnn-sequence-modeling\" hreflang=\"vi\" />\r\n<link rel=\"alternate\" href=\"https://ocr.oldfish.cn/ko/Article.aspx?id=81&slug=rnn-sequence-modeling\" hreflang=\"ko\" />\r\n<link rel=\"alternate\" href=\"https://ocr.oldfish.cn/ta/Article.aspx?id=81&slug=rnn-sequence-modeling\" hreflang=\"ta\" />\r\n<link rel=\"alternate\" href=\"https://ocr.oldfish.cn/ur/Article.aspx?id=81&slug=rnn-sequence-modeling\" hreflang=\"ur\" />\r\n<link rel=\"alternate\" href=\"https://ocr.oldfish.cn/tr/Article.aspx?id=81&slug=rnn-sequence-modeling\" hreflang=\"tr\" />\r\n<link rel=\"alternate\" href=\"https://ocr.oldfish.cn/it/Article.aspx?id=81&slug=rnn-sequence-modeling\" hreflang=\"it\" />\r\n<link rel=\"alternate\" href=\"https://ocr.oldfish.cn/gu/Article.aspx?id=81&slug=rnn-sequence-modeling\" hreflang=\"gu\" />\r\n<link rel=\"alternate\" href=\"https://ocr.oldfish.cn/pl/Article.aspx?id=81&slug=rnn-sequence-modeling\" hreflang=\"pl\" />\r\n<link rel=\"alternate\" href=\"https://ocr.oldfish.cn/uk/Article.aspx?id=81&slug=rnn-sequence-modeling\" hreflang=\"uk\" />\r\n<link rel=\"alternate\" href=\"https://ocr.oldfish.cn/ms/Article.aspx?id=81&slug=rnn-sequence-modeling\" hreflang=\"ms\" />\r\n<link rel=\"alternate\" href=\"https://ocr.oldfish.cn/id/Article.aspx?id=81&slug=rnn-sequence-modeling\" hreflang=\"id\" />\r\n<link rel=\"alternate\" href=\"https://ocr.oldfish.cn/ml/Article.aspx?id=81&slug=rnn-sequence-modeling\" hreflang=\"ml\" />\r\n<link rel=\"alternate\" href=\"https://ocr.oldfish.cn/kn/Article.aspx?id=81&slug=rnn-sequence-modeling\" hreflang=\"kn\" />\r\n<link rel=\"alternate\" href=\"https://ocr.oldfish.cn/fa/Article.aspx?id=81&slug=rnn-sequence-modeling\" hreflang=\"fa\" />\r\n<link rel=\"alternate\" href=\"https://ocr.oldfish.cn/nl/Article.aspx?id=81&slug=rnn-sequence-modeling\" hreflang=\"nl\" />\r\n<link rel=\"alternate\" href=\"https://ocr.oldfish.cn/th/Article.aspx?id=81&slug=rnn-sequence-modeling\" hreflang=\"th\" />\r\n<link rel=\"alternate\" href=\"https://ocr.oldfish.cn/sw/Article.aspx?id=81&slug=rnn-sequence-modeling\" hreflang=\"sw\" />\r\n<link rel=\"alternate\" href=\"https://ocr.oldfish.cn/ro/Article.aspx?id=81&slug=rnn-sequence-modeling\" hreflang=\"ro\" />\r\n<link rel=\"alternate\" href=\"https://ocr.oldfish.cn/my/Article.aspx?id=81&slug=rnn-sequence-modeling\" hreflang=\"my\" />\r\n<link rel=\"alternate\" href=\"https://ocr.oldfish.cn/or/Article.aspx?id=81&slug=rnn-sequence-modeling\" hreflang=\"or\" />\r\n<link rel=\"alternate\" href=\"https://ocr.oldfish.cn/he/Article.aspx?id=81&slug=rnn-sequence-modeling\" hreflang=\"he\" />\r\n<link rel=\"alternate\" href=\"https://ocr.oldfish.cn/am/Article.aspx?id=81&slug=rnn-sequence-modeling\" hreflang=\"am\" />\r\n<link rel=\"alternate\" href=\"https://ocr.oldfish.cn/fil/Article.aspx?id=81&slug=rnn-sequence-modeling\" hreflang=\"fil\" />\r\n<link rel=\"alternate\" href=\"https://ocr.oldfish.cn/sv/Article.aspx?id=81&slug=rnn-sequence-modeling\" hreflang=\"sv\" />\r\n<link rel=\"alternate\" href=\"https://ocr.oldfish.cn/el/Article.aspx?id=81&slug=rnn-sequence-modeling\" hreflang=\"el\" />\r\n<link rel=\"alternate\" href=\"https://ocr.oldfish.cn/cs/Article.aspx?id=81&slug=rnn-sequence-modeling\" hreflang=\"cs\" />\r\n<link rel=\"alternate\" href=\"https://ocr.oldfish.cn/hu/Article.aspx?id=81&slug=rnn-sequence-modeling\" hreflang=\"hu\" />\r\n<link rel=\"alternate\" href=\"https://ocr.oldfish.cn/be/Article.aspx?id=81&slug=rnn-sequence-modeling\" hreflang=\"be\" />\r\n<link rel=\"alternate\" href=\"https://ocr.oldfish.cn/si/Article.aspx?id=81&slug=rnn-sequence-modeling\" hreflang=\"si\" />\r\n<link rel=\"alternate\" href=\"https://ocr.oldfish.cn/ne/Article.aspx?id=81&slug=rnn-sequence-modeling\" hreflang=\"ne\" />\r\n<link rel=\"alternate\" href=\"https://ocr.oldfish.cn/km/Article.aspx?id=81&slug=rnn-sequence-modeling\" hreflang=\"km\" />\r\n<link rel=\"alternate\" href=\"https://ocr.oldfish.cn/sk/Article.aspx?id=81&slug=rnn-sequence-modeling\" hreflang=\"sk\" />\r\n<link rel=\"alternate\" href=\"https://ocr.oldfish.cn/bg/Article.aspx?id=81&slug=rnn-sequence-modeling\" hreflang=\"bg\" />\r\n<link rel=\"alternate\" href=\"https://ocr.oldfish.cn/fr-ca/Article.aspx?id=81&slug=rnn-sequence-modeling\" hreflang=\"fr-ca\" />\r\n<link rel=\"alternate\" href=\"https://ocr.oldfish.cn/ha/Article.aspx?id=81&slug=rnn-sequence-modeling\" hreflang=\"ha\" />\r\n<link rel=\"alternate\" href=\"https://ocr.oldfish.cn/yo/Article.aspx?id=81&slug=rnn-sequence-modeling\" hreflang=\"yo\" />\r\n<link rel=\"alternate\" href=\"https://ocr.oldfish.cn/ig/Article.aspx?id=81&slug=rnn-sequence-modeling\" hreflang=\"ig\" />\r\n<link rel=\"alternate\" href=\"https://ocr.oldfish.cn/ku/Article.aspx?id=81&slug=rnn-sequence-modeling\" hreflang=\"ku\" />\r\n<link rel=\"alternate\" href=\"https://ocr.oldfish.cn/rw/Article.aspx?id=81&slug=rnn-sequence-modeling\" hreflang=\"rw\" />\r\n<link rel=\"alternate\" href=\"https://ocr.oldfish.cn/ca/Article.aspx?id=81&slug=rnn-sequence-modeling\" hreflang=\"ca\" />\r\n<link rel=\"alternate\" href=\"https://ocr.oldfish.cn/da/Article.aspx?id=81&slug=rnn-sequence-modeling\" hreflang=\"da\" />\r\n<link rel=\"alternate\" href=\"https://ocr.oldfish.cn/fi/Article.aspx?id=81&slug=rnn-sequence-modeling\" hreflang=\"fi\" />\r\n<link rel=\"alternate\" href=\"https://ocr.oldfish.cn/nb/Article.aspx?id=81&slug=rnn-sequence-modeling\" hreflang=\"nb\" />\r\n<link rel=\"alternate\" href=\"https://ocr.oldfish.cn/hr/Article.aspx?id=81&slug=rnn-sequence-modeling\" hreflang=\"hr\" />\r\n<link rel=\"alternate\" href=\"https://ocr.oldfish.cn/sr-Cyrl/Article.aspx?id=81&slug=rnn-sequence-modeling\" hreflang=\"sr-cyrl\" />\r\n<link rel=\"alternate\" href=\"https://ocr.oldfish.cn/sr-Latn/Article.aspx?id=81&slug=rnn-sequence-modeling\" hreflang=\"sr-latn\" />\r\n<link rel=\"alternate\" href=\"https://ocr.oldfish.cn/sq/Article.aspx?id=81&slug=rnn-sequence-modeling\" hreflang=\"sq\" />\r\n<link rel=\"alternate\" href=\"https://ocr.oldfish.cn/so/Article.aspx?id=81&slug=rnn-sequence-modeling\" hreflang=\"so\" />\r\n<link rel=\"alternate\" href=\"https://ocr.oldfish.cn/zu/Article.aspx?id=81&slug=rnn-sequence-modeling\" hreflang=\"zu\" />\r\n<link rel=\"alternate\" href=\"https://ocr.oldfish.cn/ka/Article.aspx?id=81&slug=rnn-sequence-modeling\" hreflang=\"ka\" />\r\n<meta http-equiv=\"Content-Type\" content=\"text/html; charset=UTF-8\" /><meta http-equiv=\"X-UA-Compatible\" content=\"IE=Edge,chrome=1\" /><meta name=\"renderer\" content=\"webkit\" /><meta name=\"viewport\" content=\"width=device-width, initial-scale=1.0, maximum-scale=5.0\" /><link rel=\"icon\" href=\"/favicon.ico\" type=\"image/x-icon\" /><meta name=\"robots\" content=\"all\" /><link rel=\"preconnect\" href=\"https://scm-file-new-1301864755.cos.ap-beijing.myqcloud.com\" /><link rel=\"preconnect\" href=\"https://cdn.oldfish.cn\" /><link rel=\"dns-prefetch\" href=\"https://hm.baidu.com\" /><link rel=\"dns-prefetch\" href=\"https://cdn.oldfish.cn\" /><link rel=\"preload\" href=\"/site/css/site.css\" as=\"style\" />\r\n<script>function toArray(l){var a=[];for(var i=0;i<(l?l.length:0);i++)a.push(l[i]);return a;}function forEach(l,f){for(var i=0,n=(l?l.length:0);i<n;i++)f(l[i],i);}function hasClass(e,c){if(!e||!c)return false;return(' '+e.className+' ').indexOf(' '+c+' ')>-1;}function addClass(e,c){if(!e||!c||hasClass(e,c))return;e.className=(e.className?e.className+' ':'')+c;}function removeClass(e,c){if(!e||!c)return;var u=' '+e.className+' ';while(u.indexOf(' '+c+' ')>-1){u=u.replace(' '+c+' ',' ');}e.className=u.replace(/^\\s+|\\s+$/g,'');}function toggleClass(e,c){if(!e||!c)return;if(hasClass(e,c))removeClass(e,c);else addClass(e,c);}function matchesSelector(e,s){if(!e||e.nodeType!==1)return false;if(e.matches)return e.matches(s);var p=e.parentNode||document;var m=toArray(p.querySelectorAll(s));for(var i=0;i<m.length;i++)if(m[i]===e)return true;return false;}function closest(e,s){var n=e;while(n&&n.nodeType===1){if(matchesSelector(n,s))return n;n=n.parentNode;}return null;}function safeRemove(e){if(e&&e.parentNode)e.parentNode.removeChild(e);}function qa(s,r){return toArray((r||document).querySelectorAll(s));}function q(s,r){return(r||document).querySelector(s);}function xhrGet(u,o,e){try{var x=new XMLHttpRequest();x.open('GET',u,true);x.onreadystatechange=function(){if(x.readyState===4){if(x.status>=200&&x.status<300)o&&o(x.responseText);else e&&e(x);}};x.send(null);}catch(r){e&&e(r);}}function getScrollY(){return window.pageYOffset||document.documentElement.scrollTop||document.body.scrollTop||0;}function getOrigin(){return window.location.protocol+'//'+window.location.host;}</script>\r\n<script>var isWebP=(function(){try{return document.createElement('canvas').toDataURL('image/webp').indexOf('data:image/webp')===0;}catch(e){return false;}})();document.addEventListener('DOMContentLoaded',function(){var c=document.getElementById('user-login-container');if(c){var u=c.getAttribute('data-url');if(u){xhrGet(u,function(h){c.innerHTML=h;forEach(qa('a',c),function(l){l.style.display='inline-block';l.style.marginLeft='10px';});});}}});</script>\r\n    \n    \n\n    <meta name=\"description\" content=\"Dive into the application of RNN, LSTM, GRU in OCR. Detailed analysis of the principles of sequence modeling, solutions to gradient problems, and the benefits of bidirectional RNNs.\" />\n    <meta name=\"keywords\" content=\"RNN, LSTM, GRU, Sequence modeling, Gradient vanishing, Bidirectional RNN, Attention Mechanism, CRNN, OCR, OCR text recognition, image-to-text, OCR technology\" />\n    <meta property=\"og:title\" content=\"【Deep Learning OCR Series·4】Recurrent Neural Networks and Sequence Modeling\" />\n    <meta property=\"og:description\" content=\"Dive into the application of RNN, LSTM, GRU in OCR. Detailed analysis of the principles of sequence modeling, solutions to gradient problems, and the benefits of bidirectional RNNs.\" />\n    <meta property=\"og:type\" content=\"article\" />\n    <meta property=\"og:url\" content=\"https://ocr.oldfish.cn/Article.aspx?id=81&slug=rnn-sequence-modeling&lang=zh-Hans&_translation_source=true\" />\n    <meta property=\"og:site_name\" content=\"OCR text recognition assistant\" />\n    <meta name=\"twitter:card\" content=\"summary\" />\n    <meta name=\"twitter:title\" content=\"【Deep Learning OCR Series·4】Recurrent Neural Networks and Sequence Modeling\" />\n    <meta name=\"twitter:description\" content=\"Dive into the application of RNN, LSTM, GRU in OCR. Detailed analysis of the principles of sequence modeling, solutions to gradient problems, and the benefits of bidirectional RNNs.\" />\n\n    \n    <script type=\"application/ld+json\">\n    {\n        \"@context\": \"https://schema.org\",\n        \"@type\": \"Article\",\n        \"headline\": \"【Deep Learning OCR Series 4] Recurrent Neural Network and Sequence Modeling\",\n        \"description\": \"Dive into the application of RNN, LSTM, GRU in OCR. Detailed analysis of the principles of sequence modeling, solutions to gradient problems, and the benefits of bidirectional RNNs。\",\n        \"publisher\": {\n            \"@type\": \"Organization\",\n            \"name\": \"OCR text recognition assistant team\"\n        },\n        \"datePublished\": \"2025-08-19T06:32:05Z\",\n        \"dateModified\": \"2025-08-19T06:32:05Z\",\n        \"mainEntityOfPage\": {\n            \"@type\": \"WebPage\",\n            \"@id\": \"https://ocr.oldfish.cn/Article.aspx?id=81&slug=rnn-sequence-modeling&lang=zh-Hans&_translation_source=true\"\n        }\n    }\n    </script>\n    \n    <style>\n        .reading-progress { position: fixed; top: 0; left: 0; width: 0%; height: 3px; background: linear-gradient(90deg, #1764ff, #0d47a1); z-index: 9999; transition: width 0.2s ease; box-shadow: 0 1px 3px rgba(23, 100, 255, 0.3); }\n        .home-container.root { overflow: visible !important; }\n        .article-detail-container { background: #f5f7fc; min-height: calc(100vh - 60px); padding: 80px 0 60px; position: relative; overflow: visible; }\n        .article-detail-container .row { margin: 0 -12px; align-items: flex-start !important; min-height: 100vh !important; }\n        .article-detail-container .row > [class*=\"col-\"] { padding: 0 12px; box-sizing: border-box; min-width: 0; }\n        .article-detail-container .row > .col-lg-8, .article-detail-container .row > .col-xl-9 { overflow: hidden; }\n        .article-detail-container .row > .col-lg-4, .article-detail-container .row > .col-xl-3 { overflow: visible !important; height: 100% !important; min-height: 100% !important; position: static !important; display: block !important; transform: none !important; will-change: auto !important; contain: none !important; perspective: none !important; filter: none !important; }\n        .article-detail { background: #fff; border-radius: 8px; padding: 1.5rem; margin-bottom: 2rem; box-shadow: 0 2px 10px rgba(0,0,0,0.1); width: 100%; box-sizing: border-box; }\n        .article-title { font-size: 1.7rem; color: #1a1c1f; line-height: 1.3; margin-bottom: 1.5rem; font-weight: 600; }\n        .article-meta { display: flex; align-items: center; flex-wrap: nowrap; gap: 1.2rem; color: #6c757d; font-size: 0.88rem; margin-bottom: 2rem; padding: 1.2rem 1.4rem; background: linear-gradient(135deg, #f8f9fa 0%, #ffffff 100%); border-radius: 12px; border-left: 4px solid #1764ff; box-shadow: 0 2px 8px rgba(0,0,0,0.06); overflow-x: hidden; transition: all 0.3s ease; }\n        .article-meta:hover { box-shadow: 0 4px 16px rgba(0,0,0,0.1); transform: translateY(-1px); }\n        .article-meta .meta-item { display: flex; align-items: center; gap: 0.25rem; font-weight: 500; color: #495057; white-space: nowrap; flex-shrink: 1; transition: color 0.2s ease; cursor: default; }\n        .article-meta .meta-item:hover { color: #1764ff; }\n        .article-meta .meta-item:hover .meta-icon { transform: scale(1.1); filter: drop-shadow(0 2px 6px rgba(13, 71, 161, 0.25)); }\n        .meta-icon { display: inline-block; font-size: 16px; flex-shrink: 0; vertical-align: middle; transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1); margin-right: 4px; opacity: 0.9; }\n        .meta-item:hover .meta-icon { opacity: 1; }\n        .meta-item:hover { color: #1764ff; }\n        .meta-item { position: relative; }\n        .meta-item::after { content: ''; position: absolute; bottom: -2px; left: 0; width: 0; height: 2px; background: linear-gradient(90deg, #1764ff, #00d4ff); transition: width 0.3s ease; }\n        .meta-item:hover::after { width: 100%; }\n        .article-category, .read-time { display: inline-block; padding: 6px 12px; border-radius: 16px; font-size: 0.8rem; font-weight: 600; }\n        .article-category { background: linear-gradient(135deg, #1764ff, #0d47a1); color: white; text-decoration: none; text-transform: uppercase; letter-spacing: 0.5px; transition: all 0.3s ease; }\n        .article-category:hover { background: linear-gradient(135deg, #0d47a1, #1764ff); color: white; text-decoration: none; transform: translateY(-1px); box-shadow: 0 4px 12px rgba(23, 100, 255, 0.3); }\n        .read-time { background: #e3f2fd; color: #1565c0; border: 1px solid #90caf9; }\n        .article-tags { margin: 20px 0 1.5rem; }\n        .article-tags .tag { display: inline-block; background: #1764ff; color: #fff; padding: 4px 12px; border-radius: 12px; font-size: 0.8rem; margin: 0 8px 4px 0; }\n        .article-summary { background: #e3f2fd; border-left: 4px solid #1764ff; padding: 1rem 1.5rem; margin-bottom: 2rem; border-radius: 0 4px 4px 0; }\n        .article-content { line-height: 1.8; font-size: 16px; color: #333; word-wrap: break-word; }\n        .article-content h1, .article-content h2, .article-content h3, .article-content h4, .article-content h5, .article-content h6 { margin: 2rem 0 1rem; font-weight: 600; line-height: 1.25; color: #1a1c1f; }\n        .article-content h1 { font-size: 2rem; border-bottom: 2px solid #e9ecef; padding-bottom: 0.5rem; }\n        .article-content h2 { font-size: 1.5rem; border-bottom: 1px solid #e9ecef; padding-bottom: 0.3rem; }\n        .article-content h3 { font-size: 1.25rem; }\n        .article-content h4 { font-size: 1.1rem; }\n        .article-content h5, .article-content h6 { font-size: 1rem; }\n        .article-content ul, .article-content ol { margin: 1rem 0; padding-left: 2rem; }\n        .article-content ul ul, .article-content ol ol, .article-content ul ol, .article-content ol ul { margin: 0.5rem 0; }\n        .article-content blockquote { margin: 1rem 0; padding: 0.5rem 1rem; border-left: 4px solid #1764ff; background-color: #f8f9fa; color: #6c757d; font-style: italic; }\n        .article-content pre { background-color: #f8f9fa; border: 1px solid #e9ecef; border-radius: 6px; padding: 1rem; margin: 1rem 0; overflow-x: auto; font-family: 'Courier New', Courier, monospace; font-size: 0.9em; line-height: 1.4; }\n        .article-content pre code { background: none; border: none; padding: 0; color: inherit; }\n        .article-content p { margin: 1rem 0; }\n        .article-content p:first-child { margin-top: 0; }\n        .article-content p:last-child { margin-bottom: 0; }\n        .article-footer { margin-top: 4rem; padding: 2.5rem; background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%); border-radius: 12px; border: 1px solid #e9ecef; position: relative; overflow: hidden; }\n        .article-footer::before { content: ''; position: absolute; top: 0; left: 0; right: 0; height: 3px; background: linear-gradient(90deg, #1764ff, #17a2b8, #28a745); }\n        .article-share h6 { color: #495057; font-weight: 600; margin-bottom: 1.5rem; font-size: 16px; }\n        .share-buttons { display: flex; gap: 12px; flex-wrap: wrap; }\n        .share-buttons .btn { padding: 10px 20px; border-radius: 25px; font-weight: 500; font-size: 14px; border: none; transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1); box-shadow: 0 2px 8px rgba(0,0,0,0.1); display: inline-flex; align-items: center; gap: 8px; text-decoration: none; }\n        .btn-weibo { background: linear-gradient(135deg, #e6162d, #c41230); color: white; }\n        .btn-copy { background: #1764ff; color: white; }\n        .btn-print { background: linear-gradient(135deg, #17a2b8, #138496); color: white; }\n        .btn-weibo:hover { background: linear-gradient(135deg, #c41230, #a00e26); box-shadow: 0 4px 16px rgba(230, 22, 45, 0.3); }\n        .btn-copy:hover { background: #0d47a1; box-shadow: 0 4px 16px rgba(23, 100, 255, 0.3); }\n        .btn-print:hover { background: linear-gradient(135deg, #138496, #117a8b); box-shadow: 0 4px 16px rgba(23, 162, 184, 0.3); }\n        .btn-weibo:hover, .btn-copy:hover, .btn-print:hover { transform: translateY(-2px); color: white; }\n        .article-actions { display: flex; justify-content: flex-end; align-items: center; }\n        .related-articles { margin-top: 3rem; }\n        .related-article-item { background: #fff; border-radius: 8px; padding: 1.5rem; margin-bottom: 1rem; box-shadow: 0 2px 8px rgba(0,0,0,0.1); transition: transform 0.3s ease; }\n        .related-article-item:hover { transform: translateY(-2px); }\n        .sidebar-card { background: linear-gradient(135deg, rgba(255,255,255,0.9) 0%, rgba(255,255,255,0.8) 100%); backdrop-filter: blur(20px); border: 1px solid rgba(255,255,255,0.3); border-radius: 16px; padding: 1rem; margin-bottom: 0.5rem; box-shadow: 0 4px 20px rgba(0,0,0,0.08), inset 0 1px 0 rgba(255,255,255,0.2); overflow: visible; box-sizing: border-box; width: 100%; z-index: 10; }\n        .sidebar-card.fixed { box-shadow: 0 8px 32px rgba(0,0,0,0.12); }\n        .sidebar-card::before { content: ''; position: absolute; top: 0; left: 0; right: 0; height: 3px; background: linear-gradient(90deg, #00d4ff, #1764ff, #9c27b0); border-radius: 20px 20px 0 0; }\n        .sidebar-card h5 { color: #1a202c; padding-bottom: 0.8rem; border-bottom: 2px solid rgba(23, 100, 255, 0.2); font-weight: 700; font-size: 1.1rem; }\n        .sidebar { min-height: 100vh; position: relative !important; display: block !important; flex-direction: column !important; gap: 1rem; }\n        .toc-card { flex: 0 1 auto; max-height: 60vh; overflow-y: auto; }\n        .sidebar-card:not(.toc-card) { flex: 1 1 auto; overflow: visible; }\n        .recommended-item { padding: 0.75rem 0; border-bottom: 1px solid rgba(0,0,0,0.05); }\n        .recommended-item:last-child { border-bottom: none; }\n        .recommended-item h6 { margin: 0 0 0.5rem 0; font-size: 0.9rem; line-height: 1.4; }\n        .recommended-item h6 a { color: #1a1c1f; text-decoration: none; transition: color 0.3s ease; }\n        .recommended-item h6 a:hover { color: #1764ff; }\n        .recommended-item .text-muted { font-size: 0.75rem; margin: 0; }\n        .toc-list { list-style: none; padding: 0; margin: 0; max-height: none; overflow-y: visible; overflow-x: hidden; }\n        .toc-list::-webkit-scrollbar { width: 4px; }\n        .toc-list::-webkit-scrollbar-track { background: rgba(0,0,0,0.1); border-radius: 2px; }\n        .toc-list::-webkit-scrollbar-thumb { background: linear-gradient(135deg, #00d4ff, #1764ff); border-radius: 2px; }\n        .toc-list::-webkit-scrollbar-thumb:hover { background: linear-gradient(135deg, #1764ff, #0d47a1); }\n        .toc-list a { color: #4a5568; text-decoration: none; font-size: 13px; font-weight: 500; display: block; padding: 8px 12px; border-radius: 8px; transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1); position: relative; margin-bottom: 4px; border: 1px solid transparent; }\n        .toc-list a::before { content: ''; position: absolute; left: 0; top: 0; bottom: 0; width: 3px; background: linear-gradient(135deg, #00d4ff, #1764ff); border-radius: 0 12px 12px 0; transform: scaleY(0); transition: transform 0.3s ease; }\n        .toc-list a:hover { color: #1764ff; background: linear-gradient(135deg, rgba(0, 212, 255, 0.1), rgba(23, 100, 255, 0.1)); border-color: rgba(23, 100, 255, 0.2); transform: translateX(8px); box-shadow: 0 4px 12px rgba(0, 212, 255, 0.2); }\n        .toc-list a:hover::before, .toc-list a.active::before { transform: scaleY(1); }\n        .toc-list a.active { color: #1764ff; background: linear-gradient(135deg, rgba(0, 212, 255, 0.15), rgba(23, 100, 255, 0.15)); font-weight: 700; border-color: rgba(23, 100, 255, 0.3); transform: translateX(4px); box-shadow: 0 6px 16px rgba(0, 212, 255, 0.25); }\n        .toc-number { color: #1764ff; font-weight: 600; margin-right: 0.5rem; font-size: 0.9em; }\n        .toc-text { flex: 1; }\n        .toc-level-1 { font-weight: 700; font-size: 15px; }\n        .toc-level-2 { font-size: 14px; }\n        .toc-level-3 { font-size: 13px; opacity: 0.9; }\n        .toc-level-4, .toc-level-5, .toc-level-6 { font-size: 12px; opacity: 0.8; }\n        .article-content .formatted-list { margin: 1.5rem 0; padding-left: 0; list-style: none; }\n        .article-content .formatted-list.ordered { counter-reset: list-counter; }\n        .article-content .formatted-list.ordered li { counter-increment: list-counter; position: relative; margin: 1rem 0; padding: 1rem 1rem 1rem 3rem; background: linear-gradient(135deg, rgba(23, 100, 255, 0.03), rgba(0, 212, 255, 0.03)); border-left: 4px solid #1764ff; border-radius: 0 8px 8px 0; transition: all 0.3s ease; }\n        .article-content .formatted-list.ordered li::before { content: counter(list-counter); position: absolute; left: 0.8rem; top: 1rem; background: linear-gradient(135deg, #1764ff, #00d4ff); color: white; width: 1.5rem; height: 1.5rem; border-radius: 50%; display: flex; align-items: center; justify-content: center; font-weight: 600; font-size: 0.9rem; }\n        .article-content .formatted-list.ordered li:hover { background: linear-gradient(135deg, rgba(23, 100, 255, 0.06), rgba(0, 212, 255, 0.06)); transform: translateX(4px); box-shadow: 0 2px 8px rgba(23, 100, 255, 0.15); }\n        .article-content .formatted-list.unordered li { position: relative; margin: 0.8rem 0; padding: 0.8rem 1rem 0.8rem 2.5rem; background: linear-gradient(135deg, rgba(40, 167, 69, 0.03), rgba(25, 135, 84, 0.03)); border-left: 4px solid #28a745; border-radius: 0 6px 6px 0; transition: all 0.3s ease; }\n        .article-content .formatted-list.unordered li::before { content: \"•\"; position: absolute; left: 1rem; top: 0.8rem; color: #28a745; font-size: 1.2rem; font-weight: bold; }\n        .article-content .formatted-list.unordered li:hover { background: linear-gradient(135deg, rgba(40, 167, 69, 0.06), rgba(25, 135, 84, 0.06)); transform: translateX(4px); box-shadow: 0 2px 8px rgba(40, 167, 69, 0.15); }\n        .article-content .list-title { color: #1764ff; font-weight: 600; display: inline-block; margin-bottom: 0.3rem; }\n        .article-content .list-content { color: #2c3e50; line-height: 1.6; display: block; }\n        .article-content .inline-code { background: #f8f9fa; border: 1px solid #e9ecef; border-radius: 4px; padding: 2px 6px; font-family: 'Consolas', 'Monaco', 'Courier New', monospace; font-size: 0.9em; color: #e83e8c; }\n        .article-content .label-item { margin: 0.8rem 0; }\n        .article-content .label { color: #1764ff; font-weight: 600; }\n        .article-content .section-label { color: #2c3e50; font-weight: 600; margin: 1rem 0 0.5rem 0; font-size: 1.1em; border-left: 4px solid #1764ff; padding-left: 0.8rem; }\n        .article-content p, .article-content .content-paragraph { margin: 1.2rem 0; line-height: 1.8; text-align: justify; color: #2c3e50; font-size: 1rem; }\n        .article-content .content-paragraph { text-indent: 0; margin: 1rem 0; }\n        .article-content .indent-paragraph { text-indent: 2em; margin: 1.2rem 0; }\n        .article-content .text-indent-1 { text-indent: 2em; display: block; }\n        .article-content .text-indent-2 { text-indent: 4em; display: block; }\n        .article-content .text-indent-3 { text-indent: 6em; display: block; }\n        .article-content .network-config { background: linear-gradient(135deg, #f8f9fa, #e9ecef); border: 1px solid #dee2e6; border-radius: 6px; padding: 0.8rem 1rem; margin: 0.5rem 0; font-family: 'Consolas', 'Monaco', 'Courier New', monospace; font-size: 0.9rem; box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05); }\n        .article-content .config-label { color: #1764ff; font-weight: 600; }\n        .article-content .config-desc { color: #495057; }\n        .article-content .algorithm-step { background: linear-gradient(135deg, rgba(23, 100, 255, 0.05), rgba(0, 212, 255, 0.05)); border-left: 4px solid #1764ff; padding: 0.8rem 1rem; margin: 0.5rem 0; border-radius: 0 6px 6px 0; transition: all 0.3s ease; }\n        .article-content .step-number { color: #1764ff; font-weight: 700; margin-right: 0.5rem; }\n        .article-content .step-desc { color: #2c3e50; line-height: 1.5; }\n        .article-content table { width: 100%; border-collapse: collapse; margin: 1.5rem 0; background: white; border-radius: 8px; overflow: hidden; box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1); }\n        .article-content table th { background: linear-gradient(135deg, #1764ff, #00d4ff); color: white; padding: 1rem; text-align: left; font-weight: 600; }\n        .article-content table td { padding: 0.8rem 1rem; border-bottom: 1px solid #e9ecef; }\n        .article-content table tr:hover { background: rgba(23, 100, 255, 0.05); }\n        .article-content strong { color: #1764ff; font-weight: 600; }\n        .article-content em { color: #6c757d; font-style: italic; }\n        .article-content a { color: #1764ff; text-decoration: none; border-bottom: 1px solid transparent; transition: all 0.3s ease; }\n        .article-content a:hover { border-bottom-color: #1764ff; color: #0056b3; }\n        .article-content .definition-item { background: linear-gradient(135deg, rgba(40, 167, 69, 0.05), rgba(25, 135, 84, 0.05)); border-left: 4px solid #28a745; padding: 1rem; margin: 1rem 0; border-radius: 0 8px 8px 0; }\n        .article-content .definition-label { color: #28a745; font-weight: 600; display: block; margin-bottom: 0.5rem; }\n        .article-content .definition-content { color: #2c3e50; line-height: 1.6; }\n        .article-content .note-item { background: linear-gradient(135deg, rgba(255, 193, 7, 0.1), rgba(255, 193, 7, 0.05)); border: 1px solid rgba(255, 193, 7, 0.3); border-radius: 8px; padding: 1rem; margin: 1rem 0; position: relative; }\n        .article-content .note-item::before { content: \"💡\"; position: absolute; left: 1rem; top: 1rem; font-size: 1.2em; }\n        .article-content .note-label { color: #856404; font-weight: 600; margin-left: 2rem; }\n        .article-content .note-content { color: #2c3e50; line-height: 1.6; margin-left: 2rem; display: block; margin-top: 0.3rem; }\n        .article-content .math-formula { background: #f8f9fa; border: 1px solid #e9ecef; border-radius: 6px; padding: 1rem; margin: 1rem 0; text-align: center; font-family: 'Times New Roman', serif; font-size: 1.1em; color: #2c3e50; }\n        @media (min-width: 1400px) { .article-content { font-size: 17px; line-height: 1.8; } .article-detail { padding: 2.5rem; } }\n        @media (max-width: 1199px) and (min-width: 992px) { .article-detail { padding: 1.5rem; } .sidebar-card { padding: 1rem; } .toc-list a { font-size: 12px; padding: 6px 8px; } }\n        @media (max-width: 768px) {\n            .article-detail-container { padding: 60px 15px; }\n            .article-content .network-config, .article-content .algorithm-step, .article-content .definition-item, .article-content .note-item { margin: 0.5rem -1rem; border-radius: 0; }\n            .article-content .note-item::before { left: 0.5rem; }\n            .article-content .note-label, .article-content .note-content { margin-left: 1.5rem; }\n            .article-detail { padding: 1.5rem; }\n            .article-title { font-size: 1.6rem; line-height: 1.3; }\n            .article-meta { flex-wrap: wrap; gap: 1rem; padding: 1rem; font-size: 0.8rem; }\n            .article-meta .meta-item { flex-shrink: 1; }\n            .meta-icon { font-size: 14px; margin-right: 3px; }\n            .article-content { font-size: 15px; }\n            .article-content h1 { font-size: 1.6rem; }\n            .article-content h2 { font-size: 1.4rem; }\n            .article-content h3 { font-size: 1.2rem; }\n            .article-content h4 { font-size: 1.1rem; }\n            .article-content pre { padding: 0.75rem; font-size: 0.85em; overflow-x: auto; }\n            .article-content code { font-size: 0.85em; padding: 1px 4px; }\n            .sidebar { margin-top: 2rem; }\n            .sidebar-card { position: static; }\n            .toc-card { max-height: none; overflow-y: visible; }\n            .share-buttons { flex-direction: column; gap: 0.5rem; }\n            .share-buttons .btn { width: 100%; justify-content: center; }\n        }\n        @media print { body * { visibility: hidden; } .article-detail, .article-detail * { visibility: visible; } .article-detail { position: absolute; left: 0; top: 0; width: 100%; background: white; box-shadow: none; border: none; margin: 0; padding: 20px; } .article-meta { border: none; background: none; padding: 0; margin-bottom: 20px; } .article-footer, .reading-progress, .back-to-top { display: none !important; } .article-content { font-size: 12pt; line-height: 1.6; } .article-content h1, .article-content h2, .article-content h3, .article-content h4, .article-content h5, .article-content h6 { page-break-after: avoid; margin-top: 20pt; margin-bottom: 10pt; } .article-content pre, .article-content blockquote { page-break-inside: avoid; } }\n    </style>\n\n<script type=\"application/ld+json\">{\"@context\":\"https://schema.org\",\"@type\":\"BreadcrumbList\",\"itemListElement\":[{\"@type\":\"ListItem\",\"position\":1,\"name\":\"Inzu\",\"item\":\"https://ocr.oldfish.cn/default.aspx\"},{\"@type\":\"ListItem\",\"position\":2,\"name\":\"Ingingo za tekiniki\",\"item\":\"https://ocr.oldfish.cn/Articles.aspx\"},{\"@type\":\"ListItem\",\"position\":3,\"name\":\"Article details\",\"item\":\"https://ocr.oldfish.cn/Article.aspx?id=81&slug=rnn-sequence-modeling&lang=zh-Hans&_translation_source=true\"}]}</script>\n<link rel=\"stylesheet\" href=\"/site/css/site.css\" type=\"text/css\" /><title>【Deep Learning OCR Series·4】Recurrent Neural Networks and Sequence Modeling</title><meta http-equiv=\"Content-Language\" content=\"rw\" />\n</head>\r\n<body>\r\n    <div class=\"home-container root\">\r\n        <header class=\"v4_header_pc\">\r\n            <div class=\"header_pc_left\">\r\n                <a href=\"Default.aspx\" title=\"Urugo | AI intelligent text recognition\" class=\"pc-logo-wrap ml-4 mr-5\">\r\n                    <picture>\r\n                        <source srcset=\"site/image/logo-s.png.webp\" type=\"image/webp\" />\r\n                        <img src=\"site/image/logo-s.png\" class=\"pc-logo\" style=\"height: 100%\" alt=\"OCR Text Recognition Assistant Official Website Logo - AI Intelligent Text Recognition Platform\" />\r\n                    </picture>\r\n                    <span style=\"font-size: 18px; font-weight: bold; color: #333;\">OCR text recognition assistant</span>\r\n                </a>\r\n                <ul class=\"top-nav\" role=\"navigation\" aria-label=\"Main navigation\">\r\n                    <li><a class=\"color-default\" href=\"Default.aspx\" title=\"OCR Text Recognition Assistant homepage\">Inzu</a></li>\r\n                    <li><a class=\"color-default\" data-sub=\"product-sub-nav\" href=\"Detail.aspx\" title=\"OCR product function introduction\">Ibicuruzwa biranga ibicuruzwa:</a><div class=\"triangle\"></div>\r\n                    </li>\r\n                    <li><a class=\"color-default\" data-sub=\"experience-sub-nav\" href=\"Ocr.aspx\" title=\"Experience OCR features online\">Ubunararibonye bwo kuri interineti</a><div class=\"triangle\"></div>\r\n                    </li>\r\n                    <li><a class=\"color-default\" data-sub=\"vip-sub-nav\" href=\"Version.aspx\" title=\"OCR membership upgrade service\">Kuvugurura abanyamuryango</a><div class=\"triangle\"></div>\r\n                    </li>\r\n                    <li><a class=\"color-default\" href=\"Download.aspx\" title=\"Download OCR Text Recognition Assistant for free\">Free download</a></li>\r\n                    <li><a class=\"color-default\" data-sub=\"articles-sub-nav\" href=\"Articles.aspx\" title=\"OCR technical articles and knowledge sharing\">Guhanahana amakuru</a><div class=\"triangle\"></div>\r\n                    </li>\r\n                    <li><a class=\"color-default\" data-sub=\"support-sub-nav\" href=\"FAQ.aspx\" title=\"OCR use support and technical support\">Ikigo cy'Ubufasha</a><div class=\"triangle\"></div>\r\n                    </li>\r\n                </ul>\r\n                <div class=\"product-con\">\r\n                    <section id=\"product-sub-nav\" class=\"pls-nav-dropdown\" style=\"visibility: hidden; height: 0px; display: none;\">\r\n                        <div class=\"d-flex nav-drown-con\" style=\"height: 0px; transition: transform 0.2s ease-in-out 0s,opacity 0.2s ease-in-out 0s; opacity: 0; transform: translate(0px, -100%);\">\r\n                            <div class=\"prdocu-sub-left\">\r\n                                <div class=\"mr-auto\">\r\n                                    <picture>\r\n                                        <source class=\"p-icon\" srcset=\"site/image/icon/p_1.png.webp\" type=\"image/webp\" alt=\"\" aria-hidden=\"true\" />\r\n                                        <img class=\"p-icon\" src=\"site/image/icon/p_1.png\" alt=\"OCR product function icon\" aria-hidden=\"true\" loading=\"lazy\" />\r\n                                    </picture>\r\n                                    <div class=\"mt-4\">\r\n                                        <h3 class=\"h6\">OCR text recognition assistant</h3>\r\n                                        <p class=\"color-gray fn14 mt-3 sub-left-desc\">Kongera umusaruro, kugabanya ibiciro no gutanga umusaruro</p>\r\n                                        <p class=\"color-gray fn14 mt-3 sub-left-desc\">&nbsp;</p>\r\n                                        <p class=\"color-gray fn14 mt-3 sub-left-desc\">Kugenzura byihuse, gutunganya byihuse, hamwe no gutanga umusaruro mwiza.</p>\r\n                                        <p class=\"color-gray fn14 mt-3 sub-left-desc\">Kuva ku mwandiko kugeza ku meza, kuva ku majambo kugeza ku mashusho</p>\r\n                                        <p class=\"color-gray fn14 mt-3 sub-left-desc\">Make every word processing so easy</p>\r\n                                        <a class=\"link block disa fn14 contact-business shake\" href=\"Detail.aspx\">Menya byinshi ku bice by'umubiri<span class=\"iconfont4 icon-xiangzuo gt\"></span></a>\r\n                                    </div>\r\n                                </div>\r\n                            </div>\r\n                            <div class=\"bg-white prdocu-sub-right\">\r\n                                <div>\r\n                                    <h2 class=\"text-base-color h6\">Ibicuruzwa biranga ibicuruzwa:</h2>\r\n                                    <div class=\"mt-4 d-flex flex-wrap\">\r\n                                        <a href=\"Detail.aspx\" class=\"pro-item color-default\" title=\"Sobanukirwa n'imikorere y'inzego z'ibanze z'Ikigo cy'Igihugu cy'Imisoro n'Amahoro\">\r\n                                            <div class=\"letter-wrap\">\r\n                                                <h3 class=\"h6\">Ibintu by'ibanze:</h3>\r\n                                                <span class=\"color-gray fn14\">Menya byinshi kubyerekeye ibintu by'ingenzi n'inyungu za tekiniki za OCR Assistant, hamwe na 98%+ kumenyekana</span>\r\n                                            </div>\r\n                                        </a>\r\n                                        <a href=\"Version.aspx\" class=\"pro-item color-default\" title=\"Gereranya itandukaniro riri hagati y'amahugurwa y'abahanzi b'ibyamamare\">\r\n                                            <div class=\"letter-wrap\">\r\n                                                <h3 class=\"h6\">Version comparison</h3>\r\n                                                <span class=\"color-gray fn14\">Gereranya itandukaniro ryimikorere ya verisiyo yubusa, verisiyo yumuntu ku giti cye, verisiyo yububiko, na verisiyo ya nyuma mu buryo burambuye</span>\r\n                                            </div>\r\n                                        </a>\r\n                                        <a href=\"FAQ.aspx\" class=\"pro-item color-default\" title=\"Check out the OCR Assistant FAQ\">\r\n                                            <div class=\"letter-wrap\">\r\n                                                <h3 class=\"h6\">Q & A</h3>\r\n                                                <span class=\"color-gray fn14\">Menya byihuse imiterere yibicuruzwa, uburyo bwo gukoresha, hamwe n'ibisubizo birambuye byibibazo bikunze kubazwa</span>\r\n                                            </div>\r\n                                        </a>\r\n                                        <a href=\"Download.aspx\" class=\"pro-item color-default\" title=\"Download OCR Text Recognition Assistant for free\">\r\n                                            <div class=\"letter-wrap\">\r\n                                                <h3 class=\"h6\">Gerageza kubikora ku buntu</h3>\r\n                                                <span class=\"color-gray fn14\">Download and install OCR Assistant now to experience the powerful text recognition function for free</span>\r\n                                            </div>\r\n                                        </a>\r\n                                        <div style=\"width: 100%; margin: 15px 0;\">\r\n                                            <h4 class=\"h6\" style=\"color: #007cfa; margin-bottom: 10px;\">Online OCR recognition</h4>\r\n                                        </div>\r\n                                        <a href=\"Ocr.aspx?type=text_recognize\" class=\"pro-item color-default\" title=\"Experience universal text recognition online\">\r\n                                            <div class=\"letter-wrap\">\r\n                                                <h3 class=\"h6\">Universal Character Recognition</h3>\r\n                                                <span class=\"color-gray fn14\">Intelligent extraction of multilingual high-precision text, supporting printed and multi-scene complex image recognition</span>\r\n                                            </div>\r\n                                        </a>\r\n                                        <a href=\"Ocr.aspx?type=table\" class=\"pro-item color-default\">\r\n                                            <div class=\"letter-wrap\">\r\n                                                <h3 class=\"h6\">Universal Table Identification</h3>\r\n                                                <span class=\"color-gray fn14\">Intelligent conversion of table images to Excel files, automatic processing of complex table structures and merged cells</span>\r\n                                            </div>\r\n                                        </a>\r\n                                        <a href=\"Ocr.aspx?type=handwritten_ocr\" class=\"pro-item color-default\">\r\n                                            <div class=\"letter-wrap\">\r\n                                                <h3 class=\"h6\">Handwriting recognition</h3>\r\n                                                <span class=\"color-gray fn14\">Intelligent recognition of Chinese and English handwritten content, support classroom notes, medical records and other scenarios</span>\r\n                                            </div>\r\n                                        </a>\r\n                                        <a href=\"Ocr.aspx?type=pdf2word\" class=\"pro-item color-default\">\r\n                                            <div class=\"letter-wrap\">\r\n                                                <h3 class=\"h6\">PDF to Word</h3>\r\n                                                <span class=\"color-gray fn14\">Inyandiko za PDF zihita zihindurwa muburyo bwa Word, zibungabunga neza imiterere yumwimerere hamwe nimiterere yamashusho</span>\r\n                                            </div>\r\n                                        </a>\r\n                                    </div>\r\n                                </div>\r\n                            </div>\r\n                        </div>\r\n                    </section>\r\n                    <section id=\"experience-sub-nav\" class=\"pls-nav-dropdown solution-part\" style=\"visibility: hidden; height: 0px; display: none;\">\r\n                        <div class=\"d-flex nav-drown-con\" style=\"height: 0px; transition: transform 0.2s ease-in-out 0s,opacity 0.2s ease-in-out 0s; opacity: 0; transform: translate(0px, -100%);\">\r\n                            <div class=\"prdocu-sub-left\">\r\n                                <div class=\"mr-auto\">\r\n                                    <picture>\r\n                                        <source srcset=\"site/image/icon/p_4.png.webp\" type=\"image/webp\" />\r\n                                        <img class=\"p-icon\" src=\"site/image/icon/p_4.png\" alt=\"Online OCR Experience Center icon\" aria-hidden=\"true\" loading=\"lazy\" />\r\n                                    </picture>\r\n                                    <div class=\"mt-4\">\r\n                                        <h3 class=\"h6\">OCR text recognition assistant</h3>\r\n                                        <p class=\"color-gray fn14 mt-3 sub-left-desc\">Text, tables, formulas, documents, translations</p>\r\n                                        <p class=\"color-gray fn14 mt-3 sub-left-desc\">&nbsp;</p>\r\n                                        <p class=\"color-gray fn14 mt-3 sub-left-desc\">Kuzuza ibyo ukeneye byose mu byiciro bitatu</p>\r\n                                        <p class=\"color-gray fn14 mt-3 sub-left-desc\">Screenshot → Identify → apps</p>\r\n                                        <p class=\"color-gray fn14 mt-3 sub-left-desc\">Kongera umusaruro w'akazi ku kigero cya 300%</p>\r\n                                        <a class=\"link block disa fn14 contact-business shake\" href=\"Ocr.aspx\">Gerageza ubu<span class=\"iconfont4 icon-xiangzuo gt\"></span></a>\r\n                                    </div>\r\n                                </div>\r\n                            </div>\r\n                            <div class=\"bg-white prdocu-sub-right\">\r\n                                <div>\r\n                                    <div class=\"mt-4 d-flex flex-wrap\">\r\n                                        <div style=\"width: 100%; margin-bottom: 15px;\">\r\n                                            <h4 class=\"h6\" style=\"color: #007cfa; margin-bottom: 10px;\">OCR function experience</h4>\r\n                                        </div>\r\n                                        <a href=\"Ocr.aspx?type=index\" class=\"pro-item color-default\">\r\n                                            <div class=\"letter-wrap\">\r\n                                                <h3 class=\"h6\">Imikorere yuzuye</h3>\r\n                                                <span class=\"color-gray fn14\">Menya ibintu byose bya OCR smart ahantu hamwe kugirango ubone byihuse igisubizo cyiza kubyo ukeneye</span>\r\n                                            </div>\r\n                                        </a>\r\n                                        <a href=\"Ocr.aspx?type=text_recognize\" class=\"pro-item color-default\">\r\n                                            <div class=\"letter-wrap\">\r\n                                                <h3 class=\"h6\">Universal Character Recognition</h3>\r\n                                                <span class=\"color-gray fn14\">Intelligent extraction of multilingual high-precision text, supporting printed and multi-scene complex image recognition</span>\r\n                                            </div>\r\n                                        </a>\r\n                                        <a href=\"Ocr.aspx?type=table\" class=\"pro-item color-default\">\r\n                                            <div class=\"letter-wrap\">\r\n                                                <h3 class=\"h6\">Universal Table Identification</h3>\r\n                                                <span class=\"color-gray fn14\">Intelligent conversion of table images to Excel files, automatic processing of complex table structures and merged cells</span>\r\n                                            </div>\r\n                                        </a>\r\n                                        <a href=\"Ocr.aspx?type=handwritten_ocr\" class=\"pro-item color-default\">\r\n                                            <div class=\"letter-wrap\">\r\n                                                <h3 class=\"h6\">Handwriting recognition</h3>\r\n                                                <span class=\"color-gray fn14\">Intelligent recognition of Chinese and English handwritten content, support classroom notes, medical records and other scenarios</span>\r\n                                            </div>\r\n                                        </a>\r\n                                        <a href=\"Ocr.aspx?type=pdf2word\" class=\"pro-item color-default\">\r\n                                            <div class=\"letter-wrap\">\r\n                                                <h3 class=\"h6\">PDF to Word</h3>\r\n                                                <span class=\"color-gray fn14\">Inyandiko za PDF zihita zihindurwa muburyo bwa Word, zibungabunga neza imiterere yumwimerere hamwe nimiterere yamashusho</span>\r\n                                            </div>\r\n                                        </a>\r\n                                        <a href=\"Ocr.aspx?type=pdf2markdown\" class=\"pro-item color-default\">\r\n                                            <div class=\"letter-wrap\">\r\n                                                <h3 class=\"h6\">PDF to Markdown</h3>\r\n                                                <span class=\"color-gray fn14\">Inyandiko za PDF zihindurwa mu buryo bw'ubwenge muburyo bwa MD, kandi ibice bya kode n'imiterere y'inyandiko bitunganyijwe mu buryo bwikora kugira ngo bikorwe</span>\r\n                                            </div>\r\n                                        </a>\r\n                                        <div style=\"width: 100%; margin: 15px 0;\">\r\n                                            <h4 class=\"h6\" style=\"color: #007cfa; margin-bottom: 10px;\">Ibikoresho byo gutunganya inyandiko</h4>\r\n                                        </div>\r\n                                        <a href=\"Ocr.aspx?type=word2pdf\" class=\"pro-item color-default\">\r\n                                            <div class=\"letter-wrap\">\r\n                                                <h3 class=\"h6\">Word to PDF</h3>\r\n                                                <span class=\"color-gray fn14\">Inyandiko za Word zihindurwa muri PDF ukanda rimwe, zigumana neza imiterere yumwimerere, zikwiriye kubika no gusangira inyandiko zemewe</span>\r\n                                            </div>\r\n                                        </a>\r\n                                        <a href=\"Ocr.aspx?type=word2jpg\" class=\"pro-item color-default\">\r\n                                            <div class=\"letter-wrap\">\r\n                                                <h3 class=\"h6\">Ijambo ku ifoto</h3>\r\n                                                <span class=\"color-gray fn14\">Word document intelligent conversion to JPG image, support multi-page processing, easy to share on social media</span>\r\n                                            </div>\r\n                                        </a>\r\n                                        <a href=\"Ocr.aspx?type=pdf2jpg\" class=\"pro-item color-default\">\r\n                                            <div class=\"letter-wrap\">\r\n                                                <h3 class=\"h6\">PDF to image</h3>\r\n                                                <span class=\"color-gray fn14\">Convert PDF documents to JPG images in high definition, support batch processing and custom resolution</span>\r\n                                            </div>\r\n                                        </a>\r\n                                        <a href=\"Ocr.aspx?type=image2pdf\" class=\"pro-item color-default\">\r\n                                            <div class=\"letter-wrap\">\r\n                                                <h3 class=\"h6\">Image to PDF</h3>\r\n                                                <span class=\"color-gray fn14\">Merge multiple images into PDF documents, support sorting and page setup</span>\r\n                                            </div>\r\n                                        </a>\r\n                                        <div style=\"width: 100%; margin: 15px 0;\">\r\n                                            <h4 class=\"h6\" style=\"color: #007cfa; margin-bottom: 10px;\">Ibikoresho by'abaterankunga</h4>\r\n                                        </div>\r\n                                        <a href=\"Tool.aspx?type=Json\" class=\"pro-item color-default\">\r\n                                            <div class=\"letter-wrap\">\r\n                                                <h3 class=\"h6\">Imiterere ya JSON</h3>\r\n                                                <span class=\"color-gray fn14\">Smartly beautify the JSON code structure, support compression and expansion, and facilitate development and debugging</span>\r\n                                            </div>\r\n                                        </a>\r\n                                        <a href=\"Tool.aspx?type=Regex\" class=\"pro-item color-default\">\r\n                                            <div class=\"letter-wrap\">\r\n                                                <h3 class=\"h6\">Regular expression</h3>\r\n                                                <span class=\"color-gray fn14\">Kugenzura imikorere yububiko bwibicuruzwa mugihe nyacyo, hamwe nububiko bwibicuruzwa bisanzwe</span>\r\n                                            </div>\r\n                                        </a>\r\n                                        <a href=\"Tool.aspx?type=Encode\" class=\"pro-item color-default\">\r\n                                            <div class=\"letter-wrap\">\r\n                                                <h3 class=\"h6\">Text encoding conversion</h3>\r\n                                                <span class=\"color-gray fn14\">Ifasha guhindura imiterere myinshi ya encoding nka Base64, URL, na Unicode</span>\r\n                                            </div>\r\n                                        </a>\r\n                                        <a href=\"Tool.aspx?type=Diff\" class=\"pro-item color-default\">\r\n                                            <div class=\"letter-wrap\">\r\n                                                <h3 class=\"h6\">Guhuza inyandiko no guhuza</h3>\r\n                                                <span class=\"color-gray fn14\">Highlight text differences and support line-by-line comparison and intelligent merging</span>\r\n                                            </div>\r\n                                        </a>\r\n                                        <a href=\"Tool.aspx?type=Color\" class=\"pro-item color-default\">\r\n                                            <div class=\"letter-wrap\">\r\n                                                <h3 class=\"h6\">Igikoresho cy'amabara</h3>\r\n                                                <span class=\"color-gray fn14\">RGB / HEX color conversion, online color picker, a must-have tool for front-end development</span>\r\n                                            </div>\r\n                                        </a>\r\n                                        <a href=\"Tool.aspx?type=Count\" class=\"pro-item color-default\">\r\n                                            <div class=\"letter-wrap\">\r\n                                                <h3 class=\"h6\">Umubare w'amagambo</h3>\r\n                                                <span class=\"color-gray fn14\">Kubara neza inyuguti, imvugo, na paragarafu, no kunoza imiterere y'inyandiko mu buryo bwikora</span>\r\n                                            </div>\r\n                                        </a>\r\n                                        <a href=\"Tool.aspx?type=Timespan\" class=\"pro-item color-default\">\r\n                                            <div class=\"letter-wrap\">\r\n                                                <h3 class=\"h6\">Timestamp conversion</h3>\r\n                                                <span class=\"color-gray fn14\">Time is converted to and from Unix timestamps, and multiple formats and time zone settings are supported</span>\r\n                                            </div>\r\n                                        </a>\r\n                                        <a href=\"Tool.aspx?type=Calc\" class=\"pro-item color-default\">\r\n                                            <div class=\"letter-wrap\">\r\n                                                <h3 class=\"h6\">Igikoresho cya Calculator</h3>\r\n                                                <span class=\"color-gray fn14\">Online scientific calculator with support for basic operations and advanced mathematical function calculations</span>\r\n                                            </div>\r\n                                        </a>\r\n                                    </div>\r\n                                </div>\r\n                            </div>\r\n                        </div>\r\n                    </section>\r\n                    <section id=\"articles-sub-nav\" class=\"pls-nav-dropdown solution-part\" style=\"visibility: hidden; height: 0px; display: none;\">\r\n                        <div class=\"d-flex nav-drown-con\" style=\"height: 0px; transition: transform 0.2s ease-in-out 0s,opacity 0.2s ease-in-out 0s; opacity: 0; transform: translate(0px, -100%);\">\r\n                            <div class=\"prdocu-sub-left\">\r\n                                <div class=\"mr-auto\">\r\n                                    <picture>\r\n                                        <source srcset=\"site/image/icon/p_4.png.webp\" type=\"image/webp\" />\r\n                                        <img class=\"p-icon\" src=\"site/image/icon/p_4.png\" alt=\"Tech Sharing Center icon\" aria-hidden=\"true\" loading=\"lazy\" />\r\n                                    </picture>\r\n                                    <div class=\"mt-4\">\r\n                                        <h3 class=\"h6\">OCR technology sharing</h3>\r\n                                        <p class=\"color-gray fn14 mt-3 sub-left-desc\">Technical tutorials, application cases, tool recommendations</p>\r\n                                        <p class=\"color-gray fn14 mt-3 sub-left-desc\">&nbsp;</p>\r\n                                        <p class=\"color-gray fn14 mt-3 sub-left-desc\">A complete learning path from beginner to mastery</p>\r\n                                        <p class=\"color-gray fn14 mt-3 sub-left-desc\">Practical Cases → Technical Analysis → Tool Applications</p>\r\n                                        <p class=\"color-gray fn14 mt-3 sub-left-desc\">Empower your path to OCR technology improvement</p>\r\n                                        <a class=\"link block disa fn14 contact-business shake\" href=\"Articles.aspx\">Browse articles<span class=\"iconfont4 icon-xiangzuo gt\"></span></a>\r\n                                    </div>\r\n                                </div>\r\n                            </div>\r\n                            <div class=\"bg-white prdocu-sub-right\">\r\n                                <div>\r\n                                    <h2 class=\"text-base-color h6\">Guhanahana amakuru</h2>\r\n                                    <div class=\"mt-4 d-flex flex-wrap\">\r\n                                        <a href=\"Articles.aspx\" class=\"pro-item color-default\" title=\"View all OCR technical articles\">\r\n                                            <div class=\"letter-wrap\">\r\n                                                <h3 class=\"h6\">Ingingo zose</h3>\r\n                                                <span class=\"color-gray fn14\">Browse all OCR technical articles covering a complete body of knowledge from basic to advanced</span>\r\n                                            </div>\r\n                                        </a>\r\n                                        \r\n                                        <a href=\"Articles.aspx?category=2\" class=\"pro-item color-default\" title=\"OCR technical tutorials and getting started guides\">\r\n                                            <div class=\"letter-wrap\">\r\n                                                <h3 class=\"h6\">Ubuyobozi bw'Akarere ka Muhanga</h3>\r\n                                                <span class=\"color-gray fn14\">Kuva ku ntangiriro kugeza ku masomo ya tekiniki ya OCR, amabwiriza arambuye y'uburyo bwo kugenzura n'ingendo zifatika</span>\r\n                                            </div>\r\n                                        </a>\r\n                                        \r\n                                        <a href=\"Articles.aspx?category=3\" class=\"pro-item color-default\" title=\"Amahame y'ikoranabuhanga, algorithms na porogaramu\">\r\n                                            <div class=\"letter-wrap\">\r\n                                                <h3 class=\"h6\">Ubushakashatsi bw'ikoranabuhanga</h3>\r\n                                                <span class=\"color-gray fn14\">Explore the frontiers of OCR technology, from principles to applications, and deeply analyze core algorithms</span>\r\n                                            </div>\r\n                                        </a>\r\n                                        \r\n                                        <a href=\"Articles.aspx?category=4\" class=\"pro-item color-default\" title=\"Impinduka n'impinduka ziherutse kugaragara mu mikorere y'Ikigo cy'Igihugu cy'Imisoro n'Amahoro\">\r\n                                            <div class=\"letter-wrap\">\r\n                                                <h3 class=\"h6\">Imiterere y'inganda</h3>\r\n                                                <span class=\"color-gray fn14\">In-depth insights into OCR technology development trends, market analysis, industry dynamics, and future prospects</span>\r\n                                            </div>\r\n                                        </a>\r\n                                        \r\n                                        <a href=\"Articles.aspx?category=5\" class=\"pro-item color-default\" title=\"Gukoresha ikoranabuhanga rya OCR mu nzego zitandukanye\">\r\n                                            <div class=\"letter-wrap\">\r\n                                                <h3 class=\"h6\">Use Cases:</h3>\r\n                                                <span class=\"color-gray fn14\">Ibisubizo nyabyo, ibisubizo, n'imikorere myiza y'ikoranabuhanga rya OCR mu nganda zitandukanye zirasangiye</span>\r\n                                            </div>\r\n                                        </a>\r\n                                        \r\n                                        <a href=\"Articles.aspx?category=6\" class=\"pro-item color-default\" title=\"Professional reviews, comparative analysis, and recommended guidelines for using OCR software tools\">\r\n                                            <div class=\"letter-wrap\">\r\n                                                <h3 class=\"h6\">Tool review</h3>\r\n                                                <span class=\"color-gray fn14\">Kugenzura porogaramu zitandukanye zo kugenzura inyandiko ya OCR hamwe nibikoresho, no gutanga ibisobanuro birambuye byo kugereranya imikorere no guhitamo ibitekerezo</span>\r\n                                            </div>\r\n                                        </a>\r\n                                        \r\n                                    </div>\r\n                                </div>\r\n                            </div>\r\n                        </div>\r\n                    </section>\r\n                    <section id=\"vip-sub-nav\" class=\"pls-nav-dropdown solution-part\" style=\"visibility: hidden; height: 0px; display: none;\">\r\n                        <div class=\"d-flex nav-drown-con\" style=\"height: 0px; transition: transform 0.2s ease-in-out 0s,opacity 0.2s ease-in-out 0s; opacity: 0; transform: translate(0px, -100%);\">\r\n                            <div class=\"prdocu-sub-left\">\r\n                                <div class=\"mr-auto\">\r\n                                    <picture>\r\n                                        <source class=\"p-icon\" srcset=\"site/image/icon/p_1.png.webp\" type=\"image/webp\" alt=\"\" aria-hidden=\"true\" />\r\n                                        <img class=\"p-icon\" src=\"site/image/icon/p_1.png\" alt=\"Membership upgrade service icon\" aria-hidden=\"true\" loading=\"lazy\" />\r\n                                    </picture>\r\n                                    <div class=\"mt-4\">\r\n                                        <h3 class=\"h6\">Serivisi yo kuzamura abanyamuryango</h3>\r\n                                        <p class=\"color-gray fn14 mt-3 sub-left-desc\">Fungura ibintu byose bya premium kandi wishimire serivisi zidasanzwe</p>\r\n                                        <p class=\"color-gray fn14 mt-3 sub-left-desc\">&nbsp;</p>\r\n                                        <p class=\"color-gray fn14 mt-3 sub-left-desc\">Offline recognition, batch processing, unlimited use</p>\r\n                                        <p class=\"color-gray fn14 mt-3 sub-left-desc\">Pro → Ultimate → Enterprise</p>\r\n                                        <p class=\"color-gray fn14 mt-3 sub-left-desc\">Hari ikintu kijyanye n'ibyo ukeneye</p>\r\n                                        <a class=\"link block disa fn14 contact-business shake\" href=\"Version.aspx\">View details<span class=\"iconfont4 icon-xiangzuo gt\"></span></a>\r\n                                    </div>\r\n                                </div>\r\n                            </div>\r\n                            <div class=\"bg-white prdocu-sub-right\">\r\n                                <div>\r\n                                    <h2 class=\"text-base-color h6\" style=\"margin: inherit;\">Kuvugurura abanyamuryango</h2>\r\n                                    <div class=\"mt-4 d-flex flex-wrap\">\r\n                                        <a href=\"Version.aspx\" class=\"pro-item color-default\">\r\n                                            <div class=\"letter-wrap\">\r\n                                                <h3 class=\"h6\">Uburenganzira bw'abanyamuryango</h3>\r\n                                                <span class=\"color-gray fn14\">Menya byinshi kubyerekeye itandukaniro riri hagati y'ibitabo no guhitamo icyiciro cy'abanyamuryango kikubereye</span>\r\n                                            </div>\r\n                                        </a>\r\n                                        <a href=\"Upgrade.aspx\" class=\"pro-item color-default\">\r\n                                            <div class=\"letter-wrap\">\r\n                                                <h3 class=\"h6\">Upgrade now</h3>\r\n                                                <span class=\"color-gray fn14\">Vuba vuba kuzamura abanyamuryango bawe ba VIP kugirango ufungure ibindi bintu bya premium na serivisi zidasanzwe</span>\r\n                                            </div>\r\n                                        </a>\r\n                                        <a href=\"User.aspx\" class=\"pro-item color-default\">\r\n                                            <div class=\"letter-wrap\">\r\n                                                <h3 class=\"h6\">Inkuru yanjye</h3>\r\n                                                <span class=\"color-gray fn14\">Kugenzura amakuru ya konti, imiterere y'ifatabuguzi, n'amateka y'imikoreshereze kugirango uhindure imiterere</span>\r\n                                            </div>\r\n                                        </a>\r\n                                    </div>\r\n                                </div>\r\n                            </div>\r\n                        </div>\r\n                    </section>\r\n                    <section id=\"support-sub-nav\" class=\"pls-nav-dropdown solution-part\" style=\"visibility: hidden; height: 0px; display: none;\">\r\n                        <div class=\"d-flex nav-drown-con\" style=\"height: 0px; transition: transform 0.2s ease-in-out 0s,opacity 0.2s ease-in-out 0s; opacity: 0; transform: translate(0px, -100%);\">\r\n                            <div class=\"prdocu-sub-left\">\r\n                                <div class=\"mr-auto\">\r\n                                    <picture>\r\n                                        <source class=\"p-icon\" srcset=\"site/image/icon/p_4.png.webp\" type=\"image/webp\" alt=\"\" aria-hidden=\"true\" />\r\n                                        <img class=\"p-icon\" src=\"site/image/icon/p_4.png\" alt=\"Help Center support icon\" aria-hidden=\"true\" loading=\"lazy\" />\r\n                                    </picture>\r\n                                    <div class=\"mt-4\">\r\n                                        <h3 class=\"h6\">Ikigo cy'Ubufasha</h3>\r\n                                        <p class=\"color-gray fn14 mt-3 sub-left-desc\">Ibaruramari ryubucuruzi, ibaruramari ryihariye hamwe nububiko bwihuse</p>\r\n                                        <p class=\"color-gray fn14 mt-3 sub-left-desc\">&nbsp;</p>\r\n                                        <p class=\"color-gray fn14 mt-3 sub-left-desc\">Ntugahangayike mu gihe uhuye n'ibibazo</p>\r\n                                        <p class=\"color-gray fn14 mt-3 sub-left-desc\">Problem → Find → Solved</p>\r\n                                        <p class=\"color-gray fn14 mt-3 sub-left-desc\">Kora ubunararibonye bwawe bworoheje</p>\r\n                                        <a class=\"link block disa fn14 contact-business shake\" href=\"FAQ.aspx\">Shaka ubufasha<span class=\"iconfont4 icon-xiangzuo gt\"></span></a>\r\n                                    </div>\r\n                                </div>\r\n                            </div>\r\n                            <div class=\"bg-white prdocu-sub-right\">\r\n                                <div>\r\n                                    <h2 class=\"text-base-color h6\" style=\"margin: inherit;\">Ikigo cy'Ubufasha</h2>\r\n                                    <div class=\"mt-4 d-flex flex-wrap\">\r\n                                        <a href=\"FAQ.aspx\" class=\"pro-item color-default\">\r\n                                            <div class=\"letter-wrap\">\r\n                                                <h3 class=\"h6\">Ibibazo bikunze kubazwa</h3>\r\n                                                <span class=\"color-gray fn14\">Gusubiza byihuse ibibazo by'abakoresha no gutanga amabwiriza arambuye y'imikoreshereze n'ubufasha bwa tekiniki</span>\r\n                                            </div>\r\n                                        </a>\r\n                                        <a href=\"AboutUs.aspx\" class=\"pro-item color-default\">\r\n                                            <div class=\"letter-wrap\">\r\n                                                <h3 class=\"h6\">Ibitwerekeyeho</h3>\r\n                                                <span class=\"color-gray fn14\">Learn about the development history, core functions and service concepts of OCR text recognition assistant</span>\r\n                                            </div>\r\n                                        </a>\r\n                                        <a href=\"UserAgreement.aspx\" class=\"pro-item color-default\">\r\n                                            <div class=\"letter-wrap\">\r\n                                                <h3 class=\"h6\">Amasezerano y'umukoresha</h3>\r\n                                                <span class=\"color-gray fn14\">Amabwiriza arambuye agenga serivisi n'uburenganzira bw'abakoresha</span>\r\n                                            </div>\r\n                                        </a>\r\n                                        <a href=\"PrivacyPolicy.aspx\" class=\"pro-item color-default\">\r\n                                            <div class=\"letter-wrap\">\r\n                                                <h3 class=\"h6\">Amasezerano y'ibanga</h3>\r\n                                                <span class=\"color-gray fn14\">Politiki yo kurinda amakuru bwite n'ingamba zo kurinda amakuru</span>\r\n                                            </div>\r\n                                        </a>\r\n                                        <a href=\"Status.aspx\" class=\"pro-item color-default\">\r\n                                            <div class=\"letter-wrap\">\r\n                                                <h3 class=\"h6\">Imiterere ya sisitemu</h3>\r\n                                                <span class=\"color-gray fn14\">Kugenzura imikorere ya sisitemu yo kugenzura ibicuruzwa mugihe nyacyo no kugenzura imikorere ya sisitemu</span>\r\n                                            </div>\r\n                                        </a>\r\n                                        <a href=\"javascript:void(0);\" class=\"pro-item color-default\" onclick=\"if(window.openCustomerService) window.openCustomerService(); else alert('Kanda kuri icon y'idirishya ry'iburyo kugira ngo uhamagare serivisi y'abakiriya');\">\r\n                                            <div class=\"letter-wrap\">\r\n                                                <h3 class=\"h6\">Contact customer service</h3>\r\n                                                <span class=\"color-gray fn14\">Porogaramu yo kugenzura ibaruramari kugirango isubize byihuse ibibazo byawe n'ibyo ukeneye</span>\r\n                                            </div>\r\n                                        </a>\r\n                                    </div>\r\n                                </div>\r\n                            </div>\r\n                        </div>\r\n                    </section>\r\n                </div>\r\n            </div>\r\n            <div class=\"header_pc_right\" style=\"display: flex; align-items: center; justify-content: flex-end;\">\r\n                <div id=\"translate\"></div>\r\n                <div id=\"user-login-container\" data-url=\"User.ashx?op=userinfo\" style=\"white-space: nowrap; padding-right: 20px;\"></div>\r\n            </div>\r\n        </header>\r\n        <header class=\"v4_header_mob\">\r\n            <div class=\"container\">\r\n                <div class=\"row no-gutters justify-content-between align-items-center\">\r\n                    <a href=\"Default.aspx\" title=\"Urugo | AI intelligent text recognition\" class=\"mob-logo-wrap\">\r\n                        <picture>\r\n                            <source srcset=\"site/image/logo-s.png.webp\" type=\"image/webp\" />\r\n                            <img src=\"site/image/logo-s.png\" class=\"pc-logo\" style=\"height: 32px; width: 32px\" alt=\"OCR text recognition assistant mobile logo\" />\r\n                        </picture>\r\n                        <span style=\"font-size: 18px; font-weight: bold; color: #333;\">OCR text recognition assistant</span>\r\n                    </a>\r\n                    <div class=\"right-menu\" role=\"button\" tabindex=\"0\" aria-label=\"Open the navigation menu\" data-toggle=\"collapse\" data-target=\"#mob-header-collapse\" aria-expanded=\"false\" aria-controls=\"mob-header-collapse\">\r\n                        <nav-products id=\"nav_products\">\r\n                            <nav-button class=\"submail-mdi-nav-btn\" style=\"width: 60px\">\r\n                                <label id=\"mdi-submail-nav-btn\">\r\n                                    <span class=\"mdi-nav-products-1\">\r\n                                        <span class=\"nav-products-icon\"></span>\r\n                                    </span>\r\n                                    <span class=\"mdi-nav-products-2\">\r\n                                        <span class=\"nav-products-icon\"></span>\r\n                                    </span>\r\n                                    <span class=\"mdi-nav-products-3\">\r\n                                        <span class=\"nav-products-icon\"></span>\r\n                                    </span>\r\n                                </label>\r\n                            </nav-button>\r\n                        </nav-products>\r\n                    </div>\r\n                </div>\r\n            </div>\r\n            <div class=\"mob-nav-content\">\r\n                <div class=\"sidebar-fix\">\r\n                    <div class=\"container\">\r\n                        <div class=\"row\">\r\n                            <div class=\"col-4 bg-white sidebar-fix-left\" style=\"border-right: 1px solid #e1e6ed\">\r\n                                <div class=\"mob-nav-item active\" data=\"0\">\r\n                                    <div class=\"nav-header\"><a href=\"Default.aspx\" class=\"nav-header-letter px-3\"><span>Inzu</span></a></div>\r\n                                </div>\r\n                                <div class=\"mob-nav-item\" data=\"1\">\r\n                                    <div class=\"nav-header\"><a href=\"Detail.aspx\" class=\"nav-header-letter px-3\"><span>Function</span></a></div>\r\n                                </div>\r\n                                <div class=\"mob-nav-item\" data=\"2\">\r\n                                    <div class=\"nav-header\"><a href=\"Ocr.aspx\" class=\"nav-header-letter px-3\"><span>Ubunararibonye</span></a></div>\r\n                                </div>\r\n                                <div class=\"mob-nav-item\" data=\"3\">\r\n                                    <div class=\"nav-header\"><a href=\"Version.aspx\" class=\"nav-header-letter px-3\"><span>Umunyamuryango</span></a></div>\r\n                                </div>\r\n                                <div class=\"mob-nav-item\" data=\"4\">\r\n                                    <div class=\"nav-header\"><a href=\"Download.aspx\" class=\"nav-header-letter px-3\"><span>Download</span></a></div>\r\n                                </div>\r\n                                <div class=\"mob-nav-item\" data=\"5\">\r\n                                    <div class=\"nav-header\"><a href=\"Articles.aspx\" class=\"nav-header-letter px-3\"><span>Share</span></a></div>\r\n                                </div>\r\n                                <div class=\"mob-nav-item\" data=\"6\">\r\n                                    <div class=\"nav-header\"><a href=\"FAQ.aspx\" class=\"nav-header-letter px-3\"><span>Gufasha</span></a></div>\r\n                                </div>\r\n                            </div>\r\n                            <div class=\"col-8 bg-white sidebar-fix-rigth\">\r\n                                <div class=\"sub-nav\">\r\n                                    <div class=\"sub-nav-item py-2 pb-3 px-4\">\r\n                                        <h2 class=\"h5\" style=\"margin: inherit;\">Efficient productivity tools</h2>\r\n                                        <p class=\"color-gray fn14 mt-3 sub-left-desc\">Kugenzura byihuse, gutunganya byihuse, hamwe no gutanga umusaruro mwiza.</p>\r\n                                        <p class=\"color-gray fn14 mt-3 sub-left-desc\">&nbsp;</p>\r\n                                        <p class=\"color-gray fn14 mt-3 sub-left-desc\">Menya urupapuro rwuzuye rw'inyandiko mu gihe cy'amasegonda 3</p>\r\n                                        <p class=\"color-gray fn14 mt-3 sub-left-desc\">98%+ recognition accuracy</p>\r\n                                        <p class=\"color-gray fn14 mt-3 sub-left-desc\">Gukoresha imbuga nkoranyambaga mu buryo bwihuse mu buryo bwihuse</p>\r\n                                        <a class=\"link block disa fn14 contact-business shake\" href=\"Download.aspx\">Download the experience now<span class=\"iconfont4 icon-xiangzuo gt\"></span></a>\r\n                                    </div>\r\n                                </div>\r\n                                <div class=\"sub-nav\">\r\n                                    <div class=\"sub-nav-item py-3 px-4\">\r\n                                        <h2 class=\"h5\" style=\"margin: inherit;\">Ibicuruzwa biranga ibicuruzwa:</h2>\r\n                                        <p class=\"color-gray fn14 mt-3 sub-left-desc\">AI intelligent identification, one-stop solution</p>\r\n                                    </div>\r\n                                    <div class=\"sub-nav-item py-3 px-4\"><a href=\"Detail.aspx\">Function introduction</a></div>\r\n                                    <div class=\"sub-nav-item py-3 px-4\"><a href=\"Download.aspx\">Software download</a></div>\r\n                                    <div class=\"sub-nav-item py-3 px-4\"><a href=\"Version.aspx\">Version comparison</a></div>\r\n                                    <div class=\"sub-nav-item py-3 px-4\"><a href=\"Ocr.aspx\">Ubunararibonye bwo kuri interineti</a></div>\r\n                                    <div class=\"sub-nav-item py-3 px-4\"><a href=\"Status.aspx\">Imiterere ya sisitemu</a></div>\r\n                                </div>\r\n                                <div class=\"sub-nav\">\r\n                                    <div class=\"sub-nav-item py-3 px-4\">\r\n                                        <h2 class=\"h5\" style=\"margin: inherit;\">Ubunararibonye bwo kuri interineti</h2>\r\n                                        <p class=\"color-gray fn14 mt-3 sub-left-desc\">Free online OCR function experience</p>\r\n                                    </div>\r\n                                    <div class=\"sub-nav-item py-3 px-4\"><a href=\"Ocr.aspx\">Imikorere yuzuye</a></div>\r\n                                    <div class=\"sub-nav-item py-3 px-4\"><a href=\"Ocr.aspx?type=text_recognize\">Kumenya ijambo</a></div>\r\n                                    <div class=\"sub-nav-item py-3 px-4\"><a href=\"Ocr.aspx?type=table\">Identification de l'atable</a></div>\r\n                                    <div class=\"sub-nav-item py-3 px-4\"><a href=\"Ocr.aspx?type=pdf2word\">PDF to Word</a></div>\r\n                                </div>\r\n                                <div class=\"sub-nav\">\r\n                                    <div class=\"sub-nav-item py-3 px-4\">\r\n                                        <h2 class=\"h5\" style=\"margin: inherit;\">Kuvugurura abanyamuryango</h2>\r\n                                        <p class=\"color-gray fn14 mt-3 sub-left-desc\">Fungura ibintu byose kandi wishimire serivisi zidasanzwe</p>\r\n                                    </div>\r\n                                    <div class=\"sub-nav-item py-3 px-4\"><a href=\"Version.aspx\">Inyungu z'abanyamuryango</a></div>\r\n                                    <div class=\"sub-nav-item py-3 px-4\"><a href=\"Upgrade.aspx\">Activate immediately</a></div>\r\n                                </div>\r\n                                <div class=\"sub-nav\">\r\n                                    <div class=\"sub-nav-item py-3 px-4\">\r\n                                        <h2 class=\"h5\" style=\"margin: inherit;\">Software download</h2>\r\n                                        <p class=\"color-gray fn14 mt-3 sub-left-desc\">Download the professional OCR software for free</p>\r\n                                    </div>\r\n                                    <div class=\"sub-nav-item py-3 px-4\"><a href=\"Download.aspx\">Download now</a></div>\r\n                                    <div class=\"sub-nav-item py-3 px-4\"><a href=\"Version.aspx\">Version comparison</a></div>\r\n                                </div>\r\n                                <div class=\"sub-nav\">\r\n                                    <div class=\"sub-nav-item py-3 px-4\">\r\n                                        <h2 class=\"h5\" style=\"margin: inherit;\">Guhanahana amakuru</h2>\r\n                                        <p class=\"color-gray fn14 mt-3 sub-left-desc\">OCR technical articles and knowledge sharing</p>\r\n                                    </div>\r\n                                    <div class=\"sub-nav-item py-3 px-4\"><a href=\"Articles.aspx\">Ingingo zose</a></div>\r\n                                    \r\n                                    <div class=\"sub-nav-item py-3 px-4\"><a href=\"Articles.aspx?category=2\">Ubuyobozi bw'Akarere ka Muhanga</a></div>\r\n                                    \r\n                                    <div class=\"sub-nav-item py-3 px-4\"><a href=\"Articles.aspx?category=3\">Ubushakashatsi bw'ikoranabuhanga</a></div>\r\n                                    \r\n                                    <div class=\"sub-nav-item py-3 px-4\"><a href=\"Articles.aspx?category=4\">Imiterere y'inganda</a></div>\r\n                                    \r\n                                    <div class=\"sub-nav-item py-3 px-4\"><a href=\"Articles.aspx?category=5\">Use Cases:</a></div>\r\n                                    \r\n                                    <div class=\"sub-nav-item py-3 px-4\"><a href=\"Articles.aspx?category=6\">Tool review</a></div>\r\n                                    \r\n                                </div>\r\n                                <div class=\"sub-nav\">\r\n                                    <div class=\"sub-nav-item py-3 px-4\">\r\n                                        <h2 class=\"h5\" style=\"margin: inherit;\">Ikigo cy'Ubufasha</h2>\r\n                                        <p class=\"color-gray fn14 mt-3 sub-left-desc\">Professional Customer Service, Intimate Service</p>\r\n                                    </div>\r\n                                    <div class=\"sub-nav-item py-3 px-4\"><a href=\"FAQ.aspx\">Koresha ubufasha</a></div>\r\n                                    <div class=\"sub-nav-item py-3 px-4\"><a href=\"AboutUs.aspx\">Ibitwerekeyeho</a></div>\r\n                                    <div class=\"sub-nav-item py-3 px-4\"><a href=\"mailto:<EMAIL>\">Contact customer service</a></div>\r\n                                    <div class=\"sub-nav-item py-3 px-4\"><a href=\"UserAgreement.aspx\">Terms of Service</a></div>\r\n                                </div>\r\n                            </div>\r\n                        </div>\r\n                    </div>\r\n                </div>\r\n            </div>\r\n        </header>\r\n        <form method=\"post\" action=\"./Article.aspx?id=81&amp;slug=rnn-sequence-modeling&amp;lang=zh-Hans&amp;_translation_source=true\" id=\"aspnetForm\">\r\n<div class=\"aspNetHidden\">\r\n<input type=\"hidden\" name=\"__VIEWSTATE\" id=\"__VIEWSTATE\" value=\"Q9uUeUlUJtaRG68B9ZsvqxMA0nWMrG97wrg6XH6pF/PALpTNvdWvXETFldXabtYfiX/3jguNbdKAEx4O02Rz9ETQIoNHodxI3/t/Hx/oGKM=\" />\r\n</div>\r\n\r\n<div class=\"aspNetHidden\">\r\n\r\n\t<input type=\"hidden\" name=\"__VIEWSTATEGENERATOR\" id=\"__VIEWSTATEGENERATOR\" value=\"2173C2F0\" />\r\n</div>\r\n            <div>\r\n                \n\n<div class=\"reading-progress\" id=\"readingProgress\"></div>\n<div class=\"article-detail-container\">\n<div class=\"container\">\n<div class=\"row\">\n<div class=\"col-lg-8 col-xl-9 col-md-12\">\n                    \n                    <article class=\"article-detail\" id=\"articleDetail\" data-article-id=\"81\">\n                        <header class=\"article-header\">\n                            <h1 class=\"article-title\">【Deep Learning OCR Series·4】Recurrent Neural Networks and Sequence Modeling</h1>\n                            <div class=\"article-meta\">\n                                <div class=\"meta-item\">\n                                    <span class=\"meta-icon\">📅</span>\n                                    <span>Post time: 2025-08-19</span>\n                                </div>\n                                <div class=\"meta-item\">\n                                    <span class=\"meta-icon\">👁️</span>\n                                    <span>Gusoma:<span class=\"view-count\">1200</span></span>\n                                </div>\n                                <div class=\"meta-item\">\n                                    <span class=\"meta-icon\">⏱️</span>\n                                    <span>Approx. 50 minutes (9819 words)</span>\n                                </div>\n                                \n                                <div class=\"meta-item\">\n                                    <span class=\"meta-icon\">📁</span>\n                                    <span>Category: Advanced Guides</span>\n                                </div>\n                                \n                            </div>\n                            <div class=\"article-summary\"><p>Dive into the application of RNN, LSTM, GRU in OCR. Detailed analysis of the principles of sequence modeling, solutions to gradient problems, and the benefits of bidirectional RNNs.</p></div>\n                        </header>\n                        <div class=\"article-content\" id=\"articleContent\">## Introduction\r\n\r\nRecurrent Neural Network (RNN) ni ubwubatsi bw'umuyoboro wa neural mu kwiga byimbitse bwihariye mu gutunganya amakuru y'uruhererekane. In OCR tasks, text recognition is essentially a sequence-to-sequence conversion problem: converting a sequence of image features into a text character sequence. This article will delve into how RNN works, its main variants, and its specific applications in OCR, providing readers with a comprehensive theoretical foundation and practical guidance.\r\n\r\n## RNN Fundamentals\r\n\r\n### Limitations of Traditional Neural Networks\r\n\r\nImiyoboro ya neural ya gakondo ya feedforward ifite imbogamizi zikomeye mu gutunganya amakuru yikurikiranya. These networks assume that the input data is independent and homodistributed, and cannot capture the temporal dependencies between elements in the sequence.\r\n\r\n**Feedforward Network Problems**:\r\n- Fixed input and output length: Variable length sequences cannot be handled\r\n- Kubura ubushobozi bwo kwibuka: Kunanirwa gukoresha amakuru y'amateka\r\n- Difficult in Parameter Sharing: The same pattern needs to be learned often in different locations\r\n- Positional sensitivity: Changing the order of inputs can lead to completely different outputs\r\n\r\nIzo mbogamizi zigaragara cyane cyane mu bikorwa bya OCR. Text sequences are highly context-dependent, and the recognition results of the previous character often help determine the probability of following characters. Kurugero, mugihe ugaragaza ijambo ry'icyongereza \"the\", niba \"th\" yamaze kumenyekana, noneho inyuguti ikurikira ishobora kuba \"e\".\r\n\r\n### Igitekerezo cy'ibanze cya RNN\r\n\r\nRNN ikemura ikibazo cy'imiterere y Igitekerezo nyamukuru ni ukongera uburyo bwa \"memory\" kuri network, kugirango umuyoboro ubashe kubika no gukoresha amakuru yo mu bihe byashize.\r\n\r\n**Mathematical Representation of RNN**:\r\nAt moment t, the RNN's hidden state h_t determined by the current input x_t and the hidden state of the previous moment h_{t-1}:\r\n\r\nh_t = f(W_hh * h_{t-1} + W_xh * x_t + b_h)\r\n\r\nHariya:\r\n- W_hh is the weight matrix from hidden state to hidden state\r\n- W_xh is the weight matrix entered into the hidden state  \r\n- b_h is a bias vector\r\n- f is the activation function (usually tanh or ReLU)\r\n\r\nIbiciro by y_t ibicuruzwa byashyizwe ahagaragara hashingiwe ku bubiko bw'ibicuruzwa by\r\ny_t = W_hy * h_t + b_y\r\n\r\n*Benefits of RNNs**:\r\n- Parameter sharing: The same weights are shared across all timesteps\r\n- Variable Length Sequence Processing: Can handle input sequences of arbitrary length\r\n- Ubushobozi bwo kwibuka: Ibihugu bihishe bikora nk'\"inzibutso\" z'umuyoboro\r\n- Flexible Input and Output: Supports one-to-one, one-to-many, many-to-one, many-to-many modes and more\r\n\r\n### Expanded view of RNN\r\n\r\nKugira ngo turusheho gusobanukirwa n'imikorere y'u Rwanda, dushobora kwagura ibikorwa byacu. RNN yagutse isa nk'umuyoboro wimbitse wa feedforward, ariko ibihe byose bisangiye ibipimo bimwe.\r\n\r\n*The Significance of Time Unfolding**:\r\n- Easy to understand information flow: It is possible to clear see how information is passed between time steps\r\n- Gradient Calculation: Gradients are calculated through the Time Backpropagation (BPTT) algorithm\r\n- Parallelization Considerations: While RNNs are inherently sequential, some operations can be parallelized\r\n\r\n*Bye Bye Birdie *:\r\nKu bijyanye n'uburebure bw'amanota y'ibihuha, RNC yagutse ku buryo bukurikira:\r\nh_1 = f(W_xh * x_1 + b_h)\r\nh_2 = f(W_hh * h_1 + W_xh * x_2 + b_h)\r\nh_3 = f(W_hh * h_2 + W_xh * x_3 + b_h)\r\n...\r\nh_T = f(W_hh * h_{T-1} + W_xh * x_T + b_h)\r\n\r\nThis unfolded form clearly shows how information is passed between time steps and how parameters are shared across all time steps.\r\n\r\n## Gradient disappearance and explosion problem\r\n\r\n### Umuzi w'ikibazo\r\n\r\nMu gutoza RNNs, dukoresha algorithm ya Backpropagation Through Time (BPTT). Sisitemu yo kubara ibaruramari ikeneye kugenzura ibipimo byububiko bwibicuruzwa byibicuru\r\n\r\n**Chain Law for Gradient Calculation**:\r\nMugihe uruhererekane rurerure, gradient ikeneye gusubirwamo mubihe byinshi. According to the chain rule, a gradient will contain multiple multiplications of the weight matrix:\r\n\r\n∂L/∂W = Σ_t (∂L/∂y_t) * (∂y_t/∂h_t) * (∂h_t/∂W)\r\n\r\nwhere ∂h_t/∂W includes the product of all intermediate states from moment t to moment 1.\r\n\r\n**Mathematical Analysis of Gradient Disappearance**:\r\nReka turebere hamwe uko imiterere y'ibinyabiziga igenda igabanuka mu gihe cy'imihango:\r\n∂h_t/∂h_{t-1} = diag(f_prime(W_hh * h_{t-1} + W_xh * x_t + b_h)) * W_hh\r\n\r\nIyo uburebure bw'uruhererekane ari T, gradient iba ifite T-1 nk'igihe cy'ibicuruzwa. If the maximum eigenvalue of the W_hh is less than 1, continuous matrix multiplication will cause gradient exponential decay.\r\n\r\n**Mathematical Analysis of Gradient Explosions**:\r\nKu rundi ruhande, iyo igipimo cyo hejuru cya eigenvalue ya W_hh kiri hejuru ya 1, gradient yiyongera cyane:\r\n|| ∂h_t/∂h_1|| ≈ || W_hh|| ^{t-1}\r\n\r\nThis leads to unstable training and excessive parameter updates.\r\n\r\n# Sobanukirwa n'ibisobanuro birambuye ku kibazo cy'ibura ry'umuriro\r\n\r\nGradient Clipping:\r\nGradient clipping ni uburyo butaziguye bwo gukemura ibiturika bya gradient. When the gradient norm exceed a set threshold, the gradient is scaled to the threshold size. Ubu buryo bworoshye kandi bwizewe, ariko busaba guhitamo neza ibipimo by'ubuziranenge bw'ibipimo. A threshold that is too small will limit the learning ability, and a threshold that is too large will not effectively prevent gradient explosion.\r\n\r\n**Weight Initialization Strategy**:\r\nProper weight initialization can alleviate gradient issues:\r\n- Xavier initialization: The weight variance is 1/n, where n is the input dimension\r\n- He initialization: The weight variance is 2/n, which is suitable for ReLU activation functions\r\n- Orthogonal Initialization: Initializes the weight matrix as an orthogonal matrix\r\n\r\n**Selection of Activation Functions**:\r\nDifferent activation functions have different effects on gradient propagation:\r\n- tanh: output range [-1,1], gradient maximum value of 1\r\n- ReLU: ishobora kugabanya kuzimira kwa gradient ariko ishobora gutera urupfu rwa neuronal\r\n- Leaky ReLU: Ikemura ikibazo cy'urupfu rwa neuronal cya ReLU\r\n\r\n**Iterambere ry'imyubakire**:\r\nIgisubizo cy'ingenzi kwari uguteza imbere ubwubatsi bwa RNN, byatumye havuka LSTM na GRU. Izi nyubako zikemura gradients binyuze mu buryo bwa gating hamwe n'ibishushanyo byihariye by'amakuru.\r\n\r\n## LSTM: Long Short-Term Memory Network\r\n\r\n### Design Motivation for LSTM\r\n\r\nLSTM (Long Short-Term Memory) ni ubwoko bwa RNN bwatanzwe na Hochreiter na Schmidhuber mu 1997, bwagenewe gukemura ikibazo cyo kuzimira kwa gradient n'ibibazo byo kwiga kure.\r\n\r\n**LSTM's Core Innovations**:\r\n- Cell State: Ikora nk'\"umuhanda\" w'amakuru, ituma amakuru atembera mu buryo butaziguye hagati y'intambwe z'igihe\r\n- Gating Mechanism: Precise control over the inflow, retention, and output of information\r\n- Dissociated memory mechanisms: itandukaniro hagati y'ubushobozi bw'igihe gito (leta yihishe) n'ubwonko bw'igihe kirekire (cellular state)\r\n\r\n**How LSTM Solves Gradient Problems**:\r\nLSTM ivugurura imiterere ya selile binyuze mu bikorwa by'inyongera aho kwiyongera, bituma gradients zitembera byoroshye ku ntambwe zabanje. Updated formula for cell state:\r\n\r\nC_t = f_t ⊙ C_{t-1} + i_t ⊙ C_tilde_t\r\n\r\nElement-level addition is used here, preventing the continuous matrix multiplication in traditional RNNs.\r\n\r\n### Ibisobanuro birambuye by'ubwubatsi bwa LSTM\r\n\r\nUrwego rw'Igihugu rw'Ubugenzacyaha (RIB) rufite ibice bitatu by'ubucuruzi n'ibigo by'imari iciriritse:\r\n\r\n**1. Kwibagirwa irembo**:\r\nThe gate of oblivion decide what information to discard from the cell state:\r\nf_t = σ(W_f · [h_{t-1}, x_t] + b_f)\r\n\r\nThe output of the oblivion gate is a value between 0 and 1, with 0 being \"completely forgotten\" and 1 being \"completely retained\". This gate allows LSTM to selectively forget unimportant historical information.\r\n\r\n**2. Input Gate**:\r\nUrwego rw'Igihugu rw'Ubugenzacyaha (RIB) ruvuga ko amakuru mashya ashyirwa mu byiciro by'ubudehe:\r\ni_t = σ(W_i · [h_{t-1}, x_t] + b_i)\r\nC_tilde_t = tanh(W_C · [h_{t-1}, x_t] + b_C)\r\n\r\nThe input gate consists of two parts: the sigmoid layer determine which values to update, and the tanh layer creates candidate value vectors.\r\n\r\n**3. Cell Status Update**:\r\nGuhuza umusaruro w'irembo ry'indangamuntu n'irembo ry'ingirabuzimafatizo kugira ngo uvugurure state ya selile:\r\nC_t = f_t ⊙ C_{t-1} + i_t ⊙ C_tilde_t\r\n\r\nThis formula is at the heart of LSTM: selective retention and updating of information through element-level multiplication and addition operations.\r\n\r\n**4. Output Gate**:\r\nIgishushanyo mbonera cy'umujyi wa Kigali kigaragaza ibice by'umubiri w'umuntu:\r\no_t = σ(W_o · [h_{t-1}, x_t] + b_o)\r\nh_t = o_t ⊙ tanh(C_t)\r\n\r\nThe output gate controls which parts of the cell's state affect the current output.\r\n\r\n### LSTM variants\r\n\r\n**Peephole LSTM**:\r\nBuilding on the standard LSTM, the Peephole LSTM allows the gating unit to view the cell state:\r\nf_t = σ(W_f · [C_{t-1}, h_{t-1}, x_t] + b_f)\r\ni_t = σ(W_i · [C_{t-1}, h_{t-1}, x_t] + b_i)\r\no_t = σ(W_o · [C_t, h_{t-1}, x_t] + b_o)\r\n\r\n**Coupled LSTM**:\r\nGuhuza irembo ryibagirwa hamwe nirembo ryo kwinjiza kugirango urebe ko ingano yamakuru yibagiwe angana n'ingano y'amakuru yinjiyemo:\r\nf_t = σ(W_f · [h_{t-1}, x_t] + b_f)\r\ni_t = 1 - f_t\r\n\r\nIki gishushanyo mbonera kigabanya umubare w'ibipimo mu gihe gikomeza imikorere y'ibanze ya LSTM.\r\n\r\n## GRU: Gated Loop Unit\r\n\r\n### Igishushanyo cyoroheje cya GRU\r\n\r\nGRU (Gated Recurrent Unit) ni verisiyo yoroheje ya LSTM yashyizweho na Cho et al. mu 2014. GRU simplifies the three gates of the LSTM to two gates and merges the cellular state and the hidden state.\r\n\r\n**GRU's Design Philosophy**:\r\n- Koroshya imiterere: Kugabanya umubare w'inzugi no kugabanya uburemere bw'imibare\r\n- Maintain Performance: Simplify while keeping LSTM-comparable performance\r\n- Byoroshye gushyiramo ingufu: Ubwubatsi bworoshye butuma byoroshye gushyira mu bikorwa no kohereza\r\n\r\n### Gating mechanism of the GRU\r\n\r\n**1. Reset Gate**:\r\nr_t = σ(W_r · [h_{t-1}, x_t] + b_r)\r\n\r\nIgishushanyo mbonera cy'umujyi wa Muhanga kigaragaza uburyo bwo guhuza inyandiko nshya n'indi mishinga yahozeho. When the reset gate approaches 0, the model ignores the previous hidden state.\r\n\r\n**2. Update Gate**:\r\nz_t = σ(W_z · [h_{t-1}, x_t] + b_z)\r\n\r\nThe update gate determine how much past information to keep and how much new information to add. It controls both forgetting and input, similar to the combination of forgetting and input gates in LSTM.\r\n\r\n**3. Candidate Hidden Status**:\r\nh_tilde_t = tanh(W_h · [r_t ⊙ h_{t-1}, x_t] + b_h)\r\n\r\nCandidate hidden states use the reset gate to control the effects of the previous hidden state.\r\n\r\n**4. Final Hidden State**:\r\nh_t = (1 - z_t) ⊙ h_{t-1} + z_t ⊙ h_tilde_t\r\n\r\nThe final hidden state is a weighted average of the previous hidden state and the candidate hidden state.\r\n\r\n### GRU vs LSTM In-Depth Comparison\r\n\r\n*Bye Bye Birdie *:D\r\n- LSTM: 4 weight matrices (forgetting gate, input gate, candidate value, output gate)\r\n- GRU: 3 weight matrices (reset gate, update gate, candidate value)\r\n- Umubare w'ibipimo bya GRU ni hafi 75% ya LSTM\r\n\r\n**Computational Complexity Comparison**:\r\n- LSTM: Requires calculation of 4 gate outputs and cell state updates\r\n- GRU: Simply calculate the output of 2 gates and hidden status updates\r\n- GRU ikunze kwihuta hagati ya 20-30% na LSTM\r\n\r\n**Performance Comparison**:\r\n- On most tasks, GRU and LSTM perform comparablely\r\n- LSTM may be slightly better than GRU on some long-sequence tasks\r\n- GRU is a better choice in cases where computing resources are limited\r\n\r\n## Bidirectional RNNs\r\n\r\n### Ibyiciro by'ubudehe bigomba gukorwa mu buryo bubiri\r\n\r\nMu mikorere myinshi y'ibicuruzwa, umusaruro w'iki gihe ntabwo ushingiye gusa ku gihe cyashize ahubwo no ku makuru y'igihe kizaza. Ibi ni ingenzi cyane cyane mu bikorwa bya OCR, aho kumenya imiterere akenshi bisaba kugenzura ibisobanuro by'ijambo ryose cyangwa interuro.\r\n\r\n*Limitations of One-Way RNNs**:\r\n- Only historical information can be used, no future context can be obtained\r\n- Gukora imirimo myinshi mu mirimo imwe n'imwe, by'umwihariko ikeneye amakuru y'isi yose\r\n- Limited recognition of ambiguous characters\r\n\r\n**Benefits of Bidirectional Processing**:\r\n- Complete contextual information: Leverage both past and future information\r\n- Better disambiguation: Disambiguation with contextual information\r\n- Improved recognition accuracy: Performed better on most sequence annotation tasks\r\n\r\n### Bidirectional LSTM architecture\r\n\r\nIbice bibiri bya LSTM bigizwe n'ibice bibiri bya LSTM:\r\n- Forward LSTM: Process sequences from left to right\r\n- Backward LSTM: Process sequences from right to left\r\n\r\n**Mathematical Representation**:\r\nh_forward_t = LSTM_forward(x_t, h_forward_{t-1})\r\nh_backward_t = LSTM_backward(x_t, h_backward_{t+1})\r\nh_t = [h_forward_t; h_backward_t] # Kwikinisha imbere n'inyuma\r\n\r\n**Training Process**:\r\n1. Forward LSTM processes sequences in normal order\r\n2. LSTM y'inyuma itunganya uruhererekane mu buryo butandukanye\r\n3. At every time step, connect the hidden states in both directions\r\n4. Use the spliced state for prediction\r\n\r\n*Ibyiza n'ibibi byayo:\r\nAdvantage:\r\n- Full contextual information\r\n- Imikorere myiza\r\n- Symmetry treatment\r\n\r\nUmwanya wo hasi:\r\n- Double the complexity of the calculations\r\n- Cannot be processed in real-time (requires full sequence)\r\n- Kwiyongera kw'ibikenewe byo kwibuka\r\n\r\n## Sequence Modeling Applications in OCR\r\n\r\n# Detailed explanation of text line recognition\r\n\r\nMuri sisitemu ya OCR, kumenyekanisha umurongo w'inyandiko ni uburyo busanzwe bwo gukurikirana modeli. Iki gikorwa gikubiyemo guhindura uruhererekane rw'ibishushanyo muburyo butandukanye.\r\n\r\n**Problem Modeling**:\r\n- Input: Image feature sequence X = {x_1, x_2, ..., x_T}\r\n- Output: Character sequence Y = {y_1, y_2, ..., y_S}\r\n- Challenge: Input sequence length T and output sequence length S are often not equal\r\n\r\n**Application of CRNN Architecture in Text Line Recognition**:\r\nCRNN (Convolutional Recurrent Neural Network) ni imwe mu nyubako zigezweho muri OCR:\r\n\r\n1. **CNN Feature Extraction Layer**:\r\n   - Extract image features using convolutional neural networks\r\n   - Convert 2D image features into 1D feature sequences\r\n   - Maintain continuity of timing information\r\n\r\n2. **RNN Sequence Modeling Layer**:\r\n   - Model feature sequences using bidirectional LSTMs\r\n   - Capture contextual dependencies between characters\r\n   - Output character probability distribution for each time step\r\n\r\n3. **CTC Alignment Layer**:\r\n   - Addresses input/output sequence length mismatches\r\n   - No character-level alignment dimensions are required\r\n   - End-to-end training\r\n\r\n**Conversion of feature extraction to sequence**:\r\nUbuyobozi bw'Akarere ka Rubavu buvuga ko bugomba gushyirwa mu majwi n'ubuyobozi bw'Akarere ka Rubavu buvuga ko bugiye gushyirwa mu bikorwa:\r\n- Segment the feature map into columns, with each column as a time step\r\n- Maintain the chronology of spatial information\r\n- Genzura ko uburebure bw'igishushanyo mbonera buhuye n'ubugari bw'igishushanyo\r\n\r\n### Application of attention mechanism in OCR\r\n\r\nTraditional RNNs still have information bottlenecks when dealing with long sequences. Sisitemu yo kugenzura ibaruramari ryikora igenzura ryikora\r\n\r\n**Principles of Attention Mechanisms**:\r\nThe attention mechanism allows the model to focus on different parts of the input sequence when generating each output:\r\n- Solved the information bottleneck of fixed-length encoded vectors\r\n- Provides explainability of model decisions\r\n- Kunoza imikorere y'ibice birebire\r\n\r\n**Specific Applications in OCR**:\r\n\r\n1. **Character-Level Attention**:\r\n   - Focus on relevant image areas when identify each character\r\n   - Adjust attention weights on the fly\r\n   - Improve robustness to complex backgrounds\r\n\r\n2. **Word-Level Attention**:\r\n   - Consider contextual information at the vocabulary level\r\n   - Leverage language model knowledge\r\n   - Improve the accuracy of whole word recognition\r\n\r\n3. **Multi-Scale Attention**:\r\n   - Applying attention mechanisms at different resolutions\r\n   - Handle text of different sizes\r\n   - Improve adaptability to scale changes\r\n\r\n**Mathematical Representation of Attention Mechanism**:\r\nFor encoder output sequence H = {h_1, h_2, ..., h_T} and decoder state s_t:\r\n\r\ne_{t,i} = a(s_t, h_i) # Attention score\r\nα_{t,i} = softmax(e_{t,i}) # Attention weight\r\nc_t = Σ_i α_{t,i} * h_i # context vector\r\n\r\n## Training Strategies and Optimization\r\n\r\n### Uburyo bwo gukurikirana amahugurwa\r\n\r\n**Teacher Forcing**:\r\nMu gihe cy'imyitozo, koresha inyuguti nyayo nk'igisubizo cya decoder:\r\n- Pros: fast training speed, stable convergence\r\n- Cons: Inconsistent training and inference phases, leading to accumulation of errors\r\n\r\n**Scheduled Sampling**:\r\nBuhoro buhoro uhereye ku guhatira umwarimu gukoresha ubuhanuzi bw'icyitegererezo mu gihe cy'amahugurwa:\r\n- Use real labels in the initial stage and model predictions in the later stages\r\n- Kugabanya amakimbirane mu myitozo n'imyitozo ngororamubiri\r\n- Improve model robustness\r\n\r\n**Curriculum Learning**:\r\nTangira ukoresheje ingero zoroheje kandi wongere buhoro buhoro uburemere bw'ibipimo:\r\n- Short to Long Sequences: Train short texts first, then long texts\r\n- Clear to Blurred Images: Slowly increase the complexity of the image\r\n- Simple to Complex Fonts: From printed to handwriting\r\n\r\n### Uburyo bwo kugenzura\r\n\r\n**Application of Dropout in RNN**:\r\nApplying dropout in RNN requires special attention:\r\n- Don't apply dropout on loop connections\r\n- Dropout can be applied at the input and output layers\r\n- Variational dropout: Use the same dropout mask at all time steps\r\n\r\n**Weight Decay**:\r\nL2 regularization irinda gukabya:\r\nLoss = CrossEntropy + λ * || W|| ²\r\n\r\nNi ukuvuga ko Rayon Sports ari ikipe ya APR FC ikeneye gushyirwa mu cyiciro cya mbere cy'ubudehe.\r\n\r\n**Gradient Cropping**:\r\nUburyo bwiza bwo kwirinda ibicuruzwa by'ibicurane. When the gradient norm above the threshold, scale the gradient proportionally to keep the gradient direction unchanged.\r\n\r\n**Guhagarika hakiri kare**:\r\nMonitor validation set performance and stop training when performance is no longer improving:\r\n- Kwirinda gukabya\r\n- Save computing resources\r\n- Hitamo icyitegererezo cyiza\r\n\r\n### Hyperparameter tuning\r\n\r\n**Learning Rate Schedule**:\r\n- Initial Learning Rate: Usually set at 0.001-0.01\r\n- Learning rate decay: exponential decay or ladder decay\r\n- Adaptive Learning Rate: Use optimizers like Adam, RMSprop, etc\r\n\r\n**Batch Size Selection**:\r\n- Small batches: Better generalization performance but longer training time\r\n- High Volume: Training is fast but may affect generalization\r\n- Batch sizes between 16-128 are usually selected\r\n\r\n**Sequence Length Processing**:\r\n- Fixed Length: Truncate or fill sequences to fixed lengths\r\n- Dynamic length: Use padding and masking to handle variable length sequences\r\n- Bagging Strategy: Group sequences of similar length\r\n\r\n## Performance evaluation and analysis\r\n\r\n### Evaluate metrics\r\n\r\n**Character-Level Accuracy**:\r\nAccuracy_char = (Umubare w'inyuguti zizwi neza) / (Umubare w'inyuguti zinyuranye)\r\n\r\nIki nicyo gipimo cyibanze cyo kugenzura kandi kigaragaza mu buryo butaziguye ubushobozi bwo kumenya imiterere y'ikigereranyo.\r\n\r\n**Serial Level Accuracy**:\r\nAccuracy_seq = (Umubare w'ibipimo by'imiterere y'ibinyabiziga) / (Umubare w'ibipimo by'ibinyabiziga)\r\n\r\nIki gitekerezo ni cyiza cyane, kandi ni cyo cyonyine gifatwa nk'icy'ukuri.\r\n\r\n**Editing distance (Levenshtein distance)**:\r\nSobanukirwa itandukaniro riri hagati y'ibipimo by'ukuri n'iby'ukuri:\r\n- Umubare muto w'ibikorwa byo kwishyura, gukuramo, no gusimbuza\r\n- Standardized editing distance: editing distance / sequence length\r\n- BLEU score: Commonly used in machine translation and can also be used for OCR assessment\r\n\r\n### Isesengura ry'amakosa\r\n\r\n**Common Error Types**:\r\n1. **Character Confusion**: Misidentification of similar characters\r\n   - Umubare 0 n'inyuguti O\r\n   - Number 1 and letter l\r\n   - Inyuguti M na N\r\n\r\n2. **Sequence Error**: Error in the order of characters\r\n   - Imyanya y'imyitwarire ihindurwa\r\n   - Duplication or omission of characters\r\n\r\n3. **Length Error**: Error in predicting sequence length\r\n   - Too long: Insert non-existent characters\r\n   - Too short: Characters that are present are missing\r\n\r\n**Analysis Method**:\r\n1. **Confusion Matrix**: Isesengura imiterere y'amakosa yo ku rwego rw'imiterere\r\n2. **Attention Visualization**: Sobanukirwa impungenge z'icyitegererezo\r\n3. **Gradient Analysis**: Check the gradient flow\r\n4. **Activation Analysis**: Observe activation patterns across layers of the network\r\n\r\n### Model Diagnostics\r\n\r\n**Overfit Detection**:\r\n- Training losses continue to decline, validation losses rise rise\r\n- The training accuracy is much higher than the validation accuracy\r\n- Solution: Increase regularity and reduce model complexity\r\n\r\n**Underfit Detection**:\r\n- Both training and validation losses are high\r\nRayon Sports ntiyitwara neza mu myitozo\r\n- Solution: Increase model complexity and adjust learning rate\r\n\r\n**Gradient Problem Diagnosis**:\r\n- Gradient Loss: The gradient value is too small, slow learning\r\n- Gradient explosion: Excessive gradient values lead to unstable training\r\n- Igisubizo: Gukoresha LSTM / GRU, Gradient Cropping\r\n\r\n## Real-World Application Cases\r\n\r\n### Handwritten Character Recognition System\r\n\r\n**Application Scenarios**:\r\n- Digitize Handwritten Notes: Convert paper notes into electronic documents\r\n- Form Auto-Fill: Automatically knows handwritten form content\r\n- Historical Document Identification: Digitize ancient books and historical documents\r\n\r\n**Technical Features**:\r\n- Large character variations: Handwritten text has a high degree of personalization\r\n- Continuous pen processing: Connections between characters need to be handled\r\n- Context-Important: Use language models to improve recognition\r\n\r\n**System Architecture**:\r\n1. **Pretreatment Module**:\r\n   - Image denoising and enhancement\r\n   - Tilt correction\r\n   - Text line splitting\r\n\r\n2. **Feature Extraction Module**:\r\n   - CNN extracts visual features\r\n   - Multi-scale feature fusion\r\n   - Feature serialization\r\n\r\n3. **Sequence Modeling Module**:\r\n   - Bidirectional LSTM modeling\r\n   - Uburyo bwo kwitabwaho\r\n   - Contextual encoding\r\n\r\n4. **Decoding Module**:\r\n   - CTC decoding or attention decoding\r\n   - Language model post-processing\r\n   - Confidence assessment\r\n\r\n### Printed Document Recognition System\r\n\r\n**Application Scenarios**:\r\n- Document Digitization: Converting paper documents into editable formats\r\n- Bill Recognition: Automatically process invoices, receipts, and other bills\r\n- Signage Recognition: Identify road signs, store signs, and more\r\n\r\n**Technical Features**:\r\n- Regular font: More regular than handwritten text\r\n- Typography rules: Layout information can be used\r\n- High Accuracy Requirements: Commercial applications have strict accuracy requirements\r\n\r\n**Optimization Strategy**:\r\n1. **Multi-Font Training**: Ikoresha amakuru y'amahugurwa kuva mu myandikire myinshi\r\n2. **Data Enhancement**: Rotate, scale, noise addition\r\n3. **Post-processing optimization**: spell check, grammar correction\r\n4. **Confidence Assessment**: Provides a reliability score for the recognition results\r\n\r\n### Uburyo bwo kumenyekanisha inyandiko\r\n\r\n**Application Scenarios**:\r\n- Street View Text Recognition: Text recognition in Google Street View\r\n- Product Label Recognition: Automatic identification of supermarket products\r\n- Traffic Sign Recognition: Applications of intelligent transportation systems\r\n\r\n**Imbogamizi za tekiniki**:\r\n- Complex Backgrounds: Text is embedded in complex natural scenes\r\n- Severe deformation: Perspective deformation, bending deformation\r\n- Real-Time Requirements: Mobile apps need to be responsive\r\n\r\n**Igisubizo**:\r\n1. **Robust Feature Extraction**: Ikoresha imiyoboro yimbitse ya CNN\r\n2. ** Multi-Scale Processing **: Fata inyandiko yubunini butandukanye\r\n3. **Geometry Correction**: Automatically corrects geometric deformations\r\n4. **Model Compression**: Optimize the model for mobile\r\n\r\n## Summary\r\n\r\nRecurrent neural networks provide a powerful tool for sequence modeling in OCR. Kuva kuri RNN z'ibanze kugeza kuri LSTM na GRUs zigezweho kugeza ku buryo bwo gutunganya no kwitabwaho, iterambere ry'iri koranabuhanga ryateje imbere cyane imikorere ya sisitemu ya OCR.\r\n\r\n**Key Takeaways**:\r\n- RNNs implement sequence modeling through loop joins, but there is a gradient disappearance problem\r\n- LSTM and GRU solve the long-distance dependent learning problem through gating mechanisms\r\n- Bidirectional RNNs are able to leverage full contextual information\r\n- Attention mechanisms further enhance the ability of sequence modeling\r\n- Appropriate training strategies and regularization techniques are crucial for model performance\r\n\r\n**Future Development Directions**:\r\n- Integration with Transformer architectures\r\n- More efficient approach to sequence modeling\r\n- End-to-end multimodal learning\r\n- Balance of real-time and accuracy\r\n\r\nUko ikoranabuhanga rigenda ritera imbere, ni nako ikoranabuhanga rigenda ritera imbere. Ubunararibonye n'ikoranabuhanga byakusanyijwe na RNNs n'ubwoko bwazo mu rwego rwa OCR byashyizeho urufatiro rukomeye rwo gusobanukirwa no gushushanya uburyo bugezweho bwo gushushanya.</div>\n                        \n                        <div class=\"article-tags\">\n                            <h6>Label:</h6>\n                            <div class=\"tags-list\">\n                                \n                                <span class=\"tag\">RNN</span>\n                                \n                                <span class=\"tag\">LSTM</span>\n                                \n                                <span class=\"tag\">GRU</span>\n                                \n                                <span class=\"tag\">Sequence modeling</span>\n                                \n                                <span class=\"tag\">Igishushanyo mbonera kirazimira</span>\n                                \n                                <span class=\"tag\">Bidirectional RNN</span>\n                                \n                                <span class=\"tag\">Uburyo bwo kwitaho</span>\n                                \n                                <span class=\"tag\">CRNN</span>\n                                \n                                <span class=\"tag\">OCR</span>\n                                \n                            </div>\n                        </div>\n                        \n                    </article>\n                    \n                    <footer class=\"article-footer\" id=\"articleFooter\">\n                        <div class=\"article-share\">\n                            <h6 style=\"color: #1a1c1f; margin-bottom: 1rem;\">Share and Gukora:</h6>\n                            <div class=\"share-buttons\">\n                                <a href=\"javascript:void(0)\" onclick=\"shareToWeibo()\" class=\"btn btn-weibo\">📱 Weibo shared</a>\n                                <a href=\"javascript:void(0)\" onclick=\"copyUrl()\" class=\"btn btn-copy\">🔗 Copy link</a>\n                                <button type=\"button\" class=\"btn btn-print\" onclick=\"printArticle()\">🖨️ Print the article</button>\n                            </div>\n                        </div>\n                    </footer>\n                    \n                </div>\n                <div class=\"col-lg-4 col-xl-3 col-md-12\">\n                    <div class=\"sidebar\">\n                        <div class=\"sidebar-card toc-card\" id=\"tocCard\" style=\"display: none;\"><h5>Urutonde rw'ibikubiyemo</h5><div id=\"tableOfContents\" class=\"toc-list\"></div></div>\n                        <div class=\"sidebar-card\">\n                            <h5>Recommended reading</h5>\n                            <div id=\"recommendedArticles\">\n                                \n                                <div class=\"recommended-item\"><h6><a href=\"Article.aspx?slug=document-intelligence-future-outlook\">【Document Intelligent Processing Series·20】Development prospects of document intelligent processing technology</a></h6><p class=\"text-muted small\">2025-08-19 • 1203 Next reading</p></div>\n                                \n                                <div class=\"recommended-item\"><h6><a href=\"Article.aspx?slug=document-intelligence-quality-assurance\">【Document Intelligent Processing Series · 19】 Document Intelligent Processing Quality Assurance System</a></h6><p class=\"text-muted small\">2025-08-19 • 1200 Next reading</p></div>\n                                \n                                <div class=\"recommended-item\"><h6><a href=\"Article.aspx?slug=large-scale-document-processing-optimization\">【Document Intelligent Processing Series·18】Large-scale document processing performance optimization</a></h6><p class=\"text-muted small\">2025-08-19 • 1201 Next reading</p></div>\n                                \n                            </div>\n                        </div>\n                    </div>\n</div>\n</div>\n</div>\n</div>\n<script>\nvar articleId,viewCountKey,now=new Date().getTime();\ndocument.addEventListener('DOMContentLoaded',function(){var articleDetail=document.getElementById('articleDetail');if(articleDetail&&articleDetail.querySelector('.article-content')){formatContent();initReadingProgress();optimizeImages();updateViewCount();}});\nfunction updateViewCount(){articleId=getArticleId();if(!articleId)return;viewCountKey='article_view_'+articleId;var lastViewTime=localStorage.getItem(viewCountKey);if(lastViewTime&&(now-parseInt(lastViewTime))<3600000)return;var xhr=new XMLHttpRequest();xhr.open('POST','/Article.ashx',true);xhr.setRequestHeader('Content-Type','application/x-www-form-urlencoded');xhr.onreadystatechange=function(){if(xhr.readyState===4&&xhr.status===200){try{var response=JSON.parse(xhr.responseText);if(response.success){updateViewCountDisplay(response.viewCount);localStorage.setItem(viewCountKey,now.toString());}}catch(e){}}};xhr.send('action=updateviewcount&articleId='+encodeURIComponent(articleId));}\nfunction getArticleId(){var articleDetail=document.getElementById('articleDetail');if(articleDetail){var id=articleDetail.getAttribute('data-article-id');if(id)return id;}var urlParams=new URLSearchParams(window.location.search);return urlParams.get('id');}\nfunction updateViewCountDisplay(newCount){document.querySelectorAll('.view-count').forEach(function(element){element.textContent=newCount;});}\nfunction decodeHtmlEntities(text){var textArea=document.createElement('textarea');textArea.innerHTML=text;return textArea.value;}\nfunction formatContent(){var contentDiv=document.getElementById('articleContent');if(!contentDiv)return;var content=contentDiv.textContent||contentDiv.innerText;if(!content)return;content=decodeHtmlEntities(content);content=formatMarkdownContent(content);content=formatIndentation(content);content=formatLists(content);content=formatParagraphs(content);content=formatLinks(content);contentDiv.innerHTML=content;initTableOfContents();}\nfunction formatIndentation(content){var lines=content.split('\\n'),result=[];for(var i=0;i<lines.length;i++){var line=lines[i];if(line.trim().match(/^<[^>]+>/)){result.push(line);continue;}line=line.replace(/^\\t+/g,function(match){return '    '.repeat(match.length);});if(line.match(/^  +[^\\s]/)){var indentMatch=line.match(/^( +)(.*)$/);if(indentMatch){var indentLevel=Math.floor(indentMatch[1].length/2);line='<span class=\"text-indent-'+indentLevel+'\">'+indentMatch[2]+'</span>';}}result.push(line);}return result.join('\\n');}\nfunction formatMarkdownContent(content){content=content.replace(/^\\s*##### (.+)$/gm,'<h5>$1</h5>').replace(/^\\s*#### (.+)$/gm,'<h4>$1</h4>').replace(/^\\s*### (.+)$/gm,'<h3>$1</h3>').replace(/^\\s*## (.+)$/gm,'<h2>$1</h2>').replace(/^\\s*# (.+)$/gm,'<h1>$1</h1>').replace(/^> (.+)$/gm,'<blockquote><p>$1</p></blockquote>').replace(/\\*\\*([^*]+?)\\*\\*/g,'<strong>$1</strong>').replace(/\\*([^*\\n]+?)\\*/g,'<em>$1</em>').replace(/`([^`]+?)`/g,'<code class=\"inline-code\">$1</code>').replace(/^(\\*\\*[^*]+?\\*\\*)：/gm,'<div class=\"label-item\"><span class=\"label\">$1</span></div>').replace(/^([^：\\n]+?)：$/gm,'<div class=\"section-label\">$1：</div>').replace(/^(\\*\\*[^*]+?\\*\\*)：(.+)$/gm,'<div class=\"definition-item\"><span class=\"definition-label\">$1：</span><span class=\"definition-content\">$2</span></div>').replace(/^(note|note|note):(.+)$/gm,'<div class=\"note-item\"><span class=\"note-label\">$1：</span><span class=\"note-content\">$2</span></div>');content=formatLists(content);content=formatMathSymbols(content);content=formatParagraphs(content);return content;}\nfunction formatLists(content){var lines=content.split('\\n'),result=[],inOrderedList=false,inUnorderedList=false;for(var i=0;i<lines.length;i++){var line=lines[i],trimmed=line.trim();var orderedMatch=line.match(/^(\\d+)\\.\\s*(.+)$/);var unorderedMatch=line.match(/^[-*+]\\s*(.+)$/);if(orderedMatch){if(!inOrderedList){if(inUnorderedList){result.push('</ul>');inUnorderedList=false;}result.push('<ol class=\"formatted-list ordered\">');inOrderedList=true;}var listContent=orderedMatch[2].replace(/^\\*\\*([^*]+?)\\*\\*：(.+)$/,'<strong class=\"list-title\">$1：</strong><span class=\"list-content\">$2</span>');result.push('<li>'+listContent+'</li>');}else if(unorderedMatch){if(!inUnorderedList){if(inOrderedList){result.push('</ol>');inOrderedList=false;}result.push('<ul class=\"formatted-list unordered\">');inUnorderedList=true;}var listContent=unorderedMatch[1].replace(/^\\*\\*([^*]+?)\\*\\*：(.+)$/,'<strong class=\"list-title\">$1：</strong><span class=\"list-content\">$2</span>');result.push('<li>'+listContent+'</li>');}else{if(trimmed===''){if(i+1<lines.length){var nextLine=lines[i+1].trim();if(!nextLine.match(/^\\d+\\.\\s/)&&!nextLine.match(/^[-*+]\\s/)){if(inOrderedList){result.push('</ol>');inOrderedList=false;}if(inUnorderedList){result.push('</ul>');inUnorderedList=false;}}}result.push(line);}else{if(inOrderedList){result.push('</ol>');inOrderedList=false;}if(inUnorderedList){result.push('</ul>');inUnorderedList=false;}result.push(line);}}}if(inOrderedList)result.push('</ol>');if(inUnorderedList)result.push('</ul>');return result.join('\\n');}\nfunction formatMathSymbols(content){var mathSymbols={'α':'&alpha;','β':'&beta;','γ':'&gamma;','δ':'&delta;','ε':'&epsilon;','ζ':'&zeta;','η':'&eta;','θ':'&theta;','λ':'&lambda;','μ':'&mu;','π':'&pi;','σ':'&sigma;','∑':'&sum;','∏':'&prod;','∫':'&int;','∂':'&part;','∇':'&nabla;','∞':'&infin;','≤':'&le;','≥':'&ge;','≠':'&ne;','≈':'&asymp;','±':'&plusmn;','×':'&times;','÷':'&divide;','→':'&rarr;','←':'&larr;','↑':'&uarr;','↓':'&darr;','⇒':'&rArr;','⇐':'&lArr;'};var subMap={'₀':'0','₁':'1','₂':'2','₃':'3','₄':'4','₅':'5','₆':'6','₇':'7','₈':'8','₉':'9','ₖ':'k','ₜ':'t','ₛ':'s','₊':'+','₋':'-'};var supMap={'⁰':'0','¹':'1','²':'2','³':'3','⁴':'4','⁵':'5','⁶':'6','⁷':'7','⁸':'8','⁹':'9','ᵏ':'k','ᵗ':'t','ˢ':'s','⁺':'+','⁻':'-'};for(var symbol in mathSymbols){content=content.replace(new RegExp(escapeRegExp(symbol),'g'),mathSymbols[symbol]);}content=content.replace(/([a-zA-Z])₍([0-9]+)₎/g,'$1<sub>$2</sub>').replace(/([a-zA-Z])⁽([0-9]+)⁾/g,'$1<sup>$2</sup>').replace(/([a-zA-Z])([₀₁₂₃₄₅₆₇₈₉ₖₜₛ₊₋]+)/g,function(match,base,subscript){var converted=subscript.replace(/[₀₁₂₃₄₅₆₇₈₉ₖₜₛ₊₋]/g,function(char){return subMap[char]||char;});return base+'<sub>'+converted+'</sub>';}).replace(/([a-zA-Z])([⁰¹²³⁴⁵⁶⁷⁸⁹ᵏᵗˢ⁺⁻]+)/g,function(match,base,superscript){var converted=superscript.replace(/[⁰¹²³⁴⁵⁶⁷⁸⁹ᵏᵗˢ⁺⁻]/g,function(char){return supMap[char]||char;});return base+'<sup>'+converted+'</sup>';}).replace(/\\b([A-Za-z])_\\{([^}]+)\\}/g,'$1<sub>$2</sub>').replace(/\\b([A-Za-z])\\^\\{([^}]+)\\}/g,'$1<sup>$2</sup>');return content;}\nfunction formatParagraphs(content){var lines=content.split('\\n'),result=[],inParagraph=false,currentParagraph=[];for(var i=0;i<lines.length;i++){var originalLine=lines[i],line=originalLine.trim();if(line.match(/^<[^>]+>/)||line===''){if(inParagraph&&currentParagraph.length>0){var paragraphContent=currentParagraph.join(' ').trim();if(paragraphContent)result.push('<p class=\"content-paragraph indent-paragraph\">'+paragraphContent+'</p>');currentParagraph=[];inParagraph=false;}result.push(originalLine);continue;}if(line.match(/^#{1,6}\\s/)||line.match(/^\\d+\\.\\s/)||line.match(/^[-*+]\\s/)||line.match(/^>/)){if(inParagraph&&currentParagraph.length>0){var paragraphContent=currentParagraph.join(' ').trim();if(paragraphContent)result.push('<p class=\"content-paragraph indent-paragraph\">'+paragraphContent+'</p>');currentParagraph=[];inParagraph=false;}result.push(originalLine);continue;}if(line){currentParagraph.push(line);inParagraph=true;}}if(inParagraph&&currentParagraph.length>0){var paragraphContent=currentParagraph.join(' ').trim();if(paragraphContent)result.push('<p class=\"content-paragraph indent-paragraph\">'+paragraphContent+'</p>');}return result.join('\\n');}\nfunction escapeRegExp(string){return string.replace(/[.*+?^${}()|[\\]\\\\]/g,'\\\\$&');}\nfunction formatLinks(content){return content.replace(/<a\\s+href=\"(https?:\\/\\/[^\"]+)\"([^>]*)>/gi,function(match,url,attrs){return !attrs.includes('target=')?'<a href=\"'+url+'\"'+attrs+' target=\"_blank\" rel=\"noopener noreferrer\">':match;});}\nfunction escapeHtml(text){var div=document.createElement('div');div.textContent=text;return div.innerHTML;}\nfunction addTocClickEvents(){document.querySelectorAll('#tableOfContents a').forEach(function(link){link.addEventListener('click',function(e){e.preventDefault();var targetElement=document.getElementById(this.getAttribute('data-target'));if(targetElement){targetElement.scrollIntoView({behavior:'smooth',block:'start'});updateActiveTocItem(this);}});});}\nfunction addScrollHighlight(headings){var throttleTimer=null;function updateActiveHeading(){var windowHeight=window.innerHeight,activeHeading=null;for(var i=headings.length-1;i>=0;i--){var rect=headings[i].getBoundingClientRect();if(rect.top<=windowHeight*0.3){activeHeading=headings[i];break;}}if(activeHeading){var activeLink=document.querySelector('#tableOfContents a[data-target=\"'+activeHeading.id+'\"]');if(activeLink)updateActiveTocItem(activeLink);}}window.addEventListener('scroll',function(){if(throttleTimer)clearTimeout(throttleTimer);throttleTimer=setTimeout(updateActiveHeading,100);});updateActiveHeading();}\nfunction updateActiveTocItem(activeLink){document.querySelectorAll('#tableOfContents a').forEach(function(link){link.classList.remove('active');});if(activeLink){activeLink.classList.add('active');scrollTocToActiveItem(activeLink);}}\nfunction scrollTocToActiveItem(activeLink){var tocCard=document.getElementById('tocCard');if(!activeLink||!tocCard)return;var cardRect=tocCard.getBoundingClientRect();var activeRect=activeLink.getBoundingClientRect();var relativeTop=activeRect.top-cardRect.top;var relativeBottom=activeRect.bottom-cardRect.bottom;var cardStyle=window.getComputedStyle(tocCard);var paddingTop=parseFloat(cardStyle.paddingTop)||0;var titleHeight=tocCard.querySelector('h5')?tocCard.querySelector('h5').offsetHeight:0;var contentTop=paddingTop+titleHeight;if(relativeTop<contentTop){tocCard.scrollTop+=(relativeTop-contentTop-10);}else if(relativeBottom>0){tocCard.scrollTop+=(relativeBottom+10);}}\nfunction optimizeImages(){document.querySelectorAll('.article-content img').forEach(function(img){img.loading='lazy';img.onerror=function(){this.style.display='none';};if(!img.alt)img.alt='Article with pictures';});}\nfunction initReadingProgress(){var progressBar=document.getElementById('readingProgress');function updateProgress(){var scrollTop=window.pageYOffset||document.documentElement.scrollTop;var docHeight=document.documentElement.scrollHeight-window.innerHeight;progressBar.style.width=Math.min((scrollTop/docHeight)*100,100)+'%';}window.addEventListener('scroll',updateProgress);updateProgress();}\nfunction initTableOfContents(){var content=document.querySelector('.article-content');if(!content)return;var headings=content.querySelectorAll('h1, h2, h3, h4, h5, h6');if(headings.length<=1)return;var tocCard=document.getElementById('tocCard');var tocList=document.getElementById('tableOfContents');var html='',counters=[0,0,0,0,0,0];headings.forEach(function(heading,index){var id='heading-'+index;heading.id=id;var level=parseInt(heading.tagName.substring(1));var indent=(level-1)*15;counters[level-1]++;for(var i=level;i<counters.length;i++)counters[i]=0;var numberText='';for(var i=0;i<level;i++){if(counters[i]>0)numberText+=(numberText?'.':'')+counters[i];}numberText+='.';html+='<a href=\"#'+id+'\" class=\"toc-item toc-level-'+level+'\" style=\"padding-left: '+indent+'px;\" data-target=\"'+id+'\"><span class=\"toc-number\">'+numberText+'</span> <span class=\"toc-text\">'+heading.textContent+'</span></a>';});tocList.innerHTML=html;tocCard.style.display='block';addTocClickEvents();addScrollHighlight(headings);tocList.addEventListener('click',function(e){if(e.target.tagName==='A'){e.preventDefault();var targetElement=document.getElementById(e.target.getAttribute('data-target'));if(targetElement){targetElement.scrollIntoView({behavior:'smooth',block:'start'});targetElement.style.transition='all 0.3s ease';targetElement.style.backgroundColor='#e3f2fd';targetElement.style.padding='10px';targetElement.style.borderRadius='6px';setTimeout(function(){targetElement.style.backgroundColor='';targetElement.style.padding='';targetElement.style.borderRadius='';},2000);}}});}\nfunction shareToWeibo(){window.open('https://service.weibo.com/share/share.php?url='+encodeURIComponent(window.location.href)+'&title='+encodeURIComponent(document.title),'_blank');}\nfunction copyUrl(){navigator.clipboard.writeText(window.location.href).then(function(){alert('Iki gishushanyo cyashyizwe ku mbuga nkoranyambaga');}).catch(function(){var textArea=document.createElement('textarea');textArea.value=window.location.href;textArea.style.cssText='position:fixed;left:-999999px;top:-999999px';document.body.appendChild(textArea);textArea.focus();textArea.select();try{alert(document.execCommand('copy')?'Iki gishushanyo cyashyizwe ku mbuga nkoranyambaga':'If the copy fails, please copy the link manually');}catch(err){alert('If the copy fails, please copy the link manually');}document.body.removeChild(textArea);});}\nfunction printArticle(){var printWindow=window.open('','_blank');var articleContent=document.querySelector('.article-detail').innerHTML;var articleTitle=document.querySelector('.article-title').textContent;printWindow.document.write('<!DOCTYPE html><html lang=\"rw\"><title>'+articleTitle+'</title><style>body{font-family:-apple-system,BlinkMacSystemFont,\"Segoe UI\",Roboto,\"Helvetica Neue\",Arial,sans-serif;line-height:1.6;color:#333;max-width:800px;margin:0 auto;padding:20px}h1,h2,h3,h4,h5,h6{color:#1a1c1f;margin-top:1.5em;margin-bottom:0.5em}h1{font-size:2em;border-bottom:2px solid #e9ecef;padding-bottom:0.3em}h2{font-size:1.5em;border-bottom:1px solid #e9ecef;padding-bottom:0.3em}h3{font-size:1.25em}p{margin-bottom:1em}pre{background:#f8f9fa;border:1px solid #e9ecef;border-radius:4px;padding:1em;overflow-x:auto;font-family:Consolas,Monaco,\"Courier New\",monospace}code{background:#f1f3f4;padding:2px 4px;border-radius:3px;font-family:Consolas,Monaco,\"Courier New\",monospace}blockquote{border-left:4px solid #1764ff;background:#f8f9fa;padding:1em;margin:1em 0;border-radius:0 4px 4px 0}img{max-width:100%;height:auto}table{width:100%;border-collapse:collapse;margin:1em 0}th,td{border:1px solid #ddd;padding:8px;text-align:left}th{background-color:#f2f2f2}.article-meta,.article-footer,.share-buttons{display:none}</style><body>'+articleContent+'</body></html>');printWindow.document.close();printWindow.focus();setTimeout(function(){printWindow.print();printWindow.close();},250);}\nfunction improvedStickyPosition(){var sidebarCards=document.querySelectorAll('.sidebar-card');var originalPositions=[],isFixed=[];sidebarCards.forEach(function(card,index){var rect=card.getBoundingClientRect();originalPositions[index]=rect.top+window.pageYOffset;isFixed[index]=false;var placeholder=document.createElement('div');placeholder.style.cssText='height:'+card.offsetHeight+'px;display:none';placeholder.className='sticky-placeholder';card.parentNode.insertBefore(placeholder,card.nextSibling);});function updateStickyPosition(){var scrollTop=window.pageYOffset||document.documentElement.scrollTop;sidebarCards.forEach(function(card,index){var originalTop=originalPositions[index];var stickyTop=index>0?(document.querySelector('.toc-card').style.position==='fixed'?60+document.querySelector('.toc-card').offsetHeight+8:80):60;var placeholder=card.nextSibling;if(scrollTop>originalTop-stickyTop){if(!isFixed[index]){var colContainer=card.closest('[class*=\"col-\"]');var colRect=colContainer.getBoundingClientRect();var colStyle=window.getComputedStyle(colContainer);var paddingLeft=parseFloat(colStyle.paddingLeft)||0;var paddingRight=parseFloat(colStyle.paddingRight)||0;card.style.cssText='position:fixed;top:'+stickyTop+'px;left:'+(colRect.left+paddingLeft)+'px;width:'+(colRect.width-paddingLeft-paddingRight)+'px;z-index:9999;box-sizing:border-box';card.classList.add('fixed');placeholder.style.display='block';isFixed[index]=true;}}else if(isFixed[index]){card.style.cssText='';card.classList.remove('fixed');placeholder.style.display='none';isFixed[index]=false;}});}window.addEventListener('scroll',updateStickyPosition);window.addEventListener('resize',function(){setTimeout(function(){sidebarCards.forEach(function(card,index){if(isFixed[index]){var colContainer=card.closest('[class*=\"col-\"]');var colRect=colContainer.getBoundingClientRect();var colStyle=window.getComputedStyle(colContainer);var paddingLeft=parseFloat(colStyle.paddingLeft)||0;var paddingRight=parseFloat(colStyle.paddingRight)||0;card.style.left=(colRect.left+paddingLeft)+'px';card.style.width=(colRect.width-paddingLeft-paddingRight)+'px';}else{originalPositions[index]=card.getBoundingClientRect().top+window.pageYOffset;}});},100);});updateStickyPosition();}\ndocument.addEventListener('DOMContentLoaded',function(){setTimeout(improvedStickyPosition,1000);});\n    </script>\n\r\n            </div>\r\n        </form>\r\n\r\n        <div class=\"fixed-icon\">\r\n            <div style=\"width: 120px; position: relative; left: 1rem; top: -12px;\">\r\n            </div>\r\n            <a id=\"webchat\" class=\"wx-con disa\" href=\"http://wpa.qq.com/msgrd?v=3&uin=365833440&site=qq&menu=yes\" target=\"_blank\">\r\n                <img class=\"wechat-ico\" src=\"site/image/icon/qq.svg\" alt=\"OCR assistant QQ online customer service\" />\r\n                <div class=\"wx-text\">QQ Customer Service (365833440)</div>\r\n                <label class=\"badge badge-danger\" style=\"background: rgb(255, 34, 122); display: inline-block;\">1</label>\r\n            </a>\r\n\r\n            <a class=\"wx-con\" href=\"https://qm.qq.com/cgi-bin/qm/qr?k=bohNy9GMdgD5bsYrB-fcIjy-s6Ot3zOg&jump_from=webapi&qr=1\" target=\"_blank\">\r\n                <img class=\"wechat-ico\" src=\"site/image/icon/qq.svg\" alt=\"OCR assistant QQ user communication group\" />\r\n                <div class=\"wx-text\">QQ Group (100029010)</div>\r\n                <label class=\"badge badge-danger\" style=\"background: rgb(255, 34, 122); display: inline-block;\">2</label>\r\n            </a>\r\n            <a href=\"mailto:<EMAIL>\" class=\"wx-con\">\r\n                <img class=\"wechat-ico\" src=\"site/image/icon/im.svg\" alt=\"OCR assistant contact customer service by email\" />\r\n                <div class=\"wx-text\" style=\"width: 240px; text-align: left; left: -15.3rem; padding: 10px 20px;\">\r\n                    <div style=\"font-size: 15px\">Email: <EMAIL></div>\r\n                    <hr style=\"background: #c1c1c1; margin: 10px 10px 6px 0;\" />\r\n                    <span style=\"font-size: 13px; color: #c1c1c1\">Murakoze cyane ku bitekerezo byanyu n'ibitekerezo byanyu!</span>\r\n                </div>\r\n                <label class=\"badge badge-danger\" style=\"background: rgb(255, 34, 122); display: inline-block;\">3</label>\r\n            </a>\r\n        </div>\r\n\r\n        <style>\r\n            .content {\r\n                width: 100%;\r\n                padding: 0 !important;\r\n                margin: 0 auto\r\n            }\r\n\r\n            h3.h6 {\r\n                font-size: 1rem !important;\r\n                font-weight: bold !important;\r\n                line-height: 1.2 !important;\r\n                margin-bottom: .5rem;\r\n                font-family: inherit\r\n            }\r\n        </style>\r\n        <footer class=\"page-footer\">\r\n            <div class=\"container\">\r\n                <div class=\"row foot-cont pt-4 no-gutters\" style=\"border-top: 1px solid #e1e6ed; max-width: 100%\">\r\n                    <div class=\"col-lg-12 hu-an p-0 mb-3 mt-3 text-l color-gray\" style=\"text-align: center;\">\r\n                        OCR text recognition assistant&nbsp;©️ 2025 ALL RIGHTS RESERVED. All rights reserved&nbsp;&nbsp;&nbsp;|&nbsp;&nbsp;&nbsp;\r\n                        <a href=\"PrivacyPolicy.aspx\" style=\"text-decoration: underline;\">Amasezerano y'ibanga</a>&nbsp;&nbsp;&nbsp;|&nbsp;&nbsp;&nbsp;\r\n                        <a href=\"UserAgreement.aspx\" style=\"text-decoration: underline;\">Amasezerano y'umukoresha</a>&nbsp;&nbsp;&nbsp;|&nbsp;&nbsp;&nbsp;\r\n                        <a href=\"Status.aspx\" style=\"text-decoration: underline;\">Imiterere ya serivisi</a>&nbsp;&nbsp;&nbsp;|&nbsp;&nbsp;&nbsp;\r\n                        <a class=\"mb-2 color-gray\" href=\"https://beian.miit.gov.cn/\" target=\"_blank\" style=\"text-decoration: underline;\">E ICP Preparation No. 2021012692</a>\r\n                    </div>\r\n                </div>\r\n            </div>\r\n        </footer>\r\n        <style>\r\n            #translate > .translateSelectLanguage {\r\n                right: 2rem;\r\n                font-size: 1rem;\r\n                width: 150px;\r\n                padding: .3rem;\r\n                margin-right: 20px;\r\n                border: 1px solid #C9C9C9;\r\n                background: #fff;\r\n                color: #555\r\n            }\r\n        </style>\r\n    </div>\r\n    <script>\r\n        document.addEventListener('DOMContentLoaded', function () {\r\n            var setScroll = function (active) {\r\n                forEach(qa('.v4_header_pc,.v4_header_mob'), function (e) {\r\n                    if (active) addClass(e, 'scrollActive'); else removeClass(e, 'scrollActive');\r\n                });\r\n            };\r\n            if (getScrollY() > 60) setScroll(1);\r\n            window.addEventListener('scroll', function () { setScroll(getScrollY() > 60); });\r\n\r\n            forEach(qa('.v4_header_pc'), function (h) {\r\n                h.addEventListener('mouseenter', function () { addClass(h, 'active'); });\r\n                h.addEventListener('mouseleave', function () { removeClass(h, 'active'); });\r\n            });\r\n\r\n            var menuTimer;\r\n            var hideMenu = function (menuId) {\r\n                var m = document.getElementById(menuId);\r\n                if (m) {\r\n                    m.style.cssText = 'visibility:hidden;height:0;display:none';\r\n                    var n = q('.nav-drown-con', m);\r\n                    if (n) { n.style.cssText = 'height:0;opacity:0;transform:translate(0,-100%)'; }\r\n                }\r\n            };\r\n\r\n            forEach(qa('ul.top-nav>li'), function (i) {\r\n                i.addEventListener('mouseenter', function () {\r\n                    clearTimeout(menuTimer);\r\n                    var l = this.querySelector('a'), t = this.querySelector('.triangle'), s = l ? l.getAttribute('data-sub') : null;\r\n                    if (l) addClass(l, 'active'); if (t) addClass(t, 'active');\r\n                    if (s) {\r\n                        var m = document.getElementById(s);\r\n                        if (m) {\r\n                            m.style.cssText = 'display:block;visibility:visible';\r\n                            var siblings = m.parentNode ? toArray(m.parentNode.children) : [];\r\n                            forEach(siblings, function (c) { if (c !== m) c.style.display = 'none'; });\r\n                            var n = q('.nav-drown-con', m);\r\n                            if (n) { n.style.cssText = 'height:100%;opacity:1;transform:translate(0,0)'; }\r\n\r\n                            m.addEventListener('mouseenter', function () { clearTimeout(menuTimer); });\r\n                            m.addEventListener('mouseleave', function () { menuTimer = setTimeout(function () { hideMenu(s); }, 100); });\r\n                        }\r\n                    }\r\n                });\r\n                i.addEventListener('mouseleave', function () {\r\n                    var l = this.querySelector('a'), t = this.querySelector('.triangle'), s = l ? l.getAttribute('data-sub') : null;\r\n                    if (l) removeClass(l, 'active'); if (t) removeClass(t, 'active');\r\n                    if (s) { menuTimer = setTimeout(function () { hideMenu(s); }, 100); }\r\n                });\r\n            });\r\n\r\n            var r = q('.v4_header_mob .right-menu');\r\n            if (r) r.addEventListener('click', function () {\r\n                var arr = [r, q('.v4_header_mob'), q('#nav_products'), q('.mob-nav-content .sidebar-fix')];\r\n                for (var i = 0; i < arr.length; i++) {\r\n                    var e = arr[i];\r\n                    if (e) {\r\n                        var name = (i === 0 ? 'active' : (i === 1 ? 'active' : (i === 2 ? 'active1' : 'fixedActiveBg')));\r\n                        toggleClass(e, name);\r\n                    }\r\n                }\r\n                var f = q('.mob-nav-content .sidebar-fix');\r\n                if (f) {\r\n                    toggleClass(f, 'show');\r\n                    document.body.style.overflow = hasClass(f, 'show') ? 'hidden' : 'scroll';\r\n                }\r\n            });\r\n\r\n            document.addEventListener('click', function (e) {\r\n                e = e || window.event;\r\n                var target = e.target || e.srcElement;\r\n                var m = closest(target, '.sidebar-fix-left .mob-nav-item');\r\n                if (m) {\r\n                    var p = m.parentNode;\r\n                    if (p) forEach(toArray(p.children), function (c) { removeClass(c, 'active'); });\r\n                    addClass(m, 'active');\r\n                    var d = m.getAttribute('data');\r\n                    if (d) forEach(qa('.sidebar-fix-rigth .sub-nav'), function (n, i) { n.style.display = (i == d ? 'block' : 'none'); });\r\n                }\r\n            });\r\n        });\r\n    </script>\r\n</body>\r\n</html>\r\n\r\n\t            <script type=\"text/javascript\">\r\n                    var _hmt = _hmt || [];\r\n                    (function () {\r\n                        var hm = document.createElement('script');hm.async=1;\r\n                        hm.src = 'https://hm.baidu.com/hm.js?5ee048d7caaf0d439cf16309a1687754';\r\n                        var s = document.getElementsByTagName('script')[0];s.parentNode.insertBefore(hm, s);\r\n                    })();\r\n                </script><div id=\"translate\" class=\"ignore\" style=\"display: none\"></div><script src=\"/static/js/translate.js\"></script>"
﻿"<!DOCTYPE html>\r\n<html xmlns=\"http://www.w3.org/1999/xhtml\" lang=\"ru\">\r\n<head><link rel=\"canonical\" href=\"https://ocr.oldfish.cn/ru/Article.aspx?id=81&slug=rnn-sequence-modeling\" /><link rel=\"alternate\" href=\"https://ocr.oldfish.cn/Article.aspx?id=81&slug=rnn-sequence-modeling\" hreflang=\"x-default\" />\r\n<link rel=\"alternate\" href=\"https://ocr.oldfish.cn/Article.aspx?id=81&slug=rnn-sequence-modeling\" hreflang=\"zh\" />\r\n<link rel=\"alternate\" href=\"https://ocr.oldfish.cn/Article.aspx?id=81&slug=rnn-sequence-modeling\" hreflang=\"zh\" />\r\n<link rel=\"alternate\" href=\"https://ocr.oldfish.cn/zh-Hant/Article.aspx?id=81&slug=rnn-sequence-modeling\" hreflang=\"zh-tw\" />\r\n<link rel=\"alternate\" href=\"https://ocr.oldfish.cn/yue/Article.aspx?id=81&slug=rnn-sequence-modeling\" hreflang=\"zh-yue\" />\r\n<link rel=\"alternate\" href=\"https://ocr.oldfish.cn/en/Article.aspx?id=81&slug=rnn-sequence-modeling\" hreflang=\"en\" />\r\n<link rel=\"alternate\" href=\"https://ocr.oldfish.cn/es/Article.aspx?id=81&slug=rnn-sequence-modeling\" hreflang=\"es\" />\r\n<link rel=\"alternate\" href=\"https://ocr.oldfish.cn/hi/Article.aspx?id=81&slug=rnn-sequence-modeling\" hreflang=\"hi\" />\r\n<link rel=\"alternate\" href=\"https://ocr.oldfish.cn/ar/Article.aspx?id=81&slug=rnn-sequence-modeling\" hreflang=\"ar\" />\r\n<link rel=\"alternate\" href=\"https://ocr.oldfish.cn/pt/Article.aspx?id=81&slug=rnn-sequence-modeling\" hreflang=\"pt\" />\r\n<link rel=\"alternate\" href=\"https://ocr.oldfish.cn/bn/Article.aspx?id=81&slug=rnn-sequence-modeling\" hreflang=\"bn\" />\r\n<link rel=\"alternate\" href=\"https://ocr.oldfish.cn/ru/Article.aspx?id=81&slug=rnn-sequence-modeling\" hreflang=\"ru\" />\r\n<link rel=\"alternate\" href=\"https://ocr.oldfish.cn/ja/Article.aspx?id=81&slug=rnn-sequence-modeling\" hreflang=\"ja\" />\r\n<link rel=\"alternate\" href=\"https://ocr.oldfish.cn/pa/Article.aspx?id=81&slug=rnn-sequence-modeling\" hreflang=\"pa\" />\r\n<link rel=\"alternate\" href=\"https://ocr.oldfish.cn/de/Article.aspx?id=81&slug=rnn-sequence-modeling\" hreflang=\"de\" />\r\n<link rel=\"alternate\" href=\"https://ocr.oldfish.cn/fr/Article.aspx?id=81&slug=rnn-sequence-modeling\" hreflang=\"fr\" />\r\n<link rel=\"alternate\" href=\"https://ocr.oldfish.cn/mr/Article.aspx?id=81&slug=rnn-sequence-modeling\" hreflang=\"mr\" />\r\n<link rel=\"alternate\" href=\"https://ocr.oldfish.cn/te/Article.aspx?id=81&slug=rnn-sequence-modeling\" hreflang=\"te\" />\r\n<link rel=\"alternate\" href=\"https://ocr.oldfish.cn/vi/Article.aspx?id=81&slug=rnn-sequence-modeling\" hreflang=\"vi\" />\r\n<link rel=\"alternate\" href=\"https://ocr.oldfish.cn/ko/Article.aspx?id=81&slug=rnn-sequence-modeling\" hreflang=\"ko\" />\r\n<link rel=\"alternate\" href=\"https://ocr.oldfish.cn/ta/Article.aspx?id=81&slug=rnn-sequence-modeling\" hreflang=\"ta\" />\r\n<link rel=\"alternate\" href=\"https://ocr.oldfish.cn/ur/Article.aspx?id=81&slug=rnn-sequence-modeling\" hreflang=\"ur\" />\r\n<link rel=\"alternate\" href=\"https://ocr.oldfish.cn/tr/Article.aspx?id=81&slug=rnn-sequence-modeling\" hreflang=\"tr\" />\r\n<link rel=\"alternate\" href=\"https://ocr.oldfish.cn/it/Article.aspx?id=81&slug=rnn-sequence-modeling\" hreflang=\"it\" />\r\n<link rel=\"alternate\" href=\"https://ocr.oldfish.cn/gu/Article.aspx?id=81&slug=rnn-sequence-modeling\" hreflang=\"gu\" />\r\n<link rel=\"alternate\" href=\"https://ocr.oldfish.cn/pl/Article.aspx?id=81&slug=rnn-sequence-modeling\" hreflang=\"pl\" />\r\n<link rel=\"alternate\" href=\"https://ocr.oldfish.cn/uk/Article.aspx?id=81&slug=rnn-sequence-modeling\" hreflang=\"uk\" />\r\n<link rel=\"alternate\" href=\"https://ocr.oldfish.cn/ms/Article.aspx?id=81&slug=rnn-sequence-modeling\" hreflang=\"ms\" />\r\n<link rel=\"alternate\" href=\"https://ocr.oldfish.cn/id/Article.aspx?id=81&slug=rnn-sequence-modeling\" hreflang=\"id\" />\r\n<link rel=\"alternate\" href=\"https://ocr.oldfish.cn/ml/Article.aspx?id=81&slug=rnn-sequence-modeling\" hreflang=\"ml\" />\r\n<link rel=\"alternate\" href=\"https://ocr.oldfish.cn/kn/Article.aspx?id=81&slug=rnn-sequence-modeling\" hreflang=\"kn\" />\r\n<link rel=\"alternate\" href=\"https://ocr.oldfish.cn/fa/Article.aspx?id=81&slug=rnn-sequence-modeling\" hreflang=\"fa\" />\r\n<link rel=\"alternate\" href=\"https://ocr.oldfish.cn/nl/Article.aspx?id=81&slug=rnn-sequence-modeling\" hreflang=\"nl\" />\r\n<link rel=\"alternate\" href=\"https://ocr.oldfish.cn/th/Article.aspx?id=81&slug=rnn-sequence-modeling\" hreflang=\"th\" />\r\n<link rel=\"alternate\" href=\"https://ocr.oldfish.cn/sw/Article.aspx?id=81&slug=rnn-sequence-modeling\" hreflang=\"sw\" />\r\n<link rel=\"alternate\" href=\"https://ocr.oldfish.cn/ro/Article.aspx?id=81&slug=rnn-sequence-modeling\" hreflang=\"ro\" />\r\n<link rel=\"alternate\" href=\"https://ocr.oldfish.cn/my/Article.aspx?id=81&slug=rnn-sequence-modeling\" hreflang=\"my\" />\r\n<link rel=\"alternate\" href=\"https://ocr.oldfish.cn/or/Article.aspx?id=81&slug=rnn-sequence-modeling\" hreflang=\"or\" />\r\n<link rel=\"alternate\" href=\"https://ocr.oldfish.cn/he/Article.aspx?id=81&slug=rnn-sequence-modeling\" hreflang=\"he\" />\r\n<link rel=\"alternate\" href=\"https://ocr.oldfish.cn/am/Article.aspx?id=81&slug=rnn-sequence-modeling\" hreflang=\"am\" />\r\n<link rel=\"alternate\" href=\"https://ocr.oldfish.cn/fil/Article.aspx?id=81&slug=rnn-sequence-modeling\" hreflang=\"fil\" />\r\n<link rel=\"alternate\" href=\"https://ocr.oldfish.cn/sv/Article.aspx?id=81&slug=rnn-sequence-modeling\" hreflang=\"sv\" />\r\n<link rel=\"alternate\" href=\"https://ocr.oldfish.cn/el/Article.aspx?id=81&slug=rnn-sequence-modeling\" hreflang=\"el\" />\r\n<link rel=\"alternate\" href=\"https://ocr.oldfish.cn/cs/Article.aspx?id=81&slug=rnn-sequence-modeling\" hreflang=\"cs\" />\r\n<link rel=\"alternate\" href=\"https://ocr.oldfish.cn/hu/Article.aspx?id=81&slug=rnn-sequence-modeling\" hreflang=\"hu\" />\r\n<link rel=\"alternate\" href=\"https://ocr.oldfish.cn/be/Article.aspx?id=81&slug=rnn-sequence-modeling\" hreflang=\"be\" />\r\n<link rel=\"alternate\" href=\"https://ocr.oldfish.cn/si/Article.aspx?id=81&slug=rnn-sequence-modeling\" hreflang=\"si\" />\r\n<link rel=\"alternate\" href=\"https://ocr.oldfish.cn/ne/Article.aspx?id=81&slug=rnn-sequence-modeling\" hreflang=\"ne\" />\r\n<link rel=\"alternate\" href=\"https://ocr.oldfish.cn/km/Article.aspx?id=81&slug=rnn-sequence-modeling\" hreflang=\"km\" />\r\n<link rel=\"alternate\" href=\"https://ocr.oldfish.cn/sk/Article.aspx?id=81&slug=rnn-sequence-modeling\" hreflang=\"sk\" />\r\n<link rel=\"alternate\" href=\"https://ocr.oldfish.cn/bg/Article.aspx?id=81&slug=rnn-sequence-modeling\" hreflang=\"bg\" />\r\n<link rel=\"alternate\" href=\"https://ocr.oldfish.cn/fr-ca/Article.aspx?id=81&slug=rnn-sequence-modeling\" hreflang=\"fr-ca\" />\r\n<link rel=\"alternate\" href=\"https://ocr.oldfish.cn/ha/Article.aspx?id=81&slug=rnn-sequence-modeling\" hreflang=\"ha\" />\r\n<link rel=\"alternate\" href=\"https://ocr.oldfish.cn/yo/Article.aspx?id=81&slug=rnn-sequence-modeling\" hreflang=\"yo\" />\r\n<link rel=\"alternate\" href=\"https://ocr.oldfish.cn/ig/Article.aspx?id=81&slug=rnn-sequence-modeling\" hreflang=\"ig\" />\r\n<link rel=\"alternate\" href=\"https://ocr.oldfish.cn/ku/Article.aspx?id=81&slug=rnn-sequence-modeling\" hreflang=\"ku\" />\r\n<link rel=\"alternate\" href=\"https://ocr.oldfish.cn/rw/Article.aspx?id=81&slug=rnn-sequence-modeling\" hreflang=\"rw\" />\r\n<link rel=\"alternate\" href=\"https://ocr.oldfish.cn/ca/Article.aspx?id=81&slug=rnn-sequence-modeling\" hreflang=\"ca\" />\r\n<link rel=\"alternate\" href=\"https://ocr.oldfish.cn/da/Article.aspx?id=81&slug=rnn-sequence-modeling\" hreflang=\"da\" />\r\n<link rel=\"alternate\" href=\"https://ocr.oldfish.cn/fi/Article.aspx?id=81&slug=rnn-sequence-modeling\" hreflang=\"fi\" />\r\n<link rel=\"alternate\" href=\"https://ocr.oldfish.cn/nb/Article.aspx?id=81&slug=rnn-sequence-modeling\" hreflang=\"nb\" />\r\n<link rel=\"alternate\" href=\"https://ocr.oldfish.cn/hr/Article.aspx?id=81&slug=rnn-sequence-modeling\" hreflang=\"hr\" />\r\n<link rel=\"alternate\" href=\"https://ocr.oldfish.cn/sr-Cyrl/Article.aspx?id=81&slug=rnn-sequence-modeling\" hreflang=\"sr-cyrl\" />\r\n<link rel=\"alternate\" href=\"https://ocr.oldfish.cn/sr-Latn/Article.aspx?id=81&slug=rnn-sequence-modeling\" hreflang=\"sr-latn\" />\r\n<link rel=\"alternate\" href=\"https://ocr.oldfish.cn/sq/Article.aspx?id=81&slug=rnn-sequence-modeling\" hreflang=\"sq\" />\r\n<link rel=\"alternate\" href=\"https://ocr.oldfish.cn/so/Article.aspx?id=81&slug=rnn-sequence-modeling\" hreflang=\"so\" />\r\n<link rel=\"alternate\" href=\"https://ocr.oldfish.cn/zu/Article.aspx?id=81&slug=rnn-sequence-modeling\" hreflang=\"zu\" />\r\n<link rel=\"alternate\" href=\"https://ocr.oldfish.cn/ka/Article.aspx?id=81&slug=rnn-sequence-modeling\" hreflang=\"ka\" />\r\n<meta http-equiv=\"Content-Type\" content=\"text/html; charset=UTF-8\" /><meta http-equiv=\"X-UA-Compatible\" content=\"IE=Edge,chrome=1\" /><meta name=\"renderer\" content=\"webkit\" /><meta name=\"viewport\" content=\"width=device-width, initial-scale=1.0, maximum-scale=5.0\" /><link rel=\"icon\" href=\"/favicon.ico\" type=\"image/x-icon\" /><meta name=\"robots\" content=\"all\" /><link rel=\"preconnect\" href=\"https://scm-file-new-1301864755.cos.ap-beijing.myqcloud.com\" /><link rel=\"preconnect\" href=\"https://cdn.oldfish.cn\" /><link rel=\"dns-prefetch\" href=\"https://hm.baidu.com\" /><link rel=\"dns-prefetch\" href=\"https://cdn.oldfish.cn\" /><link rel=\"preload\" href=\"/site/css/site.css\" as=\"style\" />\r\n<script>function toArray(l){var a=[];for(var i=0;i<(l?l.length:0);i++)a.push(l[i]);return a;}function forEach(l,f){for(var i=0,n=(l?l.length:0);i<n;i++)f(l[i],i);}function hasClass(e,c){if(!e||!c)return false;return(' '+e.className+' ').indexOf(' '+c+' ')>-1;}function addClass(e,c){if(!e||!c||hasClass(e,c))return;e.className=(e.className?e.className+' ':'')+c;}function removeClass(e,c){if(!e||!c)return;var u=' '+e.className+' ';while(u.indexOf(' '+c+' ')>-1){u=u.replace(' '+c+' ',' ');}e.className=u.replace(/^\\s+|\\s+$/g,'');}function toggleClass(e,c){if(!e||!c)return;if(hasClass(e,c))removeClass(e,c);else addClass(e,c);}function matchesSelector(e,s){if(!e||e.nodeType!==1)return false;if(e.matches)return e.matches(s);var p=e.parentNode||document;var m=toArray(p.querySelectorAll(s));for(var i=0;i<m.length;i++)if(m[i]===e)return true;return false;}function closest(e,s){var n=e;while(n&&n.nodeType===1){if(matchesSelector(n,s))return n;n=n.parentNode;}return null;}function safeRemove(e){if(e&&e.parentNode)e.parentNode.removeChild(e);}function qa(s,r){return toArray((r||document).querySelectorAll(s));}function q(s,r){return(r||document).querySelector(s);}function xhrGet(u,o,e){try{var x=new XMLHttpRequest();x.open('GET',u,true);x.onreadystatechange=function(){if(x.readyState===4){if(x.status>=200&&x.status<300)o&&o(x.responseText);else e&&e(x);}};x.send(null);}catch(r){e&&e(r);}}function getScrollY(){return window.pageYOffset||document.documentElement.scrollTop||document.body.scrollTop||0;}function getOrigin(){return window.location.protocol+'//'+window.location.host;}</script>\r\n<script>var isWebP=(function(){try{return document.createElement('canvas').toDataURL('image/webp').indexOf('data:image/webp')===0;}catch(e){return false;}})();document.addEventListener('DOMContentLoaded',function(){var c=document.getElementById('user-login-container');if(c){var u=c.getAttribute('data-url');if(u){xhrGet(u,function(h){c.innerHTML=h;forEach(qa('a',c),function(l){l.style.display='inline-block';l.style.marginLeft='10px';});});}}});</script>\r\n    \n    \n\n    <meta name=\"description\" content=\"Погрузитесь в применение RNN, LSTM, GRU в OCR. Подробный анализ принципов моделирования последовательностей, решения градиентных задач и преимуществ двунаправленных РНС.\" />\n    <meta name=\"keywords\" content=\"RNN, LSTM, GRU, Моделирование последовательностей, Градиентное исчезновение, Двунаправленная RNN, Механизм внимания, CRNN, OCR, OCR распознавание текста, преобразование изображения в текст, Технология OCR\" />\n    <meta property=\"og:title\" content=\"【Серия OCR с глубоким обучением·4】Рекуррентные нейронные сети и моделирование последовательностей\" />\n    <meta property=\"og:description\" content=\"Погрузитесь в применение RNN, LSTM, GRU в OCR. Подробный анализ принципов моделирования последовательностей, решения градиентных задач и преимуществ двунаправленных РНС.\" />\n    <meta property=\"og:type\" content=\"article\" />\n    <meta property=\"og:url\" content=\"https://ocr.oldfish.cn/Article.aspx?id=81&slug=rnn-sequence-modeling&lang=zh-Hans&_translation_source=true\" />\n    <meta property=\"og:site_name\" content=\"Помощник по распознаванию текста OCR\" />\n    <meta name=\"twitter:card\" content=\"summary\" />\n    <meta name=\"twitter:title\" content=\"【Серия OCR с глубоким обучением·4】Рекуррентные нейронные сети и моделирование последовательностей\" />\n    <meta name=\"twitter:description\" content=\"Погрузитесь в применение RNN, LSTM, GRU в OCR. Подробный анализ принципов моделирования последовательностей, решения градиентных задач и преимуществ двунаправленных РНС.\" />\n\n    \n    <script type=\"application/ld+json\">\n    {\n        \"@context\": \"https://schema.org\",\n        \"@type\": \"Article\",\n        \"headline\": \"【Deep learning OCR Series 4] Рекуррентные нейронные сети и моделирование последовательностей\",\n        \"description\": \"Погрузитесь в применение RNN, LSTM, GRU в OCR. Подробный анализ принципов моделирования последовательностей, решения градиентных задач и преимуществ двунаправленных РНС。\",\n        \"publisher\": {\n            \"@type\": \"Organization\",\n            \"name\": \"Команда помощников по распознаванию текста OCR\"\n        },\n        \"datePublished\": \"2025-08-19T06:32:05Z\",\n        \"dateModified\": \"2025-08-19T06:32:05Z\",\n        \"mainEntityOfPage\": {\n            \"@type\": \"WebPage\",\n            \"@id\": \"https://ocr.oldfish.cn/Article.aspx?id=81&slug=rnn-sequence-modeling&lang=zh-Hans&_translation_source=true\"\n        }\n    }\n    </script>\n    \n    <style>\n        .reading-progress { position: fixed; top: 0; left: 0; width: 0%; height: 3px; background: linear-gradient(90deg, #1764ff, #0d47a1); z-index: 9999; transition: width 0.2s ease; box-shadow: 0 1px 3px rgba(23, 100, 255, 0.3); }\n        .home-container.root { overflow: visible !important; }\n        .article-detail-container { background: #f5f7fc; min-height: calc(100vh - 60px); padding: 80px 0 60px; position: relative; overflow: visible; }\n        .article-detail-container .row { margin: 0 -12px; align-items: flex-start !important; min-height: 100vh !important; }\n        .article-detail-container .row > [class*=\"col-\"] { padding: 0 12px; box-sizing: border-box; min-width: 0; }\n        .article-detail-container .row > .col-lg-8, .article-detail-container .row > .col-xl-9 { overflow: hidden; }\n        .article-detail-container .row > .col-lg-4, .article-detail-container .row > .col-xl-3 { overflow: visible !important; height: 100% !important; min-height: 100% !important; position: static !important; display: block !important; transform: none !important; will-change: auto !important; contain: none !important; perspective: none !important; filter: none !important; }\n        .article-detail { background: #fff; border-radius: 8px; padding: 1.5rem; margin-bottom: 2rem; box-shadow: 0 2px 10px rgba(0,0,0,0.1); width: 100%; box-sizing: border-box; }\n        .article-title { font-size: 1.7rem; color: #1a1c1f; line-height: 1.3; margin-bottom: 1.5rem; font-weight: 600; }\n        .article-meta { display: flex; align-items: center; flex-wrap: nowrap; gap: 1.2rem; color: #6c757d; font-size: 0.88rem; margin-bottom: 2rem; padding: 1.2rem 1.4rem; background: linear-gradient(135deg, #f8f9fa 0%, #ffffff 100%); border-radius: 12px; border-left: 4px solid #1764ff; box-shadow: 0 2px 8px rgba(0,0,0,0.06); overflow-x: hidden; transition: all 0.3s ease; }\n        .article-meta:hover { box-shadow: 0 4px 16px rgba(0,0,0,0.1); transform: translateY(-1px); }\n        .article-meta .meta-item { display: flex; align-items: center; gap: 0.25rem; font-weight: 500; color: #495057; white-space: nowrap; flex-shrink: 1; transition: color 0.2s ease; cursor: default; }\n        .article-meta .meta-item:hover { color: #1764ff; }\n        .article-meta .meta-item:hover .meta-icon { transform: scale(1.1); filter: drop-shadow(0 2px 6px rgba(13, 71, 161, 0.25)); }\n        .meta-icon { display: inline-block; font-size: 16px; flex-shrink: 0; vertical-align: middle; transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1); margin-right: 4px; opacity: 0.9; }\n        .meta-item:hover .meta-icon { opacity: 1; }\n        .meta-item:hover { color: #1764ff; }\n        .meta-item { position: relative; }\n        .meta-item::after { content: ''; position: absolute; bottom: -2px; left: 0; width: 0; height: 2px; background: linear-gradient(90deg, #1764ff, #00d4ff); transition: width 0.3s ease; }\n        .meta-item:hover::after { width: 100%; }\n        .article-category, .read-time { display: inline-block; padding: 6px 12px; border-radius: 16px; font-size: 0.8rem; font-weight: 600; }\n        .article-category { background: linear-gradient(135deg, #1764ff, #0d47a1); color: white; text-decoration: none; text-transform: uppercase; letter-spacing: 0.5px; transition: all 0.3s ease; }\n        .article-category:hover { background: linear-gradient(135deg, #0d47a1, #1764ff); color: white; text-decoration: none; transform: translateY(-1px); box-shadow: 0 4px 12px rgba(23, 100, 255, 0.3); }\n        .read-time { background: #e3f2fd; color: #1565c0; border: 1px solid #90caf9; }\n        .article-tags { margin: 20px 0 1.5rem; }\n        .article-tags .tag { display: inline-block; background: #1764ff; color: #fff; padding: 4px 12px; border-radius: 12px; font-size: 0.8rem; margin: 0 8px 4px 0; }\n        .article-summary { background: #e3f2fd; border-left: 4px solid #1764ff; padding: 1rem 1.5rem; margin-bottom: 2rem; border-radius: 0 4px 4px 0; }\n        .article-content { line-height: 1.8; font-size: 16px; color: #333; word-wrap: break-word; }\n        .article-content h1, .article-content h2, .article-content h3, .article-content h4, .article-content h5, .article-content h6 { margin: 2rem 0 1rem; font-weight: 600; line-height: 1.25; color: #1a1c1f; }\n        .article-content h1 { font-size: 2rem; border-bottom: 2px solid #e9ecef; padding-bottom: 0.5rem; }\n        .article-content h2 { font-size: 1.5rem; border-bottom: 1px solid #e9ecef; padding-bottom: 0.3rem; }\n        .article-content h3 { font-size: 1.25rem; }\n        .article-content h4 { font-size: 1.1rem; }\n        .article-content h5, .article-content h6 { font-size: 1rem; }\n        .article-content ul, .article-content ol { margin: 1rem 0; padding-left: 2rem; }\n        .article-content ul ul, .article-content ol ol, .article-content ul ol, .article-content ol ul { margin: 0.5rem 0; }\n        .article-content blockquote { margin: 1rem 0; padding: 0.5rem 1rem; border-left: 4px solid #1764ff; background-color: #f8f9fa; color: #6c757d; font-style: italic; }\n        .article-content pre { background-color: #f8f9fa; border: 1px solid #e9ecef; border-radius: 6px; padding: 1rem; margin: 1rem 0; overflow-x: auto; font-family: 'Courier New', Courier, monospace; font-size: 0.9em; line-height: 1.4; }\n        .article-content pre code { background: none; border: none; padding: 0; color: inherit; }\n        .article-content p { margin: 1rem 0; }\n        .article-content p:first-child { margin-top: 0; }\n        .article-content p:last-child { margin-bottom: 0; }\n        .article-footer { margin-top: 4rem; padding: 2.5rem; background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%); border-radius: 12px; border: 1px solid #e9ecef; position: relative; overflow: hidden; }\n        .article-footer::before { content: ''; position: absolute; top: 0; left: 0; right: 0; height: 3px; background: linear-gradient(90deg, #1764ff, #17a2b8, #28a745); }\n        .article-share h6 { color: #495057; font-weight: 600; margin-bottom: 1.5rem; font-size: 16px; }\n        .share-buttons { display: flex; gap: 12px; flex-wrap: wrap; }\n        .share-buttons .btn { padding: 10px 20px; border-radius: 25px; font-weight: 500; font-size: 14px; border: none; transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1); box-shadow: 0 2px 8px rgba(0,0,0,0.1); display: inline-flex; align-items: center; gap: 8px; text-decoration: none; }\n        .btn-weibo { background: linear-gradient(135deg, #e6162d, #c41230); color: white; }\n        .btn-copy { background: #1764ff; color: white; }\n        .btn-print { background: linear-gradient(135deg, #17a2b8, #138496); color: white; }\n        .btn-weibo:hover { background: linear-gradient(135deg, #c41230, #a00e26); box-shadow: 0 4px 16px rgba(230, 22, 45, 0.3); }\n        .btn-copy:hover { background: #0d47a1; box-shadow: 0 4px 16px rgba(23, 100, 255, 0.3); }\n        .btn-print:hover { background: linear-gradient(135deg, #138496, #117a8b); box-shadow: 0 4px 16px rgba(23, 162, 184, 0.3); }\n        .btn-weibo:hover, .btn-copy:hover, .btn-print:hover { transform: translateY(-2px); color: white; }\n        .article-actions { display: flex; justify-content: flex-end; align-items: center; }\n        .related-articles { margin-top: 3rem; }\n        .related-article-item { background: #fff; border-radius: 8px; padding: 1.5rem; margin-bottom: 1rem; box-shadow: 0 2px 8px rgba(0,0,0,0.1); transition: transform 0.3s ease; }\n        .related-article-item:hover { transform: translateY(-2px); }\n        .sidebar-card { background: linear-gradient(135deg, rgba(255,255,255,0.9) 0%, rgba(255,255,255,0.8) 100%); backdrop-filter: blur(20px); border: 1px solid rgba(255,255,255,0.3); border-radius: 16px; padding: 1rem; margin-bottom: 0.5rem; box-shadow: 0 4px 20px rgba(0,0,0,0.08), inset 0 1px 0 rgba(255,255,255,0.2); overflow: visible; box-sizing: border-box; width: 100%; z-index: 10; }\n        .sidebar-card.fixed { box-shadow: 0 8px 32px rgba(0,0,0,0.12); }\n        .sidebar-card::before { content: ''; position: absolute; top: 0; left: 0; right: 0; height: 3px; background: linear-gradient(90deg, #00d4ff, #1764ff, #9c27b0); border-radius: 20px 20px 0 0; }\n        .sidebar-card h5 { color: #1a202c; padding-bottom: 0.8rem; border-bottom: 2px solid rgba(23, 100, 255, 0.2); font-weight: 700; font-size: 1.1rem; }\n        .sidebar { min-height: 100vh; position: relative !important; display: block !important; flex-direction: column !important; gap: 1rem; }\n        .toc-card { flex: 0 1 auto; max-height: 60vh; overflow-y: auto; }\n        .sidebar-card:not(.toc-card) { flex: 1 1 auto; overflow: visible; }\n        .recommended-item { padding: 0.75rem 0; border-bottom: 1px solid rgba(0,0,0,0.05); }\n        .recommended-item:last-child { border-bottom: none; }\n        .recommended-item h6 { margin: 0 0 0.5rem 0; font-size: 0.9rem; line-height: 1.4; }\n        .recommended-item h6 a { color: #1a1c1f; text-decoration: none; transition: color 0.3s ease; }\n        .recommended-item h6 a:hover { color: #1764ff; }\n        .recommended-item .text-muted { font-size: 0.75rem; margin: 0; }\n        .toc-list { list-style: none; padding: 0; margin: 0; max-height: none; overflow-y: visible; overflow-x: hidden; }\n        .toc-list::-webkit-scrollbar { width: 4px; }\n        .toc-list::-webkit-scrollbar-track { background: rgba(0,0,0,0.1); border-radius: 2px; }\n        .toc-list::-webkit-scrollbar-thumb { background: linear-gradient(135deg, #00d4ff, #1764ff); border-radius: 2px; }\n        .toc-list::-webkit-scrollbar-thumb:hover { background: linear-gradient(135deg, #1764ff, #0d47a1); }\n        .toc-list a { color: #4a5568; text-decoration: none; font-size: 13px; font-weight: 500; display: block; padding: 8px 12px; border-radius: 8px; transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1); position: relative; margin-bottom: 4px; border: 1px solid transparent; }\n        .toc-list a::before { content: ''; position: absolute; left: 0; top: 0; bottom: 0; width: 3px; background: linear-gradient(135deg, #00d4ff, #1764ff); border-radius: 0 12px 12px 0; transform: scaleY(0); transition: transform 0.3s ease; }\n        .toc-list a:hover { color: #1764ff; background: linear-gradient(135deg, rgba(0, 212, 255, 0.1), rgba(23, 100, 255, 0.1)); border-color: rgba(23, 100, 255, 0.2); transform: translateX(8px); box-shadow: 0 4px 12px rgba(0, 212, 255, 0.2); }\n        .toc-list a:hover::before, .toc-list a.active::before { transform: scaleY(1); }\n        .toc-list a.active { color: #1764ff; background: linear-gradient(135deg, rgba(0, 212, 255, 0.15), rgba(23, 100, 255, 0.15)); font-weight: 700; border-color: rgba(23, 100, 255, 0.3); transform: translateX(4px); box-shadow: 0 6px 16px rgba(0, 212, 255, 0.25); }\n        .toc-number { color: #1764ff; font-weight: 600; margin-right: 0.5rem; font-size: 0.9em; }\n        .toc-text { flex: 1; }\n        .toc-level-1 { font-weight: 700; font-size: 15px; }\n        .toc-level-2 { font-size: 14px; }\n        .toc-level-3 { font-size: 13px; opacity: 0.9; }\n        .toc-level-4, .toc-level-5, .toc-level-6 { font-size: 12px; opacity: 0.8; }\n        .article-content .formatted-list { margin: 1.5rem 0; padding-left: 0; list-style: none; }\n        .article-content .formatted-list.ordered { counter-reset: list-counter; }\n        .article-content .formatted-list.ordered li { counter-increment: list-counter; position: relative; margin: 1rem 0; padding: 1rem 1rem 1rem 3rem; background: linear-gradient(135deg, rgba(23, 100, 255, 0.03), rgba(0, 212, 255, 0.03)); border-left: 4px solid #1764ff; border-radius: 0 8px 8px 0; transition: all 0.3s ease; }\n        .article-content .formatted-list.ordered li::before { content: counter(list-counter); position: absolute; left: 0.8rem; top: 1rem; background: linear-gradient(135deg, #1764ff, #00d4ff); color: white; width: 1.5rem; height: 1.5rem; border-radius: 50%; display: flex; align-items: center; justify-content: center; font-weight: 600; font-size: 0.9rem; }\n        .article-content .formatted-list.ordered li:hover { background: linear-gradient(135deg, rgba(23, 100, 255, 0.06), rgba(0, 212, 255, 0.06)); transform: translateX(4px); box-shadow: 0 2px 8px rgba(23, 100, 255, 0.15); }\n        .article-content .formatted-list.unordered li { position: relative; margin: 0.8rem 0; padding: 0.8rem 1rem 0.8rem 2.5rem; background: linear-gradient(135deg, rgba(40, 167, 69, 0.03), rgba(25, 135, 84, 0.03)); border-left: 4px solid #28a745; border-radius: 0 6px 6px 0; transition: all 0.3s ease; }\n        .article-content .formatted-list.unordered li::before { content: \"•\"; position: absolute; left: 1rem; top: 0.8rem; color: #28a745; font-size: 1.2rem; font-weight: bold; }\n        .article-content .formatted-list.unordered li:hover { background: linear-gradient(135deg, rgba(40, 167, 69, 0.06), rgba(25, 135, 84, 0.06)); transform: translateX(4px); box-shadow: 0 2px 8px rgba(40, 167, 69, 0.15); }\n        .article-content .list-title { color: #1764ff; font-weight: 600; display: inline-block; margin-bottom: 0.3rem; }\n        .article-content .list-content { color: #2c3e50; line-height: 1.6; display: block; }\n        .article-content .inline-code { background: #f8f9fa; border: 1px solid #e9ecef; border-radius: 4px; padding: 2px 6px; font-family: 'Consolas', 'Monaco', 'Courier New', monospace; font-size: 0.9em; color: #e83e8c; }\n        .article-content .label-item { margin: 0.8rem 0; }\n        .article-content .label { color: #1764ff; font-weight: 600; }\n        .article-content .section-label { color: #2c3e50; font-weight: 600; margin: 1rem 0 0.5rem 0; font-size: 1.1em; border-left: 4px solid #1764ff; padding-left: 0.8rem; }\n        .article-content p, .article-content .content-paragraph { margin: 1.2rem 0; line-height: 1.8; text-align: justify; color: #2c3e50; font-size: 1rem; }\n        .article-content .content-paragraph { text-indent: 0; margin: 1rem 0; }\n        .article-content .indent-paragraph { text-indent: 2em; margin: 1.2rem 0; }\n        .article-content .text-indent-1 { text-indent: 2em; display: block; }\n        .article-content .text-indent-2 { text-indent: 4em; display: block; }\n        .article-content .text-indent-3 { text-indent: 6em; display: block; }\n        .article-content .network-config { background: linear-gradient(135deg, #f8f9fa, #e9ecef); border: 1px solid #dee2e6; border-radius: 6px; padding: 0.8rem 1rem; margin: 0.5rem 0; font-family: 'Consolas', 'Monaco', 'Courier New', monospace; font-size: 0.9rem; box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05); }\n        .article-content .config-label { color: #1764ff; font-weight: 600; }\n        .article-content .config-desc { color: #495057; }\n        .article-content .algorithm-step { background: linear-gradient(135deg, rgba(23, 100, 255, 0.05), rgba(0, 212, 255, 0.05)); border-left: 4px solid #1764ff; padding: 0.8rem 1rem; margin: 0.5rem 0; border-radius: 0 6px 6px 0; transition: all 0.3s ease; }\n        .article-content .step-number { color: #1764ff; font-weight: 700; margin-right: 0.5rem; }\n        .article-content .step-desc { color: #2c3e50; line-height: 1.5; }\n        .article-content table { width: 100%; border-collapse: collapse; margin: 1.5rem 0; background: white; border-radius: 8px; overflow: hidden; box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1); }\n        .article-content table th { background: linear-gradient(135deg, #1764ff, #00d4ff); color: white; padding: 1rem; text-align: left; font-weight: 600; }\n        .article-content table td { padding: 0.8rem 1rem; border-bottom: 1px solid #e9ecef; }\n        .article-content table tr:hover { background: rgba(23, 100, 255, 0.05); }\n        .article-content strong { color: #1764ff; font-weight: 600; }\n        .article-content em { color: #6c757d; font-style: italic; }\n        .article-content a { color: #1764ff; text-decoration: none; border-bottom: 1px solid transparent; transition: all 0.3s ease; }\n        .article-content a:hover { border-bottom-color: #1764ff; color: #0056b3; }\n        .article-content .definition-item { background: linear-gradient(135deg, rgba(40, 167, 69, 0.05), rgba(25, 135, 84, 0.05)); border-left: 4px solid #28a745; padding: 1rem; margin: 1rem 0; border-radius: 0 8px 8px 0; }\n        .article-content .definition-label { color: #28a745; font-weight: 600; display: block; margin-bottom: 0.5rem; }\n        .article-content .definition-content { color: #2c3e50; line-height: 1.6; }\n        .article-content .note-item { background: linear-gradient(135deg, rgba(255, 193, 7, 0.1), rgba(255, 193, 7, 0.05)); border: 1px solid rgba(255, 193, 7, 0.3); border-radius: 8px; padding: 1rem; margin: 1rem 0; position: relative; }\n        .article-content .note-item::before { content: \"💡\"; position: absolute; left: 1rem; top: 1rem; font-size: 1.2em; }\n        .article-content .note-label { color: #856404; font-weight: 600; margin-left: 2rem; }\n        .article-content .note-content { color: #2c3e50; line-height: 1.6; margin-left: 2rem; display: block; margin-top: 0.3rem; }\n        .article-content .math-formula { background: #f8f9fa; border: 1px solid #e9ecef; border-radius: 6px; padding: 1rem; margin: 1rem 0; text-align: center; font-family: 'Times New Roman', serif; font-size: 1.1em; color: #2c3e50; }\n        @media (min-width: 1400px) { .article-content { font-size: 17px; line-height: 1.8; } .article-detail { padding: 2.5rem; } }\n        @media (max-width: 1199px) and (min-width: 992px) { .article-detail { padding: 1.5rem; } .sidebar-card { padding: 1rem; } .toc-list a { font-size: 12px; padding: 6px 8px; } }\n        @media (max-width: 768px) {\n            .article-detail-container { padding: 60px 15px; }\n            .article-content .network-config, .article-content .algorithm-step, .article-content .definition-item, .article-content .note-item { margin: 0.5rem -1rem; border-radius: 0; }\n            .article-content .note-item::before { left: 0.5rem; }\n            .article-content .note-label, .article-content .note-content { margin-left: 1.5rem; }\n            .article-detail { padding: 1.5rem; }\n            .article-title { font-size: 1.6rem; line-height: 1.3; }\n            .article-meta { flex-wrap: wrap; gap: 1rem; padding: 1rem; font-size: 0.8rem; }\n            .article-meta .meta-item { flex-shrink: 1; }\n            .meta-icon { font-size: 14px; margin-right: 3px; }\n            .article-content { font-size: 15px; }\n            .article-content h1 { font-size: 1.6rem; }\n            .article-content h2 { font-size: 1.4rem; }\n            .article-content h3 { font-size: 1.2rem; }\n            .article-content h4 { font-size: 1.1rem; }\n            .article-content pre { padding: 0.75rem; font-size: 0.85em; overflow-x: auto; }\n            .article-content code { font-size: 0.85em; padding: 1px 4px; }\n            .sidebar { margin-top: 2rem; }\n            .sidebar-card { position: static; }\n            .toc-card { max-height: none; overflow-y: visible; }\n            .share-buttons { flex-direction: column; gap: 0.5rem; }\n            .share-buttons .btn { width: 100%; justify-content: center; }\n        }\n        @media print { body * { visibility: hidden; } .article-detail, .article-detail * { visibility: visible; } .article-detail { position: absolute; left: 0; top: 0; width: 100%; background: white; box-shadow: none; border: none; margin: 0; padding: 20px; } .article-meta { border: none; background: none; padding: 0; margin-bottom: 20px; } .article-footer, .reading-progress, .back-to-top { display: none !important; } .article-content { font-size: 12pt; line-height: 1.6; } .article-content h1, .article-content h2, .article-content h3, .article-content h4, .article-content h5, .article-content h6 { page-break-after: avoid; margin-top: 20pt; margin-bottom: 10pt; } .article-content pre, .article-content blockquote { page-break-inside: avoid; } }\n    </style>\n\n<script type=\"application/ld+json\">{\"@context\":\"https://schema.org\",\"@type\":\"BreadcrumbList\",\"itemListElement\":[{\"@type\":\"ListItem\",\"position\":1,\"name\":\"Дом\",\"item\":\"https://ocr.oldfish.cn/default.aspx\"},{\"@type\":\"ListItem\",\"position\":2,\"name\":\"Технические статьи\",\"item\":\"https://ocr.oldfish.cn/Articles.aspx\"},{\"@type\":\"ListItem\",\"position\":3,\"name\":\"Подробнее о статье\",\"item\":\"https://ocr.oldfish.cn/Article.aspx?id=81&slug=rnn-sequence-modeling&lang=zh-Hans&_translation_source=true\"}]}</script>\n<link rel=\"stylesheet\" href=\"/site/css/site.css\" type=\"text/css\" /><title>【Серия OCR с глубоким обучением·4】Рекуррентные нейронные сети и моделирование последовательностей</title><meta http-equiv=\"Content-Language\" content=\"ru\" />\n</head>\r\n<body>\r\n    <div class=\"home-container root\">\r\n        <header class=\"v4_header_pc\">\r\n            <div class=\"header_pc_left\">\r\n                <a href=\"Default.aspx\" title=\"Главная | Интеллектуальное распознавание текста на основе искусственного интеллекта\" class=\"pc-logo-wrap ml-4 mr-5\">\r\n                    <picture>\r\n                        <source srcset=\"site/image/logo-s.png.webp\" type=\"image/webp\" />\r\n                        <img src=\"site/image/logo-s.png\" class=\"pc-logo\" style=\"height: 100%\" alt=\"OCR Text Recognition Assistant Логотип официального сайта - AI Intelligent Text Recognition Platform\" />\r\n                    </picture>\r\n                    <span style=\"font-size: 18px; font-weight: bold; color: #333;\">Помощник по распознаванию текста OCR</span>\r\n                </a>\r\n                <ul class=\"top-nav\" role=\"navigation\" aria-label=\"Основная навигация\">\r\n                    <li><a class=\"color-default\" href=\"Default.aspx\" title=\"Домашняя страница помощника по распознаванию текста OCR\">Дом</a></li>\r\n                    <li><a class=\"color-default\" data-sub=\"product-sub-nav\" href=\"Detail.aspx\" title=\"Введение в функцию продукта OCR\">Особенности продукта:</a><div class=\"triangle\"></div>\r\n                    </li>\r\n                    <li><a class=\"color-default\" data-sub=\"experience-sub-nav\" href=\"Ocr.aspx\" title=\"Ознакомьтесь с функциями OCR онлайн\">Онлайн-опыт</a><div class=\"triangle\"></div>\r\n                    </li>\r\n                    <li><a class=\"color-default\" data-sub=\"vip-sub-nav\" href=\"Version.aspx\" title=\"Услуга повышения статуса членства в OCR\">Повышение уровня членства</a><div class=\"triangle\"></div>\r\n                    </li>\r\n                    <li><a class=\"color-default\" href=\"Download.aspx\" title=\"Скачать OCR Text Recognition Assistant бесплатно\">Скачать бесплатно</a></li>\r\n                    <li><a class=\"color-default\" data-sub=\"articles-sub-nav\" href=\"Articles.aspx\" title=\"Технические статьи и обмен знаниями в области OCR\">Обмен технологиями</a><div class=\"triangle\"></div>\r\n                    </li>\r\n                    <li><a class=\"color-default\" data-sub=\"support-sub-nav\" href=\"FAQ.aspx\" title=\"Помощь по использованию OCR и техническая поддержка\">Справочный центр</a><div class=\"triangle\"></div>\r\n                    </li>\r\n                </ul>\r\n                <div class=\"product-con\">\r\n                    <section id=\"product-sub-nav\" class=\"pls-nav-dropdown\" style=\"visibility: hidden; height: 0px; display: none;\">\r\n                        <div class=\"d-flex nav-drown-con\" style=\"height: 0px; transition: transform 0.2s ease-in-out 0s,opacity 0.2s ease-in-out 0s; opacity: 0; transform: translate(0px, -100%);\">\r\n                            <div class=\"prdocu-sub-left\">\r\n                                <div class=\"mr-auto\">\r\n                                    <picture>\r\n                                        <source class=\"p-icon\" srcset=\"site/image/icon/p_1.png.webp\" type=\"image/webp\" alt=\"\" aria-hidden=\"true\" />\r\n                                        <img class=\"p-icon\" src=\"site/image/icon/p_1.png\" alt=\"Значок функции продукта OCR\" aria-hidden=\"true\" loading=\"lazy\" />\r\n                                    </picture>\r\n                                    <div class=\"mt-4\">\r\n                                        <h3 class=\"h6\">Помощник по распознаванию текста OCR</h3>\r\n                                        <p class=\"color-gray fn14 mt-3 sub-left-desc\">Повышение эффективности, снижение затрат и создание ценности</p>\r\n                                        <p class=\"color-gray fn14 mt-3 sub-left-desc\">&nbsp;</p>\r\n                                        <p class=\"color-gray fn14 mt-3 sub-left-desc\">Интеллектуальное распознавание, высокая скорость обработки и точный вывод</p>\r\n                                        <p class=\"color-gray fn14 mt-3 sub-left-desc\">От текста к таблицам, от формул к переводам</p>\r\n                                        <p class=\"color-gray fn14 mt-3 sub-left-desc\">Упростите обработку текстов</p>\r\n                                        <a class=\"link block disa fn14 contact-business shake\" href=\"Detail.aspx\">Узнайте о функциях<span class=\"iconfont4 icon-xiangzuo gt\"></span></a>\r\n                                    </div>\r\n                                </div>\r\n                            </div>\r\n                            <div class=\"bg-white prdocu-sub-right\">\r\n                                <div>\r\n                                    <h2 class=\"text-base-color h6\">Особенности продукта:</h2>\r\n                                    <div class=\"mt-4 d-flex flex-wrap\">\r\n                                        <a href=\"Detail.aspx\" class=\"pro-item color-default\" title=\"Ознакомьтесь с подробной информацией об основных функциях OCR Assistant\">\r\n                                            <div class=\"letter-wrap\">\r\n                                                <h3 class=\"h6\">Основные особенности:</h3>\r\n                                                <span class=\"color-gray fn14\">Узнайте больше об основных функциях и технических преимуществах OCR Assistant с коэффициентом распознавания 98%+</span>\r\n                                            </div>\r\n                                        </a>\r\n                                        <a href=\"Version.aspx\" class=\"pro-item color-default\" title=\"Сравнение различий между версиями OCR Assistant\">\r\n                                            <div class=\"letter-wrap\">\r\n                                                <h3 class=\"h6\">Сравнение версий</h3>\r\n                                                <span class=\"color-gray fn14\">Подробно сравните функциональные различия бесплатной версии, персональной версии, профессиональной версии и окончательной версии</span>\r\n                                            </div>\r\n                                        </a>\r\n                                        <a href=\"FAQ.aspx\" class=\"pro-item color-default\" title=\"Ознакомьтесь с часто задаваемыми вопросами о OCR Assistant\">\r\n                                            <div class=\"letter-wrap\">\r\n                                                <h3 class=\"h6\">Вопросы и ответы по продуктам</h3>\r\n                                                <span class=\"color-gray fn14\">Быстро узнавайте о функциях продукта, способах использования и подробных ответах на часто задаваемые вопросы</span>\r\n                                            </div>\r\n                                        </a>\r\n                                        <a href=\"Download.aspx\" class=\"pro-item color-default\" title=\"Скачать OCR Text Recognition Assistant бесплатно\">\r\n                                            <div class=\"letter-wrap\">\r\n                                                <h3 class=\"h6\">Попробовать бесплатно</h3>\r\n                                                <span class=\"color-gray fn14\">Загрузите и установите OCR Assistant прямо сейчас, чтобы бесплатно испытать мощную функцию распознавания текста</span>\r\n                                            </div>\r\n                                        </a>\r\n                                        <div style=\"width: 100%; margin: 15px 0;\">\r\n                                            <h4 class=\"h6\" style=\"color: #007cfa; margin-bottom: 10px;\">Распознавание OCR в режиме онлайн</h4>\r\n                                        </div>\r\n                                        <a href=\"Ocr.aspx?type=text_recognize\" class=\"pro-item color-default\" title=\"Испытайте универсальное распознавание текста онлайн\">\r\n                                            <div class=\"letter-wrap\">\r\n                                                <h3 class=\"h6\">Универсальное распознавание символов</h3>\r\n                                                <span class=\"color-gray fn14\">Интеллектуальное извлечение многоязычного высокоточного текста, поддержка распознавания печатных и многосценических сложных изображений</span>\r\n                                            </div>\r\n                                        </a>\r\n                                        <a href=\"Ocr.aspx?type=table\" class=\"pro-item color-default\">\r\n                                            <div class=\"letter-wrap\">\r\n                                                <h3 class=\"h6\">Универсальная идентификация таблиц</h3>\r\n                                                <span class=\"color-gray fn14\">Интеллектуальная конвертация изображений таблиц в файлы Excel, автоматическая обработка сложных табличных структур и объединенных ячеек</span>\r\n                                            </div>\r\n                                        </a>\r\n                                        <a href=\"Ocr.aspx?type=handwritten_ocr\" class=\"pro-item color-default\">\r\n                                            <div class=\"letter-wrap\">\r\n                                                <h3 class=\"h6\">Распознавание рукописного ввода</h3>\r\n                                                <span class=\"color-gray fn14\">Интеллектуальное распознавание рукописного текста на китайском и английском языках, поддержка заметок в классе, медицинских карт и других сценариев</span>\r\n                                            </div>\r\n                                        </a>\r\n                                        <a href=\"Ocr.aspx?type=pdf2word\" class=\"pro-item color-default\">\r\n                                            <div class=\"letter-wrap\">\r\n                                                <h3 class=\"h6\">PDF в Word</h3>\r\n                                                <span class=\"color-gray fn14\">PDF-документы быстро конвертируются в формат Word, отлично сохраняя исходную верстку и графическую верстку</span>\r\n                                            </div>\r\n                                        </a>\r\n                                    </div>\r\n                                </div>\r\n                            </div>\r\n                        </div>\r\n                    </section>\r\n                    <section id=\"experience-sub-nav\" class=\"pls-nav-dropdown solution-part\" style=\"visibility: hidden; height: 0px; display: none;\">\r\n                        <div class=\"d-flex nav-drown-con\" style=\"height: 0px; transition: transform 0.2s ease-in-out 0s,opacity 0.2s ease-in-out 0s; opacity: 0; transform: translate(0px, -100%);\">\r\n                            <div class=\"prdocu-sub-left\">\r\n                                <div class=\"mr-auto\">\r\n                                    <picture>\r\n                                        <source srcset=\"site/image/icon/p_4.png.webp\" type=\"image/webp\" />\r\n                                        <img class=\"p-icon\" src=\"site/image/icon/p_4.png\" alt=\"Иконка онлайн-центра OCR Experience Center\" aria-hidden=\"true\" loading=\"lazy\" />\r\n                                    </picture>\r\n                                    <div class=\"mt-4\">\r\n                                        <h3 class=\"h6\">Помощник по распознаванию текста OCR</h3>\r\n                                        <p class=\"color-gray fn14 mt-3 sub-left-desc\">Текст, таблицы, формулы, документы, переводы</p>\r\n                                        <p class=\"color-gray fn14 mt-3 sub-left-desc\">&nbsp;</p>\r\n                                        <p class=\"color-gray fn14 mt-3 sub-left-desc\">Выполните все свои потребности в обработке текстов в три шага</p>\r\n                                        <p class=\"color-gray fn14 mt-3 sub-left-desc\">Скриншот → Определение → приложений</p>\r\n                                        <p class=\"color-gray fn14 mt-3 sub-left-desc\">Повышение эффективности работы на 300%</p>\r\n                                        <a class=\"link block disa fn14 contact-business shake\" href=\"Ocr.aspx\">Попробуйте прямо сейчас<span class=\"iconfont4 icon-xiangzuo gt\"></span></a>\r\n                                    </div>\r\n                                </div>\r\n                            </div>\r\n                            <div class=\"bg-white prdocu-sub-right\">\r\n                                <div>\r\n                                    <div class=\"mt-4 d-flex flex-wrap\">\r\n                                        <div style=\"width: 100%; margin-bottom: 15px;\">\r\n                                            <h4 class=\"h6\" style=\"color: #007cfa; margin-bottom: 10px;\">Опыт работы с функцией OCR</h4>\r\n                                        </div>\r\n                                        <a href=\"Ocr.aspx?type=index\" class=\"pro-item color-default\">\r\n                                            <div class=\"letter-wrap\">\r\n                                                <h3 class=\"h6\">Полный функционал</h3>\r\n                                                <span class=\"color-gray fn14\">Испытайте все интеллектуальные функции OCR в одном месте, чтобы быстро найти лучшее решение для ваших нужд</span>\r\n                                            </div>\r\n                                        </a>\r\n                                        <a href=\"Ocr.aspx?type=text_recognize\" class=\"pro-item color-default\">\r\n                                            <div class=\"letter-wrap\">\r\n                                                <h3 class=\"h6\">Универсальное распознавание символов</h3>\r\n                                                <span class=\"color-gray fn14\">Интеллектуальное извлечение многоязычного высокоточного текста, поддержка распознавания печатных и многосценических сложных изображений</span>\r\n                                            </div>\r\n                                        </a>\r\n                                        <a href=\"Ocr.aspx?type=table\" class=\"pro-item color-default\">\r\n                                            <div class=\"letter-wrap\">\r\n                                                <h3 class=\"h6\">Универсальная идентификация таблиц</h3>\r\n                                                <span class=\"color-gray fn14\">Интеллектуальная конвертация изображений таблиц в файлы Excel, автоматическая обработка сложных табличных структур и объединенных ячеек</span>\r\n                                            </div>\r\n                                        </a>\r\n                                        <a href=\"Ocr.aspx?type=handwritten_ocr\" class=\"pro-item color-default\">\r\n                                            <div class=\"letter-wrap\">\r\n                                                <h3 class=\"h6\">Распознавание рукописного ввода</h3>\r\n                                                <span class=\"color-gray fn14\">Интеллектуальное распознавание рукописного текста на китайском и английском языках, поддержка заметок в классе, медицинских карт и других сценариев</span>\r\n                                            </div>\r\n                                        </a>\r\n                                        <a href=\"Ocr.aspx?type=pdf2word\" class=\"pro-item color-default\">\r\n                                            <div class=\"letter-wrap\">\r\n                                                <h3 class=\"h6\">PDF в Word</h3>\r\n                                                <span class=\"color-gray fn14\">PDF-документы быстро конвертируются в формат Word, отлично сохраняя исходную верстку и графическую верстку</span>\r\n                                            </div>\r\n                                        </a>\r\n                                        <a href=\"Ocr.aspx?type=pdf2markdown\" class=\"pro-item color-default\">\r\n                                            <div class=\"letter-wrap\">\r\n                                                <h3 class=\"h6\">PDF в Markdown</h3>\r\n                                                <span class=\"color-gray fn14\">PDF-документы интеллектуально преобразуются в формат MD, а блоки кода и текстовые структуры автоматически оптимизируются для обработки</span>\r\n                                            </div>\r\n                                        </a>\r\n                                        <div style=\"width: 100%; margin: 15px 0;\">\r\n                                            <h4 class=\"h6\" style=\"color: #007cfa; margin-bottom: 10px;\">Средства обработки документов</h4>\r\n                                        </div>\r\n                                        <a href=\"Ocr.aspx?type=word2pdf\" class=\"pro-item color-default\">\r\n                                            <div class=\"letter-wrap\">\r\n                                                <h3 class=\"h6\">Перевод из одного документа в PDF</h3>\r\n                                                <span class=\"color-gray fn14\">Документы Word конвертируются в PDF одним щелчком мыши, отлично сохраняя исходный формат, подходящий для архивирования и официального обмена документами</span>\r\n                                            </div>\r\n                                        </a>\r\n                                        <a href=\"Ocr.aspx?type=word2jpg\" class=\"pro-item color-default\">\r\n                                            <div class=\"letter-wrap\">\r\n                                                <h3 class=\"h6\">Слово в изображение</h3>\r\n                                                <span class=\"color-gray fn14\">Интеллектуальное преобразование документов Word в изображение JPG, поддержка многостраничной обработки, легко делиться в социальных сетях</span>\r\n                                            </div>\r\n                                        </a>\r\n                                        <a href=\"Ocr.aspx?type=pdf2jpg\" class=\"pro-item color-default\">\r\n                                            <div class=\"letter-wrap\">\r\n                                                <h3 class=\"h6\">PDF в изображение</h3>\r\n                                                <span class=\"color-gray fn14\">Конвертируйте PDF-документы в изображения JPG в высоком разрешении, поддерживайте пакетную обработку и пользовательское разрешение</span>\r\n                                            </div>\r\n                                        </a>\r\n                                        <a href=\"Ocr.aspx?type=image2pdf\" class=\"pro-item color-default\">\r\n                                            <div class=\"letter-wrap\">\r\n                                                <h3 class=\"h6\">Изображение в PDF</h3>\r\n                                                <span class=\"color-gray fn14\">Объединяйте несколько изображений в PDF-документы, поддерживайте сортировку и настройку страниц</span>\r\n                                            </div>\r\n                                        </a>\r\n                                        <div style=\"width: 100%; margin: 15px 0;\">\r\n                                            <h4 class=\"h6\" style=\"color: #007cfa; margin-bottom: 10px;\">Инструменты разработчика</h4>\r\n                                        </div>\r\n                                        <a href=\"Tool.aspx?type=Json\" class=\"pro-item color-default\">\r\n                                            <div class=\"letter-wrap\">\r\n                                                <h3 class=\"h6\">Форматирование JSON</h3>\r\n                                                <span class=\"color-gray fn14\">Интеллектуальная настройка структуры кода JSON, поддержка сжатия и расширения, а также упрощение разработки и отладки</span>\r\n                                            </div>\r\n                                        </a>\r\n                                        <a href=\"Tool.aspx?type=Regex\" class=\"pro-item color-default\">\r\n                                            <div class=\"letter-wrap\">\r\n                                                <h3 class=\"h6\">регулярное выражение</h3>\r\n                                                <span class=\"color-gray fn14\">Проверяйте эффекты сопоставления регулярных выражений в режиме реального времени с помощью встроенной библиотеки общих шаблонов</span>\r\n                                            </div>\r\n                                        </a>\r\n                                        <a href=\"Tool.aspx?type=Encode\" class=\"pro-item color-default\">\r\n                                            <div class=\"letter-wrap\">\r\n                                                <h3 class=\"h6\">Преобразование кодировки текста</h3>\r\n                                                <span class=\"color-gray fn14\">Он поддерживает преобразование нескольких форматов кодирования, таких как Base64, URL и Unicode</span>\r\n                                            </div>\r\n                                        </a>\r\n                                        <a href=\"Tool.aspx?type=Diff\" class=\"pro-item color-default\">\r\n                                            <div class=\"letter-wrap\">\r\n                                                <h3 class=\"h6\">Сопоставление и объединение текста</h3>\r\n                                                <span class=\"color-gray fn14\">Выделение различий в тексте и поддержка построчного сравнения и интеллектуального объединения</span>\r\n                                            </div>\r\n                                        </a>\r\n                                        <a href=\"Tool.aspx?type=Color\" class=\"pro-item color-default\">\r\n                                            <div class=\"letter-wrap\">\r\n                                                <h3 class=\"h6\">Инструмент «Цвет»</h3>\r\n                                                <span class=\"color-gray fn14\">Преобразование цветов RGB/HEX, онлайн-палитра цветов, незаменимый инструмент для фронтенд-разработки</span>\r\n                                            </div>\r\n                                        </a>\r\n                                        <a href=\"Tool.aspx?type=Count\" class=\"pro-item color-default\">\r\n                                            <div class=\"letter-wrap\">\r\n                                                <h3 class=\"h6\">Количество слов</h3>\r\n                                                <span class=\"color-gray fn14\">Интеллектуальный подсчет символов, словарного запаса и абзацев, а также автоматическая оптимизация макета текста</span>\r\n                                            </div>\r\n                                        </a>\r\n                                        <a href=\"Tool.aspx?type=Timespan\" class=\"pro-item color-default\">\r\n                                            <div class=\"letter-wrap\">\r\n                                                <h3 class=\"h6\">Преобразование временных меток</h3>\r\n                                                <span class=\"color-gray fn14\">Время преобразуется в метки времени Unix и обратно, а также поддерживается несколько форматов и настроек часовых поясов</span>\r\n                                            </div>\r\n                                        </a>\r\n                                        <a href=\"Tool.aspx?type=Calc\" class=\"pro-item color-default\">\r\n                                            <div class=\"letter-wrap\">\r\n                                                <h3 class=\"h6\">Калькулятор</h3>\r\n                                                <span class=\"color-gray fn14\">Научный онлайн-калькулятор с поддержкой основных операций и расширенных математических расчетов функций</span>\r\n                                            </div>\r\n                                        </a>\r\n                                    </div>\r\n                                </div>\r\n                            </div>\r\n                        </div>\r\n                    </section>\r\n                    <section id=\"articles-sub-nav\" class=\"pls-nav-dropdown solution-part\" style=\"visibility: hidden; height: 0px; display: none;\">\r\n                        <div class=\"d-flex nav-drown-con\" style=\"height: 0px; transition: transform 0.2s ease-in-out 0s,opacity 0.2s ease-in-out 0s; opacity: 0; transform: translate(0px, -100%);\">\r\n                            <div class=\"prdocu-sub-left\">\r\n                                <div class=\"mr-auto\">\r\n                                    <picture>\r\n                                        <source srcset=\"site/image/icon/p_4.png.webp\" type=\"image/webp\" />\r\n                                        <img class=\"p-icon\" src=\"site/image/icon/p_4.png\" alt=\"Иконка Центра обмена технологиями\" aria-hidden=\"true\" loading=\"lazy\" />\r\n                                    </picture>\r\n                                    <div class=\"mt-4\">\r\n                                        <h3 class=\"h6\">Обмен технологиями OCR</h3>\r\n                                        <p class=\"color-gray fn14 mt-3 sub-left-desc\">Технические руководства, примеры применения, рекомендации по инструментам</p>\r\n                                        <p class=\"color-gray fn14 mt-3 sub-left-desc\">&nbsp;</p>\r\n                                        <p class=\"color-gray fn14 mt-3 sub-left-desc\">Полный путь обучения от новичка до мастерства</p>\r\n                                        <p class=\"color-gray fn14 mt-3 sub-left-desc\">Практические кейсы → технического анализа → применения инструментов</p>\r\n                                        <p class=\"color-gray fn14 mt-3 sub-left-desc\">Увеличьте свой путь к совершенствованию технологии OCR</p>\r\n                                        <a class=\"link block disa fn14 contact-business shake\" href=\"Articles.aspx\">Просмотреть статьи<span class=\"iconfont4 icon-xiangzuo gt\"></span></a>\r\n                                    </div>\r\n                                </div>\r\n                            </div>\r\n                            <div class=\"bg-white prdocu-sub-right\">\r\n                                <div>\r\n                                    <h2 class=\"text-base-color h6\">Обмен технологиями</h2>\r\n                                    <div class=\"mt-4 d-flex flex-wrap\">\r\n                                        <a href=\"Articles.aspx\" class=\"pro-item color-default\" title=\"Просмотреть все технические статьи об OCR\">\r\n                                            <div class=\"letter-wrap\">\r\n                                                <h3 class=\"h6\">Все статьи</h3>\r\n                                                <span class=\"color-gray fn14\">Просмотрите все технические статьи OCR, охватывающие полный объем знаний от базовых до продвинутых</span>\r\n                                            </div>\r\n                                        </a>\r\n                                        \r\n                                        <a href=\"Articles.aspx?category=2\" class=\"pro-item color-default\" title=\"Технические учебные пособия по OCR и руководства по началу работы\">\r\n                                            <div class=\"letter-wrap\">\r\n                                                <h3 class=\"h6\">Продвинутое руководство</h3>\r\n                                                <span class=\"color-gray fn14\">От вводных до опытных технических руководств по OCR, подробных практических руководств и практических руководств</span>\r\n                                            </div>\r\n                                        </a>\r\n                                        \r\n                                        <a href=\"Articles.aspx?category=3\" class=\"pro-item color-default\" title=\"Принципы, алгоритмы и приложения технологии OCR\">\r\n                                            <div class=\"letter-wrap\">\r\n                                                <h3 class=\"h6\">Технологические исследования</h3>\r\n                                                <span class=\"color-gray fn14\">Исследуйте границы технологии OCR, от принципов до приложений, и глубоко анализируйте основные алгоритмы</span>\r\n                                            </div>\r\n                                        </a>\r\n                                        \r\n                                        <a href=\"Articles.aspx?category=4\" class=\"pro-item color-default\" title=\"Последние разработки и тенденции развития в индустрии OCR\">\r\n                                            <div class=\"letter-wrap\">\r\n                                                <h3 class=\"h6\">Отраслевые тренды</h3>\r\n                                                <span class=\"color-gray fn14\">Глубокое понимание тенденций развития технологии OCR, анализ рынка, динамика отрасли и перспективы на будущее</span>\r\n                                            </div>\r\n                                        </a>\r\n                                        \r\n                                        <a href=\"Articles.aspx?category=5\" class=\"pro-item color-default\" title=\"Кейсы применения технологии OCR в различных отраслях промышленности\">\r\n                                            <div class=\"letter-wrap\">\r\n                                                <h3 class=\"h6\">Примеры использования:</h3>\r\n                                                <span class=\"color-gray fn14\">Обмен реальными примерами применения, решениями и передовым опытом технологии OCR в различных отраслях</span>\r\n                                            </div>\r\n                                        </a>\r\n                                        \r\n                                        <a href=\"Articles.aspx?category=6\" class=\"pro-item color-default\" title=\"Профессиональные обзоры, сравнительный анализ и рекомендации по использованию программных инструментов OCR\">\r\n                                            <div class=\"letter-wrap\">\r\n                                                <h3 class=\"h6\">Обзор инструмента</h3>\r\n                                                <span class=\"color-gray fn14\">Оценивайте различные программы и инструменты для распознавания текста OCR, а также предоставляйте подробное сравнение функций и предложения по выбору</span>\r\n                                            </div>\r\n                                        </a>\r\n                                        \r\n                                    </div>\r\n                                </div>\r\n                            </div>\r\n                        </div>\r\n                    </section>\r\n                    <section id=\"vip-sub-nav\" class=\"pls-nav-dropdown solution-part\" style=\"visibility: hidden; height: 0px; display: none;\">\r\n                        <div class=\"d-flex nav-drown-con\" style=\"height: 0px; transition: transform 0.2s ease-in-out 0s,opacity 0.2s ease-in-out 0s; opacity: 0; transform: translate(0px, -100%);\">\r\n                            <div class=\"prdocu-sub-left\">\r\n                                <div class=\"mr-auto\">\r\n                                    <picture>\r\n                                        <source class=\"p-icon\" srcset=\"site/image/icon/p_1.png.webp\" type=\"image/webp\" alt=\"\" aria-hidden=\"true\" />\r\n                                        <img class=\"p-icon\" src=\"site/image/icon/p_1.png\" alt=\"Иконка услуги повышения уровня подписки\" aria-hidden=\"true\" loading=\"lazy\" />\r\n                                    </picture>\r\n                                    <div class=\"mt-4\">\r\n                                        <h3 class=\"h6\">Услуга повышения уровня членства</h3>\r\n                                        <p class=\"color-gray fn14 mt-3 sub-left-desc\">Откройте для себя все премиальные функции и наслаждайтесь эксклюзивными услугами</p>\r\n                                        <p class=\"color-gray fn14 mt-3 sub-left-desc\">&nbsp;</p>\r\n                                        <p class=\"color-gray fn14 mt-3 sub-left-desc\">Автономное распознавание, пакетная обработка, неограниченное использование</p>\r\n                                        <p class=\"color-gray fn14 mt-3 sub-left-desc\">Pro → Ultimate → Enterprise</p>\r\n                                        <p class=\"color-gray fn14 mt-3 sub-left-desc\">Есть что-то, что удовлетворит ваши потребности</p>\r\n                                        <a class=\"link block disa fn14 contact-business shake\" href=\"Version.aspx\">Подробнее<span class=\"iconfont4 icon-xiangzuo gt\"></span></a>\r\n                                    </div>\r\n                                </div>\r\n                            </div>\r\n                            <div class=\"bg-white prdocu-sub-right\">\r\n                                <div>\r\n                                    <h2 class=\"text-base-color h6\" style=\"margin: inherit;\">Повышение уровня членства</h2>\r\n                                    <div class=\"mt-4 d-flex flex-wrap\">\r\n                                        <a href=\"Version.aspx\" class=\"pro-item color-default\">\r\n                                            <div class=\"letter-wrap\">\r\n                                                <h3 class=\"h6\">Привилегии членства</h3>\r\n                                                <span class=\"color-gray fn14\">Узнайте больше о различиях между редакциями и выберите наиболее подходящий для вас уровень подписки</span>\r\n                                            </div>\r\n                                        </a>\r\n                                        <a href=\"Upgrade.aspx\" class=\"pro-item color-default\">\r\n                                            <div class=\"letter-wrap\">\r\n                                                <h3 class=\"h6\">Обновить сейчас</h3>\r\n                                                <span class=\"color-gray fn14\">Быстро повысьте уровень своего VIP-членства, чтобы получить доступ к большему количеству премиальных функций и эксклюзивных услуг</span>\r\n                                            </div>\r\n                                        </a>\r\n                                        <a href=\"User.aspx\" class=\"pro-item color-default\">\r\n                                            <div class=\"letter-wrap\">\r\n                                                <h3 class=\"h6\">Моя учетная запись</h3>\r\n                                                <span class=\"color-gray fn14\">Управление информацией об учетной записи, статусом подписки и историей использования для персонализации параметров</span>\r\n                                            </div>\r\n                                        </a>\r\n                                    </div>\r\n                                </div>\r\n                            </div>\r\n                        </div>\r\n                    </section>\r\n                    <section id=\"support-sub-nav\" class=\"pls-nav-dropdown solution-part\" style=\"visibility: hidden; height: 0px; display: none;\">\r\n                        <div class=\"d-flex nav-drown-con\" style=\"height: 0px; transition: transform 0.2s ease-in-out 0s,opacity 0.2s ease-in-out 0s; opacity: 0; transform: translate(0px, -100%);\">\r\n                            <div class=\"prdocu-sub-left\">\r\n                                <div class=\"mr-auto\">\r\n                                    <picture>\r\n                                        <source class=\"p-icon\" srcset=\"site/image/icon/p_4.png.webp\" type=\"image/webp\" alt=\"\" aria-hidden=\"true\" />\r\n                                        <img class=\"p-icon\" src=\"site/image/icon/p_4.png\" alt=\"Значок поддержки в Справочном центре\" aria-hidden=\"true\" loading=\"lazy\" />\r\n                                    </picture>\r\n                                    <div class=\"mt-4\">\r\n                                        <h3 class=\"h6\">Справочный центр</h3>\r\n                                        <p class=\"color-gray fn14 mt-3 sub-left-desc\">Профессиональное обслуживание клиентов, подробная документация и быстрая реакция</p>\r\n                                        <p class=\"color-gray fn14 mt-3 sub-left-desc\">&nbsp;</p>\r\n                                        <p class=\"color-gray fn14 mt-3 sub-left-desc\">Не паникуйте, когда сталкиваетесь с проблемами</p>\r\n                                        <p class=\"color-gray fn14 mt-3 sub-left-desc\">Проблема → найти → решена</p>\r\n                                        <p class=\"color-gray fn14 mt-3 sub-left-desc\">Сделайте свой опыт более плавным</p>\r\n                                        <a class=\"link block disa fn14 contact-business shake\" href=\"FAQ.aspx\">Получить помощь<span class=\"iconfont4 icon-xiangzuo gt\"></span></a>\r\n                                    </div>\r\n                                </div>\r\n                            </div>\r\n                            <div class=\"bg-white prdocu-sub-right\">\r\n                                <div>\r\n                                    <h2 class=\"text-base-color h6\" style=\"margin: inherit;\">Справочный центр</h2>\r\n                                    <div class=\"mt-4 d-flex flex-wrap\">\r\n                                        <a href=\"FAQ.aspx\" class=\"pro-item color-default\">\r\n                                            <div class=\"letter-wrap\">\r\n                                                <h3 class=\"h6\">Часто задаваемые вопросы</h3>\r\n                                                <span class=\"color-gray fn14\">Быстро отвечайте на распространенные вопросы пользователей и предоставляйте подробные руководства по использованию и техническую поддержку</span>\r\n                                            </div>\r\n                                        </a>\r\n                                        <a href=\"AboutUs.aspx\" class=\"pro-item color-default\">\r\n                                            <div class=\"letter-wrap\">\r\n                                                <h3 class=\"h6\">О нас</h3>\r\n                                                <span class=\"color-gray fn14\">Узнайте об истории разработки, основных функциях и сервисных концепциях помощника распознавания текста OCR</span>\r\n                                            </div>\r\n                                        </a>\r\n                                        <a href=\"UserAgreement.aspx\" class=\"pro-item color-default\">\r\n                                            <div class=\"letter-wrap\">\r\n                                                <h3 class=\"h6\">Пользовательское соглашение</h3>\r\n                                                <span class=\"color-gray fn14\">Подробные условия предоставления услуг, а также права и обязанности пользователя</span>\r\n                                            </div>\r\n                                        </a>\r\n                                        <a href=\"PrivacyPolicy.aspx\" class=\"pro-item color-default\">\r\n                                            <div class=\"letter-wrap\">\r\n                                                <h3 class=\"h6\">Соглашение о конфиденциальности</h3>\r\n                                                <span class=\"color-gray fn14\">Политика защиты личной информации и меры безопасности данных</span>\r\n                                            </div>\r\n                                        </a>\r\n                                        <a href=\"Status.aspx\" class=\"pro-item color-default\">\r\n                                            <div class=\"letter-wrap\">\r\n                                                <h3 class=\"h6\">Состояние системы</h3>\r\n                                                <span class=\"color-gray fn14\">Мониторинг рабочего состояния глобальных идентификационных узлов в режиме реального времени и просмотр данных о производительности системы</span>\r\n                                            </div>\r\n                                        </a>\r\n                                        <a href=\"javascript:void(0);\" class=\"pro-item color-default\" onclick=\"if(window.openCustomerService) window.openCustomerService(); else alert('Пожалуйста, нажмите на значок плавающего окна справа, чтобы связаться со службой поддержки');\">\r\n                                            <div class=\"letter-wrap\">\r\n                                                <h3 class=\"h6\">Связаться со службой поддержки</h3>\r\n                                                <span class=\"color-gray fn14\">Онлайн-поддержка клиентов для быстрого ответа на ваши вопросы и потребности</span>\r\n                                            </div>\r\n                                        </a>\r\n                                    </div>\r\n                                </div>\r\n                            </div>\r\n                        </div>\r\n                    </section>\r\n                </div>\r\n            </div>\r\n            <div class=\"header_pc_right\" style=\"display: flex; align-items: center; justify-content: flex-end;\">\r\n                <div id=\"translate\"></div>\r\n                <div id=\"user-login-container\" data-url=\"User.ashx?op=userinfo\" style=\"white-space: nowrap; padding-right: 20px;\"></div>\r\n            </div>\r\n        </header>\r\n        <header class=\"v4_header_mob\">\r\n            <div class=\"container\">\r\n                <div class=\"row no-gutters justify-content-between align-items-center\">\r\n                    <a href=\"Default.aspx\" title=\"Главная | Интеллектуальное распознавание текста на основе искусственного интеллекта\" class=\"mob-logo-wrap\">\r\n                        <picture>\r\n                            <source srcset=\"site/image/logo-s.png.webp\" type=\"image/webp\" />\r\n                            <img src=\"site/image/logo-s.png\" class=\"pc-logo\" style=\"height: 32px; width: 32px\" alt=\"Мобильный логотип помощника по распознаванию текста OCR\" />\r\n                        </picture>\r\n                        <span style=\"font-size: 18px; font-weight: bold; color: #333;\">Помощник по распознаванию текста OCR</span>\r\n                    </a>\r\n                    <div class=\"right-menu\" role=\"button\" tabindex=\"0\" aria-label=\"Откройте меню навигации\" data-toggle=\"collapse\" data-target=\"#mob-header-collapse\" aria-expanded=\"false\" aria-controls=\"mob-header-collapse\">\r\n                        <nav-products id=\"nav_products\">\r\n                            <nav-button class=\"submail-mdi-nav-btn\" style=\"width: 60px\">\r\n                                <label id=\"mdi-submail-nav-btn\">\r\n                                    <span class=\"mdi-nav-products-1\">\r\n                                        <span class=\"nav-products-icon\"></span>\r\n                                    </span>\r\n                                    <span class=\"mdi-nav-products-2\">\r\n                                        <span class=\"nav-products-icon\"></span>\r\n                                    </span>\r\n                                    <span class=\"mdi-nav-products-3\">\r\n                                        <span class=\"nav-products-icon\"></span>\r\n                                    </span>\r\n                                </label>\r\n                            </nav-button>\r\n                        </nav-products>\r\n                    </div>\r\n                </div>\r\n            </div>\r\n            <div class=\"mob-nav-content\">\r\n                <div class=\"sidebar-fix\">\r\n                    <div class=\"container\">\r\n                        <div class=\"row\">\r\n                            <div class=\"col-4 bg-white sidebar-fix-left\" style=\"border-right: 1px solid #e1e6ed\">\r\n                                <div class=\"mob-nav-item active\" data=\"0\">\r\n                                    <div class=\"nav-header\"><a href=\"Default.aspx\" class=\"nav-header-letter px-3\"><span>Дом</span></a></div>\r\n                                </div>\r\n                                <div class=\"mob-nav-item\" data=\"1\">\r\n                                    <div class=\"nav-header\"><a href=\"Detail.aspx\" class=\"nav-header-letter px-3\"><span>функция</span></a></div>\r\n                                </div>\r\n                                <div class=\"mob-nav-item\" data=\"2\">\r\n                                    <div class=\"nav-header\"><a href=\"Ocr.aspx\" class=\"nav-header-letter px-3\"><span>опыт</span></a></div>\r\n                                </div>\r\n                                <div class=\"mob-nav-item\" data=\"3\">\r\n                                    <div class=\"nav-header\"><a href=\"Version.aspx\" class=\"nav-header-letter px-3\"><span>член</span></a></div>\r\n                                </div>\r\n                                <div class=\"mob-nav-item\" data=\"4\">\r\n                                    <div class=\"nav-header\"><a href=\"Download.aspx\" class=\"nav-header-letter px-3\"><span>Загружать</span></a></div>\r\n                                </div>\r\n                                <div class=\"mob-nav-item\" data=\"5\">\r\n                                    <div class=\"nav-header\"><a href=\"Articles.aspx\" class=\"nav-header-letter px-3\"><span>Предоставить общий доступ</span></a></div>\r\n                                </div>\r\n                                <div class=\"mob-nav-item\" data=\"6\">\r\n                                    <div class=\"nav-header\"><a href=\"FAQ.aspx\" class=\"nav-header-letter px-3\"><span>Справка</span></a></div>\r\n                                </div>\r\n                            </div>\r\n                            <div class=\"col-8 bg-white sidebar-fix-rigth\">\r\n                                <div class=\"sub-nav\">\r\n                                    <div class=\"sub-nav-item py-2 pb-3 px-4\">\r\n                                        <h2 class=\"h5\" style=\"margin: inherit;\">Эффективные инструменты для повышения производительности</h2>\r\n                                        <p class=\"color-gray fn14 mt-3 sub-left-desc\">Интеллектуальное распознавание, высокая скорость обработки и точный вывод</p>\r\n                                        <p class=\"color-gray fn14 mt-3 sub-left-desc\">&nbsp;</p>\r\n                                        <p class=\"color-gray fn14 mt-3 sub-left-desc\">Распознавание целой страницы документов за 3 секунды</p>\r\n                                        <p class=\"color-gray fn14 mt-3 sub-left-desc\">Точность распознавания 98%+</p>\r\n                                        <p class=\"color-gray fn14 mt-3 sub-left-desc\">Многоязычная обработка в режиме реального времени без задержек</p>\r\n                                        <a class=\"link block disa fn14 contact-business shake\" href=\"Download.aspx\">Загрузите опыт прямо сейчас<span class=\"iconfont4 icon-xiangzuo gt\"></span></a>\r\n                                    </div>\r\n                                </div>\r\n                                <div class=\"sub-nav\">\r\n                                    <div class=\"sub-nav-item py-3 px-4\">\r\n                                        <h2 class=\"h5\" style=\"margin: inherit;\">Особенности продукта:</h2>\r\n                                        <p class=\"color-gray fn14 mt-3 sub-left-desc\">Интеллектуальная идентификация на основе искусственного интеллекта, универсальное решение</p>\r\n                                    </div>\r\n                                    <div class=\"sub-nav-item py-3 px-4\"><a href=\"Detail.aspx\">Введение функции</a></div>\r\n                                    <div class=\"sub-nav-item py-3 px-4\"><a href=\"Download.aspx\">Загрузка программного обеспечения</a></div>\r\n                                    <div class=\"sub-nav-item py-3 px-4\"><a href=\"Version.aspx\">Сравнение версий</a></div>\r\n                                    <div class=\"sub-nav-item py-3 px-4\"><a href=\"Ocr.aspx\">Онлайн-опыт</a></div>\r\n                                    <div class=\"sub-nav-item py-3 px-4\"><a href=\"Status.aspx\">Состояние системы</a></div>\r\n                                </div>\r\n                                <div class=\"sub-nav\">\r\n                                    <div class=\"sub-nav-item py-3 px-4\">\r\n                                        <h2 class=\"h5\" style=\"margin: inherit;\">Онлайн-опыт</h2>\r\n                                        <p class=\"color-gray fn14 mt-3 sub-left-desc\">Бесплатный онлайн-опыт работы с функцией OCR</p>\r\n                                    </div>\r\n                                    <div class=\"sub-nav-item py-3 px-4\"><a href=\"Ocr.aspx\">Полный функционал</a></div>\r\n                                    <div class=\"sub-nav-item py-3 px-4\"><a href=\"Ocr.aspx?type=text_recognize\">Распознавание слов</a></div>\r\n                                    <div class=\"sub-nav-item py-3 px-4\"><a href=\"Ocr.aspx?type=table\">Идентификация таблиц</a></div>\r\n                                    <div class=\"sub-nav-item py-3 px-4\"><a href=\"Ocr.aspx?type=pdf2word\">PDF в Word</a></div>\r\n                                </div>\r\n                                <div class=\"sub-nav\">\r\n                                    <div class=\"sub-nav-item py-3 px-4\">\r\n                                        <h2 class=\"h5\" style=\"margin: inherit;\">Повышение уровня членства</h2>\r\n                                        <p class=\"color-gray fn14 mt-3 sub-left-desc\">Откройте для себя все функции и наслаждайтесь эксклюзивными услугами</p>\r\n                                    </div>\r\n                                    <div class=\"sub-nav-item py-3 px-4\"><a href=\"Version.aspx\">Преимущества членства</a></div>\r\n                                    <div class=\"sub-nav-item py-3 px-4\"><a href=\"Upgrade.aspx\">Активировать немедленно</a></div>\r\n                                </div>\r\n                                <div class=\"sub-nav\">\r\n                                    <div class=\"sub-nav-item py-3 px-4\">\r\n                                        <h2 class=\"h5\" style=\"margin: inherit;\">Загрузка программного обеспечения</h2>\r\n                                        <p class=\"color-gray fn14 mt-3 sub-left-desc\">Скачайте профессиональное программное обеспечение для оптического распознавания символов бесплатно</p>\r\n                                    </div>\r\n                                    <div class=\"sub-nav-item py-3 px-4\"><a href=\"Download.aspx\">Загрузить сейчас</a></div>\r\n                                    <div class=\"sub-nav-item py-3 px-4\"><a href=\"Version.aspx\">Сравнение версий</a></div>\r\n                                </div>\r\n                                <div class=\"sub-nav\">\r\n                                    <div class=\"sub-nav-item py-3 px-4\">\r\n                                        <h2 class=\"h5\" style=\"margin: inherit;\">Обмен технологиями</h2>\r\n                                        <p class=\"color-gray fn14 mt-3 sub-left-desc\">Технические статьи и обмен знаниями в области OCR</p>\r\n                                    </div>\r\n                                    <div class=\"sub-nav-item py-3 px-4\"><a href=\"Articles.aspx\">Все статьи</a></div>\r\n                                    \r\n                                    <div class=\"sub-nav-item py-3 px-4\"><a href=\"Articles.aspx?category=2\">Продвинутое руководство</a></div>\r\n                                    \r\n                                    <div class=\"sub-nav-item py-3 px-4\"><a href=\"Articles.aspx?category=3\">Технологические исследования</a></div>\r\n                                    \r\n                                    <div class=\"sub-nav-item py-3 px-4\"><a href=\"Articles.aspx?category=4\">Отраслевые тренды</a></div>\r\n                                    \r\n                                    <div class=\"sub-nav-item py-3 px-4\"><a href=\"Articles.aspx?category=5\">Примеры использования:</a></div>\r\n                                    \r\n                                    <div class=\"sub-nav-item py-3 px-4\"><a href=\"Articles.aspx?category=6\">Обзор инструмента</a></div>\r\n                                    \r\n                                </div>\r\n                                <div class=\"sub-nav\">\r\n                                    <div class=\"sub-nav-item py-3 px-4\">\r\n                                        <h2 class=\"h5\" style=\"margin: inherit;\">Справочный центр</h2>\r\n                                        <p class=\"color-gray fn14 mt-3 sub-left-desc\">Профессиональное обслуживание клиентов, интимное обслуживание</p>\r\n                                    </div>\r\n                                    <div class=\"sub-nav-item py-3 px-4\"><a href=\"FAQ.aspx\">Используйте справку</a></div>\r\n                                    <div class=\"sub-nav-item py-3 px-4\"><a href=\"AboutUs.aspx\">О нас</a></div>\r\n                                    <div class=\"sub-nav-item py-3 px-4\"><a href=\"mailto:<EMAIL>\">Связаться со службой поддержки</a></div>\r\n                                    <div class=\"sub-nav-item py-3 px-4\"><a href=\"UserAgreement.aspx\">Условия предоставления услуг</a></div>\r\n                                </div>\r\n                            </div>\r\n                        </div>\r\n                    </div>\r\n                </div>\r\n            </div>\r\n        </header>\r\n        <form method=\"post\" action=\"./Article.aspx?id=81&amp;slug=rnn-sequence-modeling&amp;lang=zh-Hans&amp;_translation_source=true\" id=\"aspnetForm\">\r\n<div class=\"aspNetHidden\">\r\n<input type=\"hidden\" name=\"__VIEWSTATE\" id=\"__VIEWSTATE\" value=\"Q9uUeUlUJtaRG68B9ZsvqxMA0nWMrG97wrg6XH6pF/PALpTNvdWvXETFldXabtYfiX/3jguNbdKAEx4O02Rz9ETQIoNHodxI3/t/Hx/oGKM=\" />\r\n</div>\r\n\r\n<div class=\"aspNetHidden\">\r\n\r\n\t<input type=\"hidden\" name=\"__VIEWSTATEGENERATOR\" id=\"__VIEWSTATEGENERATOR\" value=\"2173C2F0\" />\r\n</div>\r\n            <div>\r\n                \n\n<div class=\"reading-progress\" id=\"readingProgress\"></div>\n<div class=\"article-detail-container\">\n<div class=\"container\">\n<div class=\"row\">\n<div class=\"col-lg-8 col-xl-9 col-md-12\">\n                    \n                    <article class=\"article-detail\" id=\"articleDetail\" data-article-id=\"81\">\n                        <header class=\"article-header\">\n                            <h1 class=\"article-title\">【Серия OCR с глубоким обучением·4】Рекуррентные нейронные сети и моделирование последовательностей</h1>\n                            <div class=\"article-meta\">\n                                <div class=\"meta-item\">\n                                    <span class=\"meta-icon\">📅</span>\n                                    <span>Время публикации: 2025-08-19</span>\n                                </div>\n                                <div class=\"meta-item\">\n                                    <span class=\"meta-icon\">👁️</span>\n                                    <span>Чтение:<span class=\"view-count\">1200</span></span>\n                                </div>\n                                <div class=\"meta-item\">\n                                    <span class=\"meta-icon\">⏱️</span>\n                                    <span>Около 50 минут (9819 слов)</span>\n                                </div>\n                                \n                                <div class=\"meta-item\">\n                                    <span class=\"meta-icon\">📁</span>\n                                    <span>Категория: Продвинутые руководства</span>\n                                </div>\n                                \n                            </div>\n                            <div class=\"article-summary\"><p>Погрузитесь в применение RNN, LSTM, GRU в OCR. Подробный анализ принципов моделирования последовательностей, решения градиентных задач и преимуществ двунаправленных РНС.</p></div>\n                        </header>\n                        <div class=\"article-content\" id=\"articleContent\">## Введение\r\n\r\nРекуррентная нейронная сеть (RNN) — это архитектура нейронной сети в глубоком обучении, которая специализируется на обработке данных последовательностей. В задачах OCR распознавание текста — это, по сути, задача преобразования последовательности элементов изображения в последовательность текстовых символов. В этой статье мы подробно рассмотрим, как работает RNN, его основные варианты и конкретные применения в OCR, предоставляя читателям всестороннюю теоретическую основу и практические рекомендации.\r\n\r\n## Основы RNN\r\n\r\n### Ограничения традиционных нейронных сетей\r\n\r\nТрадиционные нейронные сети с прямой связью имеют фундаментальные ограничения в обработке данных последовательностей. Эти сети предполагают, что входные данные независимы и гомораспределены, и не могут охватить временные зависимости между элементами последовательности.\r\n\r\n**Проблемы с сетью прямой связи**:\r\n- Фиксированная длина входа и выхода: последовательности переменной длины не могут быть обработаны\r\n- Недостаток памяти: неспособность использовать историческую информацию\r\n- Сложность в совместном использовании параметров: один и тот же шаблон необходимо изучать неоднократно в разных местах\r\n- Позиционная чувствительность: изменение порядка входов может привести к совершенно разным выходам\r\n\r\nЭти ограничения особенно заметны в задачах OCR. Текстовые последовательности в значительной степени зависят от контекста, и результаты распознавания предыдущего символа часто помогают определить вероятность появления последующих символов. Например, при идентификации английского слова \"the\", если \"th\" уже распознан, то следующим символом, скорее всего, будет \"e\".\r\n\r\n### Основная идея RNN\r\n\r\nRNN решает проблему моделирования последовательностей путем введения циклических соединений. Основная идея состоит в том, чтобы добавить в сеть механизм «памяти», чтобы сеть могла хранить и использовать информацию из предыдущих моментов.\r\n\r\n**Математическое представление RNN**:\r\nВ момент t скрытое состояние RNN h_t определено текущим входным x_t и скрытым состоянием предыдущего момента h_{t-1}:\r\n\r\nh_t = f(W_hh * h_{t-1} + W_xh * x_t + b_h)\r\n\r\nДля этого:\r\n- W_hh — матрица весов из скрытого состояния в скрытое\r\n- W_xh — весовая матрица, введенная в скрытое состояние  \r\n- b_h — вектор смещения\r\n- f — функция активации (обычно tanh или ReLU)\r\n\r\nВыходной y_t вычисляется из текущего скрытого состояния:\r\ny_t = W_hy * h_t + b_y\r\n\r\n**Преимущества RNN**:\r\n- Совместное использование параметров: одни и те же весовые коэффициенты используются на всех временных шагах\r\n- Обработка последовательностей переменной длины: может обрабатывать входные последовательности произвольной длины\r\n- Способность к запоминанию: скрытые состояния действуют как «воспоминания» о сети\r\n- Гибкий ввод и вывод: поддерживает режимы «один к одному», «один ко многим», «многие к одному», «многие ко многим» и многое другое\r\n\r\n### Расширенный вид RNN\r\n\r\nЧтобы лучше понять, как работают РНС, мы можем расширить их во временном измерении. Расширенная RNN выглядит как глубокая сеть прямой связи, но все временные шаги имеют одни и те же параметры.\r\n\r\n**Значение разворачивающегося времени**:\r\n- Простой для понимания поток информации: можно четко видеть, как информация передается между временными шагами\r\n- Расчет градиента: Градиенты рассчитываются с помощью алгоритма обратного распространения времени (BPTT)\r\n- Соображения по распараллеливанию: Хотя RNN по своей сути являются последовательными, некоторые операции могут быть распараллелены\r\n\r\n**Математическое описание процесса развертки**:\r\nДля последовательностей длины T RNN расширяется следующим образом:\r\nh_1 = f(W_xh * x_1 + b_h)\r\nh_2 = f(W_hh * h_1 + W_xh * x_2 + b_h)\r\nh_3 = f(W_hh * h_2 + W_xh * x_3 + b_h)\r\n...\r\nh_T = f(W_hh * h_{T-1} + W_xh * x_T + b_h)\r\n\r\nЭта развернутая форма наглядно показывает, как передается информация между временными шагами и как параметры распределяются между всеми временными шагами.\r\n\r\n## Исчезновение градиента и проблема взрыва\r\n\r\n### Корень проблемы\r\n\r\nПри обучении РНС мы используем алгоритм обратного распространения во времени (BPTT). Алгоритму необходимо рассчитать градиент функции потерь для каждого параметра временного шага.\r\n\r\n**Закон цепи для вычисления градиента**:\r\nЕсли последовательность длинная, градиент необходимо обратного распространения через несколько временных шагов. Согласно правилу цепи, градиент будет содержать кратные умножения матрицы весов:\r\n\r\n∂Л/∂Ш = Σ_t (∂Л/∂y_t) * (∂y_t/∂h_t) * (∂h_t/∂Вт)\r\n\r\nгде ∂h_t/∂W включает произведение всех промежуточных состояний от момента t до момента 1.\r\n\r\n**Математический анализ исчезновения градиента**:\r\nРассмотрим распространение градиентов между временными шагами:\r\n∂h_t/∂h_{t-1} = diag(f_prime(W_hh * h_{t-1} + W_xh * x_t + b_h)) * W_hh\r\n\r\nКогда длина последовательности равна T, градиент содержит T-1 такое произведение члена. Если максимальное собственное значение W_hh меньше 1, непрерывное умножение матриц приведет к экспоненциальному затуханию градиента.\r\n\r\n**Математический анализ градиентных взрывов**:\r\nИ наоборот, когда максимальное собственное значение W_hh больше 1, градиент увеличивается экспоненциально:\r\n|| ∂h_t/∂h_1|| ≈ || W_hh|| ^{т-1}\r\n\r\nЭто приводит к нестабильному обучению и чрезмерному обновлению параметров.\r\n\r\n### Подробное объяснение решения\r\n\r\nОбрезка градиента:\r\nГрадиентная обрезка — это самый прямой способ решения градиентных взрывов. Когда норма градиента превышает заданное пороговое значение, градиент масштабируется до порогового размера. Этот способ прост и эффективен, но требует тщательного подбора порогов. Слишком маленький порог ограничит способность к обучению, а слишком большой порог не сможет эффективно предотвратить градиентный взрыв.\r\n\r\n**Стратегия инициализации веса**:\r\nПравильная инициализация веса может облегчить проблемы с градиентом:\r\n- Инициализация Ксавье: дисперсия веса равна 1/n, где n - входной размер.\r\n- Инициализация: дисперсия веса составляет 2/n, что подходит для функций активации ReLU\r\n- Ортогональная инициализация: инициализирует весовую матрицу как ортогональную матрицу\r\n\r\n**Выбор функций активации**:\r\nРазличные функции активации по-разному влияют на распространение градиента:\r\n- tanh: выходной диапазон [-1,1], максимальное значение градиента 1\r\n- ReLU: может облегчить исчезновение градиента, но может вызвать гибель нейронов\r\n- Leaky ReLU: Решает проблему смерти нейронов ReLU\r\n\r\n**Архитектурные улучшения**:\r\nСамым принципиальным решением стало усовершенствование архитектуры RNN, что привело к появлению LSTM и GRU. Эти архитектуры решают градиенты с помощью механизмов стробирования и специализированных конструкций информационных потоков.\r\n\r\n## LSTM: Сеть долговременной кратковременной памяти\r\n\r\n### Мотивация дизайна для LSTM\r\n\r\nLSTM (Long Short-Term Memory) — вариант RNN, предложенный Хохрайтером и Шмидхубером в 1997 году, специально разработанный для решения проблемы исчезновения градиента и трудностей обучения, зависящих от расстояния.\r\n\r\n**Основные инновации LSTM**:\r\n- Состояние ячейки: служит «магистралью» для информации, позволяя ей напрямую перемещаться между временными шагами\r\n- Стробировочный механизм: точный контроль над поступлением, хранением и выводом информации\r\n- Механизмы диссоциированной памяти: различают кратковременную память (скрытое состояние) и долговременную память (клеточное состояние)\r\n\r\n**Как LSTM решает задачи градиента**:\r\nLSTM обновляет состояние ячейки с помощью аддитивных, а не мультипликативных операций, что позволяет градиентам легче перетекать к более ранним временным шагам. Обновлена формула для состояния ячейки:\r\n\r\nC_t = f_t ⊙ C_{t-1} + i_t ⊙ C_tilde_t\r\n\r\nЗдесь используется сложение на уровне элементов, что позволяет избежать непрерывного умножения матриц в традиционных РНС.\r\n\r\n### Подробное объяснение архитектуры LSTM\r\n\r\nLSTM содержит три блока стробирования и состояние ячейки:\r\n\r\n**1. Забудьте о гейте**:\r\nВрата забвения решают, какую информацию отбросить из состояния клетки:\r\nf_t = σ(W_f · [h_{t-1}, x_t] + b_f)\r\n\r\nВыходными данными ворот забвения является значение от 0 до 1, где 0 означает «полностью забыто», а 1 — «полностью сохранено». Этот гейт позволяет LSTM выборочно забывать неважную историческую информацию.\r\n\r\n**2. Входной вентиль**:\r\nВходной вентиль определяет, какая новая информация сохраняется в состоянии ячейки:\r\ni_t = σ(W_i · [h_{t-1}, x_t] + b_i)\r\nC_tilde_t = tanh(W_C · [h_{t-1}, x_t] + b_C)\r\n\r\nВходной вентиль состоит из двух частей: сигмоидальный слой определяет, какие значения следует обновить, а слой tanh создает векторы потенциальных значений.\r\n\r\n**3. Обновление статуса ячейки**:\r\nОбъедините выходы забытого и входного вентилей для обновления состояния ячейки:\r\nC_t = f_t ⊙ C_{t-1} + i_t ⊙ C_tilde_t\r\n\r\nЭта формула лежит в основе LSTM: выборочное удержание и обновление информации с помощью операций умножения и сложения на уровне элементов.\r\n\r\n**4. Выходной вентиль**:\r\nВыходной вентиль определяет, какие части ячейки будут выведены:\r\no_t = σ(W_o · [h_{t-1}, x_t] + b_o)\r\nh_t = o_t ⊙ tanh(C_t)\r\n\r\nВыходной вентиль определяет, какие части состояния ячейки влияют на выходной ток.\r\n\r\n### Варианты LSTM\r\n\r\n**Глазок LSTM**:\r\nОснованный на стандартном LSTM, Peephole LSTM позволяет литниковому блоку просматривать состояние ячейки:\r\nf_t = σ(W_f · [C_{t-1}, h_{t-1}, x_t] + b_f)\r\ni_t = σ(W_i · [C_{t-1}, h_{t-1}, x_t] + b_i)\r\no_t = σ(W_o · [C_t, h_{t-1}, x_t] + b_o)\r\n\r\n**Сопряженный LSTM**:\r\nСоедините забытый вентиль с входным штором, чтобы убедиться, что количество забытой информации равно объему введенной информации:\r\nf_t = σ(W_f · [h_{t-1}, x_t] + b_f)\r\ni_t = 1 - f_t\r\n\r\nТакая конструкция уменьшает количество параметров при сохранении основной функциональности LSTM.\r\n\r\n## GRU: Блок с закрытым контуром\r\n\r\n### Упрощенная структура ГРУ\r\n\r\nGRU (Gated Recurrent Unit) — это упрощенная версия LSTM, предложенная Cho et al. в 2014 году. ГРУ упрощает три затвора LSTM до двух и объединяет клеточное и скрытое состояние.\r\n\r\n**Философия дизайна GRU**:\r\n- Упрощенная конструкция: Уменьшает количество дверей и снижает сложность расчетов\r\n- Поддержание производительности: упрощение при сохранении производительности, сопоставимой с LSTM\r\n- Простота в реализации: более простая конструкция обеспечивает простоту внедрения и ввода в эксплуатацию\r\n\r\n### Литниковый механизм ГРУ\r\n\r\n**1. Сброс ворот**:\r\nr_t = σ(W_r · [h_{t-1}, x_t] + b_r)\r\n\r\nВентиль сброса определяет, как объединить новый вход с предыдущей памятью. Когда вентиль сброса приближается к 0, модель игнорирует предыдущее скрытое состояние.\r\n\r\n**2. Ворота обновления**:\r\nz_t = σ(W_z · [h_{t-1}, x_t] + b_z)\r\n\r\nШлюз обновления определяет, какой объем прошлой информации следует сохранить и сколько новой информации следует добавить. Он контролирует как забытие, так и ввод, подобно комбинации вентилей забывания и ввода в LSTM.\r\n\r\n**3. Скрытый статус кандидата**:\r\nh_tilde_t = tanh(W_h · [r_t ⊙ h_{t-1}, x_t] + b_h)\r\n\r\nПотенциальные скрытые состояния используют вентиль сброса для управления эффектами предыдущего скрытого состояния.\r\n\r\n**4. Окончательное скрытое состояние**:\r\nh_t = (1 - z_t) ⊙ h_{t-1} + z_t ⊙ h_tilde_t\r\n\r\nОкончательное скрытое состояние является средневзвешенным значением предыдущего скрытого состояния и потенциального скрытого состояния.\r\n\r\n### Подробное сравнение ГРУ и ЛСТМ\r\n\r\n**Сравнение количества параметров**:\r\n- LSTM: 4 весовые матрицы (забытый вентиль, входной вентиль, потенциальное значение, выходной вентиль)\r\n- ГРУ: 3 весовые матрицы (сброс затвора, обновление затвора, потенциальное значение)\r\n- Количество параметров ГРУ составляет примерно 75% от ЛСТМ\r\n\r\n**Сравнение вычислительной сложности**:\r\n- LSTM: Требуется расчет 4 выходов вентиль и обновление состояния ячейки\r\n- GRU: Просто рассчитайте вывод 2 гейтов и скрытых обновлений статуса\r\n- ГРУ обычно на 20-30% быстрее, чем LSTM\r\n\r\n**Сравнение производительности**:\r\n- На большинстве задач ГРУ и ЛСТМ выполняют сопоставимо\r\n- LSTM может быть немного лучше, чем GRU, в некоторых задачах с длинной последовательностью\r\n- ГРУ — лучший выбор в тех случаях, когда вычислительные ресурсы ограничены\r\n\r\n## Двунаправленные РНС\r\n\r\n### Необходимость двусторонней обработки\r\n\r\nВо многих задачах моделирования последовательностей вывод настоящего момента опирается не только на прошлую, но и на будущую информацию. Это особенно важно в задачах OCR, где распознавание символов часто требует рассмотрения контекста всего слова или предложения.\r\n\r\n**Ограничения односторонних RNN**:\r\n- Можно использовать только историческую информацию, будущий контекст не может быть получен\r\n- Ограниченная производительность при выполнении определенных задач, особенно тех, которые требуют глобальной информации\r\n- Ограниченное распознавание неоднозначных символов\r\n\r\n**Преимущества двунаправленной обработки**:\r\n- Полная контекстная информация: используйте как прошлую, так и будущую информацию\r\n- Улучшенное устранение неоднозначности: Устранение неоднозначности с помощью контекстуальной информации\r\n- Повышенная точность распознавания: лучше справляется с большинством задач аннотации последовательностей\r\n\r\n### Двунаправленная архитектура LSTM\r\n\r\nДвунаправленный LSTM состоит из двух слоев LSTM:\r\n- Forward LSTM: последовательность процессов слева направо\r\n- Обратный LSTM: последовательность процессов справа налево\r\n\r\n**Математическое представление**:\r\nh_forward_t = LSTM_forward(x_t, h_forward_{t-1})\r\nh_backward_t = LSTM_backward(x_t, h_backward_{t+1})\r\nh_t = [h_forward_t; h_backward_t] # Сшивание скрытых состояний вперед и назад\r\n\r\n**Тренировочный процесс**:\r\n1. Forward LSTM обрабатывает последовательности в обычном порядке\r\n2. Обратный LSTM обрабатывает последовательности в обратном порядке\r\n3. На каждом временном шаге соединяйте скрытые состояния в обоих направлениях\r\n4. Используйте сплайсированное состояние для прогнозирования\r\n\r\n**Преимущества и недостатки**:\r\nПреимущество:\r\n- Полная контекстуальная информация\r\n- Повышенная производительность\r\n- Лечение симметрии\r\n\r\nНизшее положение:\r\n- Удвоенная сложность расчетов\r\n- Не может быть обработан в режиме реального времени (требуется полная последовательность)\r\n- Повышенные требования к памяти\r\n\r\n## Приложения для моделирования последовательностей в OCR\r\n\r\n### Подробное объяснение распознавания текстовых строк\r\n\r\nВ системах OCR распознавание строк текста является типичным приложением моделирования последовательностей. Этот процесс включает в себя преобразование последовательности особенностей изображения в последовательность символов.\r\n\r\n**Моделирование проблем**:\r\n- Входные данные: Последовательность характеристик изображения X = {x_1, x_2, ..., x_T}\r\n- Вывод: Последовательность символов Y = {y_1, y_2, ..., y_S}\r\n- Проблема: длина входной последовательности T и длина выходной последовательности S часто не равны\r\n\r\n**Применение архитектуры CRNN в распознавании строк текста**:\r\nCRNN (Convolutional Recurrent Neural Network) — одна из самых удачных архитектур в OCR:\r\n\r\n1. **Уровень извлечения функций CNN**:\r\n   - Извлечение особенностей изображения с помощью сверточных нейронных сетей\r\n   - Преобразование объектов 2D-изображений в последовательности 1D-элементов\r\n   - Поддержание непрерывности информации о времени\r\n\r\n2. **Слой моделирования последовательности RNN**:\r\n   - Моделирование последовательностей признаков с помощью двунаправленных LSTM\r\n   - Захват контекстуальных зависимостей между символами\r\n   - Распределение вероятности выходных символов для каждого временного шага\r\n\r\n3. **Слой выравнивания CTC**:\r\n   - Устраняет несоответствие длины входной/выходной последовательности\r\n   - Не требуются размеры выравнивания на уровне символов\r\n   - Сквозное обучение\r\n\r\n**Преобразование извлечения признаков в последовательность**:\r\nКарта признаков, извлеченная CNN, должна быть преобразована в форму последовательности, которую RNN может обработать:\r\n- Сегментируйте карту функций на столбцы, каждый из которых является временным шагом.\r\n- Ведение хронологии пространственной информации\r\n- Убедитесь, что длина последовательности признаков пропорциональна ширине изображения\r\n\r\n### Применение механизма внимания в OCR\r\n\r\nТрадиционные RNN по-прежнему имеют узкие места в информации при работе с длинными последовательностями. Внедрение механизмов внимания еще больше расширяет возможности моделирования последовательностей.\r\n\r\n**Принципы механизмов внимания**:\r\nМеханизм внимания позволяет модели фокусироваться на различных частях входной последовательности при генерации каждого выхода:\r\n- Решено информационное узкое место закодированных векторов фиксированной длины\r\n- Обеспечивает объяснимость модельных решений\r\n- Улучшена обработка длинных последовательностей\r\n\r\n**Конкретные приложения в OCR**:\r\n\r\n1. **Внимание на уровне персонажа**:\r\n   - Сосредоточьтесь на соответствующих областях изображения при идентификации каждого персонажа\r\n   - Регулируйте вес внимания на лету\r\n   - Повышенная устойчивость к сложным фонам\r\n\r\n2. **Внимание на уровне слов**:\r\n   - Учитывайте контекстуальную информацию на уровне лексики\r\n   - Использование знаний о языковых моделях\r\n   - Повышение точности распознавания целых слов\r\n\r\n3. **Многомасштабное внимание**:\r\n   - Применение механизмов внимания при разных разрешениях\r\n   - Работа с текстом разных размеров\r\n   - Повышение адаптивности к изменениям в масштабировании\r\n\r\n**Математическое представление механизма внимания**:\r\nДля последовательности вывода энкодера H = {h_1, h_2, ..., h_T} и состояния декодера s_t:\r\n\r\ne_{t,i} = a(s_t, h_i) # Оценка внимания\r\nα_{t,i} = softmax(e_{t,i}) # Вес внимания\r\nc_t = Σ_i α_{t,i} * h_i # вектор контекста\r\n\r\n## Стратегии обучения и оптимизация\r\n\r\n### Стратегия обучения от последовательности к последовательности\r\n\r\n**Принуждение учителя**:\r\nНа этапе обучения используйте реальную целевую последовательность в качестве входных данных декодера:\r\n- Плюсы: высокая скорость обучения, стабильная сходимость\r\n- Минусы: Непоследовательные фазы обучения и вывода, приводящие к накоплению ошибок\r\n\r\n**Выборка по расписанию**:\r\nПостепенно переходите от принуждения учителя к использованию собственных прогнозов модели во время обучения:\r\n- Используйте реальные метки на начальном этапе и моделируйте прогнозы на более поздних этапах\r\n- Уменьшение различий в обучении и рассуждении\r\n- Повышение надежности модели\r\n\r\n**Обучение по учебной программе**:\r\nНачните с простых образцов и постепенно увеличивайте сложность образцов:\r\n- Короткие и длинные последовательности: сначала обучайте короткие тексты, а затем длинные тексты\r\n- От четких до размытых изображений: постепенно увеличивайте сложность изображения\r\n- От простых к сложным шрифтам: от печатных до рукописных\r\n\r\n### Методы регуляризации\r\n\r\n**Применение Dropout в RNN**:\r\nПрименение дропаута в РНН требует особого внимания:\r\n- Не применяйте выпадение при подключении к петлям\r\n- Dropout может быть применен на входном и выходном слоях\r\n- Вариационный дропаут: используйте одну и ту же маску дропаута на всех временных шагах\r\n\r\n**Снижение веса**:\r\nРегуляризация L2 предотвращает переобучение:\r\nПотери = CrossEntropy + λ * || В|| ²\r\n\r\nгде λ — коэффициент регуляризации, который необходимо оптимизировать с помощью валидационного набора.\r\n\r\n**Градиентная обрезка**:\r\nЭффективный способ предотвращения градиентных взрывов. Когда норма градиента превышает пороговое значение, масштабируйте градиент пропорционально, чтобы направление градиента оставалось неизменным.\r\n\r\n**Ранняя остановка**:\r\nОтслеживайте производительность проверочного набора и останавливайте обучение, когда производительность перестает улучшаться:\r\n- Предотвращение переобучения\r\n- Экономия вычислительных ресурсов\r\n- Выбор оптимальной модели\r\n\r\n### Настройка гиперпараметров\r\n\r\n**Планирование темпов обучения**:\r\n- Начальный коэффициент обучения: обычно устанавливается на уровне 0,001-0,01\r\n- Затухание скорости обучения: экспоненциальное затухание или затухание по лестнице\r\n- Адаптивная скорость обучения: используйте оптимизаторы, такие как Adam, RMSprop и т. д\r\n\r\n**Выбор размера партии**:\r\n- Небольшие партии: лучшая производительность обобщения, но более длительное время обучения\r\n- Большой объем: тренировка проходит быстро, но может повлиять на генерализацию\r\n- Обычно выбираются партии от 16 до 128 штук\r\n\r\n**Обработка длины последовательности**:\r\n- Фиксированная длина: усечение или заполнение последовательностей до фиксированной длины\r\n- Динамическая длина: используйте отступы и маски для обработки последовательностей переменной длины\r\n- Стратегия сбора в мешки: групповые последовательности одинаковой длины\r\n\r\n## Оценка и анализ эффективности\r\n\r\n### Оценивайте метрики\r\n\r\n**Точность на уровне символов**:\r\nAccuracy_char = (Количество правильно распознанных символов) / (Всего символов)\r\n\r\nЭто самый основной показатель оценки, который напрямую отражает возможности модели по распознаванию символов.\r\n\r\n**Точность на уровне последовательного порта**:\r\nAccuracy_seq = (количество правильно распознанных последовательностей) / (общее количество последовательностей)\r\n\r\nЭтот индикатор является более строгим, и только полностью правильная последовательность считается правильной.\r\n\r\n**Расстояние редактирования (расстояние Левенштейна)**:\r\nИзмерьте разницу между прогнозируемым и истинным рядом:\r\n- Минимальное количество операций вставки, извлечения и замены\r\n- Стандартизированное расстояние редактирования: расстояние редактирования / длина последовательности\r\n- Оценка BLEU: обычно используется в машинном переводе, а также может использоваться для оценки OCR\r\n\r\n### Анализ ошибок\r\n\r\n**Распространенные типы ошибок**:\r\n1. **Путаница с персонажами**: Неправильная идентификация похожих персонажей\r\n   - Цифра 0 и буква О\r\n   - Цифра 1 и буква l\r\n   - Буквы М и Н\r\n\r\n2. **Ошибка последовательности**: Ошибка в порядке символов\r\n   - Положение персонажей меняется местами\r\n   - Дублирование или пропуск символов\r\n\r\n3. **Ошибка длины**: Ошибка в прогнозировании длины последовательности\r\n   - Слишком длинно: вставлены несуществующие символы\r\n   - Слишком короткий: Отсутствуют символы.\r\n\r\n**Метод анализа**:\r\n1. **Матрица несоответствий**: Анализирует шаблоны ошибок на уровне символов\r\n2. **Визуализация внимания**: Поймите проблемы модели\r\n3. **Анализ градиента**: Проверьте градиентный поток\r\n4. **Анализ активации**: Наблюдение за шаблонами активации на всех уровнях сети\r\n\r\n### Диагностика модели\r\n\r\n**Обнаружение переобучения**:\r\n- Потери на обучение продолжают снижаться, потери на валидацию растут\r\n- Точность обучения намного выше, чем точность валидации\r\n- Решение: Увеличьте регулярность и упростите модель\r\n\r\n**Обнаружение недостаточной подгонки**:\r\n- Потери как на обучение, так и на валидацию высоки\r\n- Модель плохо работает на обучающей выборке\r\n- Решение: Усложните модель и отрегулируйте скорость обучения\r\n\r\n**Диагностика проблемы градиента**:\r\n- Gradient Loss: Значение градиента слишком мало, медленное обучение\r\n- Взрыв градиента: Чрезмерные значения градиента приводят к нестабильному обучению\r\n- Решение: использование LSTM/GRU, градиентная обрезка\r\n\r\n## Примеры применения в реальном мире\r\n\r\n### Система распознавания рукописных символов\r\n\r\n**Сценарии применения**:\r\n- Оцифровка рукописных заметок: преобразование бумажных заметок в электронные документы\r\n- Автозаполнение форм: автоматически распознает рукописное содержимое формы\r\n- Идентификация исторических документов: оцифровка древних книг и исторических документов\r\n\r\n**Технические характеристики**:\r\n- Большие вариации символов: рукописный текст имеет высокую степень персонализации\r\n- Непрерывная обработка пера: необходимо обрабатывать соединения между символами\r\n- Контекстно-важно: используйте языковые модели для улучшения распознавания\r\n\r\n**Архитектура системы**:\r\n1. **Модуль предварительной обработки**:\r\n   - Шумоподавление и улучшение качества изображения\r\n   - Коррекция наклона\r\n   - Разделение строк текста\r\n\r\n2. **Модуль извлечения функций**:\r\n   - CNN извлекает визуальные особенности\r\n   - Многомасштабное объединение функций\r\n   - Сериализация функций\r\n\r\n3. **Модуль моделирования последовательностей**:\r\n   - Двунаправленное моделирование LSTM\r\n   - Механизмы внимания\r\n   - Контекстное кодирование\r\n\r\n4. **Модуль декодирования**:\r\n   - Декодирование CTC или декодирование внимания\r\n   - Постобработка языковой модели\r\n   - Оценка достоверности\r\n\r\n### Система распознавания печатных документов\r\n\r\n**Сценарии применения**:\r\n- Оцифровка документов: преобразование бумажных документов в редактируемые форматы\r\n- Распознавание счетов: автоматическая обработка счетов, квитанций и других счетов\r\n- Распознавание вывесок: идентификация дорожных знаков, вывесок магазинов и т. д.\r\n\r\n**Технические характеристики**:\r\n- Обычный шрифт: более регулярный, чем рукописный текст\r\n- Правила типографики: можно использовать информацию о макете\r\n- Высокие требования к точности: Коммерческие приложения предъявляют строгие требования к точности\r\n\r\n**Стратегия оптимизации**:\r\n1. **Обучение работе с несколькими шрифтами**: Использует обучающие данные из нескольких шрифтов\r\n2. **Улучшение данных**: вращение, масштабирование, добавление шума\r\n3. **Оптимизация постобработки**: проверка орфографии, исправление грамматики\r\n4. **Оценка достоверности**: Предоставляет оценку надежности для результатов распознавания\r\n\r\n### Система распознавания текста сцены\r\n\r\n**Сценарии применения**:\r\n- Распознавание текста в Просмотре улиц: Распознавание текста в Google Просмотре улиц\r\n- Распознавание этикеток продуктов: автоматическая идентификация продуктов супермаркета\r\n- Распознавание дорожных знаков: применение интеллектуальных транспортных систем\r\n\r\n**Технические проблемы**:\r\n- Сложный фон: текст встраивается в сложные природные сцены\r\n- Сильная деформация: перспективная деформация, деформация на изгиб\r\n- Требования в реальном времени: мобильные приложения должны быть отзывчивыми\r\n\r\n**Решение**:\r\n1. **Надежное извлечение функций**: использует более глубокие сети CNN\r\n2. **Многомасштабная обработка**: Обработка текста разных размеров\r\n3. **Коррекция геометрии**: автоматическая коррекция геометрических деформаций\r\n4. **Сжатие модели**: Оптимизируйте модель для мобильных устройств\r\n\r\n## Резюме\r\n\r\nРекуррентные нейронные сети предоставляют мощный инструмент для моделирования последовательностей в OCR. От базовых РНС до усовершенствованных LSTM и ГРУ и механизмов двунаправленной обработки и внимания — развитие этих технологий значительно улучшило производительность систем OCR.\r\n\r\n**Ключевые выводы**:\r\n- RNN реализуют моделирование последовательностей через соединения циклов, но существует проблема исчезновения градиента\r\n- LSTM и ГРУ решают проблему дистанционного обучения с помощью механизмов стробирования\r\n- Двунаправленные РНС способны использовать полную контекстуальную информацию\r\n- Механизмы внимания еще больше расширяют возможности моделирования последовательностей\r\n- Соответствующие стратегии обучения и методы регуляризации имеют решающее значение для производительности модели\r\n\r\n**Дальнейшие направления развития**:\r\n- Интеграция с архитектурами трансформаторов\r\n- Более эффективный подход к моделированию последовательностей\r\n- Сквозное мультимодальное обучение\r\n- Баланс между реальным временем и точностью\r\n\r\nПо мере того, как технологии продолжают развиваться, методы моделирования последовательностей все еще развиваются. Опыт и технологии, накопленные RNN и их вариантами в области OCR, заложили прочную основу для понимания и разработки более совершенных методов моделирования последовательностей.</div>\n                        \n                        <div class=\"article-tags\">\n                            <h6>Ярлык:</h6>\n                            <div class=\"tags-list\">\n                                \n                                <span class=\"tag\">RNN</span>\n                                \n                                <span class=\"tag\">LSTM</span>\n                                \n                                <span class=\"tag\">GRU</span>\n                                \n                                <span class=\"tag\">Моделирование последовательностей</span>\n                                \n                                <span class=\"tag\">Градиент исчезает</span>\n                                \n                                <span class=\"tag\">Двунаправленная RNN</span>\n                                \n                                <span class=\"tag\">Механизм внимания</span>\n                                \n                                <span class=\"tag\">CRNN</span>\n                                \n                                <span class=\"tag\">OCR</span>\n                                \n                            </div>\n                        </div>\n                        \n                    </article>\n                    \n                    <footer class=\"article-footer\" id=\"articleFooter\">\n                        <div class=\"article-share\">\n                            <h6 style=\"color: #1a1c1f; margin-bottom: 1rem;\">Совместное использование и эксплуатация:</h6>\n                            <div class=\"share-buttons\">\n                                <a href=\"javascript:void(0)\" onclick=\"shareToWeibo()\" class=\"btn btn-weibo\">📱 Общий доступ к Weibo</a>\n                                <a href=\"javascript:void(0)\" onclick=\"copyUrl()\" class=\"btn btn-copy\">🔗 Скопировать ссылку</a>\n                                <button type=\"button\" class=\"btn btn-print\" onclick=\"printArticle()\">🖨️ Распечатать статью</button>\n                            </div>\n                        </div>\n                    </footer>\n                    \n                </div>\n                <div class=\"col-lg-4 col-xl-3 col-md-12\">\n                    <div class=\"sidebar\">\n                        <div class=\"sidebar-card toc-card\" id=\"tocCard\" style=\"display: none;\"><h5>Содержание</h5><div id=\"tableOfContents\" class=\"toc-list\"></div></div>\n                        <div class=\"sidebar-card\">\n                            <h5>Рекомендовано к прочтению</h5>\n                            <div id=\"recommendedArticles\">\n                                \n                                <div class=\"recommended-item\"><h6><a href=\"Article.aspx?slug=document-intelligence-future-outlook\">【Серия интеллектуальной обработки документов·20】Перспективы развития технологии интеллектуальной обработки документов</a></h6><p class=\"text-muted small\">2025-08-19 • 1203 Следующее чтение</p></div>\n                                \n                                <div class=\"recommended-item\"><h6><a href=\"Article.aspx?slug=document-intelligence-quality-assurance\">【Серия интеллектуальной обработки документов·19】Система обеспечения качества интеллектуальной обработки документов</a></h6><p class=\"text-muted small\">2025-08-19 • 1200 Следующее чтение</p></div>\n                                \n                                <div class=\"recommended-item\"><h6><a href=\"Article.aspx?slug=large-scale-document-processing-optimization\">【Серия интеллектуальной обработки документов·18】Оптимизация производительности крупномасштабной обработки документов</a></h6><p class=\"text-muted small\">2025-08-19 • 1201 Следующее чтение</p></div>\n                                \n                            </div>\n                        </div>\n                    </div>\n</div>\n</div>\n</div>\n</div>\n<script>\nvar articleId,viewCountKey,now=new Date().getTime();\ndocument.addEventListener('DOMContentLoaded',function(){var articleDetail=document.getElementById('articleDetail');if(articleDetail&&articleDetail.querySelector('.article-content')){formatContent();initReadingProgress();optimizeImages();updateViewCount();}});\nfunction updateViewCount(){articleId=getArticleId();if(!articleId)return;viewCountKey='article_view_'+articleId;var lastViewTime=localStorage.getItem(viewCountKey);if(lastViewTime&&(now-parseInt(lastViewTime))<3600000)return;var xhr=new XMLHttpRequest();xhr.open('POST','/Article.ashx',true);xhr.setRequestHeader('Content-Type','application/x-www-form-urlencoded');xhr.onreadystatechange=function(){if(xhr.readyState===4&&xhr.status===200){try{var response=JSON.parse(xhr.responseText);if(response.success){updateViewCountDisplay(response.viewCount);localStorage.setItem(viewCountKey,now.toString());}}catch(e){}}};xhr.send('action=updateviewcount&articleId='+encodeURIComponent(articleId));}\nfunction getArticleId(){var articleDetail=document.getElementById('articleDetail');if(articleDetail){var id=articleDetail.getAttribute('data-article-id');if(id)return id;}var urlParams=new URLSearchParams(window.location.search);return urlParams.get('id');}\nfunction updateViewCountDisplay(newCount){document.querySelectorAll('.view-count').forEach(function(element){element.textContent=newCount;});}\nfunction decodeHtmlEntities(text){var textArea=document.createElement('textarea');textArea.innerHTML=text;return textArea.value;}\nfunction formatContent(){var contentDiv=document.getElementById('articleContent');if(!contentDiv)return;var content=contentDiv.textContent||contentDiv.innerText;if(!content)return;content=decodeHtmlEntities(content);content=formatMarkdownContent(content);content=formatIndentation(content);content=formatLists(content);content=formatParagraphs(content);content=formatLinks(content);contentDiv.innerHTML=content;initTableOfContents();}\nfunction formatIndentation(content){var lines=content.split('\\n'),result=[];for(var i=0;i<lines.length;i++){var line=lines[i];if(line.trim().match(/^<[^>]+>/)){result.push(line);continue;}line=line.replace(/^\\t+/g,function(match){return '    '.repeat(match.length);});if(line.match(/^  +[^\\s]/)){var indentMatch=line.match(/^( +)(.*)$/);if(indentMatch){var indentLevel=Math.floor(indentMatch[1].length/2);line='<span class=\"text-indent-'+indentLevel+'\">'+indentMatch[2]+'</span>';}}result.push(line);}return result.join('\\n');}\nfunction formatMarkdownContent(content){content=content.replace(/^\\s*##### (.+)$/gm,'<h5>$1</h5>').replace(/^\\s*#### (.+)$/gm,'<h4>$1</h4>').replace(/^\\s*### (.+)$/gm,'<h3>$1</h3>').replace(/^\\s*## (.+)$/gm,'<h2>$1</h2>').replace(/^\\s*# (.+)$/gm,'<h1>$1</h1>').replace(/^> (.+)$/gm,'<blockquote><p>$1</p></blockquote>').replace(/\\*\\*([^*]+?)\\*\\*/g,'<strong>$1</strong>').replace(/\\*([^*\\n]+?)\\*/g,'<em>$1</em>').replace(/`([^`]+?)`/g,'<code class=\"inline-code\">$1</code>').replace(/^(\\*\\*[^*]+?\\*\\*)：/gm,'<div class=\"label-item\"><span class=\"label\">$1</span></div>').replace(/^([^：\\n]+?)：$/gm,'<div class=\"section-label\">$1：</div>').replace(/^(\\*\\*[^*]+?\\*\\*)：(.+)$/gm,'<div class=\"definition-item\"><span class=\"definition-label\">$1：</span><span class=\"definition-content\">$2</span></div>').replace(/^(note|note|note):(.+)$/gm,'<div class=\"note-item\"><span class=\"note-label\">$1：</span><span class=\"note-content\">$2</span></div>');content=formatLists(content);content=formatMathSymbols(content);content=formatParagraphs(content);return content;}\nfunction formatLists(content){var lines=content.split('\\n'),result=[],inOrderedList=false,inUnorderedList=false;for(var i=0;i<lines.length;i++){var line=lines[i],trimmed=line.trim();var orderedMatch=line.match(/^(\\d+)\\.\\s*(.+)$/);var unorderedMatch=line.match(/^[-*+]\\s*(.+)$/);if(orderedMatch){if(!inOrderedList){if(inUnorderedList){result.push('</ul>');inUnorderedList=false;}result.push('<ol class=\"formatted-list ordered\">');inOrderedList=true;}var listContent=orderedMatch[2].replace(/^\\*\\*([^*]+?)\\*\\*：(.+)$/,'<strong class=\"list-title\">$1：</strong><span class=\"list-content\">$2</span>');result.push('<li>'+listContent+'</li>');}else if(unorderedMatch){if(!inUnorderedList){if(inOrderedList){result.push('</ol>');inOrderedList=false;}result.push('<ul class=\"formatted-list unordered\">');inUnorderedList=true;}var listContent=unorderedMatch[1].replace(/^\\*\\*([^*]+?)\\*\\*：(.+)$/,'<strong class=\"list-title\">$1：</strong><span class=\"list-content\">$2</span>');result.push('<li>'+listContent+'</li>');}else{if(trimmed===''){if(i+1<lines.length){var nextLine=lines[i+1].trim();if(!nextLine.match(/^\\d+\\.\\s/)&&!nextLine.match(/^[-*+]\\s/)){if(inOrderedList){result.push('</ol>');inOrderedList=false;}if(inUnorderedList){result.push('</ul>');inUnorderedList=false;}}}result.push(line);}else{if(inOrderedList){result.push('</ol>');inOrderedList=false;}if(inUnorderedList){result.push('</ul>');inUnorderedList=false;}result.push(line);}}}if(inOrderedList)result.push('</ol>');if(inUnorderedList)result.push('</ul>');return result.join('\\n');}\nfunction formatMathSymbols(content){var mathSymbols={'α':'&alpha;','β':'&beta;','γ':'&gamma;','δ':'&delta;','ε':'&epsilon;','ζ':'&zeta;','η':'&eta;','θ':'&theta;','λ':'&lambda;','μ':'&mu;','π':'&pi;','σ':'&sigma;','∑':'&sum;','∏':'&prod;','∫':'&int;','∂':'&part;','∇':'&nabla;','∞':'&infin;','≤':'&le;','≥':'&ge;','≠':'&ne;','≈':'&asymp;','±':'&plusmn;','×':'&times;','÷':'&divide;','→':'&rarr;','←':'&larr;','↑':'&uarr;','↓':'&darr;','⇒':'&rArr;','⇐':'&lArr;'};var subMap={'₀':'0','₁':'1','₂':'2','₃':'3','₄':'4','₅':'5','₆':'6','₇':'7','₈':'8','₉':'9','ₖ':'k','ₜ':'t','ₛ':'s','₊':'+','₋':'-'};var supMap={'⁰':'0','¹':'1','²':'2','³':'3','⁴':'4','⁵':'5','⁶':'6','⁷':'7','⁸':'8','⁹':'9','ᵏ':'k','ᵗ':'t','ˢ':'s','⁺':'+','⁻':'-'};for(var symbol in mathSymbols){content=content.replace(new RegExp(escapeRegExp(symbol),'g'),mathSymbols[symbol]);}content=content.replace(/([a-zA-Z])₍([0-9]+)₎/g,'$1<sub>$2</sub>').replace(/([a-zA-Z])⁽([0-9]+)⁾/g,'$1<sup>$2</sup>').replace(/([a-zA-Z])([₀₁₂₃₄₅₆₇₈₉ₖₜₛ₊₋]+)/g,function(match,base,subscript){var converted=subscript.replace(/[₀₁₂₃₄₅₆₇₈₉ₖₜₛ₊₋]/g,function(char){return subMap[char]||char;});return base+'<sub>'+converted+'</sub>';}).replace(/([a-zA-Z])([⁰¹²³⁴⁵⁶⁷⁸⁹ᵏᵗˢ⁺⁻]+)/g,function(match,base,superscript){var converted=superscript.replace(/[⁰¹²³⁴⁵⁶⁷⁸⁹ᵏᵗˢ⁺⁻]/g,function(char){return supMap[char]||char;});return base+'<sup>'+converted+'</sup>';}).replace(/\\b([A-Za-z])_\\{([^}]+)\\}/g,'$1<sub>$2</sub>').replace(/\\b([A-Za-z])\\^\\{([^}]+)\\}/g,'$1<sup>$2</sup>');return content;}\nfunction formatParagraphs(content){var lines=content.split('\\n'),result=[],inParagraph=false,currentParagraph=[];for(var i=0;i<lines.length;i++){var originalLine=lines[i],line=originalLine.trim();if(line.match(/^<[^>]+>/)||line===''){if(inParagraph&&currentParagraph.length>0){var paragraphContent=currentParagraph.join(' ').trim();if(paragraphContent)result.push('<p class=\"content-paragraph indent-paragraph\">'+paragraphContent+'</p>');currentParagraph=[];inParagraph=false;}result.push(originalLine);continue;}if(line.match(/^#{1,6}\\s/)||line.match(/^\\d+\\.\\s/)||line.match(/^[-*+]\\s/)||line.match(/^>/)){if(inParagraph&&currentParagraph.length>0){var paragraphContent=currentParagraph.join(' ').trim();if(paragraphContent)result.push('<p class=\"content-paragraph indent-paragraph\">'+paragraphContent+'</p>');currentParagraph=[];inParagraph=false;}result.push(originalLine);continue;}if(line){currentParagraph.push(line);inParagraph=true;}}if(inParagraph&&currentParagraph.length>0){var paragraphContent=currentParagraph.join(' ').trim();if(paragraphContent)result.push('<p class=\"content-paragraph indent-paragraph\">'+paragraphContent+'</p>');}return result.join('\\n');}\nfunction escapeRegExp(string){return string.replace(/[.*+?^${}()|[\\]\\\\]/g,'\\\\$&');}\nfunction formatLinks(content){return content.replace(/<a\\s+href=\"(https?:\\/\\/[^\"]+)\"([^>]*)>/gi,function(match,url,attrs){return !attrs.includes('target=')?'<a href=\"'+url+'\"'+attrs+' target=\"_blank\" rel=\"noopener noreferrer\">':match;});}\nfunction escapeHtml(text){var div=document.createElement('div');div.textContent=text;return div.innerHTML;}\nfunction addTocClickEvents(){document.querySelectorAll('#tableOfContents a').forEach(function(link){link.addEventListener('click',function(e){e.preventDefault();var targetElement=document.getElementById(this.getAttribute('data-target'));if(targetElement){targetElement.scrollIntoView({behavior:'smooth',block:'start'});updateActiveTocItem(this);}});});}\nfunction addScrollHighlight(headings){var throttleTimer=null;function updateActiveHeading(){var windowHeight=window.innerHeight,activeHeading=null;for(var i=headings.length-1;i>=0;i--){var rect=headings[i].getBoundingClientRect();if(rect.top<=windowHeight*0.3){activeHeading=headings[i];break;}}if(activeHeading){var activeLink=document.querySelector('#tableOfContents a[data-target=\"'+activeHeading.id+'\"]');if(activeLink)updateActiveTocItem(activeLink);}}window.addEventListener('scroll',function(){if(throttleTimer)clearTimeout(throttleTimer);throttleTimer=setTimeout(updateActiveHeading,100);});updateActiveHeading();}\nfunction updateActiveTocItem(activeLink){document.querySelectorAll('#tableOfContents a').forEach(function(link){link.classList.remove('active');});if(activeLink){activeLink.classList.add('active');scrollTocToActiveItem(activeLink);}}\nfunction scrollTocToActiveItem(activeLink){var tocCard=document.getElementById('tocCard');if(!activeLink||!tocCard)return;var cardRect=tocCard.getBoundingClientRect();var activeRect=activeLink.getBoundingClientRect();var relativeTop=activeRect.top-cardRect.top;var relativeBottom=activeRect.bottom-cardRect.bottom;var cardStyle=window.getComputedStyle(tocCard);var paddingTop=parseFloat(cardStyle.paddingTop)||0;var titleHeight=tocCard.querySelector('h5')?tocCard.querySelector('h5').offsetHeight:0;var contentTop=paddingTop+titleHeight;if(relativeTop<contentTop){tocCard.scrollTop+=(relativeTop-contentTop-10);}else if(relativeBottom>0){tocCard.scrollTop+=(relativeBottom+10);}}\nfunction optimizeImages(){document.querySelectorAll('.article-content img').forEach(function(img){img.loading='lazy';img.onerror=function(){this.style.display='none';};if(!img.alt)img.alt='Статья с картинками';});}\nfunction initReadingProgress(){var progressBar=document.getElementById('readingProgress');function updateProgress(){var scrollTop=window.pageYOffset||document.documentElement.scrollTop;var docHeight=document.documentElement.scrollHeight-window.innerHeight;progressBar.style.width=Math.min((scrollTop/docHeight)*100,100)+'%';}window.addEventListener('scroll',updateProgress);updateProgress();}\nfunction initTableOfContents(){var content=document.querySelector('.article-content');if(!content)return;var headings=content.querySelectorAll('h1, h2, h3, h4, h5, h6');if(headings.length<=1)return;var tocCard=document.getElementById('tocCard');var tocList=document.getElementById('tableOfContents');var html='',counters=[0,0,0,0,0,0];headings.forEach(function(heading,index){var id='heading-'+index;heading.id=id;var level=parseInt(heading.tagName.substring(1));var indent=(level-1)*15;counters[level-1]++;for(var i=level;i<counters.length;i++)counters[i]=0;var numberText='';for(var i=0;i<level;i++){if(counters[i]>0)numberText+=(numberText?'.':'')+counters[i];}numberText+='.';html+='<a href=\"#'+id+'\" class=\"toc-item toc-level-'+level+'\" style=\"padding-left: '+indent+'px;\" data-target=\"'+id+'\"><span class=\"toc-number\">'+numberText+'</span> <span class=\"toc-text\">'+heading.textContent+'</span></a>';});tocList.innerHTML=html;tocCard.style.display='block';addTocClickEvents();addScrollHighlight(headings);tocList.addEventListener('click',function(e){if(e.target.tagName==='A'){e.preventDefault();var targetElement=document.getElementById(e.target.getAttribute('data-target'));if(targetElement){targetElement.scrollIntoView({behavior:'smooth',block:'start'});targetElement.style.transition='all 0.3s ease';targetElement.style.backgroundColor='#e3f2fd';targetElement.style.padding='10px';targetElement.style.borderRadius='6px';setTimeout(function(){targetElement.style.backgroundColor='';targetElement.style.padding='';targetElement.style.borderRadius='';},2000);}}});}\nfunction shareToWeibo(){window.open('https://service.weibo.com/share/share.php?url='+encodeURIComponent(window.location.href)+'&title='+encodeURIComponent(document.title),'_blank');}\nfunction copyUrl(){navigator.clipboard.writeText(window.location.href).then(function(){alert('Ссылка скопирована в буфер обмена');}).catch(function(){var textArea=document.createElement('textarea');textArea.value=window.location.href;textArea.style.cssText='position:fixed;left:-999999px;top:-999999px';document.body.appendChild(textArea);textArea.focus();textArea.select();try{alert(document.execCommand('copy')?'Ссылка скопирована в буфер обмена':'Если копирование не удалось, скопируйте ссылку вручную');}catch(err){alert('Если копирование не удалось, скопируйте ссылку вручную');}document.body.removeChild(textArea);});}\nfunction printArticle(){var printWindow=window.open('','_blank');var articleContent=document.querySelector('.article-detail').innerHTML;var articleTitle=document.querySelector('.article-title').textContent;printWindow.document.write('<!DOCTYPE html><html lang=\"ru\"><title>'+articleTitle+'</title><style>body{font-family:-apple-system,BlinkMacSystemFont,\"Segoe UI\",Roboto,\"Helvetica Neue\",Arial,sans-serif;line-height:1.6;color:#333;max-width:800px;margin:0 auto;padding:20px}h1,h2,h3,h4,h5,h6{color:#1a1c1f;margin-top:1.5em;margin-bottom:0.5em}h1{font-size:2em;border-bottom:2px solid #e9ecef;padding-bottom:0.3em}h2{font-size:1.5em;border-bottom:1px solid #e9ecef;padding-bottom:0.3em}h3{font-size:1.25em}p{margin-bottom:1em}pre{background:#f8f9fa;border:1px solid #e9ecef;border-radius:4px;padding:1em;overflow-x:auto;font-family:Consolas,Monaco,\"Courier New\",monospace}code{background:#f1f3f4;padding:2px 4px;border-radius:3px;font-family:Consolas,Monaco,\"Courier New\",monospace}blockquote{border-left:4px solid #1764ff;background:#f8f9fa;padding:1em;margin:1em 0;border-radius:0 4px 4px 0}img{max-width:100%;height:auto}table{width:100%;border-collapse:collapse;margin:1em 0}th,td{border:1px solid #ddd;padding:8px;text-align:left}th{background-color:#f2f2f2}.article-meta,.article-footer,.share-buttons{display:none}</style><body>'+articleContent+'</body></html>');printWindow.document.close();printWindow.focus();setTimeout(function(){printWindow.print();printWindow.close();},250);}\nfunction improvedStickyPosition(){var sidebarCards=document.querySelectorAll('.sidebar-card');var originalPositions=[],isFixed=[];sidebarCards.forEach(function(card,index){var rect=card.getBoundingClientRect();originalPositions[index]=rect.top+window.pageYOffset;isFixed[index]=false;var placeholder=document.createElement('div');placeholder.style.cssText='height:'+card.offsetHeight+'px;display:none';placeholder.className='sticky-placeholder';card.parentNode.insertBefore(placeholder,card.nextSibling);});function updateStickyPosition(){var scrollTop=window.pageYOffset||document.documentElement.scrollTop;sidebarCards.forEach(function(card,index){var originalTop=originalPositions[index];var stickyTop=index>0?(document.querySelector('.toc-card').style.position==='fixed'?60+document.querySelector('.toc-card').offsetHeight+8:80):60;var placeholder=card.nextSibling;if(scrollTop>originalTop-stickyTop){if(!isFixed[index]){var colContainer=card.closest('[class*=\"col-\"]');var colRect=colContainer.getBoundingClientRect();var colStyle=window.getComputedStyle(colContainer);var paddingLeft=parseFloat(colStyle.paddingLeft)||0;var paddingRight=parseFloat(colStyle.paddingRight)||0;card.style.cssText='position:fixed;top:'+stickyTop+'px;left:'+(colRect.left+paddingLeft)+'px;width:'+(colRect.width-paddingLeft-paddingRight)+'px;z-index:9999;box-sizing:border-box';card.classList.add('fixed');placeholder.style.display='block';isFixed[index]=true;}}else if(isFixed[index]){card.style.cssText='';card.classList.remove('fixed');placeholder.style.display='none';isFixed[index]=false;}});}window.addEventListener('scroll',updateStickyPosition);window.addEventListener('resize',function(){setTimeout(function(){sidebarCards.forEach(function(card,index){if(isFixed[index]){var colContainer=card.closest('[class*=\"col-\"]');var colRect=colContainer.getBoundingClientRect();var colStyle=window.getComputedStyle(colContainer);var paddingLeft=parseFloat(colStyle.paddingLeft)||0;var paddingRight=parseFloat(colStyle.paddingRight)||0;card.style.left=(colRect.left+paddingLeft)+'px';card.style.width=(colRect.width-paddingLeft-paddingRight)+'px';}else{originalPositions[index]=card.getBoundingClientRect().top+window.pageYOffset;}});},100);});updateStickyPosition();}\ndocument.addEventListener('DOMContentLoaded',function(){setTimeout(improvedStickyPosition,1000);});\n    </script>\n\r\n            </div>\r\n        </form>\r\n\r\n        <div class=\"fixed-icon\">\r\n            <div style=\"width: 120px; position: relative; left: 1rem; top: -12px;\">\r\n            </div>\r\n            <a id=\"webchat\" class=\"wx-con disa\" href=\"http://wpa.qq.com/msgrd?v=3&uin=365833440&site=qq&menu=yes\" target=\"_blank\">\r\n                <img class=\"wechat-ico\" src=\"site/image/icon/qq.svg\" alt=\"Ассистент OCR QQ онлайн служба поддержки клиентов\" />\r\n                <div class=\"wx-text\">Служба поддержки клиентов QQ (365833440)</div>\r\n                <label class=\"badge badge-danger\" style=\"background: rgb(255, 34, 122); display: inline-block;\">1</label>\r\n            </a>\r\n\r\n            <a class=\"wx-con\" href=\"https://qm.qq.com/cgi-bin/qm/qr?k=bohNy9GMdgD5bsYrB-fcIjy-s6Ot3zOg&jump_from=webapi&qr=1\" target=\"_blank\">\r\n                <img class=\"wechat-ico\" src=\"site/image/icon/qq.svg\" alt=\"Группа общения с пользователями OCR assistant QQ\" />\r\n                <div class=\"wx-text\">Группа QQ (100029010)</div>\r\n                <label class=\"badge badge-danger\" style=\"background: rgb(255, 34, 122); display: inline-block;\">2</label>\r\n            </a>\r\n            <a href=\"mailto:<EMAIL>\" class=\"wx-con\">\r\n                <img class=\"wechat-ico\" src=\"site/image/icon/im.svg\" alt=\"Помощник OCR связывается со службой поддержки по электронной почте\" />\r\n                <div class=\"wx-text\" style=\"width: 240px; text-align: left; left: -15.3rem; padding: 10px 20px;\">\r\n                    <div style=\"font-size: 15px\">Электронная почта: <EMAIL></div>\r\n                    <hr style=\"background: #c1c1c1; margin: 10px 10px 6px 0;\" />\r\n                    <span style=\"font-size: 13px; color: #c1c1c1\">Спасибо за ваши замечания и предложения!</span>\r\n                </div>\r\n                <label class=\"badge badge-danger\" style=\"background: rgb(255, 34, 122); display: inline-block;\">3</label>\r\n            </a>\r\n        </div>\r\n\r\n        <style>\r\n            .content {\r\n                width: 100%;\r\n                padding: 0 !important;\r\n                margin: 0 auto\r\n            }\r\n\r\n            h3.h6 {\r\n                font-size: 1rem !important;\r\n                font-weight: bold !important;\r\n                line-height: 1.2 !important;\r\n                margin-bottom: .5rem;\r\n                font-family: inherit\r\n            }\r\n        </style>\r\n        <footer class=\"page-footer\">\r\n            <div class=\"container\">\r\n                <div class=\"row foot-cont pt-4 no-gutters\" style=\"border-top: 1px solid #e1e6ed; max-width: 100%\">\r\n                    <div class=\"col-lg-12 hu-an p-0 mb-3 mt-3 text-l color-gray\" style=\"text-align: center;\">\r\n                        Помощник по распознаванию текста OCR&nbsp;©️ 2025 ALL RIGHTS RESERVED. Все права защищены&nbsp;&nbsp;&nbsp;|&nbsp;&nbsp;&nbsp;\r\n                        <a href=\"PrivacyPolicy.aspx\" style=\"text-decoration: underline;\">Соглашение о конфиденциальности</a>&nbsp;&nbsp;&nbsp;|&nbsp;&nbsp;&nbsp;\r\n                        <a href=\"UserAgreement.aspx\" style=\"text-decoration: underline;\">Пользовательское соглашение</a>&nbsp;&nbsp;&nbsp;|&nbsp;&nbsp;&nbsp;\r\n                        <a href=\"Status.aspx\" style=\"text-decoration: underline;\">Статус службы</a>&nbsp;&nbsp;&nbsp;|&nbsp;&nbsp;&nbsp;\r\n                        <a class=\"mb-2 color-gray\" href=\"https://beian.miit.gov.cn/\" target=\"_blank\" style=\"text-decoration: underline;\">E Подготовка ПМС No 2021012692</a>\r\n                    </div>\r\n                </div>\r\n            </div>\r\n        </footer>\r\n        <style>\r\n            #translate > .translateSelectLanguage {\r\n                right: 2rem;\r\n                font-size: 1rem;\r\n                width: 150px;\r\n                padding: .3rem;\r\n                margin-right: 20px;\r\n                border: 1px solid #C9C9C9;\r\n                background: #fff;\r\n                color: #555\r\n            }\r\n        </style>\r\n    </div>\r\n    <script>\r\n        document.addEventListener('DOMContentLoaded', function () {\r\n            var setScroll = function (active) {\r\n                forEach(qa('.v4_header_pc,.v4_header_mob'), function (e) {\r\n                    if (active) addClass(e, 'scrollActive'); else removeClass(e, 'scrollActive');\r\n                });\r\n            };\r\n            if (getScrollY() > 60) setScroll(1);\r\n            window.addEventListener('scroll', function () { setScroll(getScrollY() > 60); });\r\n\r\n            forEach(qa('.v4_header_pc'), function (h) {\r\n                h.addEventListener('mouseenter', function () { addClass(h, 'active'); });\r\n                h.addEventListener('mouseleave', function () { removeClass(h, 'active'); });\r\n            });\r\n\r\n            var menuTimer;\r\n            var hideMenu = function (menuId) {\r\n                var m = document.getElementById(menuId);\r\n                if (m) {\r\n                    m.style.cssText = 'visibility:hidden;height:0;display:none';\r\n                    var n = q('.nav-drown-con', m);\r\n                    if (n) { n.style.cssText = 'height:0;opacity:0;transform:translate(0,-100%)'; }\r\n                }\r\n            };\r\n\r\n            forEach(qa('ul.top-nav>li'), function (i) {\r\n                i.addEventListener('mouseenter', function () {\r\n                    clearTimeout(menuTimer);\r\n                    var l = this.querySelector('a'), t = this.querySelector('.triangle'), s = l ? l.getAttribute('data-sub') : null;\r\n                    if (l) addClass(l, 'active'); if (t) addClass(t, 'active');\r\n                    if (s) {\r\n                        var m = document.getElementById(s);\r\n                        if (m) {\r\n                            m.style.cssText = 'display:block;visibility:visible';\r\n                            var siblings = m.parentNode ? toArray(m.parentNode.children) : [];\r\n                            forEach(siblings, function (c) { if (c !== m) c.style.display = 'none'; });\r\n                            var n = q('.nav-drown-con', m);\r\n                            if (n) { n.style.cssText = 'height:100%;opacity:1;transform:translate(0,0)'; }\r\n\r\n                            m.addEventListener('mouseenter', function () { clearTimeout(menuTimer); });\r\n                            m.addEventListener('mouseleave', function () { menuTimer = setTimeout(function () { hideMenu(s); }, 100); });\r\n                        }\r\n                    }\r\n                });\r\n                i.addEventListener('mouseleave', function () {\r\n                    var l = this.querySelector('a'), t = this.querySelector('.triangle'), s = l ? l.getAttribute('data-sub') : null;\r\n                    if (l) removeClass(l, 'active'); if (t) removeClass(t, 'active');\r\n                    if (s) { menuTimer = setTimeout(function () { hideMenu(s); }, 100); }\r\n                });\r\n            });\r\n\r\n            var r = q('.v4_header_mob .right-menu');\r\n            if (r) r.addEventListener('click', function () {\r\n                var arr = [r, q('.v4_header_mob'), q('#nav_products'), q('.mob-nav-content .sidebar-fix')];\r\n                for (var i = 0; i < arr.length; i++) {\r\n                    var e = arr[i];\r\n                    if (e) {\r\n                        var name = (i === 0 ? 'active' : (i === 1 ? 'active' : (i === 2 ? 'active1' : 'fixedActiveBg')));\r\n                        toggleClass(e, name);\r\n                    }\r\n                }\r\n                var f = q('.mob-nav-content .sidebar-fix');\r\n                if (f) {\r\n                    toggleClass(f, 'show');\r\n                    document.body.style.overflow = hasClass(f, 'show') ? 'hidden' : 'scroll';\r\n                }\r\n            });\r\n\r\n            document.addEventListener('click', function (e) {\r\n                e = e || window.event;\r\n                var target = e.target || e.srcElement;\r\n                var m = closest(target, '.sidebar-fix-left .mob-nav-item');\r\n                if (m) {\r\n                    var p = m.parentNode;\r\n                    if (p) forEach(toArray(p.children), function (c) { removeClass(c, 'active'); });\r\n                    addClass(m, 'active');\r\n                    var d = m.getAttribute('data');\r\n                    if (d) forEach(qa('.sidebar-fix-rigth .sub-nav'), function (n, i) { n.style.display = (i == d ? 'block' : 'none'); });\r\n                }\r\n            });\r\n        });\r\n    </script>\r\n</body>\r\n</html>\r\n\r\n\t            <script type=\"text/javascript\">\r\n                    var _hmt = _hmt || [];\r\n                    (function () {\r\n                        var hm = document.createElement('script');hm.async=1;\r\n                        hm.src = 'https://hm.baidu.com/hm.js?5ee048d7caaf0d439cf16309a1687754';\r\n                        var s = document.getElementsByTagName('script')[0];s.parentNode.insertBefore(hm, s);\r\n                    })();\r\n                </script><div id=\"translate\" class=\"ignore\" style=\"display: none\"></div><script src=\"/static/js/translate.js\"></script>"
﻿using System;
using System.Configuration;
using System.Runtime.InteropServices;
using System.Security.Cryptography;

namespace Account.Web.pay._360
{
    /// <summary>
    /// 360SDK调用（自动调用64位或32位）
    /// </summary>
    public static class Qh360SdkWrapper
    {
        static Qh360SdkWrapper()
        {
            Qh360SdkHelper.Init(new Qh360SdkEnv
            {
                AppId = ConfigurationManager.AppSettings["Pay360AppId"],
                Qid = ulong.Parse(ConfigurationManager.AppSettings["Pay360QId"])
            });
        }

        /// <summary>
        /// 初始化sdk
        /// </summary>
        /// <param name="pEnvInfo"></param>
        /// <returns>0:成功;其它值:失败</returns>
        public static int SDK360_Init(EnvInfo pEnvInfo)
        {
            if (Environment.Is64BitProcess)
            {
                return Qh360Sdkx64.SDK360_Init(ref pEnvInfo);
            }
            return Qh360Sdkx86.SDK360_Init(ref pEnvInfo);
        }

        /// <summary>
        /// 卸载sdk
        /// </summary>
        /// <returns>0:成功;其它值:失败</returns>
        public static int SDK360_UnInit()
        {
            if (Environment.Is64BitProcess)
            {
                return Qh360Sdkx64.SDK360_UnInit();
            }
            return Qh360Sdkx86.SDK360_UnInit();
        }

        /// <summary>
        /// 创建订单
        /// </summary>
        /// <param name="fpOrderRequest">厂商提供的创建订单所需相关信息</param>
        /// <param name="fpOrderResponse">返回的创建订单的详细信息</param>
        /// <param name="fnPayStatusCallBack">订单创建成功后，支付状态变更的通知回调接口</param>
        /// <returns>当前订单的dwTicket，唯一id</returns>
        public static uint SDK360_Pay(OderRequest fpOrderRequest, ref OderResponse fpOrderResponse, SDK360_PAYSTATUS_CALLBACK fnPayStatusCallBack)
        {
            if (Environment.Is64BitProcess)
            {
                return Qh360Sdkx64.SDK360_Pay(ref fpOrderRequest, ref fpOrderResponse, fnPayStatusCallBack);
            }
            return Qh360Sdkx86.SDK360_Pay(ref fpOrderRequest, ref fpOrderResponse, fnPayStatusCallBack);
        }

        /// <summary>
        /// 创建订单
        /// </summary>
        /// <param name="fpOrderRequest">厂商提供的创建订单所需相关信息</param>
        /// <param name="fnOrderResultCallback">订单创建完成的接口回调函数</param>
        /// <param name="fnPayStatusCallback">订单创建成功后，支付状态变更的通知回调接口</param>
        /// <returns>当前订单的dwTicket，唯一id</returns>
        public static uint SDK360_AsyncPay(OderRequest fpOrderRequest, SDK360_ORDERRESULT_CALLBACK fnOrderResultCallback, SDK360_PAYSTATUS_CALLBACK fnPayStatusCallback)
        {
            if (Environment.Is64BitProcess)
            {
                return Qh360Sdkx64.SDK360_AsyncPay(ref fpOrderRequest, fnOrderResultCallback, fnPayStatusCallback);
            }
            return Qh360Sdkx86.SDK360_AsyncPay(ref fpOrderRequest, fnOrderResultCallback, fnPayStatusCallback);
        }

        /// <summary>
        /// 取消订单，sdk在等待用户支付时，底层会做轮询。取消订单可节省系统资源占用
        /// </summary>
        /// <returns>0:取消成功;其它值:取消失败</returns>
        public static int SDK360_CancelPay(uint dwTicket)
        {
            if (Environment.Is64BitProcess)
            {
                return Qh360Sdkx64.SDK360_CancelPay(dwTicket);
            }
            return Qh360Sdkx86.SDK360_CancelPay(dwTicket);
        }
    }

    /// <summary>
    /// 360SDK 64位
    /// </summary>
    public static class Qh360Sdkx64
    {
        private const string SDKFileName = "Qh360Sdk\\x64\\360Lysdk64.dll";

        /// <summary>
        /// 初始化sdk
        /// </summary>
        /// <param name="pEnvInfo"></param>
        /// <returns>0:成功;其它值:失败</returns>
        [DllImport(SDKFileName, CallingConvention = CallingConvention.StdCall)]
        public static extern int SDK360_Init(ref EnvInfo pEnvInfo);

        /// <summary>
        /// 卸载sdk
        /// </summary>
        /// <returns>0:成功;其它值:失败</returns>
        [DllImport(SDKFileName, CallingConvention = CallingConvention.StdCall)]
        public static extern int SDK360_UnInit();

        /// <summary>
        /// 创建订单
        /// </summary>
        /// <param name="fpOrderRequest">厂商提供的创建订单所需相关信息</param>
        /// <param name="fpOrderResponse">返回的创建订单的详细信息</param>
        /// <param name="fnPayStatusCallBack">订单创建成功后，支付状态变更的通知回调接口</param>
        /// <returns>当前订单的dwTicket，唯一id</returns>
        [DllImport(SDKFileName, CallingConvention = CallingConvention.StdCall)]
        public static extern uint SDK360_Pay(ref OderRequest fpOrderRequest, ref OderResponse fpOrderResponse, SDK360_PAYSTATUS_CALLBACK fnPayStatusCallBack);

        /// <summary>
        /// 创建订单
        /// </summary>
        /// <param name="fpOrderRequest">厂商提供的创建订单所需相关信息</param>
        /// <param name="fnOrderResultCallback">订单创建完成的接口回调函数</param>
        /// <param name="fnPayStatusCallback">订单创建成功后，支付状态变更的通知回调接口</param>
        /// <returns>当前订单的dwTicket，唯一id</returns>
        [DllImport(SDKFileName, CallingConvention = CallingConvention.StdCall)]
        public static extern uint SDK360_AsyncPay(ref OderRequest fpOrderRequest, SDK360_ORDERRESULT_CALLBACK fnOrderResultCallback, SDK360_PAYSTATUS_CALLBACK fnPayStatusCallback);

        /// <summary>
        /// 取消订单，sdk在等待用户支付时，底层会做轮询。取消订单可节省系统资源占用
        /// </summary>
        /// <returns>0:取消成功;其它值:取消失败</returns>
        [DllImport(SDKFileName, CallingConvention = CallingConvention.StdCall)]
        public static extern int SDK360_CancelPay(uint dwTicket);
    }

    /// <summary>
    /// 360SDK 32位
    /// </summary>
    public static class Qh360Sdkx86
    {
        private const string SDKFileName = "Qh360Sdk\\x86\\360Lysdk.dll";

        /// <summary>
        /// 初始化sdk
        /// </summary>
        /// <param name="pEnvInfo"></param>
        /// <returns>0:成功;其它值:失败</returns>
        [DllImport(SDKFileName, CallingConvention = CallingConvention.StdCall)]
        public static extern int SDK360_Init(ref EnvInfo pEnvInfo);

        /// <summary>
        /// 卸载sdk
        /// </summary>
        /// <returns>0:成功;其它值:失败</returns>
        [DllImport(SDKFileName, CallingConvention = CallingConvention.StdCall)]
        public static extern int SDK360_UnInit();

        /// <summary>
        /// 创建订单
        /// </summary>
        /// <param name="fpOrderRequest">厂商提供的创建订单所需相关信息</param>
        /// <param name="fpOrderResponse">返回的创建订单的详细信息</param>
        /// <param name="fnPayStatusCallBack">订单创建成功后，支付状态变更的通知回调接口</param>
        /// <returns>当前订单的dwTicket，唯一id</returns>
        [DllImport(SDKFileName, CallingConvention = CallingConvention.StdCall)]
        public static extern uint SDK360_Pay(ref OderRequest fpOrderRequest, ref OderResponse fpOrderResponse, SDK360_PAYSTATUS_CALLBACK fnPayStatusCallBack);

        /// <summary>
        /// 创建订单
        /// </summary>
        /// <param name="fpOrderRequest">厂商提供的创建订单所需相关信息</param>
        /// <param name="fnOrderResultCallback">订单创建完成的接口回调函数</param>
        /// <param name="fnPayStatusCallback">订单创建成功后，支付状态变更的通知回调接口</param>
        /// <returns>当前订单的dwTicket，唯一id</returns>
        [DllImport(SDKFileName, CallingConvention = CallingConvention.StdCall)]
        public static extern uint SDK360_AsyncPay(ref OderRequest fpOrderRequest, SDK360_ORDERRESULT_CALLBACK fnOrderResultCallback, SDK360_PAYSTATUS_CALLBACK fnPayStatusCallback);

        /// <summary>
        /// 取消订单，sdk在等待用户支付时，底层会做轮询。取消订单可节省系统资源占用
        /// </summary>
        /// <returns>0:取消成功;其它值:取消失败</returns>
        [DllImport(SDKFileName, CallingConvention = CallingConvention.StdCall)]
        public static extern int SDK360_CancelPay(uint dwTicket);
    }
}

﻿"<!DOCTYPE html>\r\n<html xmlns=\"http://www.w3.org/1999/xhtml\" lang=\"es\">\r\n<head><link rel=\"canonical\" href=\"https://ocr.oldfish.cn/es/Article.aspx?id=82&slug=attention-mechanism-principles\" /><link rel=\"alternate\" href=\"https://ocr.oldfish.cn/Article.aspx?id=82&slug=attention-mechanism-principles\" hreflang=\"x-default\" />\r\n<link rel=\"alternate\" href=\"https://ocr.oldfish.cn/Article.aspx?id=82&slug=attention-mechanism-principles\" hreflang=\"zh\" />\r\n<link rel=\"alternate\" href=\"https://ocr.oldfish.cn/Article.aspx?id=82&slug=attention-mechanism-principles\" hreflang=\"zh\" />\r\n<link rel=\"alternate\" href=\"https://ocr.oldfish.cn/zh-Hant/Article.aspx?id=82&slug=attention-mechanism-principles\" hreflang=\"zh-tw\" />\r\n<link rel=\"alternate\" href=\"https://ocr.oldfish.cn/yue/Article.aspx?id=82&slug=attention-mechanism-principles\" hreflang=\"zh-yue\" />\r\n<link rel=\"alternate\" href=\"https://ocr.oldfish.cn/en/Article.aspx?id=82&slug=attention-mechanism-principles\" hreflang=\"en\" />\r\n<link rel=\"alternate\" href=\"https://ocr.oldfish.cn/es/Article.aspx?id=82&slug=attention-mechanism-principles\" hreflang=\"es\" />\r\n<link rel=\"alternate\" href=\"https://ocr.oldfish.cn/hi/Article.aspx?id=82&slug=attention-mechanism-principles\" hreflang=\"hi\" />\r\n<link rel=\"alternate\" href=\"https://ocr.oldfish.cn/ar/Article.aspx?id=82&slug=attention-mechanism-principles\" hreflang=\"ar\" />\r\n<link rel=\"alternate\" href=\"https://ocr.oldfish.cn/pt/Article.aspx?id=82&slug=attention-mechanism-principles\" hreflang=\"pt\" />\r\n<link rel=\"alternate\" href=\"https://ocr.oldfish.cn/bn/Article.aspx?id=82&slug=attention-mechanism-principles\" hreflang=\"bn\" />\r\n<link rel=\"alternate\" href=\"https://ocr.oldfish.cn/ru/Article.aspx?id=82&slug=attention-mechanism-principles\" hreflang=\"ru\" />\r\n<link rel=\"alternate\" href=\"https://ocr.oldfish.cn/ja/Article.aspx?id=82&slug=attention-mechanism-principles\" hreflang=\"ja\" />\r\n<link rel=\"alternate\" href=\"https://ocr.oldfish.cn/pa/Article.aspx?id=82&slug=attention-mechanism-principles\" hreflang=\"pa\" />\r\n<link rel=\"alternate\" href=\"https://ocr.oldfish.cn/de/Article.aspx?id=82&slug=attention-mechanism-principles\" hreflang=\"de\" />\r\n<link rel=\"alternate\" href=\"https://ocr.oldfish.cn/fr/Article.aspx?id=82&slug=attention-mechanism-principles\" hreflang=\"fr\" />\r\n<link rel=\"alternate\" href=\"https://ocr.oldfish.cn/mr/Article.aspx?id=82&slug=attention-mechanism-principles\" hreflang=\"mr\" />\r\n<link rel=\"alternate\" href=\"https://ocr.oldfish.cn/te/Article.aspx?id=82&slug=attention-mechanism-principles\" hreflang=\"te\" />\r\n<link rel=\"alternate\" href=\"https://ocr.oldfish.cn/vi/Article.aspx?id=82&slug=attention-mechanism-principles\" hreflang=\"vi\" />\r\n<link rel=\"alternate\" href=\"https://ocr.oldfish.cn/ko/Article.aspx?id=82&slug=attention-mechanism-principles\" hreflang=\"ko\" />\r\n<link rel=\"alternate\" href=\"https://ocr.oldfish.cn/ta/Article.aspx?id=82&slug=attention-mechanism-principles\" hreflang=\"ta\" />\r\n<link rel=\"alternate\" href=\"https://ocr.oldfish.cn/ur/Article.aspx?id=82&slug=attention-mechanism-principles\" hreflang=\"ur\" />\r\n<link rel=\"alternate\" href=\"https://ocr.oldfish.cn/tr/Article.aspx?id=82&slug=attention-mechanism-principles\" hreflang=\"tr\" />\r\n<link rel=\"alternate\" href=\"https://ocr.oldfish.cn/it/Article.aspx?id=82&slug=attention-mechanism-principles\" hreflang=\"it\" />\r\n<link rel=\"alternate\" href=\"https://ocr.oldfish.cn/gu/Article.aspx?id=82&slug=attention-mechanism-principles\" hreflang=\"gu\" />\r\n<link rel=\"alternate\" href=\"https://ocr.oldfish.cn/pl/Article.aspx?id=82&slug=attention-mechanism-principles\" hreflang=\"pl\" />\r\n<link rel=\"alternate\" href=\"https://ocr.oldfish.cn/uk/Article.aspx?id=82&slug=attention-mechanism-principles\" hreflang=\"uk\" />\r\n<link rel=\"alternate\" href=\"https://ocr.oldfish.cn/ms/Article.aspx?id=82&slug=attention-mechanism-principles\" hreflang=\"ms\" />\r\n<link rel=\"alternate\" href=\"https://ocr.oldfish.cn/id/Article.aspx?id=82&slug=attention-mechanism-principles\" hreflang=\"id\" />\r\n<link rel=\"alternate\" href=\"https://ocr.oldfish.cn/ml/Article.aspx?id=82&slug=attention-mechanism-principles\" hreflang=\"ml\" />\r\n<link rel=\"alternate\" href=\"https://ocr.oldfish.cn/kn/Article.aspx?id=82&slug=attention-mechanism-principles\" hreflang=\"kn\" />\r\n<link rel=\"alternate\" href=\"https://ocr.oldfish.cn/fa/Article.aspx?id=82&slug=attention-mechanism-principles\" hreflang=\"fa\" />\r\n<link rel=\"alternate\" href=\"https://ocr.oldfish.cn/nl/Article.aspx?id=82&slug=attention-mechanism-principles\" hreflang=\"nl\" />\r\n<link rel=\"alternate\" href=\"https://ocr.oldfish.cn/th/Article.aspx?id=82&slug=attention-mechanism-principles\" hreflang=\"th\" />\r\n<link rel=\"alternate\" href=\"https://ocr.oldfish.cn/sw/Article.aspx?id=82&slug=attention-mechanism-principles\" hreflang=\"sw\" />\r\n<link rel=\"alternate\" href=\"https://ocr.oldfish.cn/ro/Article.aspx?id=82&slug=attention-mechanism-principles\" hreflang=\"ro\" />\r\n<link rel=\"alternate\" href=\"https://ocr.oldfish.cn/my/Article.aspx?id=82&slug=attention-mechanism-principles\" hreflang=\"my\" />\r\n<link rel=\"alternate\" href=\"https://ocr.oldfish.cn/or/Article.aspx?id=82&slug=attention-mechanism-principles\" hreflang=\"or\" />\r\n<link rel=\"alternate\" href=\"https://ocr.oldfish.cn/he/Article.aspx?id=82&slug=attention-mechanism-principles\" hreflang=\"he\" />\r\n<link rel=\"alternate\" href=\"https://ocr.oldfish.cn/am/Article.aspx?id=82&slug=attention-mechanism-principles\" hreflang=\"am\" />\r\n<link rel=\"alternate\" href=\"https://ocr.oldfish.cn/fil/Article.aspx?id=82&slug=attention-mechanism-principles\" hreflang=\"fil\" />\r\n<link rel=\"alternate\" href=\"https://ocr.oldfish.cn/sv/Article.aspx?id=82&slug=attention-mechanism-principles\" hreflang=\"sv\" />\r\n<link rel=\"alternate\" href=\"https://ocr.oldfish.cn/el/Article.aspx?id=82&slug=attention-mechanism-principles\" hreflang=\"el\" />\r\n<link rel=\"alternate\" href=\"https://ocr.oldfish.cn/cs/Article.aspx?id=82&slug=attention-mechanism-principles\" hreflang=\"cs\" />\r\n<link rel=\"alternate\" href=\"https://ocr.oldfish.cn/hu/Article.aspx?id=82&slug=attention-mechanism-principles\" hreflang=\"hu\" />\r\n<link rel=\"alternate\" href=\"https://ocr.oldfish.cn/be/Article.aspx?id=82&slug=attention-mechanism-principles\" hreflang=\"be\" />\r\n<link rel=\"alternate\" href=\"https://ocr.oldfish.cn/si/Article.aspx?id=82&slug=attention-mechanism-principles\" hreflang=\"si\" />\r\n<link rel=\"alternate\" href=\"https://ocr.oldfish.cn/ne/Article.aspx?id=82&slug=attention-mechanism-principles\" hreflang=\"ne\" />\r\n<link rel=\"alternate\" href=\"https://ocr.oldfish.cn/km/Article.aspx?id=82&slug=attention-mechanism-principles\" hreflang=\"km\" />\r\n<link rel=\"alternate\" href=\"https://ocr.oldfish.cn/sk/Article.aspx?id=82&slug=attention-mechanism-principles\" hreflang=\"sk\" />\r\n<link rel=\"alternate\" href=\"https://ocr.oldfish.cn/bg/Article.aspx?id=82&slug=attention-mechanism-principles\" hreflang=\"bg\" />\r\n<link rel=\"alternate\" href=\"https://ocr.oldfish.cn/fr-ca/Article.aspx?id=82&slug=attention-mechanism-principles\" hreflang=\"fr-ca\" />\r\n<link rel=\"alternate\" href=\"https://ocr.oldfish.cn/ha/Article.aspx?id=82&slug=attention-mechanism-principles\" hreflang=\"ha\" />\r\n<link rel=\"alternate\" href=\"https://ocr.oldfish.cn/yo/Article.aspx?id=82&slug=attention-mechanism-principles\" hreflang=\"yo\" />\r\n<link rel=\"alternate\" href=\"https://ocr.oldfish.cn/ig/Article.aspx?id=82&slug=attention-mechanism-principles\" hreflang=\"ig\" />\r\n<link rel=\"alternate\" href=\"https://ocr.oldfish.cn/ku/Article.aspx?id=82&slug=attention-mechanism-principles\" hreflang=\"ku\" />\r\n<link rel=\"alternate\" href=\"https://ocr.oldfish.cn/rw/Article.aspx?id=82&slug=attention-mechanism-principles\" hreflang=\"rw\" />\r\n<link rel=\"alternate\" href=\"https://ocr.oldfish.cn/ca/Article.aspx?id=82&slug=attention-mechanism-principles\" hreflang=\"ca\" />\r\n<link rel=\"alternate\" href=\"https://ocr.oldfish.cn/da/Article.aspx?id=82&slug=attention-mechanism-principles\" hreflang=\"da\" />\r\n<link rel=\"alternate\" href=\"https://ocr.oldfish.cn/fi/Article.aspx?id=82&slug=attention-mechanism-principles\" hreflang=\"fi\" />\r\n<link rel=\"alternate\" href=\"https://ocr.oldfish.cn/nb/Article.aspx?id=82&slug=attention-mechanism-principles\" hreflang=\"nb\" />\r\n<link rel=\"alternate\" href=\"https://ocr.oldfish.cn/hr/Article.aspx?id=82&slug=attention-mechanism-principles\" hreflang=\"hr\" />\r\n<link rel=\"alternate\" href=\"https://ocr.oldfish.cn/sr-Cyrl/Article.aspx?id=82&slug=attention-mechanism-principles\" hreflang=\"sr-cyrl\" />\r\n<link rel=\"alternate\" href=\"https://ocr.oldfish.cn/sr-Latn/Article.aspx?id=82&slug=attention-mechanism-principles\" hreflang=\"sr-latn\" />\r\n<link rel=\"alternate\" href=\"https://ocr.oldfish.cn/sq/Article.aspx?id=82&slug=attention-mechanism-principles\" hreflang=\"sq\" />\r\n<link rel=\"alternate\" href=\"https://ocr.oldfish.cn/so/Article.aspx?id=82&slug=attention-mechanism-principles\" hreflang=\"so\" />\r\n<link rel=\"alternate\" href=\"https://ocr.oldfish.cn/zu/Article.aspx?id=82&slug=attention-mechanism-principles\" hreflang=\"zu\" />\r\n<link rel=\"alternate\" href=\"https://ocr.oldfish.cn/ka/Article.aspx?id=82&slug=attention-mechanism-principles\" hreflang=\"ka\" />\r\n<meta http-equiv=\"Content-Type\" content=\"text/html; charset=UTF-8\" /><meta http-equiv=\"X-UA-Compatible\" content=\"IE=Edge,chrome=1\" /><meta name=\"renderer\" content=\"webkit\" /><meta name=\"viewport\" content=\"width=device-width, initial-scale=1.0, maximum-scale=5.0\" /><link rel=\"icon\" href=\"/favicon.ico\" type=\"image/x-icon\" /><meta name=\"robots\" content=\"all\" /><link rel=\"preconnect\" href=\"https://scm-file-new-1301864755.cos.ap-beijing.myqcloud.com\" /><link rel=\"preconnect\" href=\"https://cdn.oldfish.cn\" /><link rel=\"dns-prefetch\" href=\"https://hm.baidu.com\" /><link rel=\"dns-prefetch\" href=\"https://cdn.oldfish.cn\" /><link rel=\"preload\" href=\"/site/css/site.css\" as=\"style\" />\r\n<script>function toArray(l){var a=[];for(var i=0;i<(l?l.length:0);i++)a.push(l[i]);return a;}function forEach(l,f){for(var i=0,n=(l?l.length:0);i<n;i++)f(l[i],i);}function hasClass(e,c){if(!e||!c)return false;return(' '+e.className+' ').indexOf(' '+c+' ')>-1;}function addClass(e,c){if(!e||!c||hasClass(e,c))return;e.className=(e.className?e.className+' ':'')+c;}function removeClass(e,c){if(!e||!c)return;var u=' '+e.className+' ';while(u.indexOf(' '+c+' ')>-1){u=u.replace(' '+c+' ',' ');}e.className=u.replace(/^\\s+|\\s+$/g,'');}function toggleClass(e,c){if(!e||!c)return;if(hasClass(e,c))removeClass(e,c);else addClass(e,c);}function matchesSelector(e,s){if(!e||e.nodeType!==1)return false;if(e.matches)return e.matches(s);var p=e.parentNode||document;var m=toArray(p.querySelectorAll(s));for(var i=0;i<m.length;i++)if(m[i]===e)return true;return false;}function closest(e,s){var n=e;while(n&&n.nodeType===1){if(matchesSelector(n,s))return n;n=n.parentNode;}return null;}function safeRemove(e){if(e&&e.parentNode)e.parentNode.removeChild(e);}function qa(s,r){return toArray((r||document).querySelectorAll(s));}function q(s,r){return(r||document).querySelector(s);}function xhrGet(u,o,e){try{var x=new XMLHttpRequest();x.open('GET',u,true);x.onreadystatechange=function(){if(x.readyState===4){if(x.status>=200&&x.status<300)o&&o(x.responseText);else e&&e(x);}};x.send(null);}catch(r){e&&e(r);}}function getScrollY(){return window.pageYOffset||document.documentElement.scrollTop||document.body.scrollTop||0;}function getOrigin(){return window.location.protocol+'//'+window.location.host;}</script>\r\n<script>var isWebP=(function(){try{return document.createElement('canvas').toDataURL('image/webp').indexOf('data:image/webp')===0;}catch(e){return false;}})();document.addEventListener('DOMContentLoaded',function(){var c=document.getElementById('user-login-container');if(c){var u=c.getAttribute('data-url');if(u){xhrGet(u,function(h){c.innerHTML=h;forEach(qa('a',c),function(l){l.style.display='inline-block';l.style.marginLeft='10px';});});}}});</script>\r\n    \n    \n\n    <meta name=\"description\" content=\"Profundiza en los principios matemáticos de los mecanismos de atención, la atención multicabezal, los mecanismos de autoatención y las aplicaciones específicas en OCR. Análisis detallado de cálculos de peso de atención, codificación de posición y estrategias de optimización del rendimiento.\" />\n    <meta name=\"keywords\" content=\"Mecanismo de atención, atención multicabezal, autoatención, codificación de posición, atención cruzada, atención dispersa, OCR, Transformer, reconocimiento de texto OCR, imagen a texto, tecnología OCR\" />\n    <meta property=\"og:title\" content=\"【Serie OCR de aprendizaje profundo · 5】 Principio e implementación del mecanismo de atención\" />\n    <meta property=\"og:description\" content=\"Profundiza en los principios matemáticos de los mecanismos de atención, la atención multicabezal, los mecanismos de autoatención y las aplicaciones específicas en OCR. Análisis detallado de cálculos de peso de atención, codificación de posición y estrategias de optimización del rendimiento.\" />\n    <meta property=\"og:type\" content=\"article\" />\n    <meta property=\"og:url\" content=\"https://ocr.oldfish.cn/Article.aspx?id=82&slug=attention-mechanism-principles&lang=zh-Hans&_translation_source=true\" />\n    <meta property=\"og:site_name\" content=\"Asistente de reconocimiento de texto OCR\" />\n    <meta name=\"twitter:card\" content=\"summary\" />\n    <meta name=\"twitter:title\" content=\"【Serie OCR de aprendizaje profundo · 5】 Principio e implementación del mecanismo de atención\" />\n    <meta name=\"twitter:description\" content=\"Profundiza en los principios matemáticos de los mecanismos de atención, la atención multicabezal, los mecanismos de autoatención y las aplicaciones específicas en OCR. Análisis detallado de cálculos de peso de atención, codificación de posición y estrategias de optimización del rendimiento.\" />\n\n    \n    <script type=\"application/ld+json\">\n    {\n        \"@context\": \"https://schema.org\",\n        \"@type\": \"Article\",\n        \"headline\": \"【Deep Learning OCR Serie 5] Principio e implementación del mecanismo de atención\",\n        \"description\": \"Profundiza en los principios matemáticos de los mecanismos de atención, la atención multicabezal, los mecanismos de autoatención y las aplicaciones específicas en OCR. Análisis detallado de cálculos de peso de atención, codificación de posición y estrategias de optimización del rendimiento。\",\n        \"publisher\": {\n            \"@type\": \"Organization\",\n            \"name\": \"Equipo asistente de reconocimiento de texto de OCR\"\n        },\n        \"datePublished\": \"2025-08-19T06:32:10Z\",\n        \"dateModified\": \"2025-08-19T06:32:10Z\",\n        \"mainEntityOfPage\": {\n            \"@type\": \"WebPage\",\n            \"@id\": \"https://ocr.oldfish.cn/Article.aspx?id=82&slug=attention-mechanism-principles&lang=zh-Hans&_translation_source=true\"\n        }\n    }\n    </script>\n    \n    <style>\n        .reading-progress { position: fixed; top: 0; left: 0; width: 0%; height: 3px; background: linear-gradient(90deg, #1764ff, #0d47a1); z-index: 9999; transition: width 0.2s ease; box-shadow: 0 1px 3px rgba(23, 100, 255, 0.3); }\n        .home-container.root { overflow: visible !important; }\n        .article-detail-container { background: #f5f7fc; min-height: calc(100vh - 60px); padding: 80px 0 60px; position: relative; overflow: visible; }\n        .article-detail-container .row { margin: 0 -12px; align-items: flex-start !important; min-height: 100vh !important; }\n        .article-detail-container .row > [class*=\"col-\"] { padding: 0 12px; box-sizing: border-box; min-width: 0; }\n        .article-detail-container .row > .col-lg-8, .article-detail-container .row > .col-xl-9 { overflow: hidden; }\n        .article-detail-container .row > .col-lg-4, .article-detail-container .row > .col-xl-3 { overflow: visible !important; height: 100% !important; min-height: 100% !important; position: static !important; display: block !important; transform: none !important; will-change: auto !important; contain: none !important; perspective: none !important; filter: none !important; }\n        .article-detail { background: #fff; border-radius: 8px; padding: 1.5rem; margin-bottom: 2rem; box-shadow: 0 2px 10px rgba(0,0,0,0.1); width: 100%; box-sizing: border-box; }\n        .article-title { font-size: 1.7rem; color: #1a1c1f; line-height: 1.3; margin-bottom: 1.5rem; font-weight: 600; }\n        .article-meta { display: flex; align-items: center; flex-wrap: nowrap; gap: 1.2rem; color: #6c757d; font-size: 0.88rem; margin-bottom: 2rem; padding: 1.2rem 1.4rem; background: linear-gradient(135deg, #f8f9fa 0%, #ffffff 100%); border-radius: 12px; border-left: 4px solid #1764ff; box-shadow: 0 2px 8px rgba(0,0,0,0.06); overflow-x: hidden; transition: all 0.3s ease; }\n        .article-meta:hover { box-shadow: 0 4px 16px rgba(0,0,0,0.1); transform: translateY(-1px); }\n        .article-meta .meta-item { display: flex; align-items: center; gap: 0.25rem; font-weight: 500; color: #495057; white-space: nowrap; flex-shrink: 1; transition: color 0.2s ease; cursor: default; }\n        .article-meta .meta-item:hover { color: #1764ff; }\n        .article-meta .meta-item:hover .meta-icon { transform: scale(1.1); filter: drop-shadow(0 2px 6px rgba(13, 71, 161, 0.25)); }\n        .meta-icon { display: inline-block; font-size: 16px; flex-shrink: 0; vertical-align: middle; transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1); margin-right: 4px; opacity: 0.9; }\n        .meta-item:hover .meta-icon { opacity: 1; }\n        .meta-item:hover { color: #1764ff; }\n        .meta-item { position: relative; }\n        .meta-item::after { content: ''; position: absolute; bottom: -2px; left: 0; width: 0; height: 2px; background: linear-gradient(90deg, #1764ff, #00d4ff); transition: width 0.3s ease; }\n        .meta-item:hover::after { width: 100%; }\n        .article-category, .read-time { display: inline-block; padding: 6px 12px; border-radius: 16px; font-size: 0.8rem; font-weight: 600; }\n        .article-category { background: linear-gradient(135deg, #1764ff, #0d47a1); color: white; text-decoration: none; text-transform: uppercase; letter-spacing: 0.5px; transition: all 0.3s ease; }\n        .article-category:hover { background: linear-gradient(135deg, #0d47a1, #1764ff); color: white; text-decoration: none; transform: translateY(-1px); box-shadow: 0 4px 12px rgba(23, 100, 255, 0.3); }\n        .read-time { background: #e3f2fd; color: #1565c0; border: 1px solid #90caf9; }\n        .article-tags { margin: 20px 0 1.5rem; }\n        .article-tags .tag { display: inline-block; background: #1764ff; color: #fff; padding: 4px 12px; border-radius: 12px; font-size: 0.8rem; margin: 0 8px 4px 0; }\n        .article-summary { background: #e3f2fd; border-left: 4px solid #1764ff; padding: 1rem 1.5rem; margin-bottom: 2rem; border-radius: 0 4px 4px 0; }\n        .article-content { line-height: 1.8; font-size: 16px; color: #333; word-wrap: break-word; }\n        .article-content h1, .article-content h2, .article-content h3, .article-content h4, .article-content h5, .article-content h6 { margin: 2rem 0 1rem; font-weight: 600; line-height: 1.25; color: #1a1c1f; }\n        .article-content h1 { font-size: 2rem; border-bottom: 2px solid #e9ecef; padding-bottom: 0.5rem; }\n        .article-content h2 { font-size: 1.5rem; border-bottom: 1px solid #e9ecef; padding-bottom: 0.3rem; }\n        .article-content h3 { font-size: 1.25rem; }\n        .article-content h4 { font-size: 1.1rem; }\n        .article-content h5, .article-content h6 { font-size: 1rem; }\n        .article-content ul, .article-content ol { margin: 1rem 0; padding-left: 2rem; }\n        .article-content ul ul, .article-content ol ol, .article-content ul ol, .article-content ol ul { margin: 0.5rem 0; }\n        .article-content blockquote { margin: 1rem 0; padding: 0.5rem 1rem; border-left: 4px solid #1764ff; background-color: #f8f9fa; color: #6c757d; font-style: italic; }\n        .article-content pre { background-color: #f8f9fa; border: 1px solid #e9ecef; border-radius: 6px; padding: 1rem; margin: 1rem 0; overflow-x: auto; font-family: 'Courier New', Courier, monospace; font-size: 0.9em; line-height: 1.4; }\n        .article-content pre code { background: none; border: none; padding: 0; color: inherit; }\n        .article-content p { margin: 1rem 0; }\n        .article-content p:first-child { margin-top: 0; }\n        .article-content p:last-child { margin-bottom: 0; }\n        .article-footer { margin-top: 4rem; padding: 2.5rem; background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%); border-radius: 12px; border: 1px solid #e9ecef; position: relative; overflow: hidden; }\n        .article-footer::before { content: ''; position: absolute; top: 0; left: 0; right: 0; height: 3px; background: linear-gradient(90deg, #1764ff, #17a2b8, #28a745); }\n        .article-share h6 { color: #495057; font-weight: 600; margin-bottom: 1.5rem; font-size: 16px; }\n        .share-buttons { display: flex; gap: 12px; flex-wrap: wrap; }\n        .share-buttons .btn { padding: 10px 20px; border-radius: 25px; font-weight: 500; font-size: 14px; border: none; transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1); box-shadow: 0 2px 8px rgba(0,0,0,0.1); display: inline-flex; align-items: center; gap: 8px; text-decoration: none; }\n        .btn-weibo { background: linear-gradient(135deg, #e6162d, #c41230); color: white; }\n        .btn-copy { background: #1764ff; color: white; }\n        .btn-print { background: linear-gradient(135deg, #17a2b8, #138496); color: white; }\n        .btn-weibo:hover { background: linear-gradient(135deg, #c41230, #a00e26); box-shadow: 0 4px 16px rgba(230, 22, 45, 0.3); }\n        .btn-copy:hover { background: #0d47a1; box-shadow: 0 4px 16px rgba(23, 100, 255, 0.3); }\n        .btn-print:hover { background: linear-gradient(135deg, #138496, #117a8b); box-shadow: 0 4px 16px rgba(23, 162, 184, 0.3); }\n        .btn-weibo:hover, .btn-copy:hover, .btn-print:hover { transform: translateY(-2px); color: white; }\n        .article-actions { display: flex; justify-content: flex-end; align-items: center; }\n        .related-articles { margin-top: 3rem; }\n        .related-article-item { background: #fff; border-radius: 8px; padding: 1.5rem; margin-bottom: 1rem; box-shadow: 0 2px 8px rgba(0,0,0,0.1); transition: transform 0.3s ease; }\n        .related-article-item:hover { transform: translateY(-2px); }\n        .sidebar-card { background: linear-gradient(135deg, rgba(255,255,255,0.9) 0%, rgba(255,255,255,0.8) 100%); backdrop-filter: blur(20px); border: 1px solid rgba(255,255,255,0.3); border-radius: 16px; padding: 1rem; margin-bottom: 0.5rem; box-shadow: 0 4px 20px rgba(0,0,0,0.08), inset 0 1px 0 rgba(255,255,255,0.2); overflow: visible; box-sizing: border-box; width: 100%; z-index: 10; }\n        .sidebar-card.fixed { box-shadow: 0 8px 32px rgba(0,0,0,0.12); }\n        .sidebar-card::before { content: ''; position: absolute; top: 0; left: 0; right: 0; height: 3px; background: linear-gradient(90deg, #00d4ff, #1764ff, #9c27b0); border-radius: 20px 20px 0 0; }\n        .sidebar-card h5 { color: #1a202c; padding-bottom: 0.8rem; border-bottom: 2px solid rgba(23, 100, 255, 0.2); font-weight: 700; font-size: 1.1rem; }\n        .sidebar { min-height: 100vh; position: relative !important; display: block !important; flex-direction: column !important; gap: 1rem; }\n        .toc-card { flex: 0 1 auto; max-height: 60vh; overflow-y: auto; }\n        .sidebar-card:not(.toc-card) { flex: 1 1 auto; overflow: visible; }\n        .recommended-item { padding: 0.75rem 0; border-bottom: 1px solid rgba(0,0,0,0.05); }\n        .recommended-item:last-child { border-bottom: none; }\n        .recommended-item h6 { margin: 0 0 0.5rem 0; font-size: 0.9rem; line-height: 1.4; }\n        .recommended-item h6 a { color: #1a1c1f; text-decoration: none; transition: color 0.3s ease; }\n        .recommended-item h6 a:hover { color: #1764ff; }\n        .recommended-item .text-muted { font-size: 0.75rem; margin: 0; }\n        .toc-list { list-style: none; padding: 0; margin: 0; max-height: none; overflow-y: visible; overflow-x: hidden; }\n        .toc-list::-webkit-scrollbar { width: 4px; }\n        .toc-list::-webkit-scrollbar-track { background: rgba(0,0,0,0.1); border-radius: 2px; }\n        .toc-list::-webkit-scrollbar-thumb { background: linear-gradient(135deg, #00d4ff, #1764ff); border-radius: 2px; }\n        .toc-list::-webkit-scrollbar-thumb:hover { background: linear-gradient(135deg, #1764ff, #0d47a1); }\n        .toc-list a { color: #4a5568; text-decoration: none; font-size: 13px; font-weight: 500; display: block; padding: 8px 12px; border-radius: 8px; transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1); position: relative; margin-bottom: 4px; border: 1px solid transparent; }\n        .toc-list a::before { content: ''; position: absolute; left: 0; top: 0; bottom: 0; width: 3px; background: linear-gradient(135deg, #00d4ff, #1764ff); border-radius: 0 12px 12px 0; transform: scaleY(0); transition: transform 0.3s ease; }\n        .toc-list a:hover { color: #1764ff; background: linear-gradient(135deg, rgba(0, 212, 255, 0.1), rgba(23, 100, 255, 0.1)); border-color: rgba(23, 100, 255, 0.2); transform: translateX(8px); box-shadow: 0 4px 12px rgba(0, 212, 255, 0.2); }\n        .toc-list a:hover::before, .toc-list a.active::before { transform: scaleY(1); }\n        .toc-list a.active { color: #1764ff; background: linear-gradient(135deg, rgba(0, 212, 255, 0.15), rgba(23, 100, 255, 0.15)); font-weight: 700; border-color: rgba(23, 100, 255, 0.3); transform: translateX(4px); box-shadow: 0 6px 16px rgba(0, 212, 255, 0.25); }\n        .toc-number { color: #1764ff; font-weight: 600; margin-right: 0.5rem; font-size: 0.9em; }\n        .toc-text { flex: 1; }\n        .toc-level-1 { font-weight: 700; font-size: 15px; }\n        .toc-level-2 { font-size: 14px; }\n        .toc-level-3 { font-size: 13px; opacity: 0.9; }\n        .toc-level-4, .toc-level-5, .toc-level-6 { font-size: 12px; opacity: 0.8; }\n        .article-content .formatted-list { margin: 1.5rem 0; padding-left: 0; list-style: none; }\n        .article-content .formatted-list.ordered { counter-reset: list-counter; }\n        .article-content .formatted-list.ordered li { counter-increment: list-counter; position: relative; margin: 1rem 0; padding: 1rem 1rem 1rem 3rem; background: linear-gradient(135deg, rgba(23, 100, 255, 0.03), rgba(0, 212, 255, 0.03)); border-left: 4px solid #1764ff; border-radius: 0 8px 8px 0; transition: all 0.3s ease; }\n        .article-content .formatted-list.ordered li::before { content: counter(list-counter); position: absolute; left: 0.8rem; top: 1rem; background: linear-gradient(135deg, #1764ff, #00d4ff); color: white; width: 1.5rem; height: 1.5rem; border-radius: 50%; display: flex; align-items: center; justify-content: center; font-weight: 600; font-size: 0.9rem; }\n        .article-content .formatted-list.ordered li:hover { background: linear-gradient(135deg, rgba(23, 100, 255, 0.06), rgba(0, 212, 255, 0.06)); transform: translateX(4px); box-shadow: 0 2px 8px rgba(23, 100, 255, 0.15); }\n        .article-content .formatted-list.unordered li { position: relative; margin: 0.8rem 0; padding: 0.8rem 1rem 0.8rem 2.5rem; background: linear-gradient(135deg, rgba(40, 167, 69, 0.03), rgba(25, 135, 84, 0.03)); border-left: 4px solid #28a745; border-radius: 0 6px 6px 0; transition: all 0.3s ease; }\n        .article-content .formatted-list.unordered li::before { content: \"•\"; position: absolute; left: 1rem; top: 0.8rem; color: #28a745; font-size: 1.2rem; font-weight: bold; }\n        .article-content .formatted-list.unordered li:hover { background: linear-gradient(135deg, rgba(40, 167, 69, 0.06), rgba(25, 135, 84, 0.06)); transform: translateX(4px); box-shadow: 0 2px 8px rgba(40, 167, 69, 0.15); }\n        .article-content .list-title { color: #1764ff; font-weight: 600; display: inline-block; margin-bottom: 0.3rem; }\n        .article-content .list-content { color: #2c3e50; line-height: 1.6; display: block; }\n        .article-content .inline-code { background: #f8f9fa; border: 1px solid #e9ecef; border-radius: 4px; padding: 2px 6px; font-family: 'Consolas', 'Monaco', 'Courier New', monospace; font-size: 0.9em; color: #e83e8c; }\n        .article-content .label-item { margin: 0.8rem 0; }\n        .article-content .label { color: #1764ff; font-weight: 600; }\n        .article-content .section-label { color: #2c3e50; font-weight: 600; margin: 1rem 0 0.5rem 0; font-size: 1.1em; border-left: 4px solid #1764ff; padding-left: 0.8rem; }\n        .article-content p, .article-content .content-paragraph { margin: 1.2rem 0; line-height: 1.8; text-align: justify; color: #2c3e50; font-size: 1rem; }\n        .article-content .content-paragraph { text-indent: 0; margin: 1rem 0; }\n        .article-content .indent-paragraph { text-indent: 2em; margin: 1.2rem 0; }\n        .article-content .text-indent-1 { text-indent: 2em; display: block; }\n        .article-content .text-indent-2 { text-indent: 4em; display: block; }\n        .article-content .text-indent-3 { text-indent: 6em; display: block; }\n        .article-content .network-config { background: linear-gradient(135deg, #f8f9fa, #e9ecef); border: 1px solid #dee2e6; border-radius: 6px; padding: 0.8rem 1rem; margin: 0.5rem 0; font-family: 'Consolas', 'Monaco', 'Courier New', monospace; font-size: 0.9rem; box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05); }\n        .article-content .config-label { color: #1764ff; font-weight: 600; }\n        .article-content .config-desc { color: #495057; }\n        .article-content .algorithm-step { background: linear-gradient(135deg, rgba(23, 100, 255, 0.05), rgba(0, 212, 255, 0.05)); border-left: 4px solid #1764ff; padding: 0.8rem 1rem; margin: 0.5rem 0; border-radius: 0 6px 6px 0; transition: all 0.3s ease; }\n        .article-content .step-number { color: #1764ff; font-weight: 700; margin-right: 0.5rem; }\n        .article-content .step-desc { color: #2c3e50; line-height: 1.5; }\n        .article-content table { width: 100%; border-collapse: collapse; margin: 1.5rem 0; background: white; border-radius: 8px; overflow: hidden; box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1); }\n        .article-content table th { background: linear-gradient(135deg, #1764ff, #00d4ff); color: white; padding: 1rem; text-align: left; font-weight: 600; }\n        .article-content table td { padding: 0.8rem 1rem; border-bottom: 1px solid #e9ecef; }\n        .article-content table tr:hover { background: rgba(23, 100, 255, 0.05); }\n        .article-content strong { color: #1764ff; font-weight: 600; }\n        .article-content em { color: #6c757d; font-style: italic; }\n        .article-content a { color: #1764ff; text-decoration: none; border-bottom: 1px solid transparent; transition: all 0.3s ease; }\n        .article-content a:hover { border-bottom-color: #1764ff; color: #0056b3; }\n        .article-content .definition-item { background: linear-gradient(135deg, rgba(40, 167, 69, 0.05), rgba(25, 135, 84, 0.05)); border-left: 4px solid #28a745; padding: 1rem; margin: 1rem 0; border-radius: 0 8px 8px 0; }\n        .article-content .definition-label { color: #28a745; font-weight: 600; display: block; margin-bottom: 0.5rem; }\n        .article-content .definition-content { color: #2c3e50; line-height: 1.6; }\n        .article-content .note-item { background: linear-gradient(135deg, rgba(255, 193, 7, 0.1), rgba(255, 193, 7, 0.05)); border: 1px solid rgba(255, 193, 7, 0.3); border-radius: 8px; padding: 1rem; margin: 1rem 0; position: relative; }\n        .article-content .note-item::before { content: \"💡\"; position: absolute; left: 1rem; top: 1rem; font-size: 1.2em; }\n        .article-content .note-label { color: #856404; font-weight: 600; margin-left: 2rem; }\n        .article-content .note-content { color: #2c3e50; line-height: 1.6; margin-left: 2rem; display: block; margin-top: 0.3rem; }\n        .article-content .math-formula { background: #f8f9fa; border: 1px solid #e9ecef; border-radius: 6px; padding: 1rem; margin: 1rem 0; text-align: center; font-family: 'Times New Roman', serif; font-size: 1.1em; color: #2c3e50; }\n        @media (min-width: 1400px) { .article-content { font-size: 17px; line-height: 1.8; } .article-detail { padding: 2.5rem; } }\n        @media (max-width: 1199px) and (min-width: 992px) { .article-detail { padding: 1.5rem; } .sidebar-card { padding: 1rem; } .toc-list a { font-size: 12px; padding: 6px 8px; } }\n        @media (max-width: 768px) {\n            .article-detail-container { padding: 60px 15px; }\n            .article-content .network-config, .article-content .algorithm-step, .article-content .definition-item, .article-content .note-item { margin: 0.5rem -1rem; border-radius: 0; }\n            .article-content .note-item::before { left: 0.5rem; }\n            .article-content .note-label, .article-content .note-content { margin-left: 1.5rem; }\n            .article-detail { padding: 1.5rem; }\n            .article-title { font-size: 1.6rem; line-height: 1.3; }\n            .article-meta { flex-wrap: wrap; gap: 1rem; padding: 1rem; font-size: 0.8rem; }\n            .article-meta .meta-item { flex-shrink: 1; }\n            .meta-icon { font-size: 14px; margin-right: 3px; }\n            .article-content { font-size: 15px; }\n            .article-content h1 { font-size: 1.6rem; }\n            .article-content h2 { font-size: 1.4rem; }\n            .article-content h3 { font-size: 1.2rem; }\n            .article-content h4 { font-size: 1.1rem; }\n            .article-content pre { padding: 0.75rem; font-size: 0.85em; overflow-x: auto; }\n            .article-content code { font-size: 0.85em; padding: 1px 4px; }\n            .sidebar { margin-top: 2rem; }\n            .sidebar-card { position: static; }\n            .toc-card { max-height: none; overflow-y: visible; }\n            .share-buttons { flex-direction: column; gap: 0.5rem; }\n            .share-buttons .btn { width: 100%; justify-content: center; }\n        }\n        @media print { body * { visibility: hidden; } .article-detail, .article-detail * { visibility: visible; } .article-detail { position: absolute; left: 0; top: 0; width: 100%; background: white; box-shadow: none; border: none; margin: 0; padding: 20px; } .article-meta { border: none; background: none; padding: 0; margin-bottom: 20px; } .article-footer, .reading-progress, .back-to-top { display: none !important; } .article-content { font-size: 12pt; line-height: 1.6; } .article-content h1, .article-content h2, .article-content h3, .article-content h4, .article-content h5, .article-content h6 { page-break-after: avoid; margin-top: 20pt; margin-bottom: 10pt; } .article-content pre, .article-content blockquote { page-break-inside: avoid; } }\n    </style>\n\n<script type=\"application/ld+json\">{\"@context\":\"https://schema.org\",\"@type\":\"BreadcrumbList\",\"itemListElement\":[{\"@type\":\"ListItem\",\"position\":1,\"name\":\"Hogar\",\"item\":\"https://ocr.oldfish.cn/default.aspx\"},{\"@type\":\"ListItem\",\"position\":2,\"name\":\"Artículos técnicos\",\"item\":\"https://ocr.oldfish.cn/Articles.aspx\"},{\"@type\":\"ListItem\",\"position\":3,\"name\":\"Detalles del artículo\",\"item\":\"https://ocr.oldfish.cn/Article.aspx?id=82&slug=attention-mechanism-principles&lang=zh-Hans&_translation_source=true\"}]}</script>\n<link rel=\"stylesheet\" href=\"/site/css/site.css\" type=\"text/css\" /><title>【Serie OCR de aprendizaje profundo · 5】 Principio e implementación del mecanismo de atención</title><meta http-equiv=\"Content-Language\" content=\"es\" />\n</head>\r\n<body>\r\n    <div class=\"home-container root\">\r\n        <header class=\"v4_header_pc\">\r\n            <div class=\"header_pc_left\">\r\n                <a href=\"Default.aspx\" title=\"Inicio | Reconocimiento de texto inteligente con IA\" class=\"pc-logo-wrap ml-4 mr-5\">\r\n                    <picture>\r\n                        <source srcset=\"site/image/logo-s.png.webp\" type=\"image/webp\" />\r\n                        <img src=\"site/image/logo-s.png\" class=\"pc-logo\" style=\"height: 100%\" alt=\"Logotipo del sitio web oficial del asistente de reconocimiento de texto OCR - Plataforma de reconocimiento de texto inteligente AI\" />\r\n                    </picture>\r\n                    <span style=\"font-size: 18px; font-weight: bold; color: #333;\">Asistente de reconocimiento de texto OCR</span>\r\n                </a>\r\n                <ul class=\"top-nav\" role=\"navigation\" aria-label=\"Navegación principal\">\r\n                    <li><a class=\"color-default\" href=\"Default.aspx\" title=\"Página de inicio del Asistente de reconocimiento de texto de OCR\">Hogar</a></li>\r\n                    <li><a class=\"color-default\" data-sub=\"product-sub-nav\" href=\"Detail.aspx\" title=\"Introducción a la función del producto OCR\">Características del producto:</a><div class=\"triangle\"></div>\r\n                    </li>\r\n                    <li><a class=\"color-default\" data-sub=\"experience-sub-nav\" href=\"Ocr.aspx\" title=\"Experimente las funciones de OCR en línea\">Experiencia en línea</a><div class=\"triangle\"></div>\r\n                    </li>\r\n                    <li><a class=\"color-default\" data-sub=\"vip-sub-nav\" href=\"Version.aspx\" title=\"Servicio de actualización de membresía de OCR\">Actualizaciones de membresía</a><div class=\"triangle\"></div>\r\n                    </li>\r\n                    <li><a class=\"color-default\" href=\"Download.aspx\" title=\"Descargue el Asistente de reconocimiento de texto OCR gratis\">Descarga gratis</a></li>\r\n                    <li><a class=\"color-default\" data-sub=\"articles-sub-nav\" href=\"Articles.aspx\" title=\"Artículos técnicos de OCR e intercambio de conocimientos\">Intercambio de tecnología</a><div class=\"triangle\"></div>\r\n                    </li>\r\n                    <li><a class=\"color-default\" data-sub=\"support-sub-nav\" href=\"FAQ.aspx\" title=\"Ayuda para el uso de OCR y soporte técnico\">Centro de ayuda</a><div class=\"triangle\"></div>\r\n                    </li>\r\n                </ul>\r\n                <div class=\"product-con\">\r\n                    <section id=\"product-sub-nav\" class=\"pls-nav-dropdown\" style=\"visibility: hidden; height: 0px; display: none;\">\r\n                        <div class=\"d-flex nav-drown-con\" style=\"height: 0px; transition: transform 0.2s ease-in-out 0s,opacity 0.2s ease-in-out 0s; opacity: 0; transform: translate(0px, -100%);\">\r\n                            <div class=\"prdocu-sub-left\">\r\n                                <div class=\"mr-auto\">\r\n                                    <picture>\r\n                                        <source class=\"p-icon\" srcset=\"site/image/icon/p_1.png.webp\" type=\"image/webp\" alt=\"\" aria-hidden=\"true\" />\r\n                                        <img class=\"p-icon\" src=\"site/image/icon/p_1.png\" alt=\"Icono de función de producto OCR\" aria-hidden=\"true\" loading=\"lazy\" />\r\n                                    </picture>\r\n                                    <div class=\"mt-4\">\r\n                                        <h3 class=\"h6\">Asistente de reconocimiento de texto OCR</h3>\r\n                                        <p class=\"color-gray fn14 mt-3 sub-left-desc\">Mejore la eficiencia, reduzca los costos y cree valor</p>\r\n                                        <p class=\"color-gray fn14 mt-3 sub-left-desc\">&nbsp;</p>\r\n                                        <p class=\"color-gray fn14 mt-3 sub-left-desc\">Reconocimiento inteligente, procesamiento de alta velocidad y salida precisa</p>\r\n                                        <p class=\"color-gray fn14 mt-3 sub-left-desc\">Del texto a las tablas, de las fórmulas a las traducciones</p>\r\n                                        <p class=\"color-gray fn14 mt-3 sub-left-desc\">Haz que cada procesamiento de texto sea tan fácil</p>\r\n                                        <a class=\"link block disa fn14 contact-business shake\" href=\"Detail.aspx\">Más información sobre las funciones<span class=\"iconfont4 icon-xiangzuo gt\"></span></a>\r\n                                    </div>\r\n                                </div>\r\n                            </div>\r\n                            <div class=\"bg-white prdocu-sub-right\">\r\n                                <div>\r\n                                    <h2 class=\"text-base-color h6\">Características del producto:</h2>\r\n                                    <div class=\"mt-4 d-flex flex-wrap\">\r\n                                        <a href=\"Detail.aspx\" class=\"pro-item color-default\" title=\"Consulte los detalles de las funciones principales del Asistente de OCR\">\r\n                                            <div class=\"letter-wrap\">\r\n                                                <h3 class=\"h6\">Características principales:</h3>\r\n                                                <span class=\"color-gray fn14\">Obtenga más información sobre las características principales y los beneficios técnicos de OCR Assistant, con una tasa de reconocimiento del 98% +</span>\r\n                                            </div>\r\n                                        </a>\r\n                                        <a href=\"Version.aspx\" class=\"pro-item color-default\" title=\"Comparar las diferencias entre las versiones de OCR Assistant\">\r\n                                            <div class=\"letter-wrap\">\r\n                                                <h3 class=\"h6\">Comparación de versiones</h3>\r\n                                                <span class=\"color-gray fn14\">Compare las diferencias funcionales de la versión gratuita, la versión personal, la versión profesional y la versión definitiva en detalle</span>\r\n                                            </div>\r\n                                        </a>\r\n                                        <a href=\"FAQ.aspx\" class=\"pro-item color-default\" title=\"Consulte las preguntas frecuentes sobre el Asistente de OCR\">\r\n                                            <div class=\"letter-wrap\">\r\n                                                <h3 class=\"h6\">Preguntas y respuestas sobre productos</h3>\r\n                                                <span class=\"color-gray fn14\">Aprenda rápidamente sobre las características del producto, los métodos de uso y las respuestas detalladas a las preguntas más frecuentes</span>\r\n                                            </div>\r\n                                        </a>\r\n                                        <a href=\"Download.aspx\" class=\"pro-item color-default\" title=\"Descargue el Asistente de reconocimiento de texto OCR gratis\">\r\n                                            <div class=\"letter-wrap\">\r\n                                                <h3 class=\"h6\">Pruébalo gratis</h3>\r\n                                                <span class=\"color-gray fn14\">Descargue e instale OCR Assistant ahora para experimentar la potente función de reconocimiento de texto de forma gratuita</span>\r\n                                            </div>\r\n                                        </a>\r\n                                        <div style=\"width: 100%; margin: 15px 0;\">\r\n                                            <h4 class=\"h6\" style=\"color: #007cfa; margin-bottom: 10px;\">Reconocimiento OCR en línea</h4>\r\n                                        </div>\r\n                                        <a href=\"Ocr.aspx?type=text_recognize\" class=\"pro-item color-default\" title=\"Experimente el reconocimiento universal de texto en línea\">\r\n                                            <div class=\"letter-wrap\">\r\n                                                <h3 class=\"h6\">Reconocimiento universal de caracteres</h3>\r\n                                                <span class=\"color-gray fn14\">Extracción inteligente de texto multilingüe de alta precisión, compatible con el reconocimiento de imágenes complejas impresas y multiescena</span>\r\n                                            </div>\r\n                                        </a>\r\n                                        <a href=\"Ocr.aspx?type=table\" class=\"pro-item color-default\">\r\n                                            <div class=\"letter-wrap\">\r\n                                                <h3 class=\"h6\">Identificación universal de tablas</h3>\r\n                                                <span class=\"color-gray fn14\">Conversión inteligente de imágenes de tablas a archivos de Excel, procesamiento automático de estructuras de tablas complejas y celdas combinadas</span>\r\n                                            </div>\r\n                                        </a>\r\n                                        <a href=\"Ocr.aspx?type=handwritten_ocr\" class=\"pro-item color-default\">\r\n                                            <div class=\"letter-wrap\">\r\n                                                <h3 class=\"h6\">Reconocimiento de escritura a mano</h3>\r\n                                                <span class=\"color-gray fn14\">Reconocimiento inteligente de contenido escrito a mano en chino e inglés, notas de apoyo en el aula, registros médicos y otros escenarios</span>\r\n                                            </div>\r\n                                        </a>\r\n                                        <a href=\"Ocr.aspx?type=pdf2word\" class=\"pro-item color-default\">\r\n                                            <div class=\"letter-wrap\">\r\n                                                <h3 class=\"h6\">PDF a Word</h3>\r\n                                                <span class=\"color-gray fn14\">Los documentos PDF se convierten rápidamente a formato Word, conservando perfectamente el diseño original y el diseño gráfico</span>\r\n                                            </div>\r\n                                        </a>\r\n                                    </div>\r\n                                </div>\r\n                            </div>\r\n                        </div>\r\n                    </section>\r\n                    <section id=\"experience-sub-nav\" class=\"pls-nav-dropdown solution-part\" style=\"visibility: hidden; height: 0px; display: none;\">\r\n                        <div class=\"d-flex nav-drown-con\" style=\"height: 0px; transition: transform 0.2s ease-in-out 0s,opacity 0.2s ease-in-out 0s; opacity: 0; transform: translate(0px, -100%);\">\r\n                            <div class=\"prdocu-sub-left\">\r\n                                <div class=\"mr-auto\">\r\n                                    <picture>\r\n                                        <source srcset=\"site/image/icon/p_4.png.webp\" type=\"image/webp\" />\r\n                                        <img class=\"p-icon\" src=\"site/image/icon/p_4.png\" alt=\"Icono del Centro de experiencia de OCR en línea\" aria-hidden=\"true\" loading=\"lazy\" />\r\n                                    </picture>\r\n                                    <div class=\"mt-4\">\r\n                                        <h3 class=\"h6\">Asistente de reconocimiento de texto OCR</h3>\r\n                                        <p class=\"color-gray fn14 mt-3 sub-left-desc\">Texto, tablas, fórmulas, documentos, traducciones</p>\r\n                                        <p class=\"color-gray fn14 mt-3 sub-left-desc\">&nbsp;</p>\r\n                                        <p class=\"color-gray fn14 mt-3 sub-left-desc\">Complete todas sus necesidades de procesamiento de textos en tres pasos</p>\r\n                                        <p class=\"color-gray fn14 mt-3 sub-left-desc\">Captura de pantalla → Identificar aplicaciones →</p>\r\n                                        <p class=\"color-gray fn14 mt-3 sub-left-desc\">Aumente la eficiencia del trabajo en un 300%</p>\r\n                                        <a class=\"link block disa fn14 contact-business shake\" href=\"Ocr.aspx\">Pruébalo ahora<span class=\"iconfont4 icon-xiangzuo gt\"></span></a>\r\n                                    </div>\r\n                                </div>\r\n                            </div>\r\n                            <div class=\"bg-white prdocu-sub-right\">\r\n                                <div>\r\n                                    <div class=\"mt-4 d-flex flex-wrap\">\r\n                                        <div style=\"width: 100%; margin-bottom: 15px;\">\r\n                                            <h4 class=\"h6\" style=\"color: #007cfa; margin-bottom: 10px;\">Experiencia en funciones de OCR</h4>\r\n                                        </div>\r\n                                        <a href=\"Ocr.aspx?type=index\" class=\"pro-item color-default\">\r\n                                            <div class=\"letter-wrap\">\r\n                                                <h3 class=\"h6\">Funcionalidad completa</h3>\r\n                                                <span class=\"color-gray fn14\">Experimente todas las funciones inteligentes de OCR en un solo lugar para encontrar rápidamente la mejor solución para sus necesidades</span>\r\n                                            </div>\r\n                                        </a>\r\n                                        <a href=\"Ocr.aspx?type=text_recognize\" class=\"pro-item color-default\">\r\n                                            <div class=\"letter-wrap\">\r\n                                                <h3 class=\"h6\">Reconocimiento universal de caracteres</h3>\r\n                                                <span class=\"color-gray fn14\">Extracción inteligente de texto multilingüe de alta precisión, compatible con el reconocimiento de imágenes complejas impresas y multiescena</span>\r\n                                            </div>\r\n                                        </a>\r\n                                        <a href=\"Ocr.aspx?type=table\" class=\"pro-item color-default\">\r\n                                            <div class=\"letter-wrap\">\r\n                                                <h3 class=\"h6\">Identificación universal de tablas</h3>\r\n                                                <span class=\"color-gray fn14\">Conversión inteligente de imágenes de tablas a archivos de Excel, procesamiento automático de estructuras de tablas complejas y celdas combinadas</span>\r\n                                            </div>\r\n                                        </a>\r\n                                        <a href=\"Ocr.aspx?type=handwritten_ocr\" class=\"pro-item color-default\">\r\n                                            <div class=\"letter-wrap\">\r\n                                                <h3 class=\"h6\">Reconocimiento de escritura a mano</h3>\r\n                                                <span class=\"color-gray fn14\">Reconocimiento inteligente de contenido escrito a mano en chino e inglés, notas de apoyo en el aula, registros médicos y otros escenarios</span>\r\n                                            </div>\r\n                                        </a>\r\n                                        <a href=\"Ocr.aspx?type=pdf2word\" class=\"pro-item color-default\">\r\n                                            <div class=\"letter-wrap\">\r\n                                                <h3 class=\"h6\">PDF a Word</h3>\r\n                                                <span class=\"color-gray fn14\">Los documentos PDF se convierten rápidamente a formato Word, conservando perfectamente el diseño original y el diseño gráfico</span>\r\n                                            </div>\r\n                                        </a>\r\n                                        <a href=\"Ocr.aspx?type=pdf2markdown\" class=\"pro-item color-default\">\r\n                                            <div class=\"letter-wrap\">\r\n                                                <h3 class=\"h6\">PDF a Markdown</h3>\r\n                                                <span class=\"color-gray fn14\">Los documentos PDF se convierten de forma inteligente al formato MD, y los bloques de código y las estructuras de texto se optimizan automáticamente para su procesamiento</span>\r\n                                            </div>\r\n                                        </a>\r\n                                        <div style=\"width: 100%; margin: 15px 0;\">\r\n                                            <h4 class=\"h6\" style=\"color: #007cfa; margin-bottom: 10px;\">Herramientas de procesamiento de documentos</h4>\r\n                                        </div>\r\n                                        <a href=\"Ocr.aspx?type=word2pdf\" class=\"pro-item color-default\">\r\n                                            <div class=\"letter-wrap\">\r\n                                                <h3 class=\"h6\">De Word a PDF</h3>\r\n                                                <span class=\"color-gray fn14\">Los documentos de Word se convierten a PDF con un solo clic, conservando perfectamente el formato original, adecuados para archivar y compartir documentos oficiales</span>\r\n                                            </div>\r\n                                        </a>\r\n                                        <a href=\"Ocr.aspx?type=word2jpg\" class=\"pro-item color-default\">\r\n                                            <div class=\"letter-wrap\">\r\n                                                <h3 class=\"h6\">De la palabra a la imagen</h3>\r\n                                                <span class=\"color-gray fn14\">Conversión inteligente de documentos de Word a imagen JPG, admite procesamiento de varias páginas, fácil de compartir en las redes sociales</span>\r\n                                            </div>\r\n                                        </a>\r\n                                        <a href=\"Ocr.aspx?type=pdf2jpg\" class=\"pro-item color-default\">\r\n                                            <div class=\"letter-wrap\">\r\n                                                <h3 class=\"h6\">PDF a imagen</h3>\r\n                                                <span class=\"color-gray fn14\">Convierta documentos PDF a imágenes JPG en alta definición, admita el procesamiento por lotes y la resolución personalizada</span>\r\n                                            </div>\r\n                                        </a>\r\n                                        <a href=\"Ocr.aspx?type=image2pdf\" class=\"pro-item color-default\">\r\n                                            <div class=\"letter-wrap\">\r\n                                                <h3 class=\"h6\">Imagen a PDF</h3>\r\n                                                <span class=\"color-gray fn14\">Combine varias imágenes en documentos PDF, admita la clasificación y la configuración de páginas</span>\r\n                                            </div>\r\n                                        </a>\r\n                                        <div style=\"width: 100%; margin: 15px 0;\">\r\n                                            <h4 class=\"h6\" style=\"color: #007cfa; margin-bottom: 10px;\">Herramientas para desarrolladores</h4>\r\n                                        </div>\r\n                                        <a href=\"Tool.aspx?type=Json\" class=\"pro-item color-default\">\r\n                                            <div class=\"letter-wrap\">\r\n                                                <h3 class=\"h6\">Formato JSON</h3>\r\n                                                <span class=\"color-gray fn14\">Embellezca de forma inteligente la estructura del código JSON, admita la compresión y la expansión, y facilite el desarrollo y la depuración</span>\r\n                                            </div>\r\n                                        </a>\r\n                                        <a href=\"Tool.aspx?type=Regex\" class=\"pro-item color-default\">\r\n                                            <div class=\"letter-wrap\">\r\n                                                <h3 class=\"h6\">expresión regular</h3>\r\n                                                <span class=\"color-gray fn14\">Verifique los efectos de coincidencia de expresiones regulares en tiempo real, con una biblioteca integrada de patrones comunes</span>\r\n                                            </div>\r\n                                        </a>\r\n                                        <a href=\"Tool.aspx?type=Encode\" class=\"pro-item color-default\">\r\n                                            <div class=\"letter-wrap\">\r\n                                                <h3 class=\"h6\">Conversión de codificación de texto</h3>\r\n                                                <span class=\"color-gray fn14\">Admite la conversión de múltiples formatos de codificación como Base64, URL y Unicode</span>\r\n                                            </div>\r\n                                        </a>\r\n                                        <a href=\"Tool.aspx?type=Diff\" class=\"pro-item color-default\">\r\n                                            <div class=\"letter-wrap\">\r\n                                                <h3 class=\"h6\">Coincidencia y combinación de texto</h3>\r\n                                                <span class=\"color-gray fn14\">Resalte las diferencias de texto y admita la comparación línea por línea y la fusión inteligente</span>\r\n                                            </div>\r\n                                        </a>\r\n                                        <a href=\"Tool.aspx?type=Color\" class=\"pro-item color-default\">\r\n                                            <div class=\"letter-wrap\">\r\n                                                <h3 class=\"h6\">Herramienta Color</h3>\r\n                                                <span class=\"color-gray fn14\">Conversión de color RGB/HEX, selector de color en línea, una herramienta imprescindible para el desarrollo front-end</span>\r\n                                            </div>\r\n                                        </a>\r\n                                        <a href=\"Tool.aspx?type=Count\" class=\"pro-item color-default\">\r\n                                            <div class=\"letter-wrap\">\r\n                                                <h3 class=\"h6\">Recuento de palabras</h3>\r\n                                                <span class=\"color-gray fn14\">Conteo inteligente de caracteres, vocabulario y párrafos, y optimización automática del diseño del texto</span>\r\n                                            </div>\r\n                                        </a>\r\n                                        <a href=\"Tool.aspx?type=Timespan\" class=\"pro-item color-default\">\r\n                                            <div class=\"letter-wrap\">\r\n                                                <h3 class=\"h6\">Conversión de marca de tiempo</h3>\r\n                                                <span class=\"color-gray fn14\">La hora se convierte hacia y desde marcas de tiempo de Unix, y se admiten múltiples formatos y configuraciones de zona horaria</span>\r\n                                            </div>\r\n                                        </a>\r\n                                        <a href=\"Tool.aspx?type=Calc\" class=\"pro-item color-default\">\r\n                                            <div class=\"letter-wrap\">\r\n                                                <h3 class=\"h6\">Herramienta de calculadora</h3>\r\n                                                <span class=\"color-gray fn14\">Calculadora científica en línea con soporte para operaciones básicas y cálculos avanzados de funciones matemáticas</span>\r\n                                            </div>\r\n                                        </a>\r\n                                    </div>\r\n                                </div>\r\n                            </div>\r\n                        </div>\r\n                    </section>\r\n                    <section id=\"articles-sub-nav\" class=\"pls-nav-dropdown solution-part\" style=\"visibility: hidden; height: 0px; display: none;\">\r\n                        <div class=\"d-flex nav-drown-con\" style=\"height: 0px; transition: transform 0.2s ease-in-out 0s,opacity 0.2s ease-in-out 0s; opacity: 0; transform: translate(0px, -100%);\">\r\n                            <div class=\"prdocu-sub-left\">\r\n                                <div class=\"mr-auto\">\r\n                                    <picture>\r\n                                        <source srcset=\"site/image/icon/p_4.png.webp\" type=\"image/webp\" />\r\n                                        <img class=\"p-icon\" src=\"site/image/icon/p_4.png\" alt=\"Icono del Centro de intercambio tecnológico\" aria-hidden=\"true\" loading=\"lazy\" />\r\n                                    </picture>\r\n                                    <div class=\"mt-4\">\r\n                                        <h3 class=\"h6\">Uso compartido de tecnología OCR</h3>\r\n                                        <p class=\"color-gray fn14 mt-3 sub-left-desc\">Tutoriales técnicos, casos de aplicación, recomendaciones de herramientas</p>\r\n                                        <p class=\"color-gray fn14 mt-3 sub-left-desc\">&nbsp;</p>\r\n                                        <p class=\"color-gray fn14 mt-3 sub-left-desc\">Un camino de aprendizaje completo desde el principiante hasta el dominio</p>\r\n                                        <p class=\"color-gray fn14 mt-3 sub-left-desc\">Casos prácticos → análisis técnico → aplicaciones de herramientas</p>\r\n                                        <p class=\"color-gray fn14 mt-3 sub-left-desc\">Potencie su camino hacia la mejora de la tecnología OCR</p>\r\n                                        <a class=\"link block disa fn14 contact-business shake\" href=\"Articles.aspx\">Explorar artículos<span class=\"iconfont4 icon-xiangzuo gt\"></span></a>\r\n                                    </div>\r\n                                </div>\r\n                            </div>\r\n                            <div class=\"bg-white prdocu-sub-right\">\r\n                                <div>\r\n                                    <h2 class=\"text-base-color h6\">Intercambio de tecnología</h2>\r\n                                    <div class=\"mt-4 d-flex flex-wrap\">\r\n                                        <a href=\"Articles.aspx\" class=\"pro-item color-default\" title=\"Ver todos los artículos técnicos de OCR\">\r\n                                            <div class=\"letter-wrap\">\r\n                                                <h3 class=\"h6\">Todos los artículos</h3>\r\n                                                <span class=\"color-gray fn14\">Explore todos los artículos técnicos de OCR que cubren un cuerpo completo de conocimientos, desde lo básico hasta lo avanzado</span>\r\n                                            </div>\r\n                                        </a>\r\n                                        \r\n                                        <a href=\"Articles.aspx?category=2\" class=\"pro-item color-default\" title=\"Tutoriales técnicos de OCR y guías de introducción\">\r\n                                            <div class=\"letter-wrap\">\r\n                                                <h3 class=\"h6\">Guía avanzada</h3>\r\n                                                <span class=\"color-gray fn14\">Desde tutoriales técnicos de OCR introductorios hasta competentes, guías prácticas detalladas y tutoriales prácticos</span>\r\n                                            </div>\r\n                                        </a>\r\n                                        \r\n                                        <a href=\"Articles.aspx?category=3\" class=\"pro-item color-default\" title=\"Principios, algoritmos y aplicaciones de la tecnología OCR\">\r\n                                            <div class=\"letter-wrap\">\r\n                                                <h3 class=\"h6\">Exploración tecnológica</h3>\r\n                                                <span class=\"color-gray fn14\">Explore las fronteras de la tecnología OCR, desde los principios hasta las aplicaciones, y analice en profundidad los algoritmos básicos</span>\r\n                                            </div>\r\n                                        </a>\r\n                                        \r\n                                        <a href=\"Articles.aspx?category=4\" class=\"pro-item color-default\" title=\"Los últimos desarrollos y tendencias de desarrollo en la industria OCR\">\r\n                                            <div class=\"letter-wrap\">\r\n                                                <h3 class=\"h6\">Tendencias de la industria</h3>\r\n                                                <span class=\"color-gray fn14\">Información detallada sobre las tendencias de desarrollo de la tecnología OCR, el análisis de mercado, la dinámica de la industria y las perspectivas futuras</span>\r\n                                            </div>\r\n                                        </a>\r\n                                        \r\n                                        <a href=\"Articles.aspx?category=5\" class=\"pro-item color-default\" title=\"Casos de aplicación de la tecnología OCR en diversas industrias\">\r\n                                            <div class=\"letter-wrap\">\r\n                                                <h3 class=\"h6\">Casos de uso:</h3>\r\n                                                <span class=\"color-gray fn14\">Se comparten casos de aplicación del mundo real, soluciones y mejores prácticas de la tecnología OCR en diversas industrias</span>\r\n                                            </div>\r\n                                        </a>\r\n                                        \r\n                                        <a href=\"Articles.aspx?category=6\" class=\"pro-item color-default\" title=\"Revisiones profesionales, análisis comparativos y pautas recomendadas para usar herramientas de software OCR\">\r\n                                            <div class=\"letter-wrap\">\r\n                                                <h3 class=\"h6\">Revisión de herramientas</h3>\r\n                                                <span class=\"color-gray fn14\">Evalúe varios software y herramientas de reconocimiento de texto de OCR y proporcione sugerencias detalladas de comparación y selección de funciones</span>\r\n                                            </div>\r\n                                        </a>\r\n                                        \r\n                                    </div>\r\n                                </div>\r\n                            </div>\r\n                        </div>\r\n                    </section>\r\n                    <section id=\"vip-sub-nav\" class=\"pls-nav-dropdown solution-part\" style=\"visibility: hidden; height: 0px; display: none;\">\r\n                        <div class=\"d-flex nav-drown-con\" style=\"height: 0px; transition: transform 0.2s ease-in-out 0s,opacity 0.2s ease-in-out 0s; opacity: 0; transform: translate(0px, -100%);\">\r\n                            <div class=\"prdocu-sub-left\">\r\n                                <div class=\"mr-auto\">\r\n                                    <picture>\r\n                                        <source class=\"p-icon\" srcset=\"site/image/icon/p_1.png.webp\" type=\"image/webp\" alt=\"\" aria-hidden=\"true\" />\r\n                                        <img class=\"p-icon\" src=\"site/image/icon/p_1.png\" alt=\"Icono de servicio de actualización de membresía\" aria-hidden=\"true\" loading=\"lazy\" />\r\n                                    </picture>\r\n                                    <div class=\"mt-4\">\r\n                                        <h3 class=\"h6\">Servicio de actualización de membresía</h3>\r\n                                        <p class=\"color-gray fn14 mt-3 sub-left-desc\">Desbloquea todas las funciones premium y disfruta de servicios exclusivos</p>\r\n                                        <p class=\"color-gray fn14 mt-3 sub-left-desc\">&nbsp;</p>\r\n                                        <p class=\"color-gray fn14 mt-3 sub-left-desc\">Reconocimiento fuera de línea, procesamiento por lotes, uso ilimitado</p>\r\n                                        <p class=\"color-gray fn14 mt-3 sub-left-desc\">Pro → Ultimate → Enterprise</p>\r\n                                        <p class=\"color-gray fn14 mt-3 sub-left-desc\">Hay algo que se adapta a tus necesidades</p>\r\n                                        <a class=\"link block disa fn14 contact-business shake\" href=\"Version.aspx\">Ver detalles<span class=\"iconfont4 icon-xiangzuo gt\"></span></a>\r\n                                    </div>\r\n                                </div>\r\n                            </div>\r\n                            <div class=\"bg-white prdocu-sub-right\">\r\n                                <div>\r\n                                    <h2 class=\"text-base-color h6\" style=\"margin: inherit;\">Actualizaciones de membresía</h2>\r\n                                    <div class=\"mt-4 d-flex flex-wrap\">\r\n                                        <a href=\"Version.aspx\" class=\"pro-item color-default\">\r\n                                            <div class=\"letter-wrap\">\r\n                                                <h3 class=\"h6\">Privilegios de membresía</h3>\r\n                                                <span class=\"color-gray fn14\">Obtenga más información sobre las diferencias entre ediciones y elija el nivel de membresía que más le convenga</span>\r\n                                            </div>\r\n                                        </a>\r\n                                        <a href=\"Upgrade.aspx\" class=\"pro-item color-default\">\r\n                                            <div class=\"letter-wrap\">\r\n                                                <h3 class=\"h6\">Actualizar ahora</h3>\r\n                                                <span class=\"color-gray fn14\">Actualice rápidamente su membresía VIP para desbloquear más funciones premium y servicios exclusivos</span>\r\n                                            </div>\r\n                                        </a>\r\n                                        <a href=\"User.aspx\" class=\"pro-item color-default\">\r\n                                            <div class=\"letter-wrap\">\r\n                                                <h3 class=\"h6\">Mi cuenta</h3>\r\n                                                <span class=\"color-gray fn14\">Administre la información de la cuenta, el estado de la suscripción y el historial de uso para personalizar la configuración</span>\r\n                                            </div>\r\n                                        </a>\r\n                                    </div>\r\n                                </div>\r\n                            </div>\r\n                        </div>\r\n                    </section>\r\n                    <section id=\"support-sub-nav\" class=\"pls-nav-dropdown solution-part\" style=\"visibility: hidden; height: 0px; display: none;\">\r\n                        <div class=\"d-flex nav-drown-con\" style=\"height: 0px; transition: transform 0.2s ease-in-out 0s,opacity 0.2s ease-in-out 0s; opacity: 0; transform: translate(0px, -100%);\">\r\n                            <div class=\"prdocu-sub-left\">\r\n                                <div class=\"mr-auto\">\r\n                                    <picture>\r\n                                        <source class=\"p-icon\" srcset=\"site/image/icon/p_4.png.webp\" type=\"image/webp\" alt=\"\" aria-hidden=\"true\" />\r\n                                        <img class=\"p-icon\" src=\"site/image/icon/p_4.png\" alt=\"Icono de soporte del Centro de ayuda\" aria-hidden=\"true\" loading=\"lazy\" />\r\n                                    </picture>\r\n                                    <div class=\"mt-4\">\r\n                                        <h3 class=\"h6\">Centro de ayuda</h3>\r\n                                        <p class=\"color-gray fn14 mt-3 sub-left-desc\">Servicio al cliente profesional, documentación detallada y respuesta rápida</p>\r\n                                        <p class=\"color-gray fn14 mt-3 sub-left-desc\">&nbsp;</p>\r\n                                        <p class=\"color-gray fn14 mt-3 sub-left-desc\">No entres en pánico cuando encuentres problemas</p>\r\n                                        <p class=\"color-gray fn14 mt-3 sub-left-desc\">Problema → encontrar → resuelto</p>\r\n                                        <p class=\"color-gray fn14 mt-3 sub-left-desc\">Haz que tu experiencia sea más fluida</p>\r\n                                        <a class=\"link block disa fn14 contact-business shake\" href=\"FAQ.aspx\">Obtener ayuda<span class=\"iconfont4 icon-xiangzuo gt\"></span></a>\r\n                                    </div>\r\n                                </div>\r\n                            </div>\r\n                            <div class=\"bg-white prdocu-sub-right\">\r\n                                <div>\r\n                                    <h2 class=\"text-base-color h6\" style=\"margin: inherit;\">Centro de ayuda</h2>\r\n                                    <div class=\"mt-4 d-flex flex-wrap\">\r\n                                        <a href=\"FAQ.aspx\" class=\"pro-item color-default\">\r\n                                            <div class=\"letter-wrap\">\r\n                                                <h3 class=\"h6\">Preguntas frecuentes</h3>\r\n                                                <span class=\"color-gray fn14\">Responda rápidamente a las preguntas comunes de los usuarios y proporcione guías de uso detalladas y soporte técnico</span>\r\n                                            </div>\r\n                                        </a>\r\n                                        <a href=\"AboutUs.aspx\" class=\"pro-item color-default\">\r\n                                            <div class=\"letter-wrap\">\r\n                                                <h3 class=\"h6\">Sobre nosotros</h3>\r\n                                                <span class=\"color-gray fn14\">Conozca el historial de desarrollo, las funciones principales y los conceptos de servicio del asistente de reconocimiento de texto OCR</span>\r\n                                            </div>\r\n                                        </a>\r\n                                        <a href=\"UserAgreement.aspx\" class=\"pro-item color-default\">\r\n                                            <div class=\"letter-wrap\">\r\n                                                <h3 class=\"h6\">Acuerdo de usuario</h3>\r\n                                                <span class=\"color-gray fn14\">Términos de servicio detallados y derechos y obligaciones del usuario</span>\r\n                                            </div>\r\n                                        </a>\r\n                                        <a href=\"PrivacyPolicy.aspx\" class=\"pro-item color-default\">\r\n                                            <div class=\"letter-wrap\">\r\n                                                <h3 class=\"h6\">Acuerdo de privacidad</h3>\r\n                                                <span class=\"color-gray fn14\">Política de protección de datos personales y medidas de seguridad de datos</span>\r\n                                            </div>\r\n                                        </a>\r\n                                        <a href=\"Status.aspx\" class=\"pro-item color-default\">\r\n                                            <div class=\"letter-wrap\">\r\n                                                <h3 class=\"h6\">Estado del sistema</h3>\r\n                                                <span class=\"color-gray fn14\">Supervise el estado de funcionamiento de los nodos de identificación global en tiempo real y vea los datos de rendimiento del sistema</span>\r\n                                            </div>\r\n                                        </a>\r\n                                        <a href=\"javascript:void(0);\" class=\"pro-item color-default\" onclick=\"if(window.openCustomerService) window.openCustomerService(); else alert('Haga clic en el icono de la ventana flotante a la derecha para ponerse en contacto con el servicio de atención al cliente');\">\r\n                                            <div class=\"letter-wrap\">\r\n                                                <h3 class=\"h6\">Póngase en contacto con el servicio de atención al cliente</h3>\r\n                                                <span class=\"color-gray fn14\">Soporte de servicio al cliente en línea para responder rápidamente a sus preguntas y necesidades</span>\r\n                                            </div>\r\n                                        </a>\r\n                                    </div>\r\n                                </div>\r\n                            </div>\r\n                        </div>\r\n                    </section>\r\n                </div>\r\n            </div>\r\n            <div class=\"header_pc_right\" style=\"display: flex; align-items: center; justify-content: flex-end;\">\r\n                <div id=\"translate\"></div>\r\n                <div id=\"user-login-container\" data-url=\"User.ashx?op=userinfo\" style=\"white-space: nowrap; padding-right: 20px;\"></div>\r\n            </div>\r\n        </header>\r\n        <header class=\"v4_header_mob\">\r\n            <div class=\"container\">\r\n                <div class=\"row no-gutters justify-content-between align-items-center\">\r\n                    <a href=\"Default.aspx\" title=\"Inicio | Reconocimiento de texto inteligente con IA\" class=\"mob-logo-wrap\">\r\n                        <picture>\r\n                            <source srcset=\"site/image/logo-s.png.webp\" type=\"image/webp\" />\r\n                            <img src=\"site/image/logo-s.png\" class=\"pc-logo\" style=\"height: 32px; width: 32px\" alt=\"Logotipo móvil del asistente de reconocimiento de texto OCR\" />\r\n                        </picture>\r\n                        <span style=\"font-size: 18px; font-weight: bold; color: #333;\">Asistente de reconocimiento de texto OCR</span>\r\n                    </a>\r\n                    <div class=\"right-menu\" role=\"button\" tabindex=\"0\" aria-label=\"Abrir el menú de navegación\" data-toggle=\"collapse\" data-target=\"#mob-header-collapse\" aria-expanded=\"false\" aria-controls=\"mob-header-collapse\">\r\n                        <nav-products id=\"nav_products\">\r\n                            <nav-button class=\"submail-mdi-nav-btn\" style=\"width: 60px\">\r\n                                <label id=\"mdi-submail-nav-btn\">\r\n                                    <span class=\"mdi-nav-products-1\">\r\n                                        <span class=\"nav-products-icon\"></span>\r\n                                    </span>\r\n                                    <span class=\"mdi-nav-products-2\">\r\n                                        <span class=\"nav-products-icon\"></span>\r\n                                    </span>\r\n                                    <span class=\"mdi-nav-products-3\">\r\n                                        <span class=\"nav-products-icon\"></span>\r\n                                    </span>\r\n                                </label>\r\n                            </nav-button>\r\n                        </nav-products>\r\n                    </div>\r\n                </div>\r\n            </div>\r\n            <div class=\"mob-nav-content\">\r\n                <div class=\"sidebar-fix\">\r\n                    <div class=\"container\">\r\n                        <div class=\"row\">\r\n                            <div class=\"col-4 bg-white sidebar-fix-left\" style=\"border-right: 1px solid #e1e6ed\">\r\n                                <div class=\"mob-nav-item active\" data=\"0\">\r\n                                    <div class=\"nav-header\"><a href=\"Default.aspx\" class=\"nav-header-letter px-3\"><span>Hogar</span></a></div>\r\n                                </div>\r\n                                <div class=\"mob-nav-item\" data=\"1\">\r\n                                    <div class=\"nav-header\"><a href=\"Detail.aspx\" class=\"nav-header-letter px-3\"><span>función</span></a></div>\r\n                                </div>\r\n                                <div class=\"mob-nav-item\" data=\"2\">\r\n                                    <div class=\"nav-header\"><a href=\"Ocr.aspx\" class=\"nav-header-letter px-3\"><span>experiencia</span></a></div>\r\n                                </div>\r\n                                <div class=\"mob-nav-item\" data=\"3\">\r\n                                    <div class=\"nav-header\"><a href=\"Version.aspx\" class=\"nav-header-letter px-3\"><span>miembro</span></a></div>\r\n                                </div>\r\n                                <div class=\"mob-nav-item\" data=\"4\">\r\n                                    <div class=\"nav-header\"><a href=\"Download.aspx\" class=\"nav-header-letter px-3\"><span>Descargar</span></a></div>\r\n                                </div>\r\n                                <div class=\"mob-nav-item\" data=\"5\">\r\n                                    <div class=\"nav-header\"><a href=\"Articles.aspx\" class=\"nav-header-letter px-3\"><span>Compartir</span></a></div>\r\n                                </div>\r\n                                <div class=\"mob-nav-item\" data=\"6\">\r\n                                    <div class=\"nav-header\"><a href=\"FAQ.aspx\" class=\"nav-header-letter px-3\"><span>Ayuda</span></a></div>\r\n                                </div>\r\n                            </div>\r\n                            <div class=\"col-8 bg-white sidebar-fix-rigth\">\r\n                                <div class=\"sub-nav\">\r\n                                    <div class=\"sub-nav-item py-2 pb-3 px-4\">\r\n                                        <h2 class=\"h5\" style=\"margin: inherit;\">Herramientas de productividad eficientes</h2>\r\n                                        <p class=\"color-gray fn14 mt-3 sub-left-desc\">Reconocimiento inteligente, procesamiento de alta velocidad y salida precisa</p>\r\n                                        <p class=\"color-gray fn14 mt-3 sub-left-desc\">&nbsp;</p>\r\n                                        <p class=\"color-gray fn14 mt-3 sub-left-desc\">Reconoce una página completa de documentos en 3 segundos</p>\r\n                                        <p class=\"color-gray fn14 mt-3 sub-left-desc\">Precisión de reconocimiento del 98%+</p>\r\n                                        <p class=\"color-gray fn14 mt-3 sub-left-desc\">Procesamiento multilingüe en tiempo real sin demora</p>\r\n                                        <a class=\"link block disa fn14 contact-business shake\" href=\"Download.aspx\">Descarga la experiencia ahora<span class=\"iconfont4 icon-xiangzuo gt\"></span></a>\r\n                                    </div>\r\n                                </div>\r\n                                <div class=\"sub-nav\">\r\n                                    <div class=\"sub-nav-item py-3 px-4\">\r\n                                        <h2 class=\"h5\" style=\"margin: inherit;\">Características del producto:</h2>\r\n                                        <p class=\"color-gray fn14 mt-3 sub-left-desc\">Identificación inteligente con IA, solución integral</p>\r\n                                    </div>\r\n                                    <div class=\"sub-nav-item py-3 px-4\"><a href=\"Detail.aspx\">Introducción a la función</a></div>\r\n                                    <div class=\"sub-nav-item py-3 px-4\"><a href=\"Download.aspx\">Descarga de software</a></div>\r\n                                    <div class=\"sub-nav-item py-3 px-4\"><a href=\"Version.aspx\">Comparación de versiones</a></div>\r\n                                    <div class=\"sub-nav-item py-3 px-4\"><a href=\"Ocr.aspx\">Experiencia en línea</a></div>\r\n                                    <div class=\"sub-nav-item py-3 px-4\"><a href=\"Status.aspx\">Estado del sistema</a></div>\r\n                                </div>\r\n                                <div class=\"sub-nav\">\r\n                                    <div class=\"sub-nav-item py-3 px-4\">\r\n                                        <h2 class=\"h5\" style=\"margin: inherit;\">Experiencia en línea</h2>\r\n                                        <p class=\"color-gray fn14 mt-3 sub-left-desc\">Experiencia gratuita de función OCR en línea</p>\r\n                                    </div>\r\n                                    <div class=\"sub-nav-item py-3 px-4\"><a href=\"Ocr.aspx\">Funcionalidad completa</a></div>\r\n                                    <div class=\"sub-nav-item py-3 px-4\"><a href=\"Ocr.aspx?type=text_recognize\">Reconocimiento de palabras</a></div>\r\n                                    <div class=\"sub-nav-item py-3 px-4\"><a href=\"Ocr.aspx?type=table\">Identificación de tablas</a></div>\r\n                                    <div class=\"sub-nav-item py-3 px-4\"><a href=\"Ocr.aspx?type=pdf2word\">PDF a Word</a></div>\r\n                                </div>\r\n                                <div class=\"sub-nav\">\r\n                                    <div class=\"sub-nav-item py-3 px-4\">\r\n                                        <h2 class=\"h5\" style=\"margin: inherit;\">Actualizaciones de membresía</h2>\r\n                                        <p class=\"color-gray fn14 mt-3 sub-left-desc\">Desbloquea todas las funciones y disfruta de servicios exclusivos</p>\r\n                                    </div>\r\n                                    <div class=\"sub-nav-item py-3 px-4\"><a href=\"Version.aspx\">Beneficios de la membresía</a></div>\r\n                                    <div class=\"sub-nav-item py-3 px-4\"><a href=\"Upgrade.aspx\">Activar inmediatamente</a></div>\r\n                                </div>\r\n                                <div class=\"sub-nav\">\r\n                                    <div class=\"sub-nav-item py-3 px-4\">\r\n                                        <h2 class=\"h5\" style=\"margin: inherit;\">Descarga de software</h2>\r\n                                        <p class=\"color-gray fn14 mt-3 sub-left-desc\">Descargue el software OCR profesional de forma gratuita</p>\r\n                                    </div>\r\n                                    <div class=\"sub-nav-item py-3 px-4\"><a href=\"Download.aspx\">Descargar ahora</a></div>\r\n                                    <div class=\"sub-nav-item py-3 px-4\"><a href=\"Version.aspx\">Comparación de versiones</a></div>\r\n                                </div>\r\n                                <div class=\"sub-nav\">\r\n                                    <div class=\"sub-nav-item py-3 px-4\">\r\n                                        <h2 class=\"h5\" style=\"margin: inherit;\">Intercambio de tecnología</h2>\r\n                                        <p class=\"color-gray fn14 mt-3 sub-left-desc\">Artículos técnicos de OCR e intercambio de conocimientos</p>\r\n                                    </div>\r\n                                    <div class=\"sub-nav-item py-3 px-4\"><a href=\"Articles.aspx\">Todos los artículos</a></div>\r\n                                    \r\n                                    <div class=\"sub-nav-item py-3 px-4\"><a href=\"Articles.aspx?category=2\">Guía avanzada</a></div>\r\n                                    \r\n                                    <div class=\"sub-nav-item py-3 px-4\"><a href=\"Articles.aspx?category=3\">Exploración tecnológica</a></div>\r\n                                    \r\n                                    <div class=\"sub-nav-item py-3 px-4\"><a href=\"Articles.aspx?category=4\">Tendencias de la industria</a></div>\r\n                                    \r\n                                    <div class=\"sub-nav-item py-3 px-4\"><a href=\"Articles.aspx?category=5\">Casos de uso:</a></div>\r\n                                    \r\n                                    <div class=\"sub-nav-item py-3 px-4\"><a href=\"Articles.aspx?category=6\">Revisión de herramientas</a></div>\r\n                                    \r\n                                </div>\r\n                                <div class=\"sub-nav\">\r\n                                    <div class=\"sub-nav-item py-3 px-4\">\r\n                                        <h2 class=\"h5\" style=\"margin: inherit;\">Centro de ayuda</h2>\r\n                                        <p class=\"color-gray fn14 mt-3 sub-left-desc\">Servicio al cliente profesional, servicio íntimo</p>\r\n                                    </div>\r\n                                    <div class=\"sub-nav-item py-3 px-4\"><a href=\"FAQ.aspx\">Usar ayuda</a></div>\r\n                                    <div class=\"sub-nav-item py-3 px-4\"><a href=\"AboutUs.aspx\">Sobre nosotros</a></div>\r\n                                    <div class=\"sub-nav-item py-3 px-4\"><a href=\"mailto:<EMAIL>\">Póngase en contacto con el servicio de atención al cliente</a></div>\r\n                                    <div class=\"sub-nav-item py-3 px-4\"><a href=\"UserAgreement.aspx\">Términos de servicio</a></div>\r\n                                </div>\r\n                            </div>\r\n                        </div>\r\n                    </div>\r\n                </div>\r\n            </div>\r\n        </header>\r\n        <form method=\"post\" action=\"./Article.aspx?id=82&amp;slug=attention-mechanism-principles&amp;lang=zh-Hans&amp;_translation_source=true\" id=\"aspnetForm\">\r\n<div class=\"aspNetHidden\">\r\n<input type=\"hidden\" name=\"__VIEWSTATE\" id=\"__VIEWSTATE\" value=\"BqrhS+P8MW5VC3DOBfGwj6vOm3+QcH2E0p5DXnNjlyGuctPQR14YJZeSsWbjw86X57yDpdIFIGGGE04n8DnmgHdYirvyFNNsxJ81r0FsIZo=\" />\r\n</div>\r\n\r\n<div class=\"aspNetHidden\">\r\n\r\n\t<input type=\"hidden\" name=\"__VIEWSTATEGENERATOR\" id=\"__VIEWSTATEGENERATOR\" value=\"2173C2F0\" />\r\n</div>\r\n            <div>\r\n                \n\n<div class=\"reading-progress\" id=\"readingProgress\"></div>\n<div class=\"article-detail-container\">\n<div class=\"container\">\n<div class=\"row\">\n<div class=\"col-lg-8 col-xl-9 col-md-12\">\n                    \n                    <article class=\"article-detail\" id=\"articleDetail\" data-article-id=\"82\">\n                        <header class=\"article-header\">\n                            <h1 class=\"article-title\">【Serie OCR de aprendizaje profundo · 5】 Principio e implementación del mecanismo de atención</h1>\n                            <div class=\"article-meta\">\n                                <div class=\"meta-item\">\n                                    <span class=\"meta-icon\">📅</span>\n                                    <span>Hora de publicación: 2025-08-19</span>\n                                </div>\n                                <div class=\"meta-item\">\n                                    <span class=\"meta-icon\">👁️</span>\n                                    <span>Lectura:<span class=\"view-count\">1250</span></span>\n                                </div>\n                                <div class=\"meta-item\">\n                                    <span class=\"meta-icon\">⏱️</span>\n                                    <span>Aprox. 58 minutos (11464 palabras)</span>\n                                </div>\n                                \n                                <div class=\"meta-item\">\n                                    <span class=\"meta-icon\">📁</span>\n                                    <span>Categoría: Guías Avanzadas</span>\n                                </div>\n                                \n                            </div>\n                            <div class=\"article-summary\"><p>Profundiza en los principios matemáticos de los mecanismos de atención, la atención multicabezal, los mecanismos de autoatención y las aplicaciones específicas en OCR. Análisis detallado de cálculos de peso de atención, codificación de posición y estrategias de optimización del rendimiento.</p></div>\n                        </header>\n                        <div class=\"article-content\" id=\"articleContent\">## Introducción\r\n\r\nEl Mecanismo de Atención es una innovación importante en el campo del aprendizaje profundo, que simula la atención selectiva en los procesos cognitivos humanos. En las tareas de OCR, el mecanismo de atención puede ayudar al modelo a enfocarse dinámicamente en áreas importantes de la imagen, mejorando significativamente la precisión y la eficiencia del reconocimiento de texto. Este artículo profundizará en los fundamentos teóricos, los principios matemáticos, los métodos de implementación y las aplicaciones específicas de los mecanismos de atención en OCR, brindando a los lectores una comprensión técnica integral y orientación práctica.\r\n\r\n## Implicaciones biológicas de los mecanismos de atención\r\n\r\n### Sistema de atención visual humana\r\n\r\nEl sistema visual humano tiene una gran capacidad para prestar atención selectivamente, lo que nos permite extraer información útil de manera eficiente en entornos visuales complejos. Cuando leemos un fragmento de texto, los ojos se enfocan automáticamente en el personaje que se está reconociendo actualmente, con una supresión moderada de la información circundante.\r\n\r\n**Características de la atención humana**:\r\n- Selectividad: Capacidad para seleccionar secciones importantes de una gran cantidad de información\r\n- Dinámico: los enfoques de atención se ajustan dinámicamente en función de las demandas de la tarea\r\n- Jerarquía: La atención se puede distribuir en diferentes niveles de abstracción\r\n- Paralelismo: Se pueden enfocar varias regiones relacionadas simultáneamente\r\n- Sensibilidad al contexto: la asignación de atención está influenciada por la información contextual\r\n\r\n**Mecanismos neuronales de la atención visual**:\r\nEn la investigación en neurociencia, la atención visual implica el trabajo coordinado de múltiples regiones del cerebro:\r\n- Corteza parietal: responsable del control de la atención espacial\r\n- Corteza prefrontal: responsable del control de la atención orientado a objetivos\r\n- Corteza visual: Responsable de la detección y representación de características\r\n- Tálamo: sirve como estación de retransmisión para la información de atención\r\n\r\n### Requisitos del modelo computacional\r\n\r\nLas redes neuronales tradicionales suelen comprimir toda la información de entrada en un vector de longitud fija al procesar datos de secuencia. Este enfoque tiene cuellos de botella de información obvios, especialmente cuando se trata de secuencias largas, donde la información temprana se sobrescribe fácilmente con información posterior.\r\n\r\n**Limitaciones de los métodos tradicionales**:\r\n- Cuellos de botella de información: los vectores codificados de longitud fija luchan por contener toda la información importante\r\n- Dependencias de larga distancia: dificultad para modelar relaciones entre elementos que están muy separados en una secuencia de entrada\r\n- Eficiencia computacional: Es necesario procesar toda la secuencia para obtener el resultado final\r\n- Explicabilidad: Dificultad para comprender el proceso de toma de decisiones del modelo\r\n- Flexibilidad: Incapaz de ajustar dinámicamente las estrategias de procesamiento de información en función de las demandas de la tarea\r\n\r\n**Soluciones a los Mecanismos de Atención**:\r\nEl mecanismo de atención permite que el modelo se centre selectivamente en diferentes partes de la entrada mientras procesa cada salida mediante la introducción de un mecanismo dinámico de asignación de peso:\r\n- Selección dinámica: seleccione dinámicamente la información relevante en función de los requisitos actuales de la tarea\r\n- Acceso global: acceso directo a cualquier ubicación de la secuencia de entrada\r\n- Computación paralela: Admite el procesamiento paralelo para mejorar la eficiencia computacional\r\n- Explicabilidad: Los pesos de atención proporcionan una explicación visual de las decisiones del modelo\r\n\r\n## Principios matemáticos de los mecanismos de atención\r\n\r\n### Modelo de atención básica\r\n\r\nLa idea central del mecanismo de atención es asignar un peso a cada elemento de la secuencia de entrada, lo que refleja la importancia de ese elemento para la tarea en cuestión.\r\n\r\n**Representación matemática**:\r\nDada la secuencia de entrada X = {x₁, x₂, ..., xn} y el vector de consulta q, el mecanismo de atención calcula el peso de atención para cada elemento de entrada:\r\n\r\nα_i = f(q, x_i) # Función de puntuación de atención\r\nα̃_i = softmax(α_i) = exp(α_i) / Σj exp(αj) # Peso normalizado\r\n\r\nEl vector de contexto final se obtiene mediante la suma ponderada:\r\nc = Σi α̃_i · x_i\r\n\r\n**Componentes de los Mecanismos de Atención**:\r\n1. Consulta: Indica la información a la que se debe prestar atención en la actualidad\r\n2. Clave: La información de referencia utilizada para calcular el peso de la atención\r\n3. Valor: Información que realmente participa en la suma ponderada\r\n4. **Función de atención**: Una función que calcula la similitud entre consultas y claves\r\n\r\n### Explicación detallada de la función de puntuación de atención\r\n\r\nLa función de puntuación de atención determina cómo se calcula la correlación entre la consulta y la entrada. Las diferentes funciones de puntuación son adecuadas para diferentes escenarios de aplicación.\r\n\r\n**1. Atención punto-producto**:\r\nα_i = q^T · x_i\r\n\r\nEste es el mecanismo de atención más simple y es computacionalmente eficiente, pero requiere que las consultas y las entradas tengan las mismas dimensiones.\r\n\r\n**Mérito**:\r\n- Cálculos sencillos y alta eficiencia\r\n- Pequeño número de parámetros y no se requieren parámetros adicionales que se puedan aprender\r\n- Distinguir eficazmente entre vectores similares y diferentes en el espacio de alta dimensión\r\n\r\n**Defecto**:\r\n- Requerir que las consultas y las claves tengan las mismas dimensiones\r\n- La inestabilidad numérica puede ocurrir en un espacio de alta dimensión\r\n- Falta de capacidad de aprendizaje para adaptarse a relaciones de similitud complejas\r\n\r\n**2. Atención del producto de puntos a escala**:\r\nα_i = (q^T · x_i) / √d\r\n\r\ndonde d es la dimensión del vector. El factor de escala evita el problema de desaparición del gradiente causado por el valor del producto de punto grande en un espacio de alta dimensión.\r\n\r\n**La necesidad de escalar**:\r\nCuando la dimensión d es grande, la varianza del producto de puntos aumenta, lo que hace que la función softmax entre en la región de saturación y el gradiente se vuelva pequeño. Al dividir por √d, la varianza del producto punto se puede mantener estable.\r\n\r\n**Derivación matemática**:\r\nSuponiendo que los elementos q y k son variables aleatorias independientes, con una media de 0 y una varianza de 1, entonces:\r\n- q^T · La varianza de k es d\r\n- La varianza de (q^T · k) / √d es 1\r\n\r\n**3. Atención aditiva**:\r\nα_i = v^T · tanh (W_q · q + W_x · x_i)\r\n\r\nLas consultas y las entradas se asignan al mismo espacio a través de una matriz de parámetros aprendible W_q y W_x, y luego se calcula la similitud.\r\n\r\n**Análisis de ventajas**:\r\n- Flexibilidad: Puede manejar consultas y claves en diferentes dimensiones\r\n- Capacidades de aprendizaje: Adáptese a relaciones de similitud complejas con parámetros aprendibles\r\n- Capacidades de expresión: las transformaciones no lineales proporcionan capacidades de expresión mejoradas\r\n\r\n**Análisis de parámetros**:\r\n- W_q ∈ R^{d_h×d_q}: Consulta la matriz de proyección\r\n- W_x ∈ R^{d_h×d_x}: Matriz de proyección clave\r\n- v ∈ R^{d_h}: Vector de peso de atención\r\n- d_h: Dimensiones de capa ocultas\r\n\r\n**4. Atención MLP**:\r\nα_i = MLP([q; x_i])\r\n\r\nUtilice perceptrones multicapa para aprender directamente las funciones de correlación entre consultas y entradas.\r\n\r\n**Estructura de la red**:\r\nLas MLP suelen contener 2-3 capas completamente conectadas:\r\n- Capa de entrada: consultas de empalme y vectores clave\r\n- Capa oculta: Activa funciones usando ReLU o tanh\r\n- Capa de salida: Genera puntuaciones de atención escalar\r\n\r\n**Análisis de pros y contras**:\r\nMérito:\r\n- Habilidades expresivas más fuertes\r\n- Se pueden aprender relaciones no lineales complejas\r\n- Sin restricciones en las dimensiones de entrada\r\n\r\nDefecto:\r\n- Gran cantidad de parámetros y fácil sobreajuste\r\n- Alta complejidad computacional\r\n- Largo tiempo de entrenamiento\r\n\r\n### Mecanismo de atención de múltiples cabezales\r\n\r\nLa atención multicabezal es un componente central de la arquitectura Transformer, que permite a los modelos prestar atención a diferentes tipos de información en paralelo en diferentes subespacios de representación.\r\n\r\n**Definición matemática**:\r\nMultiCabeza(Q, K, V) = Concatenar(cabeza₁, cabeza₂, ..., cabezah) · W^O\r\n\r\ndonde cada cabeza de atención se define como:\r\nheadi = Atención(Q· W_i^Q, K· W_i^K, V·W_i^V)\r\n\r\n**Matriz de parámetros**:\r\n- W_i^Q ∈ R^{d_model×d_k}: La matriz de proyección de consulta del encabezamiento i-ésimo\r\n- W_i^K ∈ R^{d_model×d_k}: la matriz de proyección de la clave del encabezamiento i-ésimo\r\n- W_i^V ∈ R^{d_model×d_v}: Matriz de proyección de valor para la i-ésima cabeza\r\n- W^O ∈ R^{h·d_v×d_model}: Matriz de proyección de salida\r\n\r\n**Ventajas de Bull Attention**:\r\n1. **Diversidad**: Diferentes cabezas pueden enfocarse en diferentes tipos de rasgos\r\n2. **Paralelismo**: se pueden calcular varios cabezales en paralelo, lo que mejora la eficiencia\r\n3. **Capacidad de expresión**: Se mejoró la capacidad de aprendizaje de representación del modelo\r\n4. **Estabilidad**: el efecto de integración de múltiples cabezales es más estable\r\n5. **Especialización**: Cada jefe puede especializarse en tipos específicos de relaciones\r\n\r\n**Consideraciones para la selección de cabezales**:\r\n- Muy pocas cabezas: puede no capturar suficiente diversidad de información\r\n- Recuento excesivo de personal: aumenta la complejidad computacional, lo que puede conducir a un sobreajuste\r\n- Opciones comunes: 8 o 16 cabezales, ajustados según el tamaño del modelo y la complejidad de la tarea\r\n\r\n**Estrategia de asignación de dimensiones**:\r\nPor lo general, establezca d_k = d_v = d_model / h para asegurarse de que la cantidad total de parámetros sea razonable:\r\n- Mantener el volumen computacional total relativamente estable\r\n- Cada cabeza tiene suficiente capacidad de representación\r\n- Evitar la pérdida de información causada por dimensiones demasiado pequeñas\r\n\r\n## Mecanismo de autoatención\r\n\r\n### El concepto de autoatención\r\n\r\nLa autoatención es una forma especial de mecanismo de atención en la que las consultas, las claves y los valores provienen de la misma secuencia de entrada. Este mecanismo permite que cada elemento de la secuencia se centre en todos los demás elementos de la secuencia.\r\n\r\n**Representación matemática**:\r\nPara la secuencia de entrada X = {x₁, x₂, ..., xn}:\r\n- Matriz de consulta: Q = X · W^Q\r\n- Matriz de claves: K = X · W^K  \r\n- Matriz de valores: V = X · W^V\r\n\r\nSalida de atención:\r\nAtención(Q, K, V) = softmax(QK^T / √d_k) · V\r\n\r\n**Proceso de cálculo de la autoatención**:\r\n1. **Transformación lineal**: La secuencia de entrada se obtiene mediante tres transformaciones lineales diferentes para obtener Q, K y V\r\n2. **Cálculo de similitud**: Calcule la matriz de similitud entre todos los pares de posiciones\r\n3. **Normalización de peso**: use la función softmax para normalizar los pesos de atención\r\n4. **Suma ponderada**: Suma ponderada de vectores de valores basada en ponderaciones de atención\r\n\r\n### Ventajas de la autoatención\r\n\r\n**1. Modelado de dependencias a larga distancia**:\r\nLa autoatención puede modelar directamente la relación entre dos posiciones cualesquiera en una secuencia, independientemente de la distancia. Esto es especialmente importante para las tareas de OCR, donde el reconocimiento de caracteres a menudo requiere la consideración de información contextual a distancia.\r\n\r\n**Análisis de complejidad temporal**:\r\n- RNN: Cálculo de secuencia O(n), difícil de paralelizar\r\n- CNN: O(log n) para cubrir toda la secuencia\r\n- Autoatención: La longitud de la ruta de O(1) se conecta directamente a cualquier ubicación\r\n\r\n**2. Cálculo paralelo**:\r\nA diferencia de las RNN, el cálculo de la autoatención se puede paralelizar por completo, lo que mejora en gran medida la eficiencia del entrenamiento.\r\n\r\n**Ventajas de la paralelización**:\r\n- Los pesos de atención para todas las posiciones se pueden calcular simultáneamente\r\n- Las operaciones matriciales pueden aprovechar al máximo la potencia de cálculo paralelo de las GPU\r\n- El tiempo de entrenamiento se reduce significativamente en comparación con RNN\r\n\r\n**3. Interpretabilidad**:\r\nLa matriz de peso de atención proporciona una explicación visual de las decisiones del modelo, lo que facilita la comprensión de cómo funciona el modelo.\r\n\r\n**Análisis visual**:\r\n- Mapa de calor de atención: muestra cuánta atención presta cada ubicación a las demás\r\n- Patrones de atención: Analice patrones de atención de diferentes cabezas\r\n- Análisis jerárquico: Observe los cambios en los patrones de atención en diferentes niveles\r\n\r\n**4. Flexibilidad**:\r\nSe puede extender fácilmente a secuencias de diferentes longitudes sin modificar la arquitectura del modelo.\r\n\r\n### Codificación de posición\r\n\r\nDado que el mecanismo de autoatención en sí mismo no contiene información de posición, es necesario proporcionar al modelo información de posición de los elementos de la secuencia a través de la codificación de posición.\r\n\r\n**La necesidad de la codificación de posición**:\r\nEl mecanismo de autoatención es inmutable, es decir, cambiar el orden de la secuencia de entrada no afecta la salida. Pero en las tareas de OCR, la información de ubicación de los personajes es crucial.\r\n\r\n**Codificación de posición sinusoidal**:\r\nPE(pos, 2i) = sin(pos / 10000^(2i/d_model))\r\nPE(pos, 2i+1) = cos(pos / 10000^(2i/d_model))\r\n\r\nAllí:\r\n- pos: Índice de ubicación\r\n- i: Índice de dimensiones\r\n- d_model: Dimensión del modelo\r\n\r\n**Ventajas de la codificación de posición sinusoidal**:\r\n- Determinista: No requiere aprendizaje, lo que reduce la cantidad de parámetros\r\n- Extrapolación: Puede manejar secuencias más largas que cuando se entrena\r\n- Periodicidad: Tiene una buena naturaleza periódica, lo cual es conveniente para que el modelo aprenda relaciones de posición relativa\r\n\r\n**Codificación de posición aprendible**:\r\nLa codificación de posición se utiliza como un parámetro que se puede aprender y la representación de posición óptima se aprende automáticamente a través del proceso de entrenamiento.\r\n\r\n**Método de implementación**:\r\n- Asigne un vector aprendible a cada posición\r\n- Suma con las incrustaciones de entrada para obtener la entrada final\r\n- Actualizar el código de posición con retropropagación\r\n\r\n**Pros y contras de la codificación de posición aprendible**:\r\nMérito:\r\n- Adaptable para aprender representaciones posicionales específicas de la tarea\r\n- El rendimiento es generalmente ligeramente mejor que la codificación de posición fija\r\n\r\nDefecto:\r\n- Aumentar la cantidad de parámetros\r\n- Incapacidad para procesar secuencias más allá de la duración del entrenamiento\r\n- Se necesitan más datos de entrenamiento\r\n\r\n**Codificación de posición relativa**:\r\nNo codifica directamente la posición absoluta, sino las relaciones de posición relativa.\r\n\r\n**Principio de implementación**:\r\n- Agregar sesgo de posición relativa a los cálculos de atención\r\n- Concéntrese solo en la distancia relativa entre los elementos, no en su posición absoluta\r\n- Mejor capacidad de generalización\r\n\r\n## Atención Aplicaciones en OCR\r\n\r\n### Atención secuencia a secuencia\r\n\r\nLa aplicación más común en las tareas de OCR es el uso de mecanismos de atención en modelos de secuencia a secuencia. El codificador codifica la imagen de entrada en una secuencia de características y el decodificador se enfoca en la parte relevante del codificador a través de un mecanismo de atención a medida que genera cada carácter.\r\n\r\n**Arquitectura codificador-decodificador**:\r\n1. **Codificador**: CNN extrae características de la imagen, RNN codifica como representación de secuencia\r\n2. **Módulo de atención**: Calcule el peso de atención del estado del decodificador y la salida del codificador\r\n3. **Decodificador**: Genera secuencias de caracteres basadas en vectores de contexto ponderados por la atención\r\n\r\n**Proceso de cálculo de la atención**:\r\nEn el momento de decodificación t, el estado del decodificador es s_t y la salida del codificador es H = {h₁, h₂, ..., hn}:\r\n\r\ne_ti = a(s_t, h_i) # Puntuación de atención\r\nα_ti = softmax(e_ti) # Atención peso\r\nc_t = Σi α_ti · h_i # Vector de contexto\r\n\r\n**Selección de funciones de atención**:\r\nLas funciones de atención comúnmente utilizadas incluyen:\r\n- Atención acumulada: e_ti = s_t^T · h_i\r\n- Atención aditiva: e_ti = v^T · tanh(W_s · s_t + W_h · h_i)\r\n- Atención bilineal: e_ti = s_t^T · W · h_i\r\n\r\n### Módulo de atención visual\r\n\r\nLa atención visual aplica mecanismos de atención directamente en el mapa de características de la imagen, lo que permite que el modelo se centre en áreas importantes de la imagen.\r\n\r\n**Atención espacial**:\r\nCalcule los pesos de atención para cada posición espacial del mapa de características:\r\nA(i,j) = σ(W_a · [F(i,j); g])\r\n\r\nAllí:\r\n- F(i,j): vector propio de la posición (i,j).\r\n- g: Información de contexto global\r\n- W_a: Matriz de peso aprendible\r\n- σ: función de activación sigmoide\r\n\r\n**Pasos para lograr la atención espacial**:\r\n1. **Extracción de características**: use CNN para extraer mapas de características de imágenes\r\n2. **Agregación de información global**: Obtenga características globales a través de la agrupación promedio global o la agrupación máxima global\r\n3. **Cálculo de atención**: Calcule los pesos de atención en función de las características locales y globales\r\n4. **Mejora de funciones**: Mejore la función original con pesos de atención\r\n\r\n**Atención del canal**:\r\nLos pesos de atención se calculan para cada canal del gráfico de características:\r\nA_c = σ(W_c · GAP(F_c))\r\n\r\nAllí:\r\n- GAP: Agrupación promedio global\r\n- F_c: Mapa de características del canal c\r\n- W_c: La matriz de peso de la atención del canal\r\n\r\n**Principios de la atención del canal**:\r\n- Diferentes canales capturan diferentes tipos de características\r\n- Selección de canales de características importantes a través de mecanismos de atención\r\n- Suprimir características irrelevantes y mejorar las útiles\r\n\r\n**Atención mixta**:\r\nCombine la atención espacial y la atención del canal:\r\nF_output = F ⊙ A_spatial ⊙ A_channel\r\n\r\ndonde ⊙ representa la multiplicación a nivel de elemento.\r\n\r\n**Ventajas de la atención mixta**:\r\n- Considerar la importancia de las dimensiones espaciales y de paso\r\n- Capacidades de selección de características más refinadas\r\n- Mejor rendimiento\r\n\r\n### Atención multiescala\r\n\r\nEl texto de la tarea de OCR tiene diferentes escalas, y el mecanismo de atención multiescala puede prestar atención a la información relevante en diferentes resoluciones.\r\n\r\n**Atención característica de la pirámide**:\r\nEl mecanismo de atención se aplica a los mapas de características de diferentes escalas, y luego se fusionan los resultados de atención de múltiples escalas.\r\n\r\n**Arquitectura de implementación**:\r\n1. **Extracción de entidades multiescala**: utilice redes de pirámides de entidades para extraer entidades a diferentes escalas\r\n2. **Atención específica de la escala**: Calcule los pesos de atención de forma independiente en cada escala\r\n3. **Fusión entre escalas**: Integra los resultados de atención de diferentes escalas\r\n4. **Predicción final**: Haz una predicción final basada en las características fusionadas\r\n\r\n**Selección de escala adaptativa**:\r\nDe acuerdo con las necesidades de la tarea de reconocimiento actual, se selecciona dinámicamente la escala de características más adecuada.\r\n\r\n**Estrategia de selección**:\r\n- Selección basada en contenido: selecciona automáticamente la escala adecuada en función del contenido de la imagen\r\n- Selección basada en tareas: seleccione la escala en función de las características de la tarea identificada\r\n- Asignación dinámica de pesos: Asigne pesos dinámicos a diferentes básculas\r\n\r\n## Variaciones de los mecanismos de atención\r\n\r\n### Atención escasa\r\n\r\nLa complejidad computacional del mecanismo estándar de autoatención es O(n²), que es computacionalmente costoso para secuencias largas. La atención dispersa reduce la complejidad computacional al limitar el rango de atención.\r\n\r\n**Atención local**:\r\nCada ubicación se centra solo en la ubicación dentro de la ventana fija que la rodea.\r\n\r\n**Representación matemática**:\r\nPara la posición i, solo se calcula el peso de atención dentro del rango de posición [i-w, i+w], donde w es el tamaño de la ventana.\r\n\r\n**Análisis de pros y contras**:\r\nMérito:\r\n- Complejidad computacional reducida a O(n·w)\r\n- Se mantiene la información del contexto local\r\n- Adecuado para manejar secuencias largas\r\n\r\nDefecto:\r\n- No se pueden capturar dependencias de larga distancia\r\n- El tamaño de la ventana debe ajustarse cuidadosamente\r\n- Pérdida potencial de información global importante\r\n\r\n** Atención de fragmentación **:\r\nDivida la secuencia en trozos, cada uno enfocándose solo en el resto dentro del mismo bloque.\r\n\r\n**Método de implementación**:\r\n1. Divida la secuencia de longitud n en bloques n / b, cada uno de los cuales es de tamaño b\r\n2. Calcula la atención completa dentro de cada bloque\r\n3. Sin cálculo de atención entre bloques\r\n\r\nComplejidad computacional: O(n·b), donde b << n\r\n\r\n**Atención aleatoria**:\r\nCada posición selecciona aleatoriamente una parte de la ubicación para el cálculo de la atención.\r\n\r\n**Estrategia de selección aleatoria**:\r\n- Aleatorio fijo: patrones de conexión aleatorios predeterminados\r\n- Dinámico aleatorio: seleccione dinámicamente las conexiones durante el entrenamiento\r\n- Aleatorio estructurado: combina conexiones locales y aleatorias\r\n\r\n### Atención lineal\r\n\r\nLa atención lineal reduce la complejidad de los cálculos de atención de O(n²) a O(n) a través de transformaciones matemáticas.\r\n\r\n**Atención Nucleada**:\r\nAproximación de operaciones softmax usando funciones del kernel:\r\nAtención(Q, K, V) ≈ φ(Q) · (φ(K)^T · V)\r\n\r\nφ de ellas son funciones de asignación de características.\r\n\r\n**Funciones comunes del kernel**:\r\n- Núcleo ReLU: φ(x) = ReLU(x)\r\n- Kernel de ELU: φ(x) = ELU(x) + 1\r\n- Kernels de características aleatorias: use características aleatorias de Fourier\r\n\r\n**Ventajas de la atención lineal**:\r\n- La complejidad computacional aumenta linealmente\r\n- Los requisitos de memoria se reducen significativamente\r\n- Adecuado para manejar secuencias muy largas\r\n\r\n**Compensaciones de rendimiento**:\r\n- Precisión: Por lo general, ligeramente por debajo de la atención estándar\r\n- Eficiencia: Mejora significativamente la eficiencia computacional\r\n- Aplicabilidad: Adecuado para escenarios con recursos limitados\r\n\r\n### Atención cruzada\r\n\r\nEn las tareas multimodales, la atención cruzada permite la interacción de información entre diferentes modalidades.\r\n\r\n**Atención cruzada imagen-texto**:\r\nLas entidades de texto se utilizan como consultas y las entidades de imagen se utilizan como claves y valores para realizar la atención del texto a las imágenes.\r\n\r\n**Representación matemática**:\r\nCrossAttention(Q_text, K_image, V_image) = softmax(Q_text · K_image^T / √d) · V_image\r\n\r\n**Escenarios de aplicación**:\r\n- Generación de descripciones de imágenes\r\n- Preguntas y respuestas visuales\r\n- Comprensión multimodal de documentos\r\n\r\n**Atención cruzada bidireccional**:\r\nCalcule la atención de imagen a texto y de texto a imagen.\r\n\r\n**Método de implementación**:\r\n1. Imagen a texto: atención (Q_image, K_text, V_text)\r\n2. Texto a imagen: atención (Q_text, K_image, V_image)\r\n3. Fusión de características: Fusionar los resultados de la atención en ambas direcciones\r\n\r\n## Estrategias de entrenamiento y optimización\r\n\r\n### Supervisión de la atención\r\n\r\nGuíe al modelo para que aprenda los patrones de atención correctos proporcionando señales supervisadas para la atención.\r\n\r\n**Pérdida de alineación de la atención**:\r\nL_align = || A - A_gt|| ²\r\n\r\nAllí:\r\n- A: Matriz de peso de atención prevista\r\n- A_gt: Etiquetas de atención auténticas\r\n\r\n**Adquisición de señal supervisada**:\r\n- Anotación manual: los expertos marcan áreas importantes\r\n- Heurística: Generar etiquetas de atención basadas en reglas\r\n- Supervisión débil: Utilice señales de supervisión de grano grueso\r\n\r\n**Regularización de la atención**:\r\nFomente la escasez o suavidad de los pesos de atención:\r\nL_reg = λ₁ · || A|| ₁ + λ₂ · || ∇A|| ²\r\n\r\nAllí:\r\n- || A|| ₁: Regularización L1 para fomentar la escasez\r\n- || ∇A|| ²: Regularización de la suavidad, fomentando pesos de atención similares en posiciones adyacentes\r\n\r\n**Aprendizaje multitarea**:\r\nLa predicción de la atención se utiliza como una tarea secundaria y se entrena junto con la tarea principal.\r\n\r\n**Diseño de la función de pérdida**:\r\nL_total = L_main + α · L_attention + β · L_reg\r\n\r\ndonde α y β son los hiperparámetros que equilibran diferentes términos de pérdida.\r\n\r\n### Visualización de la atención\r\n\r\nLa visualización de los pesos de atención ayuda a comprender cómo funciona el modelo y depurar los problemas del modelo.\r\n\r\n**Visualización del mapa de calor**:\r\nAsigne los pesos de atención como un mapa de calor, superponiéndolos en la imagen original para mostrar el área de interés del modelo.\r\n\r\n**Pasos de implementación**:\r\n1. Extraer la matriz de peso de atención\r\n2. Asigne los valores de peso al espacio de color\r\n3. Ajuste el tamaño del mapa de calor para que coincida con la imagen original\r\n4. Superposición o lado a lado\r\n\r\n**Trayectoria de atención**:\r\nMuestra la trayectoria de movimiento del foco de atención durante la decodificación, lo que ayuda a comprender el proceso de reconocimiento del modelo.\r\n\r\n**Análisis de trayectoria**:\r\n- El orden en que se mueve la atención\r\n- Vivienda con capacidad de atención\r\n- Patrón de saltos de atención\r\n- Identificación de comportamiento anormal de atención\r\n\r\n**Visualización de atención de múltiples cabezales**:\r\nLa distribución del peso de los diferentes cabezales de atención se visualiza por separado y se analiza el grado de especialización de cada cabeza.\r\n\r\n**Dimensiones analíticas**:\r\n- Diferencias cara a cara: diferencias regionales de preocupación para diferentes cabezas\r\n- Especialización de la cabeza: Algunas cabezas se especializan en tipos específicos de características\r\n- Importancia de las cabezas: La contribución de diferentes cabezas al resultado final\r\n\r\n### Optimización computacional\r\n\r\n**Optimización de memoria**:\r\n- Puntos de control de gradiente: use puntos de control de gradiente en el entrenamiento de secuencia larga para reducir la huella de memoria\r\n- Precisión mixta: reduce los requisitos de memoria con el entrenamiento FP16\r\n- Almacenamiento en caché de atención: los pesos de atención calculados de los cachés\r\n\r\n**Aceleración computacional**:\r\n- Fragmentación de matrices: calcule matrices grandes en fragmentos para reducir los picos de memoria\r\n- Cálculos dispersos: Acelere los cálculos con la escasez de pesos de atención\r\n- Optimización de hardware: Optimice los cálculos de atención para hardware específico\r\n\r\n**Estrategia de paralelización**:\r\n- Paralelismo de datos: Procese diferentes muestras en paralelo en múltiples GPU\r\n- Paralelismo de modelos: distribuya los cálculos de atención en varios dispositivos\r\n- Paralelización de canalización: canalización de diferentes capas de cómputo\r\n\r\n## Evaluación y análisis del desempeño\r\n\r\n### Evaluación de la calidad de la atención\r\n\r\n**Precisión de la atención**:\r\nMida la alineación de los pesos de atención con anotaciones manuales.\r\n\r\nFórmula de cálculo:\r\nPrecisión = (Número de posiciones correctamente enfocadas) / (Posiciones totales)\r\n\r\n**Concentración**:\r\nLa concentración de la distribución de la atención se mide utilizando la entropía o el coeficiente de Gini.\r\n\r\nCálculo de entropía:\r\nH(A) = -Σi αi · log(αi)\r\n\r\ndonde αi es el peso de atención de la i-ésima posición.\r\n\r\n**Estabilidad de la atención**:\r\nEvaluar la consistencia de los patrones de atención bajo entradas similares.\r\n\r\nIndicadores de estabilidad:\r\nEstabilidad = 1 - || A₁ - A₂|| ₂ / 2\r\n\r\ndonde A₁ y A₂ son las matrices de peso de atención de entradas similares.\r\n\r\n### Análisis de eficiencia computacional\r\n\r\n**Complejidad de tiempo**:\r\nAnalizar la complejidad computacional y el tiempo de ejecución real de diferentes mecanismos de atención.\r\n\r\nComparación de complejidad:\r\n- Atención estándar: O(n²d)\r\n- Atención escasa: O(n·k·d), k<< n\r\n- Atención lineal: O(n·d²)\r\n\r\n**Uso de memoria**:\r\nEvaluar la demanda de memoria GPU para mecanismos de atención.\r\n\r\nAnálisis de memoria:\r\n- Matriz de peso de atención: O (n²)\r\n- Resultado intermedio del cálculo: O(n·d)\r\n- Almacenamiento en gradiente: O(n²d)\r\n\r\n**Análisis de consumo de energía**:\r\nEvaluar el impacto en el consumo energético de los mecanismos de atención en dispositivos móviles.\r\n\r\nFactores de consumo de energía:\r\n- Fuerza de cálculo: Número de operaciones de punto flotante\r\n- Acceso a la memoria: sobrecarga de transferencia de datos\r\n- Utilización de hardware: Uso eficiente de los recursos informáticos\r\n\r\n## Casos de aplicación en el mundo real\r\n\r\n### Reconocimiento de texto escrito a mano\r\n\r\nEn el reconocimiento de texto escrito a mano, el mecanismo de atención ayuda al modelo a concentrarse en el carácter que está reconociendo actualmente, ignorando otra información que distrae.\r\n\r\n**Efectos de aplicación**:\r\n- La precisión del reconocimiento aumentó en un 15-20%\r\n- Robustez mejorada para fondos complejos\r\n- Capacidad mejorada para manejar texto organizado irregularmente\r\n\r\n**Implementación técnica**:\r\n1. **Atención espacial**: Presta atención al área espacial donde se encuentra el personaje\r\n2. **Atención temporal**: Utiliza la relación temporal entre los personajes\r\n3. **Atención multiescala**: Maneja caracteres de diferentes tamaños\r\n\r\n**Estudio de casos**:\r\nEn las tareas de reconocimiento de palabras escritas a mano en inglés, los mecanismos de atención pueden:\r\n- Localiza con precisión la posición de cada personaje\r\n- Tratar el fenómeno de los trazos continuos entre los personajes\r\n- Utilizar el conocimiento del modelo de lenguaje a nivel de palabra\r\n\r\n### Reconocimiento de texto de escena\r\n\r\nEn escenas naturales, el texto a menudo está incrustado en fondos complejos y los mecanismos de atención pueden separar eficazmente el texto y el fondo.\r\n\r\n**Características técnicas**:\r\n- Atención multiescala para trabajar con texto de diferentes tamaños\r\n- Atención espacial para localizar áreas de texto\r\n- Canaliza la selección de funciones útiles\r\n\r\n**Desafíos y soluciones**:\r\n1. **Distracción de fondo**: Filtra el ruido de fondo con atención espacial\r\n2. **Cambios de iluminación**: Adáptese a diferentes condiciones de iluminación a través de la atención del canal\r\n3. **Deformación geométrica**: Incorpora mecanismos de corrección geométrica y atención\r\n\r\n**Mejoras de rendimiento**:\r\n- Mejora del 10-15% en la precisión de los conjuntos de datos ICDAR\r\n- Adaptabilidad significativamente mejorada a escenarios complejos\r\n- La velocidad de razonamiento se mantiene dentro de límites aceptables\r\n\r\n### Análisis de documentos\r\n\r\nEn las tareas de análisis de documentos, los mecanismos de atención ayudan a los modelos a comprender la estructura y las relaciones jerárquicas de los documentos.\r\n\r\n**Escenarios de aplicación**:\r\n- Identificación de tablas: Concéntrese en la estructura de columnas de la tabla\r\n- Análisis de diseño: Identifique elementos como titulares, cuerpo, imágenes y más\r\n- Extracción de información: localiza la ubicación de la información clave\r\n\r\n**Innovación Tecnológica**:\r\n1. **Atención jerárquica**: Aplicar atención en diferentes niveles\r\n2. **Atención estructurada**: Considere la información estructurada del documento\r\n3. **Atención multimodal**: Combinación de texto e información visual\r\n\r\n**Resultados prácticos**:\r\n- Aumentar la precisión del reconocimiento de tablas en más de un 20%\r\n- Potencia de procesamiento significativamente mayor para diseños complejos\r\n- La precisión de la extracción de información se ha mejorado considerablemente\r\n\r\n## Tendencias de desarrollo futuro\r\n\r\n### Mecanismo de atención eficiente\r\n\r\nA medida que aumenta la longitud de la secuencia, el costo computacional del mecanismo de atención se convierte en un cuello de botella. Las direcciones de investigación futuras incluyen:\r\n\r\n**Optimización de algoritmos**:\r\n- Modo de atención dispersa más eficiente\r\n- Mejoras en los métodos de cálculo aproximado\r\n- Diseño de atención amigable con el hardware\r\n\r\n**Innovación arquitectónica**:\r\n- Mecanismo de atención jerárquica\r\n- Enrutamiento dinámico de la atención\r\n- Gráficos de cálculo adaptativos\r\n\r\n**Avance teórico**:\r\n- Análisis teórico del mecanismo de atención\r\n- Prueba matemática de patrones de atención óptimos\r\n- Teoría unificada de la atención y otros mecanismos\r\n\r\n### Atención multimodal\r\n\r\nLos futuros sistemas de OCR integrarán más información de múltiples modalidades:\r\n\r\n**Fusión de lenguaje visual**:\r\n- Atención conjunta de imágenes y texto\r\n- Transmisión de información a través de modalidades\r\n- Representación multimodal unificada\r\n\r\n**Fusión de información temporal**:\r\n- Tiempo de atención en el OCR de vídeo\r\n- Seguimiento de texto para escenas dinámicas\r\n- Modelado conjunto del espacio-tiempo\r\n\r\n**Fusión multisensor**:\r\n- Atención 3D combinada con información de profundidad\r\n- Mecanismos de atención para imágenes multiespectrales\r\n- Modelado conjunto de datos de sensores\r\n\r\n### Mejora de la interpretabilidad\r\n\r\nMejorar la interpretabilidad de los mecanismos de atención es una dirección de investigación importante:\r\n\r\n**Explicación de la atención**:\r\n- Métodos de visualización más intuitivos\r\n- Explicación semántica de los patrones de atención\r\n- Herramientas de análisis y depuración de errores\r\n\r\n**Razonamiento causal**:\r\n- Análisis causal de la atención\r\n- Métodos de razonamiento contrafáctico\r\n- Tecnología de verificación de robustez\r\n\r\n**Interactivo**:\r\n- Ajustes de atención interactivos\r\n- Incorporación de comentarios de los usuarios\r\n- Modo de atención personalizada\r\n\r\n## Resumen\r\n\r\nComo parte importante del aprendizaje profundo, el mecanismo de atención juega un papel cada vez más importante en el campo del OCR. Desde la atención básica de secuencia a secuencia hasta la autoatención compleja de múltiples cabezales, desde la atención espacial hasta la atención multiescala, el desarrollo de estas tecnologías ha mejorado enormemente el rendimiento de los sistemas OCR.\r\n\r\n**Conclusiones clave**:\r\n- El mecanismo de atención simula la capacidad de la atención selectiva humana y resuelve el problema de los cuellos de botella de información\r\n- Los principios matemáticos se basan en la suma ponderada, lo que permite la selección de información mediante el aprendizaje de pesos de atención\r\n- La atención multicabezal y la autoatención son las técnicas centrales de los mecanismos de atención modernos\r\n- Las aplicaciones en OCR incluyen modelado de secuencias, atención visual, procesamiento multiescala y más\r\n- Las direcciones de desarrollo futuras incluyen optimización de la eficiencia, fusión multimodal, mejora de la interpretabilidad, etc\r\n\r\n**Consejos prácticos**:\r\n- Elegir el mecanismo de atención adecuado para la tarea específica\r\n- Preste atención al equilibrio entre la eficiencia computacional y el rendimiento\r\n- Aprovechar al máximo la interpretabilidad de la atención para la depuración del modelo\r\n- Esté atento a los últimos avances en investigación y desarrollos tecnológicos\r\n\r\nA medida que la tecnología continúe evolucionando, los mecanismos de atención continuarán evolucionando, proporcionando herramientas aún más poderosas para OCR y otras aplicaciones de IA. Comprender y dominar los principios y aplicaciones de los mecanismos de atención es crucial para los técnicos que participan en la investigación y el desarrollo de OCR.</div>\n                        \n                        <div class=\"article-tags\">\n                            <h6>Etiqueta:</h6>\n                            <div class=\"tags-list\">\n                                \n                                <span class=\"tag\">Mecanismo de atención</span>\n                                \n                                <span class=\"tag\">Atención de toros</span>\n                                \n                                <span class=\"tag\">Autoatención</span>\n                                \n                                <span class=\"tag\">Codificación de posición</span>\n                                \n                                <span class=\"tag\">Atención cruzada</span>\n                                \n                                <span class=\"tag\">Atención escasa</span>\n                                \n                                <span class=\"tag\">OCR</span>\n                                \n                                <span class=\"tag\">Transformer</span>\n                                \n                            </div>\n                        </div>\n                        \n                    </article>\n                    \n                    <footer class=\"article-footer\" id=\"articleFooter\">\n                        <div class=\"article-share\">\n                            <h6 style=\"color: #1a1c1f; margin-bottom: 1rem;\">Compartir y operar:</h6>\n                            <div class=\"share-buttons\">\n                                <a href=\"javascript:void(0)\" onclick=\"shareToWeibo()\" class=\"btn btn-weibo\">📱 Weibo compartido</a>\n                                <a href=\"javascript:void(0)\" onclick=\"copyUrl()\" class=\"btn btn-copy\">🔗 Copiar enlace</a>\n                                <button type=\"button\" class=\"btn btn-print\" onclick=\"printArticle()\">🖨️ Imprimir el artículo</button>\n                            </div>\n                        </div>\n                    </footer>\n                    \n                </div>\n                <div class=\"col-lg-4 col-xl-3 col-md-12\">\n                    <div class=\"sidebar\">\n                        <div class=\"sidebar-card toc-card\" id=\"tocCard\" style=\"display: none;\"><h5>Tabla de contenidos</h5><div id=\"tableOfContents\" class=\"toc-list\"></div></div>\n                        <div class=\"sidebar-card\">\n                            <h5>Lectura recomendada</h5>\n                            <div id=\"recommendedArticles\">\n                                \n                                <div class=\"recommended-item\"><h6><a href=\"Article.aspx?slug=document-intelligence-future-outlook\">【Serie de procesamiento inteligente de documentos · 20】 Perspectivas de desarrollo de la tecnología de procesamiento inteligente de documentos</a></h6><p class=\"text-muted small\">2025-08-19 • 1203 Siguiente lectura</p></div>\n                                \n                                <div class=\"recommended-item\"><h6><a href=\"Article.aspx?slug=document-intelligence-quality-assurance\">【Serie de procesamiento inteligente de documentos · 19】 Sistema de garantía de calidad de procesamiento inteligente de documentos</a></h6><p class=\"text-muted small\">2025-08-19 • 1200 Siguiente lectura</p></div>\n                                \n                                <div class=\"recommended-item\"><h6><a href=\"Article.aspx?slug=large-scale-document-processing-optimization\">【Serie de procesamiento inteligente de documentos · 18】 Optimización del rendimiento del procesamiento de documentos a gran escala</a></h6><p class=\"text-muted small\">2025-08-19 • 1201 Siguiente lectura</p></div>\n                                \n                            </div>\n                        </div>\n                    </div>\n</div>\n</div>\n</div>\n</div>\n<script>\nvar articleId,viewCountKey,now=new Date().getTime();\ndocument.addEventListener('DOMContentLoaded',function(){var articleDetail=document.getElementById('articleDetail');if(articleDetail&&articleDetail.querySelector('.article-content')){formatContent();initReadingProgress();optimizeImages();updateViewCount();}});\nfunction updateViewCount(){articleId=getArticleId();if(!articleId)return;viewCountKey='article_view_'+articleId;var lastViewTime=localStorage.getItem(viewCountKey);if(lastViewTime&&(now-parseInt(lastViewTime))<3600000)return;var xhr=new XMLHttpRequest();xhr.open('POST','/Article.ashx',true);xhr.setRequestHeader('Content-Type','application/x-www-form-urlencoded');xhr.onreadystatechange=function(){if(xhr.readyState===4&&xhr.status===200){try{var response=JSON.parse(xhr.responseText);if(response.success){updateViewCountDisplay(response.viewCount);localStorage.setItem(viewCountKey,now.toString());}}catch(e){}}};xhr.send('action=updateviewcount&articleId='+encodeURIComponent(articleId));}\nfunction getArticleId(){var articleDetail=document.getElementById('articleDetail');if(articleDetail){var id=articleDetail.getAttribute('data-article-id');if(id)return id;}var urlParams=new URLSearchParams(window.location.search);return urlParams.get('id');}\nfunction updateViewCountDisplay(newCount){document.querySelectorAll('.view-count').forEach(function(element){element.textContent=newCount;});}\nfunction decodeHtmlEntities(text){var textArea=document.createElement('textarea');textArea.innerHTML=text;return textArea.value;}\nfunction formatContent(){var contentDiv=document.getElementById('articleContent');if(!contentDiv)return;var content=contentDiv.textContent||contentDiv.innerText;if(!content)return;content=decodeHtmlEntities(content);content=formatMarkdownContent(content);content=formatIndentation(content);content=formatLists(content);content=formatParagraphs(content);content=formatLinks(content);contentDiv.innerHTML=content;initTableOfContents();}\nfunction formatIndentation(content){var lines=content.split('\\n'),result=[];for(var i=0;i<lines.length;i++){var line=lines[i];if(line.trim().match(/^<[^>]+>/)){result.push(line);continue;}line=line.replace(/^\\t+/g,function(match){return '    '.repeat(match.length);});if(line.match(/^  +[^\\s]/)){var indentMatch=line.match(/^( +)(.*)$/);if(indentMatch){var indentLevel=Math.floor(indentMatch[1].length/2);line='<span class=\"text-indent-'+indentLevel+'\">'+indentMatch[2]+'</span>';}}result.push(line);}return result.join('\\n');}\nfunction formatMarkdownContent(content){content=content.replace(/^\\s*##### (.+)$/gm,'<h5>$1</h5>').replace(/^\\s*#### (.+)$/gm,'<h4>$1</h4>').replace(/^\\s*### (.+)$/gm,'<h3>$1</h3>').replace(/^\\s*## (.+)$/gm,'<h2>$1</h2>').replace(/^\\s*# (.+)$/gm,'<h1>$1</h1>').replace(/^> (.+)$/gm,'<blockquote><p>$1</p></blockquote>').replace(/\\*\\*([^*]+?)\\*\\*/g,'<strong>$1</strong>').replace(/\\*([^*\\n]+?)\\*/g,'<em>$1</em>').replace(/`([^`]+?)`/g,'<code class=\"inline-code\">$1</code>').replace(/^(\\*\\*[^*]+?\\*\\*)：/gm,'<div class=\"label-item\"><span class=\"label\">$1</span></div>').replace(/^([^：\\n]+?)：$/gm,'<div class=\"section-label\">$1：</div>').replace(/^(\\*\\*[^*]+?\\*\\*)：(.+)$/gm,'<div class=\"definition-item\"><span class=\"definition-label\">$1：</span><span class=\"definition-content\">$2</span></div>').replace(/^(note|note|note):(.+)$/gm,'<div class=\"note-item\"><span class=\"note-label\">$1：</span><span class=\"note-content\">$2</span></div>');content=formatLists(content);content=formatMathSymbols(content);content=formatParagraphs(content);return content;}\nfunction formatLists(content){var lines=content.split('\\n'),result=[],inOrderedList=false,inUnorderedList=false;for(var i=0;i<lines.length;i++){var line=lines[i],trimmed=line.trim();var orderedMatch=line.match(/^(\\d+)\\.\\s*(.+)$/);var unorderedMatch=line.match(/^[-*+]\\s*(.+)$/);if(orderedMatch){if(!inOrderedList){if(inUnorderedList){result.push('</ul>');inUnorderedList=false;}result.push('<ol class=\"formatted-list ordered\">');inOrderedList=true;}var listContent=orderedMatch[2].replace(/^\\*\\*([^*]+?)\\*\\*：(.+)$/,'<strong class=\"list-title\">$1：</strong><span class=\"list-content\">$2</span>');result.push('<li>'+listContent+'</li>');}else if(unorderedMatch){if(!inUnorderedList){if(inOrderedList){result.push('</ol>');inOrderedList=false;}result.push('<ul class=\"formatted-list unordered\">');inUnorderedList=true;}var listContent=unorderedMatch[1].replace(/^\\*\\*([^*]+?)\\*\\*：(.+)$/,'<strong class=\"list-title\">$1：</strong><span class=\"list-content\">$2</span>');result.push('<li>'+listContent+'</li>');}else{if(trimmed===''){if(i+1<lines.length){var nextLine=lines[i+1].trim();if(!nextLine.match(/^\\d+\\.\\s/)&&!nextLine.match(/^[-*+]\\s/)){if(inOrderedList){result.push('</ol>');inOrderedList=false;}if(inUnorderedList){result.push('</ul>');inUnorderedList=false;}}}result.push(line);}else{if(inOrderedList){result.push('</ol>');inOrderedList=false;}if(inUnorderedList){result.push('</ul>');inUnorderedList=false;}result.push(line);}}}if(inOrderedList)result.push('</ol>');if(inUnorderedList)result.push('</ul>');return result.join('\\n');}\nfunction formatMathSymbols(content){var mathSymbols={'α':'&alpha;','β':'&beta;','γ':'&gamma;','δ':'&delta;','ε':'&epsilon;','ζ':'&zeta;','η':'&eta;','θ':'&theta;','λ':'&lambda;','μ':'&mu;','π':'&pi;','σ':'&sigma;','∑':'&sum;','∏':'&prod;','∫':'&int;','∂':'&part;','∇':'&nabla;','∞':'&infin;','≤':'&le;','≥':'&ge;','≠':'&ne;','≈':'&asymp;','±':'&plusmn;','×':'&times;','÷':'&divide;','→':'&rarr;','←':'&larr;','↑':'&uarr;','↓':'&darr;','⇒':'&rArr;','⇐':'&lArr;'};var subMap={'₀':'0','₁':'1','₂':'2','₃':'3','₄':'4','₅':'5','₆':'6','₇':'7','₈':'8','₉':'9','ₖ':'k','ₜ':'t','ₛ':'s','₊':'+','₋':'-'};var supMap={'⁰':'0','¹':'1','²':'2','³':'3','⁴':'4','⁵':'5','⁶':'6','⁷':'7','⁸':'8','⁹':'9','ᵏ':'k','ᵗ':'t','ˢ':'s','⁺':'+','⁻':'-'};for(var symbol in mathSymbols){content=content.replace(new RegExp(escapeRegExp(symbol),'g'),mathSymbols[symbol]);}content=content.replace(/([a-zA-Z])₍([0-9]+)₎/g,'$1<sub>$2</sub>').replace(/([a-zA-Z])⁽([0-9]+)⁾/g,'$1<sup>$2</sup>').replace(/([a-zA-Z])([₀₁₂₃₄₅₆₇₈₉ₖₜₛ₊₋]+)/g,function(match,base,subscript){var converted=subscript.replace(/[₀₁₂₃₄₅₆₇₈₉ₖₜₛ₊₋]/g,function(char){return subMap[char]||char;});return base+'<sub>'+converted+'</sub>';}).replace(/([a-zA-Z])([⁰¹²³⁴⁵⁶⁷⁸⁹ᵏᵗˢ⁺⁻]+)/g,function(match,base,superscript){var converted=superscript.replace(/[⁰¹²³⁴⁵⁶⁷⁸⁹ᵏᵗˢ⁺⁻]/g,function(char){return supMap[char]||char;});return base+'<sup>'+converted+'</sup>';}).replace(/\\b([A-Za-z])_\\{([^}]+)\\}/g,'$1<sub>$2</sub>').replace(/\\b([A-Za-z])\\^\\{([^}]+)\\}/g,'$1<sup>$2</sup>');return content;}\nfunction formatParagraphs(content){var lines=content.split('\\n'),result=[],inParagraph=false,currentParagraph=[];for(var i=0;i<lines.length;i++){var originalLine=lines[i],line=originalLine.trim();if(line.match(/^<[^>]+>/)||line===''){if(inParagraph&&currentParagraph.length>0){var paragraphContent=currentParagraph.join(' ').trim();if(paragraphContent)result.push('<p class=\"content-paragraph indent-paragraph\">'+paragraphContent+'</p>');currentParagraph=[];inParagraph=false;}result.push(originalLine);continue;}if(line.match(/^#{1,6}\\s/)||line.match(/^\\d+\\.\\s/)||line.match(/^[-*+]\\s/)||line.match(/^>/)){if(inParagraph&&currentParagraph.length>0){var paragraphContent=currentParagraph.join(' ').trim();if(paragraphContent)result.push('<p class=\"content-paragraph indent-paragraph\">'+paragraphContent+'</p>');currentParagraph=[];inParagraph=false;}result.push(originalLine);continue;}if(line){currentParagraph.push(line);inParagraph=true;}}if(inParagraph&&currentParagraph.length>0){var paragraphContent=currentParagraph.join(' ').trim();if(paragraphContent)result.push('<p class=\"content-paragraph indent-paragraph\">'+paragraphContent+'</p>');}return result.join('\\n');}\nfunction escapeRegExp(string){return string.replace(/[.*+?^${}()|[\\]\\\\]/g,'\\\\$&');}\nfunction formatLinks(content){return content.replace(/<a\\s+href=\"(https?:\\/\\/[^\"]+)\"([^>]*)>/gi,function(match,url,attrs){return !attrs.includes('target=')?'<a href=\"'+url+'\"'+attrs+' target=\"_blank\" rel=\"noopener noreferrer\">':match;});}\nfunction escapeHtml(text){var div=document.createElement('div');div.textContent=text;return div.innerHTML;}\nfunction addTocClickEvents(){document.querySelectorAll('#tableOfContents a').forEach(function(link){link.addEventListener('click',function(e){e.preventDefault();var targetElement=document.getElementById(this.getAttribute('data-target'));if(targetElement){targetElement.scrollIntoView({behavior:'smooth',block:'start'});updateActiveTocItem(this);}});});}\nfunction addScrollHighlight(headings){var throttleTimer=null;function updateActiveHeading(){var windowHeight=window.innerHeight,activeHeading=null;for(var i=headings.length-1;i>=0;i--){var rect=headings[i].getBoundingClientRect();if(rect.top<=windowHeight*0.3){activeHeading=headings[i];break;}}if(activeHeading){var activeLink=document.querySelector('#tableOfContents a[data-target=\"'+activeHeading.id+'\"]');if(activeLink)updateActiveTocItem(activeLink);}}window.addEventListener('scroll',function(){if(throttleTimer)clearTimeout(throttleTimer);throttleTimer=setTimeout(updateActiveHeading,100);});updateActiveHeading();}\nfunction updateActiveTocItem(activeLink){document.querySelectorAll('#tableOfContents a').forEach(function(link){link.classList.remove('active');});if(activeLink){activeLink.classList.add('active');scrollTocToActiveItem(activeLink);}}\nfunction scrollTocToActiveItem(activeLink){var tocCard=document.getElementById('tocCard');if(!activeLink||!tocCard)return;var cardRect=tocCard.getBoundingClientRect();var activeRect=activeLink.getBoundingClientRect();var relativeTop=activeRect.top-cardRect.top;var relativeBottom=activeRect.bottom-cardRect.bottom;var cardStyle=window.getComputedStyle(tocCard);var paddingTop=parseFloat(cardStyle.paddingTop)||0;var titleHeight=tocCard.querySelector('h5')?tocCard.querySelector('h5').offsetHeight:0;var contentTop=paddingTop+titleHeight;if(relativeTop<contentTop){tocCard.scrollTop+=(relativeTop-contentTop-10);}else if(relativeBottom>0){tocCard.scrollTop+=(relativeBottom+10);}}\nfunction optimizeImages(){document.querySelectorAll('.article-content img').forEach(function(img){img.loading='lazy';img.onerror=function(){this.style.display='none';};if(!img.alt)img.alt='Artículo con imágenes';});}\nfunction initReadingProgress(){var progressBar=document.getElementById('readingProgress');function updateProgress(){var scrollTop=window.pageYOffset||document.documentElement.scrollTop;var docHeight=document.documentElement.scrollHeight-window.innerHeight;progressBar.style.width=Math.min((scrollTop/docHeight)*100,100)+'%';}window.addEventListener('scroll',updateProgress);updateProgress();}\nfunction initTableOfContents(){var content=document.querySelector('.article-content');if(!content)return;var headings=content.querySelectorAll('h1, h2, h3, h4, h5, h6');if(headings.length<=1)return;var tocCard=document.getElementById('tocCard');var tocList=document.getElementById('tableOfContents');var html='',counters=[0,0,0,0,0,0];headings.forEach(function(heading,index){var id='heading-'+index;heading.id=id;var level=parseInt(heading.tagName.substring(1));var indent=(level-1)*15;counters[level-1]++;for(var i=level;i<counters.length;i++)counters[i]=0;var numberText='';for(var i=0;i<level;i++){if(counters[i]>0)numberText+=(numberText?'.':'')+counters[i];}numberText+='.';html+='<a href=\"#'+id+'\" class=\"toc-item toc-level-'+level+'\" style=\"padding-left: '+indent+'px;\" data-target=\"'+id+'\"><span class=\"toc-number\">'+numberText+'</span> <span class=\"toc-text\">'+heading.textContent+'</span></a>';});tocList.innerHTML=html;tocCard.style.display='block';addTocClickEvents();addScrollHighlight(headings);tocList.addEventListener('click',function(e){if(e.target.tagName==='A'){e.preventDefault();var targetElement=document.getElementById(e.target.getAttribute('data-target'));if(targetElement){targetElement.scrollIntoView({behavior:'smooth',block:'start'});targetElement.style.transition='all 0.3s ease';targetElement.style.backgroundColor='#e3f2fd';targetElement.style.padding='10px';targetElement.style.borderRadius='6px';setTimeout(function(){targetElement.style.backgroundColor='';targetElement.style.padding='';targetElement.style.borderRadius='';},2000);}}});}\nfunction shareToWeibo(){window.open('https://service.weibo.com/share/share.php?url='+encodeURIComponent(window.location.href)+'&title='+encodeURIComponent(document.title),'_blank');}\nfunction copyUrl(){navigator.clipboard.writeText(window.location.href).then(function(){alert('El enlace se ha copiado en el portapapeles');}).catch(function(){var textArea=document.createElement('textarea');textArea.value=window.location.href;textArea.style.cssText='position:fixed;left:-999999px;top:-999999px';document.body.appendChild(textArea);textArea.focus();textArea.select();try{alert(document.execCommand('copy')?'El enlace se ha copiado en el portapapeles':'Si la copia falla, copie el enlace manualmente');}catch(err){alert('Si la copia falla, copie el enlace manualmente');}document.body.removeChild(textArea);});}\nfunction printArticle(){var printWindow=window.open('','_blank');var articleContent=document.querySelector('.article-detail').innerHTML;var articleTitle=document.querySelector('.article-title').textContent;printWindow.document.write('<!DOCTYPE html><html lang=\"es\"><title>'+articleTitle+'</title><style>body{font-family:-apple-system,BlinkMacSystemFont,\"Segoe UI\",Roboto,\"Helvetica Neue\",Arial,sans-serif;line-height:1.6;color:#333;max-width:800px;margin:0 auto;padding:20px}h1,h2,h3,h4,h5,h6{color:#1a1c1f;margin-top:1.5em;margin-bottom:0.5em}h1{font-size:2em;border-bottom:2px solid #e9ecef;padding-bottom:0.3em}h2{font-size:1.5em;border-bottom:1px solid #e9ecef;padding-bottom:0.3em}h3{font-size:1.25em}p{margin-bottom:1em}pre{background:#f8f9fa;border:1px solid #e9ecef;border-radius:4px;padding:1em;overflow-x:auto;font-family:Consolas,Monaco,\"Courier New\",monospace}code{background:#f1f3f4;padding:2px 4px;border-radius:3px;font-family:Consolas,Monaco,\"Courier New\",monospace}blockquote{border-left:4px solid #1764ff;background:#f8f9fa;padding:1em;margin:1em 0;border-radius:0 4px 4px 0}img{max-width:100%;height:auto}table{width:100%;border-collapse:collapse;margin:1em 0}th,td{border:1px solid #ddd;padding:8px;text-align:left}th{background-color:#f2f2f2}.article-meta,.article-footer,.share-buttons{display:none}</style><body>'+articleContent+'</body></html>');printWindow.document.close();printWindow.focus();setTimeout(function(){printWindow.print();printWindow.close();},250);}\nfunction improvedStickyPosition(){var sidebarCards=document.querySelectorAll('.sidebar-card');var originalPositions=[],isFixed=[];sidebarCards.forEach(function(card,index){var rect=card.getBoundingClientRect();originalPositions[index]=rect.top+window.pageYOffset;isFixed[index]=false;var placeholder=document.createElement('div');placeholder.style.cssText='height:'+card.offsetHeight+'px;display:none';placeholder.className='sticky-placeholder';card.parentNode.insertBefore(placeholder,card.nextSibling);});function updateStickyPosition(){var scrollTop=window.pageYOffset||document.documentElement.scrollTop;sidebarCards.forEach(function(card,index){var originalTop=originalPositions[index];var stickyTop=index>0?(document.querySelector('.toc-card').style.position==='fixed'?60+document.querySelector('.toc-card').offsetHeight+8:80):60;var placeholder=card.nextSibling;if(scrollTop>originalTop-stickyTop){if(!isFixed[index]){var colContainer=card.closest('[class*=\"col-\"]');var colRect=colContainer.getBoundingClientRect();var colStyle=window.getComputedStyle(colContainer);var paddingLeft=parseFloat(colStyle.paddingLeft)||0;var paddingRight=parseFloat(colStyle.paddingRight)||0;card.style.cssText='position:fixed;top:'+stickyTop+'px;left:'+(colRect.left+paddingLeft)+'px;width:'+(colRect.width-paddingLeft-paddingRight)+'px;z-index:9999;box-sizing:border-box';card.classList.add('fixed');placeholder.style.display='block';isFixed[index]=true;}}else if(isFixed[index]){card.style.cssText='';card.classList.remove('fixed');placeholder.style.display='none';isFixed[index]=false;}});}window.addEventListener('scroll',updateStickyPosition);window.addEventListener('resize',function(){setTimeout(function(){sidebarCards.forEach(function(card,index){if(isFixed[index]){var colContainer=card.closest('[class*=\"col-\"]');var colRect=colContainer.getBoundingClientRect();var colStyle=window.getComputedStyle(colContainer);var paddingLeft=parseFloat(colStyle.paddingLeft)||0;var paddingRight=parseFloat(colStyle.paddingRight)||0;card.style.left=(colRect.left+paddingLeft)+'px';card.style.width=(colRect.width-paddingLeft-paddingRight)+'px';}else{originalPositions[index]=card.getBoundingClientRect().top+window.pageYOffset;}});},100);});updateStickyPosition();}\ndocument.addEventListener('DOMContentLoaded',function(){setTimeout(improvedStickyPosition,1000);});\n    </script>\n\r\n            </div>\r\n        </form>\r\n\r\n        <div class=\"fixed-icon\">\r\n            <div style=\"width: 120px; position: relative; left: 1rem; top: -12px;\">\r\n            </div>\r\n            <a id=\"webchat\" class=\"wx-con disa\" href=\"http://wpa.qq.com/msgrd?v=3&uin=365833440&site=qq&menu=yes\" target=\"_blank\">\r\n                <img class=\"wechat-ico\" src=\"site/image/icon/qq.svg\" alt=\"Asistente de OCR Servicio de atención al cliente en línea QQ\" />\r\n                <div class=\"wx-text\">Servicio al cliente de QQ (365833440)</div>\r\n                <label class=\"badge badge-danger\" style=\"background: rgb(255, 34, 122); display: inline-block;\">1</label>\r\n            </a>\r\n\r\n            <a class=\"wx-con\" href=\"https://qm.qq.com/cgi-bin/qm/qr?k=bohNy9GMdgD5bsYrB-fcIjy-s6Ot3zOg&jump_from=webapi&qr=1\" target=\"_blank\">\r\n                <img class=\"wechat-ico\" src=\"site/image/icon/qq.svg\" alt=\"Grupo de comunicación de usuarios QQ asistente de OCR\" />\r\n                <div class=\"wx-text\">Grupo QQ (100029010)</div>\r\n                <label class=\"badge badge-danger\" style=\"background: rgb(255, 34, 122); display: inline-block;\">2</label>\r\n            </a>\r\n            <a href=\"mailto:<EMAIL>\" class=\"wx-con\">\r\n                <img class=\"wechat-ico\" src=\"site/image/icon/im.svg\" alt=\"Asistente de OCR Póngase en contacto con el servicio de atención al cliente por correo electrónico\" />\r\n                <div class=\"wx-text\" style=\"width: 240px; text-align: left; left: -15.3rem; padding: 10px 20px;\">\r\n                    <div style=\"font-size: 15px\">Correo electrónico: <EMAIL></div>\r\n                    <hr style=\"background: #c1c1c1; margin: 10px 10px 6px 0;\" />\r\n                    <span style=\"font-size: 13px; color: #c1c1c1\">¡Gracias por sus comentarios y sugerencias!</span>\r\n                </div>\r\n                <label class=\"badge badge-danger\" style=\"background: rgb(255, 34, 122); display: inline-block;\">3</label>\r\n            </a>\r\n        </div>\r\n\r\n        <style>\r\n            .content {\r\n                width: 100%;\r\n                padding: 0 !important;\r\n                margin: 0 auto\r\n            }\r\n\r\n            h3.h6 {\r\n                font-size: 1rem !important;\r\n                font-weight: bold !important;\r\n                line-height: 1.2 !important;\r\n                margin-bottom: .5rem;\r\n                font-family: inherit\r\n            }\r\n        </style>\r\n        <footer class=\"page-footer\">\r\n            <div class=\"container\">\r\n                <div class=\"row foot-cont pt-4 no-gutters\" style=\"border-top: 1px solid #e1e6ed; max-width: 100%\">\r\n                    <div class=\"col-lg-12 hu-an p-0 mb-3 mt-3 text-l color-gray\" style=\"text-align: center;\">\r\n                        Asistente de reconocimiento de texto OCR&nbsp;©️ 2025 ALL RIGHTS RESERVED. Todos los derechos reservados&nbsp;&nbsp;&nbsp;|&nbsp;&nbsp;&nbsp;\r\n                        <a href=\"PrivacyPolicy.aspx\" style=\"text-decoration: underline;\">Acuerdo de privacidad</a>&nbsp;&nbsp;&nbsp;|&nbsp;&nbsp;&nbsp;\r\n                        <a href=\"UserAgreement.aspx\" style=\"text-decoration: underline;\">Acuerdo de usuario</a>&nbsp;&nbsp;&nbsp;|&nbsp;&nbsp;&nbsp;\r\n                        <a href=\"Status.aspx\" style=\"text-decoration: underline;\">Estado del servicio</a>&nbsp;&nbsp;&nbsp;|&nbsp;&nbsp;&nbsp;\r\n                        <a class=\"mb-2 color-gray\" href=\"https://beian.miit.gov.cn/\" target=\"_blank\" style=\"text-decoration: underline;\">E Preparación del PCI Nº 2021012692</a>\r\n                    </div>\r\n                </div>\r\n            </div>\r\n        </footer>\r\n        <style>\r\n            #translate > .translateSelectLanguage {\r\n                right: 2rem;\r\n                font-size: 1rem;\r\n                width: 150px;\r\n                padding: .3rem;\r\n                margin-right: 20px;\r\n                border: 1px solid #C9C9C9;\r\n                background: #fff;\r\n                color: #555\r\n            }\r\n        </style>\r\n    </div>\r\n    <script>\r\n        document.addEventListener('DOMContentLoaded', function () {\r\n            var setScroll = function (active) {\r\n                forEach(qa('.v4_header_pc,.v4_header_mob'), function (e) {\r\n                    if (active) addClass(e, 'scrollActive'); else removeClass(e, 'scrollActive');\r\n                });\r\n            };\r\n            if (getScrollY() > 60) setScroll(1);\r\n            window.addEventListener('scroll', function () { setScroll(getScrollY() > 60); });\r\n\r\n            forEach(qa('.v4_header_pc'), function (h) {\r\n                h.addEventListener('mouseenter', function () { addClass(h, 'active'); });\r\n                h.addEventListener('mouseleave', function () { removeClass(h, 'active'); });\r\n            });\r\n\r\n            var menuTimer;\r\n            var hideMenu = function (menuId) {\r\n                var m = document.getElementById(menuId);\r\n                if (m) {\r\n                    m.style.cssText = 'visibility:hidden;height:0;display:none';\r\n                    var n = q('.nav-drown-con', m);\r\n                    if (n) { n.style.cssText = 'height:0;opacity:0;transform:translate(0,-100%)'; }\r\n                }\r\n            };\r\n\r\n            forEach(qa('ul.top-nav>li'), function (i) {\r\n                i.addEventListener('mouseenter', function () {\r\n                    clearTimeout(menuTimer);\r\n                    var l = this.querySelector('a'), t = this.querySelector('.triangle'), s = l ? l.getAttribute('data-sub') : null;\r\n                    if (l) addClass(l, 'active'); if (t) addClass(t, 'active');\r\n                    if (s) {\r\n                        var m = document.getElementById(s);\r\n                        if (m) {\r\n                            m.style.cssText = 'display:block;visibility:visible';\r\n                            var siblings = m.parentNode ? toArray(m.parentNode.children) : [];\r\n                            forEach(siblings, function (c) { if (c !== m) c.style.display = 'none'; });\r\n                            var n = q('.nav-drown-con', m);\r\n                            if (n) { n.style.cssText = 'height:100%;opacity:1;transform:translate(0,0)'; }\r\n\r\n                            m.addEventListener('mouseenter', function () { clearTimeout(menuTimer); });\r\n                            m.addEventListener('mouseleave', function () { menuTimer = setTimeout(function () { hideMenu(s); }, 100); });\r\n                        }\r\n                    }\r\n                });\r\n                i.addEventListener('mouseleave', function () {\r\n                    var l = this.querySelector('a'), t = this.querySelector('.triangle'), s = l ? l.getAttribute('data-sub') : null;\r\n                    if (l) removeClass(l, 'active'); if (t) removeClass(t, 'active');\r\n                    if (s) { menuTimer = setTimeout(function () { hideMenu(s); }, 100); }\r\n                });\r\n            });\r\n\r\n            var r = q('.v4_header_mob .right-menu');\r\n            if (r) r.addEventListener('click', function () {\r\n                var arr = [r, q('.v4_header_mob'), q('#nav_products'), q('.mob-nav-content .sidebar-fix')];\r\n                for (var i = 0; i < arr.length; i++) {\r\n                    var e = arr[i];\r\n                    if (e) {\r\n                        var name = (i === 0 ? 'active' : (i === 1 ? 'active' : (i === 2 ? 'active1' : 'fixedActiveBg')));\r\n                        toggleClass(e, name);\r\n                    }\r\n                }\r\n                var f = q('.mob-nav-content .sidebar-fix');\r\n                if (f) {\r\n                    toggleClass(f, 'show');\r\n                    document.body.style.overflow = hasClass(f, 'show') ? 'hidden' : 'scroll';\r\n                }\r\n            });\r\n\r\n            document.addEventListener('click', function (e) {\r\n                e = e || window.event;\r\n                var target = e.target || e.srcElement;\r\n                var m = closest(target, '.sidebar-fix-left .mob-nav-item');\r\n                if (m) {\r\n                    var p = m.parentNode;\r\n                    if (p) forEach(toArray(p.children), function (c) { removeClass(c, 'active'); });\r\n                    addClass(m, 'active');\r\n                    var d = m.getAttribute('data');\r\n                    if (d) forEach(qa('.sidebar-fix-rigth .sub-nav'), function (n, i) { n.style.display = (i == d ? 'block' : 'none'); });\r\n                }\r\n            });\r\n        });\r\n    </script>\r\n</body>\r\n</html>\r\n\r\n\t            <script type=\"text/javascript\">\r\n                    var _hmt = _hmt || [];\r\n                    (function () {\r\n                        var hm = document.createElement('script');hm.async=1;\r\n                        hm.src = 'https://hm.baidu.com/hm.js?5ee048d7caaf0d439cf16309a1687754';\r\n                        var s = document.getElementsByTagName('script')[0];s.parentNode.insertBefore(hm, s);\r\n                    })();\r\n                </script><div id=\"translate\" class=\"ignore\" style=\"display: none\"></div><script src=\"/static/js/translate.js\"></script>"
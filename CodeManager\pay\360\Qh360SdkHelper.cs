﻿using System;
using System.Collections.Generic;
using System.Runtime.InteropServices;
using System.Threading.Tasks;

namespace Account.Web.pay._360
{
    /// <summary>
    /// 用来标注是测试方法，在测试程序中加载和调用这个方法
    /// </summary>
    internal class MethodTestAttribute : Attribute
    {
    }

    /// <summary>
    /// 用来标记方法参数需要保存到文件，以便下次打开测试程序加载之前的参数；
    /// 标注在方法上时，表示方法返回值需要保存的文件。
    /// </summary>
    internal class SaveFlagAttribute : Attribute
    {
    }

    /// <summary>
    /// 360 SDK调用助手
    /// 对360SDK的方法调用进行了进一步封装，适配C#编程风格。
    /// 如果你想直接调用SDK的方法，可以使用<see cref="T:Netpower.Qh360Sdk.Native.Qh360SdkWrapper" />、<see cref="T:Netpower.Qh360Sdk.Native.Qh360Sdkx64" />、<see cref="T:Netpower.Qh360Sdk.Native.Qh360Sdkx86" />
    /// </summary>
    public static class Qh360SdkHelper
    {
        private static bool s_IsInit = false;

        /// <summary>
        /// 创建订单后，这里缓存订单信息；
        /// 自动移除规则：
        /// 1.Uninit后清空；
        /// 2.CancelPay之后移除指定订单；
        /// 3.支付状态改变事件中Handled设置true后移除指定订单。
        /// 注意：移除订单信息后，即使sdk回调了支付状态改变函数，支付状态事件也不会触发
        /// </summary>
        private static readonly Dictionary<uint, Sdk360OrderContext> s_r_Orders = new Dictionary<uint, Sdk360OrderContext>();

        /// <summary>
        /// 支付状态回调
        /// </summary>
        private static readonly SDK360_PAYSTATUS_CALLBACK s_r_PayStatusCallback = PayStatusCallback;

        /// <summary>
        /// 最后一次调用SDK方法时记录的返回值
        /// </summary>
        public static int LastResultCode { get; private set; }

        /// <summary>
        /// 支付状态改变事件
        /// </summary>
        public static event Sdk360PayStatusChangedHandler PayStatusChanged;

        /// <summary>
        /// 初始化360 SDK（异步）
        /// 此方法内部已经做了处理，重复的初始化会被忽略，不重复调用SDK初始化。
        /// </summary>
        /// <param name="envInfo"></param>
        /// <returns></returns>
        [MethodTest]
        public static bool Init([SaveFlag] Qh360SdkEnv envInfo)
        {
            if (s_IsInit)
            {
                return true;
            }
            s_IsInit = (LastResultCode = Qh360SdkWrapper.SDK360_Init(envInfo.ToEnvInfo())) == 0;
            return s_IsInit;
        }

        /// <summary>
        /// 释放SDK资源
        /// </summary>
        /// <returns></returns>
        [MethodTest]
        public static bool Uninit()
        {
            s_IsInit = false;
            s_r_Orders.Clear();
            return (LastResultCode = Qh360SdkWrapper.SDK360_UnInit()) == 0;
        }

        /// <summary>
        /// 创建二维码支付订单
        /// 返回的二维码链接转二维码后扫码支付
        /// </summary>
        [MethodTest]
        public static Qh360SdkOrderResponse CreateQrcodeOrder([SaveFlag] Qh360SdkOrderRequest orderRequest)
        {
            OderResponse fpOrderResponse = default(OderResponse);
            uint key = (uint)(LastResultCode = (int)Qh360SdkWrapper.SDK360_Pay(orderRequest.ToOderRequest(), ref fpOrderResponse, s_r_PayStatusCallback));
            Qh360SdkOrderResponse qh360SdkOrderResponse = new Qh360SdkOrderResponse();
            qh360SdkOrderResponse.CopyFromOderResponse(fpOrderResponse);
            s_r_Orders[key] = new Sdk360OrderContext
            {
                OrderRequest = orderRequest,
                OrderResponse = qh360SdkOrderResponse
            };
            return qh360SdkOrderResponse;
        }

        /// <summary>
        /// 创建二维码支付订单（异步）
        /// 返回的二维码链接转二维码后扫码支付
        /// </summary>
        [MethodTest]
        public static Task<Qh360SdkOrderResponse> CreateQrcodeOrderAsync([SaveFlag] Qh360SdkOrderRequest orderRequest)
        {
            TaskCompletionSource<Qh360SdkOrderResponse> tcs = new TaskCompletionSource<Qh360SdkOrderResponse>();
            GCHandle callback_handle = default(GCHandle);
            SDK360_ORDERRESULT_CALLBACK sDK360_ORDERRESULT_CALLBACK = delegate (uint dwTicket, ref OderResponse fpOderResponse)
            {
                try
                {
                    Qh360SdkOrderResponse qh360SdkOrderResponse = new Qh360SdkOrderResponse();
                    qh360SdkOrderResponse.CopyFromOderResponse(fpOderResponse);
                    s_r_Orders[fpOderResponse.dwTicket] = new Sdk360OrderContext
                    {
                        OrderRequest = orderRequest,
                        OrderResponse = qh360SdkOrderResponse
                    };
                    tcs.SetResult(qh360SdkOrderResponse);
                }
                catch
                {
                }
                finally
                {
                    callback_handle.Free();
                }
            };
            callback_handle = GCHandle.Alloc(sDK360_ORDERRESULT_CALLBACK);
            try
            {
                LastResultCode = (int)Qh360SdkWrapper.SDK360_AsyncPay(orderRequest.ToOderRequest(), sDK360_ORDERRESULT_CALLBACK, s_r_PayStatusCallback);
            }
            catch
            {
                callback_handle.Free();
                throw;
            }
            return tcs.Task;
        }

        /// <summary>
        /// 取消支付
        /// SDK在等待用户支付时，底层会做轮询。取消订单可节省系统资源占用
        /// </summary>
        /// <param name="ticket"></param>
        [MethodTest]
        public static bool CancelPay(uint ticket)
        {
            int num2 = (LastResultCode = Qh360SdkWrapper.SDK360_CancelPay(ticket));
            s_r_Orders.Remove(ticket);
            return num2 == 0;
        }

        private static void PayStatusCallback(uint dwTicket, int iOderStatus, int iPayChanel)
        {
            if (!s_r_Orders.TryGetValue(dwTicket, out var value))
            {
                return;
            }
            try
            {
                Sdk360PayStatusChangedEventArgs sdk360PayStatusChangedEventArgs = new Sdk360PayStatusChangedEventArgs
                {
                    OrderContext = value,
                    Handled = false,
                    PayChannel = (Sdk360PayChannel)iPayChanel,
                    PayStatus = (Sdk360PayStatus)iOderStatus
                };
                Qh360SdkHelper.PayStatusChanged?.Invoke(sdk360PayStatusChangedEventArgs);
                if (sdk360PayStatusChangedEventArgs.Handled)
                {
                    CancelPay(dwTicket);
                }
            }
            catch
            {
            }
        }
    }
}

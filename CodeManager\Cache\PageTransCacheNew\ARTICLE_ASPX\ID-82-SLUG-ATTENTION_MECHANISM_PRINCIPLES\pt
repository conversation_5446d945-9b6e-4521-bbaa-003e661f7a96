﻿"<!DOCTYPE html>\r\n<html xmlns=\"http://www.w3.org/1999/xhtml\" lang=\"pt\">\r\n<head><link rel=\"canonical\" href=\"https://ocr.oldfish.cn/pt/Article.aspx?id=82&slug=attention-mechanism-principles\" /><link rel=\"alternate\" href=\"https://ocr.oldfish.cn/Article.aspx?id=82&slug=attention-mechanism-principles\" hreflang=\"x-default\" />\r\n<link rel=\"alternate\" href=\"https://ocr.oldfish.cn/Article.aspx?id=82&slug=attention-mechanism-principles\" hreflang=\"zh\" />\r\n<link rel=\"alternate\" href=\"https://ocr.oldfish.cn/Article.aspx?id=82&slug=attention-mechanism-principles\" hreflang=\"zh\" />\r\n<link rel=\"alternate\" href=\"https://ocr.oldfish.cn/zh-Hant/Article.aspx?id=82&slug=attention-mechanism-principles\" hreflang=\"zh-tw\" />\r\n<link rel=\"alternate\" href=\"https://ocr.oldfish.cn/yue/Article.aspx?id=82&slug=attention-mechanism-principles\" hreflang=\"zh-yue\" />\r\n<link rel=\"alternate\" href=\"https://ocr.oldfish.cn/en/Article.aspx?id=82&slug=attention-mechanism-principles\" hreflang=\"en\" />\r\n<link rel=\"alternate\" href=\"https://ocr.oldfish.cn/es/Article.aspx?id=82&slug=attention-mechanism-principles\" hreflang=\"es\" />\r\n<link rel=\"alternate\" href=\"https://ocr.oldfish.cn/hi/Article.aspx?id=82&slug=attention-mechanism-principles\" hreflang=\"hi\" />\r\n<link rel=\"alternate\" href=\"https://ocr.oldfish.cn/ar/Article.aspx?id=82&slug=attention-mechanism-principles\" hreflang=\"ar\" />\r\n<link rel=\"alternate\" href=\"https://ocr.oldfish.cn/pt/Article.aspx?id=82&slug=attention-mechanism-principles\" hreflang=\"pt\" />\r\n<link rel=\"alternate\" href=\"https://ocr.oldfish.cn/bn/Article.aspx?id=82&slug=attention-mechanism-principles\" hreflang=\"bn\" />\r\n<link rel=\"alternate\" href=\"https://ocr.oldfish.cn/ru/Article.aspx?id=82&slug=attention-mechanism-principles\" hreflang=\"ru\" />\r\n<link rel=\"alternate\" href=\"https://ocr.oldfish.cn/ja/Article.aspx?id=82&slug=attention-mechanism-principles\" hreflang=\"ja\" />\r\n<link rel=\"alternate\" href=\"https://ocr.oldfish.cn/pa/Article.aspx?id=82&slug=attention-mechanism-principles\" hreflang=\"pa\" />\r\n<link rel=\"alternate\" href=\"https://ocr.oldfish.cn/de/Article.aspx?id=82&slug=attention-mechanism-principles\" hreflang=\"de\" />\r\n<link rel=\"alternate\" href=\"https://ocr.oldfish.cn/fr/Article.aspx?id=82&slug=attention-mechanism-principles\" hreflang=\"fr\" />\r\n<link rel=\"alternate\" href=\"https://ocr.oldfish.cn/mr/Article.aspx?id=82&slug=attention-mechanism-principles\" hreflang=\"mr\" />\r\n<link rel=\"alternate\" href=\"https://ocr.oldfish.cn/te/Article.aspx?id=82&slug=attention-mechanism-principles\" hreflang=\"te\" />\r\n<link rel=\"alternate\" href=\"https://ocr.oldfish.cn/vi/Article.aspx?id=82&slug=attention-mechanism-principles\" hreflang=\"vi\" />\r\n<link rel=\"alternate\" href=\"https://ocr.oldfish.cn/ko/Article.aspx?id=82&slug=attention-mechanism-principles\" hreflang=\"ko\" />\r\n<link rel=\"alternate\" href=\"https://ocr.oldfish.cn/ta/Article.aspx?id=82&slug=attention-mechanism-principles\" hreflang=\"ta\" />\r\n<link rel=\"alternate\" href=\"https://ocr.oldfish.cn/ur/Article.aspx?id=82&slug=attention-mechanism-principles\" hreflang=\"ur\" />\r\n<link rel=\"alternate\" href=\"https://ocr.oldfish.cn/tr/Article.aspx?id=82&slug=attention-mechanism-principles\" hreflang=\"tr\" />\r\n<link rel=\"alternate\" href=\"https://ocr.oldfish.cn/it/Article.aspx?id=82&slug=attention-mechanism-principles\" hreflang=\"it\" />\r\n<link rel=\"alternate\" href=\"https://ocr.oldfish.cn/gu/Article.aspx?id=82&slug=attention-mechanism-principles\" hreflang=\"gu\" />\r\n<link rel=\"alternate\" href=\"https://ocr.oldfish.cn/pl/Article.aspx?id=82&slug=attention-mechanism-principles\" hreflang=\"pl\" />\r\n<link rel=\"alternate\" href=\"https://ocr.oldfish.cn/uk/Article.aspx?id=82&slug=attention-mechanism-principles\" hreflang=\"uk\" />\r\n<link rel=\"alternate\" href=\"https://ocr.oldfish.cn/ms/Article.aspx?id=82&slug=attention-mechanism-principles\" hreflang=\"ms\" />\r\n<link rel=\"alternate\" href=\"https://ocr.oldfish.cn/id/Article.aspx?id=82&slug=attention-mechanism-principles\" hreflang=\"id\" />\r\n<link rel=\"alternate\" href=\"https://ocr.oldfish.cn/ml/Article.aspx?id=82&slug=attention-mechanism-principles\" hreflang=\"ml\" />\r\n<link rel=\"alternate\" href=\"https://ocr.oldfish.cn/kn/Article.aspx?id=82&slug=attention-mechanism-principles\" hreflang=\"kn\" />\r\n<link rel=\"alternate\" href=\"https://ocr.oldfish.cn/fa/Article.aspx?id=82&slug=attention-mechanism-principles\" hreflang=\"fa\" />\r\n<link rel=\"alternate\" href=\"https://ocr.oldfish.cn/nl/Article.aspx?id=82&slug=attention-mechanism-principles\" hreflang=\"nl\" />\r\n<link rel=\"alternate\" href=\"https://ocr.oldfish.cn/th/Article.aspx?id=82&slug=attention-mechanism-principles\" hreflang=\"th\" />\r\n<link rel=\"alternate\" href=\"https://ocr.oldfish.cn/sw/Article.aspx?id=82&slug=attention-mechanism-principles\" hreflang=\"sw\" />\r\n<link rel=\"alternate\" href=\"https://ocr.oldfish.cn/ro/Article.aspx?id=82&slug=attention-mechanism-principles\" hreflang=\"ro\" />\r\n<link rel=\"alternate\" href=\"https://ocr.oldfish.cn/my/Article.aspx?id=82&slug=attention-mechanism-principles\" hreflang=\"my\" />\r\n<link rel=\"alternate\" href=\"https://ocr.oldfish.cn/or/Article.aspx?id=82&slug=attention-mechanism-principles\" hreflang=\"or\" />\r\n<link rel=\"alternate\" href=\"https://ocr.oldfish.cn/he/Article.aspx?id=82&slug=attention-mechanism-principles\" hreflang=\"he\" />\r\n<link rel=\"alternate\" href=\"https://ocr.oldfish.cn/am/Article.aspx?id=82&slug=attention-mechanism-principles\" hreflang=\"am\" />\r\n<link rel=\"alternate\" href=\"https://ocr.oldfish.cn/fil/Article.aspx?id=82&slug=attention-mechanism-principles\" hreflang=\"fil\" />\r\n<link rel=\"alternate\" href=\"https://ocr.oldfish.cn/sv/Article.aspx?id=82&slug=attention-mechanism-principles\" hreflang=\"sv\" />\r\n<link rel=\"alternate\" href=\"https://ocr.oldfish.cn/el/Article.aspx?id=82&slug=attention-mechanism-principles\" hreflang=\"el\" />\r\n<link rel=\"alternate\" href=\"https://ocr.oldfish.cn/cs/Article.aspx?id=82&slug=attention-mechanism-principles\" hreflang=\"cs\" />\r\n<link rel=\"alternate\" href=\"https://ocr.oldfish.cn/hu/Article.aspx?id=82&slug=attention-mechanism-principles\" hreflang=\"hu\" />\r\n<link rel=\"alternate\" href=\"https://ocr.oldfish.cn/be/Article.aspx?id=82&slug=attention-mechanism-principles\" hreflang=\"be\" />\r\n<link rel=\"alternate\" href=\"https://ocr.oldfish.cn/si/Article.aspx?id=82&slug=attention-mechanism-principles\" hreflang=\"si\" />\r\n<link rel=\"alternate\" href=\"https://ocr.oldfish.cn/ne/Article.aspx?id=82&slug=attention-mechanism-principles\" hreflang=\"ne\" />\r\n<link rel=\"alternate\" href=\"https://ocr.oldfish.cn/km/Article.aspx?id=82&slug=attention-mechanism-principles\" hreflang=\"km\" />\r\n<link rel=\"alternate\" href=\"https://ocr.oldfish.cn/sk/Article.aspx?id=82&slug=attention-mechanism-principles\" hreflang=\"sk\" />\r\n<link rel=\"alternate\" href=\"https://ocr.oldfish.cn/bg/Article.aspx?id=82&slug=attention-mechanism-principles\" hreflang=\"bg\" />\r\n<link rel=\"alternate\" href=\"https://ocr.oldfish.cn/fr-ca/Article.aspx?id=82&slug=attention-mechanism-principles\" hreflang=\"fr-ca\" />\r\n<link rel=\"alternate\" href=\"https://ocr.oldfish.cn/ha/Article.aspx?id=82&slug=attention-mechanism-principles\" hreflang=\"ha\" />\r\n<link rel=\"alternate\" href=\"https://ocr.oldfish.cn/yo/Article.aspx?id=82&slug=attention-mechanism-principles\" hreflang=\"yo\" />\r\n<link rel=\"alternate\" href=\"https://ocr.oldfish.cn/ig/Article.aspx?id=82&slug=attention-mechanism-principles\" hreflang=\"ig\" />\r\n<link rel=\"alternate\" href=\"https://ocr.oldfish.cn/ku/Article.aspx?id=82&slug=attention-mechanism-principles\" hreflang=\"ku\" />\r\n<link rel=\"alternate\" href=\"https://ocr.oldfish.cn/rw/Article.aspx?id=82&slug=attention-mechanism-principles\" hreflang=\"rw\" />\r\n<link rel=\"alternate\" href=\"https://ocr.oldfish.cn/ca/Article.aspx?id=82&slug=attention-mechanism-principles\" hreflang=\"ca\" />\r\n<link rel=\"alternate\" href=\"https://ocr.oldfish.cn/da/Article.aspx?id=82&slug=attention-mechanism-principles\" hreflang=\"da\" />\r\n<link rel=\"alternate\" href=\"https://ocr.oldfish.cn/fi/Article.aspx?id=82&slug=attention-mechanism-principles\" hreflang=\"fi\" />\r\n<link rel=\"alternate\" href=\"https://ocr.oldfish.cn/nb/Article.aspx?id=82&slug=attention-mechanism-principles\" hreflang=\"nb\" />\r\n<link rel=\"alternate\" href=\"https://ocr.oldfish.cn/hr/Article.aspx?id=82&slug=attention-mechanism-principles\" hreflang=\"hr\" />\r\n<link rel=\"alternate\" href=\"https://ocr.oldfish.cn/sr-Cyrl/Article.aspx?id=82&slug=attention-mechanism-principles\" hreflang=\"sr-cyrl\" />\r\n<link rel=\"alternate\" href=\"https://ocr.oldfish.cn/sr-Latn/Article.aspx?id=82&slug=attention-mechanism-principles\" hreflang=\"sr-latn\" />\r\n<link rel=\"alternate\" href=\"https://ocr.oldfish.cn/sq/Article.aspx?id=82&slug=attention-mechanism-principles\" hreflang=\"sq\" />\r\n<link rel=\"alternate\" href=\"https://ocr.oldfish.cn/so/Article.aspx?id=82&slug=attention-mechanism-principles\" hreflang=\"so\" />\r\n<link rel=\"alternate\" href=\"https://ocr.oldfish.cn/zu/Article.aspx?id=82&slug=attention-mechanism-principles\" hreflang=\"zu\" />\r\n<link rel=\"alternate\" href=\"https://ocr.oldfish.cn/ka/Article.aspx?id=82&slug=attention-mechanism-principles\" hreflang=\"ka\" />\r\n<meta http-equiv=\"Content-Type\" content=\"text/html; charset=UTF-8\" /><meta http-equiv=\"X-UA-Compatible\" content=\"IE=Edge,chrome=1\" /><meta name=\"renderer\" content=\"webkit\" /><meta name=\"viewport\" content=\"width=device-width, initial-scale=1.0, maximum-scale=5.0\" /><link rel=\"icon\" href=\"/favicon.ico\" type=\"image/x-icon\" /><meta name=\"robots\" content=\"all\" /><link rel=\"preconnect\" href=\"https://scm-file-new-1301864755.cos.ap-beijing.myqcloud.com\" /><link rel=\"preconnect\" href=\"https://cdn.oldfish.cn\" /><link rel=\"dns-prefetch\" href=\"https://hm.baidu.com\" /><link rel=\"dns-prefetch\" href=\"https://cdn.oldfish.cn\" /><link rel=\"preload\" href=\"/site/css/site.css\" as=\"style\" />\r\n<script>function toArray(l){var a=[];for(var i=0;i<(l?l.length:0);i++)a.push(l[i]);return a;}function forEach(l,f){for(var i=0,n=(l?l.length:0);i<n;i++)f(l[i],i);}function hasClass(e,c){if(!e||!c)return false;return(' '+e.className+' ').indexOf(' '+c+' ')>-1;}function addClass(e,c){if(!e||!c||hasClass(e,c))return;e.className=(e.className?e.className+' ':'')+c;}function removeClass(e,c){if(!e||!c)return;var u=' '+e.className+' ';while(u.indexOf(' '+c+' ')>-1){u=u.replace(' '+c+' ',' ');}e.className=u.replace(/^\\s+|\\s+$/g,'');}function toggleClass(e,c){if(!e||!c)return;if(hasClass(e,c))removeClass(e,c);else addClass(e,c);}function matchesSelector(e,s){if(!e||e.nodeType!==1)return false;if(e.matches)return e.matches(s);var p=e.parentNode||document;var m=toArray(p.querySelectorAll(s));for(var i=0;i<m.length;i++)if(m[i]===e)return true;return false;}function closest(e,s){var n=e;while(n&&n.nodeType===1){if(matchesSelector(n,s))return n;n=n.parentNode;}return null;}function safeRemove(e){if(e&&e.parentNode)e.parentNode.removeChild(e);}function qa(s,r){return toArray((r||document).querySelectorAll(s));}function q(s,r){return(r||document).querySelector(s);}function xhrGet(u,o,e){try{var x=new XMLHttpRequest();x.open('GET',u,true);x.onreadystatechange=function(){if(x.readyState===4){if(x.status>=200&&x.status<300)o&&o(x.responseText);else e&&e(x);}};x.send(null);}catch(r){e&&e(r);}}function getScrollY(){return window.pageYOffset||document.documentElement.scrollTop||document.body.scrollTop||0;}function getOrigin(){return window.location.protocol+'//'+window.location.host;}</script>\r\n<script>var isWebP=(function(){try{return document.createElement('canvas').toDataURL('image/webp').indexOf('data:image/webp')===0;}catch(e){return false;}})();document.addEventListener('DOMContentLoaded',function(){var c=document.getElementById('user-login-container');if(c){var u=c.getAttribute('data-url');if(u){xhrGet(u,function(h){c.innerHTML=h;forEach(qa('a',c),function(l){l.style.display='inline-block';l.style.marginLeft='10px';});});}}});</script>\r\n    \n    \n\n    <meta name=\"description\" content=\"Aprofunde-se nos princípios matemáticos dos mecanismos de atenção, atenção com várias cabeças, mecanismos de autoatenção e aplicações específicas em OCR. Análise detalhada de cálculos de peso de atenção, codificação de posição e estratégias de otimização de desempenho.\" />\n    <meta name=\"keywords\" content=\"Mecanismo de atenção, atenção de várias cabeças, autoatenção, codificação de posição, atenção cruzada, atenção esparsa, OCR, Transformador, reconhecimento de texto OCR, imagem para texto, tecnologia OCR\" />\n    <meta property=\"og:title\" content=\"【Série OCR de Aprendizado Profundo·5】Princípio e Implementação do Mecanismo de Atenção\" />\n    <meta property=\"og:description\" content=\"Aprofunde-se nos princípios matemáticos dos mecanismos de atenção, atenção com várias cabeças, mecanismos de autoatenção e aplicações específicas em OCR. Análise detalhada de cálculos de peso de atenção, codificação de posição e estratégias de otimização de desempenho.\" />\n    <meta property=\"og:type\" content=\"article\" />\n    <meta property=\"og:url\" content=\"https://ocr.oldfish.cn/Article.aspx?id=82&slug=attention-mechanism-principles&lang=zh-Hans&_translation_source=true\" />\n    <meta property=\"og:site_name\" content=\"Assistente de reconhecimento de texto OCR\" />\n    <meta name=\"twitter:card\" content=\"summary\" />\n    <meta name=\"twitter:title\" content=\"【Série OCR de Aprendizado Profundo·5】Princípio e Implementação do Mecanismo de Atenção\" />\n    <meta name=\"twitter:description\" content=\"Aprofunde-se nos princípios matemáticos dos mecanismos de atenção, atenção com várias cabeças, mecanismos de autoatenção e aplicações específicas em OCR. Análise detalhada de cálculos de peso de atenção, codificação de posição e estratégias de otimização de desempenho.\" />\n\n    \n    <script type=\"application/ld+json\">\n    {\n        \"@context\": \"https://schema.org\",\n        \"@type\": \"Article\",\n        \"headline\": \"【Deep Learning OCR Series 5] Princípio e Implementação do Mecanismo de Atenção\",\n        \"description\": \"Aprofunde-se nos princípios matemáticos dos mecanismos de atenção, atenção com várias cabeças, mecanismos de autoatenção e aplicações específicas em OCR. Análise detalhada de cálculos de peso de atenção, codificação de posição e estratégias de otimização de desempenho。\",\n        \"publisher\": {\n            \"@type\": \"Organization\",\n            \"name\": \"Equipe de assistente de reconhecimento de texto OCR\"\n        },\n        \"datePublished\": \"2025-08-19T06:32:10Z\",\n        \"dateModified\": \"2025-08-19T06:32:10Z\",\n        \"mainEntityOfPage\": {\n            \"@type\": \"WebPage\",\n            \"@id\": \"https://ocr.oldfish.cn/Article.aspx?id=82&slug=attention-mechanism-principles&lang=zh-Hans&_translation_source=true\"\n        }\n    }\n    </script>\n    \n    <style>\n        .reading-progress { position: fixed; top: 0; left: 0; width: 0%; height: 3px; background: linear-gradient(90deg, #1764ff, #0d47a1); z-index: 9999; transition: width 0.2s ease; box-shadow: 0 1px 3px rgba(23, 100, 255, 0.3); }\n        .home-container.root { overflow: visible !important; }\n        .article-detail-container { background: #f5f7fc; min-height: calc(100vh - 60px); padding: 80px 0 60px; position: relative; overflow: visible; }\n        .article-detail-container .row { margin: 0 -12px; align-items: flex-start !important; min-height: 100vh !important; }\n        .article-detail-container .row > [class*=\"col-\"] { padding: 0 12px; box-sizing: border-box; min-width: 0; }\n        .article-detail-container .row > .col-lg-8, .article-detail-container .row > .col-xl-9 { overflow: hidden; }\n        .article-detail-container .row > .col-lg-4, .article-detail-container .row > .col-xl-3 { overflow: visible !important; height: 100% !important; min-height: 100% !important; position: static !important; display: block !important; transform: none !important; will-change: auto !important; contain: none !important; perspective: none !important; filter: none !important; }\n        .article-detail { background: #fff; border-radius: 8px; padding: 1.5rem; margin-bottom: 2rem; box-shadow: 0 2px 10px rgba(0,0,0,0.1); width: 100%; box-sizing: border-box; }\n        .article-title { font-size: 1.7rem; color: #1a1c1f; line-height: 1.3; margin-bottom: 1.5rem; font-weight: 600; }\n        .article-meta { display: flex; align-items: center; flex-wrap: nowrap; gap: 1.2rem; color: #6c757d; font-size: 0.88rem; margin-bottom: 2rem; padding: 1.2rem 1.4rem; background: linear-gradient(135deg, #f8f9fa 0%, #ffffff 100%); border-radius: 12px; border-left: 4px solid #1764ff; box-shadow: 0 2px 8px rgba(0,0,0,0.06); overflow-x: hidden; transition: all 0.3s ease; }\n        .article-meta:hover { box-shadow: 0 4px 16px rgba(0,0,0,0.1); transform: translateY(-1px); }\n        .article-meta .meta-item { display: flex; align-items: center; gap: 0.25rem; font-weight: 500; color: #495057; white-space: nowrap; flex-shrink: 1; transition: color 0.2s ease; cursor: default; }\n        .article-meta .meta-item:hover { color: #1764ff; }\n        .article-meta .meta-item:hover .meta-icon { transform: scale(1.1); filter: drop-shadow(0 2px 6px rgba(13, 71, 161, 0.25)); }\n        .meta-icon { display: inline-block; font-size: 16px; flex-shrink: 0; vertical-align: middle; transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1); margin-right: 4px; opacity: 0.9; }\n        .meta-item:hover .meta-icon { opacity: 1; }\n        .meta-item:hover { color: #1764ff; }\n        .meta-item { position: relative; }\n        .meta-item::after { content: ''; position: absolute; bottom: -2px; left: 0; width: 0; height: 2px; background: linear-gradient(90deg, #1764ff, #00d4ff); transition: width 0.3s ease; }\n        .meta-item:hover::after { width: 100%; }\n        .article-category, .read-time { display: inline-block; padding: 6px 12px; border-radius: 16px; font-size: 0.8rem; font-weight: 600; }\n        .article-category { background: linear-gradient(135deg, #1764ff, #0d47a1); color: white; text-decoration: none; text-transform: uppercase; letter-spacing: 0.5px; transition: all 0.3s ease; }\n        .article-category:hover { background: linear-gradient(135deg, #0d47a1, #1764ff); color: white; text-decoration: none; transform: translateY(-1px); box-shadow: 0 4px 12px rgba(23, 100, 255, 0.3); }\n        .read-time { background: #e3f2fd; color: #1565c0; border: 1px solid #90caf9; }\n        .article-tags { margin: 20px 0 1.5rem; }\n        .article-tags .tag { display: inline-block; background: #1764ff; color: #fff; padding: 4px 12px; border-radius: 12px; font-size: 0.8rem; margin: 0 8px 4px 0; }\n        .article-summary { background: #e3f2fd; border-left: 4px solid #1764ff; padding: 1rem 1.5rem; margin-bottom: 2rem; border-radius: 0 4px 4px 0; }\n        .article-content { line-height: 1.8; font-size: 16px; color: #333; word-wrap: break-word; }\n        .article-content h1, .article-content h2, .article-content h3, .article-content h4, .article-content h5, .article-content h6 { margin: 2rem 0 1rem; font-weight: 600; line-height: 1.25; color: #1a1c1f; }\n        .article-content h1 { font-size: 2rem; border-bottom: 2px solid #e9ecef; padding-bottom: 0.5rem; }\n        .article-content h2 { font-size: 1.5rem; border-bottom: 1px solid #e9ecef; padding-bottom: 0.3rem; }\n        .article-content h3 { font-size: 1.25rem; }\n        .article-content h4 { font-size: 1.1rem; }\n        .article-content h5, .article-content h6 { font-size: 1rem; }\n        .article-content ul, .article-content ol { margin: 1rem 0; padding-left: 2rem; }\n        .article-content ul ul, .article-content ol ol, .article-content ul ol, .article-content ol ul { margin: 0.5rem 0; }\n        .article-content blockquote { margin: 1rem 0; padding: 0.5rem 1rem; border-left: 4px solid #1764ff; background-color: #f8f9fa; color: #6c757d; font-style: italic; }\n        .article-content pre { background-color: #f8f9fa; border: 1px solid #e9ecef; border-radius: 6px; padding: 1rem; margin: 1rem 0; overflow-x: auto; font-family: 'Courier New', Courier, monospace; font-size: 0.9em; line-height: 1.4; }\n        .article-content pre code { background: none; border: none; padding: 0; color: inherit; }\n        .article-content p { margin: 1rem 0; }\n        .article-content p:first-child { margin-top: 0; }\n        .article-content p:last-child { margin-bottom: 0; }\n        .article-footer { margin-top: 4rem; padding: 2.5rem; background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%); border-radius: 12px; border: 1px solid #e9ecef; position: relative; overflow: hidden; }\n        .article-footer::before { content: ''; position: absolute; top: 0; left: 0; right: 0; height: 3px; background: linear-gradient(90deg, #1764ff, #17a2b8, #28a745); }\n        .article-share h6 { color: #495057; font-weight: 600; margin-bottom: 1.5rem; font-size: 16px; }\n        .share-buttons { display: flex; gap: 12px; flex-wrap: wrap; }\n        .share-buttons .btn { padding: 10px 20px; border-radius: 25px; font-weight: 500; font-size: 14px; border: none; transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1); box-shadow: 0 2px 8px rgba(0,0,0,0.1); display: inline-flex; align-items: center; gap: 8px; text-decoration: none; }\n        .btn-weibo { background: linear-gradient(135deg, #e6162d, #c41230); color: white; }\n        .btn-copy { background: #1764ff; color: white; }\n        .btn-print { background: linear-gradient(135deg, #17a2b8, #138496); color: white; }\n        .btn-weibo:hover { background: linear-gradient(135deg, #c41230, #a00e26); box-shadow: 0 4px 16px rgba(230, 22, 45, 0.3); }\n        .btn-copy:hover { background: #0d47a1; box-shadow: 0 4px 16px rgba(23, 100, 255, 0.3); }\n        .btn-print:hover { background: linear-gradient(135deg, #138496, #117a8b); box-shadow: 0 4px 16px rgba(23, 162, 184, 0.3); }\n        .btn-weibo:hover, .btn-copy:hover, .btn-print:hover { transform: translateY(-2px); color: white; }\n        .article-actions { display: flex; justify-content: flex-end; align-items: center; }\n        .related-articles { margin-top: 3rem; }\n        .related-article-item { background: #fff; border-radius: 8px; padding: 1.5rem; margin-bottom: 1rem; box-shadow: 0 2px 8px rgba(0,0,0,0.1); transition: transform 0.3s ease; }\n        .related-article-item:hover { transform: translateY(-2px); }\n        .sidebar-card { background: linear-gradient(135deg, rgba(255,255,255,0.9) 0%, rgba(255,255,255,0.8) 100%); backdrop-filter: blur(20px); border: 1px solid rgba(255,255,255,0.3); border-radius: 16px; padding: 1rem; margin-bottom: 0.5rem; box-shadow: 0 4px 20px rgba(0,0,0,0.08), inset 0 1px 0 rgba(255,255,255,0.2); overflow: visible; box-sizing: border-box; width: 100%; z-index: 10; }\n        .sidebar-card.fixed { box-shadow: 0 8px 32px rgba(0,0,0,0.12); }\n        .sidebar-card::before { content: ''; position: absolute; top: 0; left: 0; right: 0; height: 3px; background: linear-gradient(90deg, #00d4ff, #1764ff, #9c27b0); border-radius: 20px 20px 0 0; }\n        .sidebar-card h5 { color: #1a202c; padding-bottom: 0.8rem; border-bottom: 2px solid rgba(23, 100, 255, 0.2); font-weight: 700; font-size: 1.1rem; }\n        .sidebar { min-height: 100vh; position: relative !important; display: block !important; flex-direction: column !important; gap: 1rem; }\n        .toc-card { flex: 0 1 auto; max-height: 60vh; overflow-y: auto; }\n        .sidebar-card:not(.toc-card) { flex: 1 1 auto; overflow: visible; }\n        .recommended-item { padding: 0.75rem 0; border-bottom: 1px solid rgba(0,0,0,0.05); }\n        .recommended-item:last-child { border-bottom: none; }\n        .recommended-item h6 { margin: 0 0 0.5rem 0; font-size: 0.9rem; line-height: 1.4; }\n        .recommended-item h6 a { color: #1a1c1f; text-decoration: none; transition: color 0.3s ease; }\n        .recommended-item h6 a:hover { color: #1764ff; }\n        .recommended-item .text-muted { font-size: 0.75rem; margin: 0; }\n        .toc-list { list-style: none; padding: 0; margin: 0; max-height: none; overflow-y: visible; overflow-x: hidden; }\n        .toc-list::-webkit-scrollbar { width: 4px; }\n        .toc-list::-webkit-scrollbar-track { background: rgba(0,0,0,0.1); border-radius: 2px; }\n        .toc-list::-webkit-scrollbar-thumb { background: linear-gradient(135deg, #00d4ff, #1764ff); border-radius: 2px; }\n        .toc-list::-webkit-scrollbar-thumb:hover { background: linear-gradient(135deg, #1764ff, #0d47a1); }\n        .toc-list a { color: #4a5568; text-decoration: none; font-size: 13px; font-weight: 500; display: block; padding: 8px 12px; border-radius: 8px; transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1); position: relative; margin-bottom: 4px; border: 1px solid transparent; }\n        .toc-list a::before { content: ''; position: absolute; left: 0; top: 0; bottom: 0; width: 3px; background: linear-gradient(135deg, #00d4ff, #1764ff); border-radius: 0 12px 12px 0; transform: scaleY(0); transition: transform 0.3s ease; }\n        .toc-list a:hover { color: #1764ff; background: linear-gradient(135deg, rgba(0, 212, 255, 0.1), rgba(23, 100, 255, 0.1)); border-color: rgba(23, 100, 255, 0.2); transform: translateX(8px); box-shadow: 0 4px 12px rgba(0, 212, 255, 0.2); }\n        .toc-list a:hover::before, .toc-list a.active::before { transform: scaleY(1); }\n        .toc-list a.active { color: #1764ff; background: linear-gradient(135deg, rgba(0, 212, 255, 0.15), rgba(23, 100, 255, 0.15)); font-weight: 700; border-color: rgba(23, 100, 255, 0.3); transform: translateX(4px); box-shadow: 0 6px 16px rgba(0, 212, 255, 0.25); }\n        .toc-number { color: #1764ff; font-weight: 600; margin-right: 0.5rem; font-size: 0.9em; }\n        .toc-text { flex: 1; }\n        .toc-level-1 { font-weight: 700; font-size: 15px; }\n        .toc-level-2 { font-size: 14px; }\n        .toc-level-3 { font-size: 13px; opacity: 0.9; }\n        .toc-level-4, .toc-level-5, .toc-level-6 { font-size: 12px; opacity: 0.8; }\n        .article-content .formatted-list { margin: 1.5rem 0; padding-left: 0; list-style: none; }\n        .article-content .formatted-list.ordered { counter-reset: list-counter; }\n        .article-content .formatted-list.ordered li { counter-increment: list-counter; position: relative; margin: 1rem 0; padding: 1rem 1rem 1rem 3rem; background: linear-gradient(135deg, rgba(23, 100, 255, 0.03), rgba(0, 212, 255, 0.03)); border-left: 4px solid #1764ff; border-radius: 0 8px 8px 0; transition: all 0.3s ease; }\n        .article-content .formatted-list.ordered li::before { content: counter(list-counter); position: absolute; left: 0.8rem; top: 1rem; background: linear-gradient(135deg, #1764ff, #00d4ff); color: white; width: 1.5rem; height: 1.5rem; border-radius: 50%; display: flex; align-items: center; justify-content: center; font-weight: 600; font-size: 0.9rem; }\n        .article-content .formatted-list.ordered li:hover { background: linear-gradient(135deg, rgba(23, 100, 255, 0.06), rgba(0, 212, 255, 0.06)); transform: translateX(4px); box-shadow: 0 2px 8px rgba(23, 100, 255, 0.15); }\n        .article-content .formatted-list.unordered li { position: relative; margin: 0.8rem 0; padding: 0.8rem 1rem 0.8rem 2.5rem; background: linear-gradient(135deg, rgba(40, 167, 69, 0.03), rgba(25, 135, 84, 0.03)); border-left: 4px solid #28a745; border-radius: 0 6px 6px 0; transition: all 0.3s ease; }\n        .article-content .formatted-list.unordered li::before { content: \"•\"; position: absolute; left: 1rem; top: 0.8rem; color: #28a745; font-size: 1.2rem; font-weight: bold; }\n        .article-content .formatted-list.unordered li:hover { background: linear-gradient(135deg, rgba(40, 167, 69, 0.06), rgba(25, 135, 84, 0.06)); transform: translateX(4px); box-shadow: 0 2px 8px rgba(40, 167, 69, 0.15); }\n        .article-content .list-title { color: #1764ff; font-weight: 600; display: inline-block; margin-bottom: 0.3rem; }\n        .article-content .list-content { color: #2c3e50; line-height: 1.6; display: block; }\n        .article-content .inline-code { background: #f8f9fa; border: 1px solid #e9ecef; border-radius: 4px; padding: 2px 6px; font-family: 'Consolas', 'Monaco', 'Courier New', monospace; font-size: 0.9em; color: #e83e8c; }\n        .article-content .label-item { margin: 0.8rem 0; }\n        .article-content .label { color: #1764ff; font-weight: 600; }\n        .article-content .section-label { color: #2c3e50; font-weight: 600; margin: 1rem 0 0.5rem 0; font-size: 1.1em; border-left: 4px solid #1764ff; padding-left: 0.8rem; }\n        .article-content p, .article-content .content-paragraph { margin: 1.2rem 0; line-height: 1.8; text-align: justify; color: #2c3e50; font-size: 1rem; }\n        .article-content .content-paragraph { text-indent: 0; margin: 1rem 0; }\n        .article-content .indent-paragraph { text-indent: 2em; margin: 1.2rem 0; }\n        .article-content .text-indent-1 { text-indent: 2em; display: block; }\n        .article-content .text-indent-2 { text-indent: 4em; display: block; }\n        .article-content .text-indent-3 { text-indent: 6em; display: block; }\n        .article-content .network-config { background: linear-gradient(135deg, #f8f9fa, #e9ecef); border: 1px solid #dee2e6; border-radius: 6px; padding: 0.8rem 1rem; margin: 0.5rem 0; font-family: 'Consolas', 'Monaco', 'Courier New', monospace; font-size: 0.9rem; box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05); }\n        .article-content .config-label { color: #1764ff; font-weight: 600; }\n        .article-content .config-desc { color: #495057; }\n        .article-content .algorithm-step { background: linear-gradient(135deg, rgba(23, 100, 255, 0.05), rgba(0, 212, 255, 0.05)); border-left: 4px solid #1764ff; padding: 0.8rem 1rem; margin: 0.5rem 0; border-radius: 0 6px 6px 0; transition: all 0.3s ease; }\n        .article-content .step-number { color: #1764ff; font-weight: 700; margin-right: 0.5rem; }\n        .article-content .step-desc { color: #2c3e50; line-height: 1.5; }\n        .article-content table { width: 100%; border-collapse: collapse; margin: 1.5rem 0; background: white; border-radius: 8px; overflow: hidden; box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1); }\n        .article-content table th { background: linear-gradient(135deg, #1764ff, #00d4ff); color: white; padding: 1rem; text-align: left; font-weight: 600; }\n        .article-content table td { padding: 0.8rem 1rem; border-bottom: 1px solid #e9ecef; }\n        .article-content table tr:hover { background: rgba(23, 100, 255, 0.05); }\n        .article-content strong { color: #1764ff; font-weight: 600; }\n        .article-content em { color: #6c757d; font-style: italic; }\n        .article-content a { color: #1764ff; text-decoration: none; border-bottom: 1px solid transparent; transition: all 0.3s ease; }\n        .article-content a:hover { border-bottom-color: #1764ff; color: #0056b3; }\n        .article-content .definition-item { background: linear-gradient(135deg, rgba(40, 167, 69, 0.05), rgba(25, 135, 84, 0.05)); border-left: 4px solid #28a745; padding: 1rem; margin: 1rem 0; border-radius: 0 8px 8px 0; }\n        .article-content .definition-label { color: #28a745; font-weight: 600; display: block; margin-bottom: 0.5rem; }\n        .article-content .definition-content { color: #2c3e50; line-height: 1.6; }\n        .article-content .note-item { background: linear-gradient(135deg, rgba(255, 193, 7, 0.1), rgba(255, 193, 7, 0.05)); border: 1px solid rgba(255, 193, 7, 0.3); border-radius: 8px; padding: 1rem; margin: 1rem 0; position: relative; }\n        .article-content .note-item::before { content: \"💡\"; position: absolute; left: 1rem; top: 1rem; font-size: 1.2em; }\n        .article-content .note-label { color: #856404; font-weight: 600; margin-left: 2rem; }\n        .article-content .note-content { color: #2c3e50; line-height: 1.6; margin-left: 2rem; display: block; margin-top: 0.3rem; }\n        .article-content .math-formula { background: #f8f9fa; border: 1px solid #e9ecef; border-radius: 6px; padding: 1rem; margin: 1rem 0; text-align: center; font-family: 'Times New Roman', serif; font-size: 1.1em; color: #2c3e50; }\n        @media (min-width: 1400px) { .article-content { font-size: 17px; line-height: 1.8; } .article-detail { padding: 2.5rem; } }\n        @media (max-width: 1199px) and (min-width: 992px) { .article-detail { padding: 1.5rem; } .sidebar-card { padding: 1rem; } .toc-list a { font-size: 12px; padding: 6px 8px; } }\n        @media (max-width: 768px) {\n            .article-detail-container { padding: 60px 15px; }\n            .article-content .network-config, .article-content .algorithm-step, .article-content .definition-item, .article-content .note-item { margin: 0.5rem -1rem; border-radius: 0; }\n            .article-content .note-item::before { left: 0.5rem; }\n            .article-content .note-label, .article-content .note-content { margin-left: 1.5rem; }\n            .article-detail { padding: 1.5rem; }\n            .article-title { font-size: 1.6rem; line-height: 1.3; }\n            .article-meta { flex-wrap: wrap; gap: 1rem; padding: 1rem; font-size: 0.8rem; }\n            .article-meta .meta-item { flex-shrink: 1; }\n            .meta-icon { font-size: 14px; margin-right: 3px; }\n            .article-content { font-size: 15px; }\n            .article-content h1 { font-size: 1.6rem; }\n            .article-content h2 { font-size: 1.4rem; }\n            .article-content h3 { font-size: 1.2rem; }\n            .article-content h4 { font-size: 1.1rem; }\n            .article-content pre { padding: 0.75rem; font-size: 0.85em; overflow-x: auto; }\n            .article-content code { font-size: 0.85em; padding: 1px 4px; }\n            .sidebar { margin-top: 2rem; }\n            .sidebar-card { position: static; }\n            .toc-card { max-height: none; overflow-y: visible; }\n            .share-buttons { flex-direction: column; gap: 0.5rem; }\n            .share-buttons .btn { width: 100%; justify-content: center; }\n        }\n        @media print { body * { visibility: hidden; } .article-detail, .article-detail * { visibility: visible; } .article-detail { position: absolute; left: 0; top: 0; width: 100%; background: white; box-shadow: none; border: none; margin: 0; padding: 20px; } .article-meta { border: none; background: none; padding: 0; margin-bottom: 20px; } .article-footer, .reading-progress, .back-to-top { display: none !important; } .article-content { font-size: 12pt; line-height: 1.6; } .article-content h1, .article-content h2, .article-content h3, .article-content h4, .article-content h5, .article-content h6 { page-break-after: avoid; margin-top: 20pt; margin-bottom: 10pt; } .article-content pre, .article-content blockquote { page-break-inside: avoid; } }\n    </style>\n\n<script type=\"application/ld+json\">{\"@context\":\"https://schema.org\",\"@type\":\"BreadcrumbList\",\"itemListElement\":[{\"@type\":\"ListItem\",\"position\":1,\"name\":\"Casa\",\"item\":\"https://ocr.oldfish.cn/default.aspx\"},{\"@type\":\"ListItem\",\"position\":2,\"name\":\"Artigos técnicos\",\"item\":\"https://ocr.oldfish.cn/Articles.aspx\"},{\"@type\":\"ListItem\",\"position\":3,\"name\":\"Detalhes do artigo\",\"item\":\"https://ocr.oldfish.cn/Article.aspx?id=82&slug=attention-mechanism-principles&lang=zh-Hans&_translation_source=true\"}]}</script>\n<link rel=\"stylesheet\" href=\"/site/css/site.css\" type=\"text/css\" /><title>【Série OCR de Aprendizado Profundo·5】Princípio e Implementação do Mecanismo de Atenção</title><meta http-equiv=\"Content-Language\" content=\"pt\" />\n</head>\r\n<body>\r\n    <div class=\"home-container root\">\r\n        <header class=\"v4_header_pc\">\r\n            <div class=\"header_pc_left\">\r\n                <a href=\"Default.aspx\" title=\"Início | Reconhecimento de texto inteligente AI\" class=\"pc-logo-wrap ml-4 mr-5\">\r\n                    <picture>\r\n                        <source srcset=\"site/image/logo-s.png.webp\" type=\"image/webp\" />\r\n                        <img src=\"site/image/logo-s.png\" class=\"pc-logo\" style=\"height: 100%\" alt=\"Logotipo do site oficial do assistente de reconhecimento de texto OCR - Plataforma de reconhecimento de texto inteligente AI\" />\r\n                    </picture>\r\n                    <span style=\"font-size: 18px; font-weight: bold; color: #333;\">Assistente de reconhecimento de texto OCR</span>\r\n                </a>\r\n                <ul class=\"top-nav\" role=\"navigation\" aria-label=\"Navegação principal\">\r\n                    <li><a class=\"color-default\" href=\"Default.aspx\" title=\"Página inicial do Assistente de reconhecimento de texto OCR\">Casa</a></li>\r\n                    <li><a class=\"color-default\" data-sub=\"product-sub-nav\" href=\"Detail.aspx\" title=\"Introdução à função do produto OCR\">Características do produto:</a><div class=\"triangle\"></div>\r\n                    </li>\r\n                    <li><a class=\"color-default\" data-sub=\"experience-sub-nav\" href=\"Ocr.aspx\" title=\"Experimente os recursos de OCR online\">Experiência online</a><div class=\"triangle\"></div>\r\n                    </li>\r\n                    <li><a class=\"color-default\" data-sub=\"vip-sub-nav\" href=\"Version.aspx\" title=\"Serviço de atualização de associação OCR\">Upgrades de associação</a><div class=\"triangle\"></div>\r\n                    </li>\r\n                    <li><a class=\"color-default\" href=\"Download.aspx\" title=\"Descarregar o OCR Text Recognition Assistant grátis\">Download grátis</a></li>\r\n                    <li><a class=\"color-default\" data-sub=\"articles-sub-nav\" href=\"Articles.aspx\" title=\"Artigos técnicos de OCR e compartilhamento de conhecimento\">Compartilhamento de tecnologia</a><div class=\"triangle\"></div>\r\n                    </li>\r\n                    <li><a class=\"color-default\" data-sub=\"support-sub-nav\" href=\"FAQ.aspx\" title=\"Ajuda de uso de OCR e suporte técnico\">Central de Ajuda</a><div class=\"triangle\"></div>\r\n                    </li>\r\n                </ul>\r\n                <div class=\"product-con\">\r\n                    <section id=\"product-sub-nav\" class=\"pls-nav-dropdown\" style=\"visibility: hidden; height: 0px; display: none;\">\r\n                        <div class=\"d-flex nav-drown-con\" style=\"height: 0px; transition: transform 0.2s ease-in-out 0s,opacity 0.2s ease-in-out 0s; opacity: 0; transform: translate(0px, -100%);\">\r\n                            <div class=\"prdocu-sub-left\">\r\n                                <div class=\"mr-auto\">\r\n                                    <picture>\r\n                                        <source class=\"p-icon\" srcset=\"site/image/icon/p_1.png.webp\" type=\"image/webp\" alt=\"\" aria-hidden=\"true\" />\r\n                                        <img class=\"p-icon\" src=\"site/image/icon/p_1.png\" alt=\"Ícone da função do produto OCR\" aria-hidden=\"true\" loading=\"lazy\" />\r\n                                    </picture>\r\n                                    <div class=\"mt-4\">\r\n                                        <h3 class=\"h6\">Assistente de reconhecimento de texto OCR</h3>\r\n                                        <p class=\"color-gray fn14 mt-3 sub-left-desc\">Melhore a eficiência, reduza custos e crie valor</p>\r\n                                        <p class=\"color-gray fn14 mt-3 sub-left-desc\">&nbsp;</p>\r\n                                        <p class=\"color-gray fn14 mt-3 sub-left-desc\">Reconhecimento inteligente, processamento de alta velocidade e saída precisa</p>\r\n                                        <p class=\"color-gray fn14 mt-3 sub-left-desc\">Do texto às tabelas, das fórmulas às traduções</p>\r\n                                        <p class=\"color-gray fn14 mt-3 sub-left-desc\">Torne cada processamento de texto tão fácil</p>\r\n                                        <a class=\"link block disa fn14 contact-business shake\" href=\"Detail.aspx\">Saiba mais sobre os recursos<span class=\"iconfont4 icon-xiangzuo gt\"></span></a>\r\n                                    </div>\r\n                                </div>\r\n                            </div>\r\n                            <div class=\"bg-white prdocu-sub-right\">\r\n                                <div>\r\n                                    <h2 class=\"text-base-color h6\">Características do produto:</h2>\r\n                                    <div class=\"mt-4 d-flex flex-wrap\">\r\n                                        <a href=\"Detail.aspx\" class=\"pro-item color-default\" title=\"Confira os detalhes das principais funções do OCR Assistant\">\r\n                                            <div class=\"letter-wrap\">\r\n                                                <h3 class=\"h6\">Características principais:</h3>\r\n                                                <span class=\"color-gray fn14\">Saiba mais sobre os principais recursos e benefícios técnicos do OCR Assistant, com uma taxa de reconhecimento de 98%+</span>\r\n                                            </div>\r\n                                        </a>\r\n                                        <a href=\"Version.aspx\" class=\"pro-item color-default\" title=\"Compare as diferenças entre as versões do Assistente de OCR\">\r\n                                            <div class=\"letter-wrap\">\r\n                                                <h3 class=\"h6\">Comparação de versões</h3>\r\n                                                <span class=\"color-gray fn14\">Compare as diferenças funcionais da versão gratuita, versão pessoal, versão profissional e versão final em detalhes</span>\r\n                                            </div>\r\n                                        </a>\r\n                                        <a href=\"FAQ.aspx\" class=\"pro-item color-default\" title=\"Confira as perguntas frequentes do OCR Assistant\">\r\n                                            <div class=\"letter-wrap\">\r\n                                                <h3 class=\"h6\">Perguntas e respostas sobre produtos</h3>\r\n                                                <span class=\"color-gray fn14\">Aprenda rapidamente sobre os recursos do produto, métodos de uso e respostas detalhadas para perguntas frequentes</span>\r\n                                            </div>\r\n                                        </a>\r\n                                        <a href=\"Download.aspx\" class=\"pro-item color-default\" title=\"Descarregar o OCR Text Recognition Assistant grátis\">\r\n                                            <div class=\"letter-wrap\">\r\n                                                <h3 class=\"h6\">Experimente gratuitamente</h3>\r\n                                                <span class=\"color-gray fn14\">Baixe e instale o OCR Assistant agora para experimentar a poderosa função de reconhecimento de texto gratuitamente</span>\r\n                                            </div>\r\n                                        </a>\r\n                                        <div style=\"width: 100%; margin: 15px 0;\">\r\n                                            <h4 class=\"h6\" style=\"color: #007cfa; margin-bottom: 10px;\">Reconhecimento de OCR online</h4>\r\n                                        </div>\r\n                                        <a href=\"Ocr.aspx?type=text_recognize\" class=\"pro-item color-default\" title=\"Experimente o reconhecimento universal de texto online\">\r\n                                            <div class=\"letter-wrap\">\r\n                                                <h3 class=\"h6\">Reconhecimento universal de caracteres</h3>\r\n                                                <span class=\"color-gray fn14\">Extração inteligente de texto multilíngue de alta precisão, suportando reconhecimento de imagem complexa impressa e multicena</span>\r\n                                            </div>\r\n                                        </a>\r\n                                        <a href=\"Ocr.aspx?type=table\" class=\"pro-item color-default\">\r\n                                            <div class=\"letter-wrap\">\r\n                                                <h3 class=\"h6\">Identificação universal da tabela</h3>\r\n                                                <span class=\"color-gray fn14\">Conversão inteligente de imagens de tabela em arquivos Excel, processamento automático de estruturas de tabela complexas e células mescladas</span>\r\n                                            </div>\r\n                                        </a>\r\n                                        <a href=\"Ocr.aspx?type=handwritten_ocr\" class=\"pro-item color-default\">\r\n                                            <div class=\"letter-wrap\">\r\n                                                <h3 class=\"h6\">Reconhecimento de manuscrito</h3>\r\n                                                <span class=\"color-gray fn14\">Reconhecimento inteligente de conteúdo manuscrito em chinês e inglês, notas de suporte em sala de aula, registros médicos e outros cenários</span>\r\n                                            </div>\r\n                                        </a>\r\n                                        <a href=\"Ocr.aspx?type=pdf2word\" class=\"pro-item color-default\">\r\n                                            <div class=\"letter-wrap\">\r\n                                                <h3 class=\"h6\">PDF para Word</h3>\r\n                                                <span class=\"color-gray fn14\">Os documentos PDF são rapidamente convertidos para o formato Word, preservando perfeitamente o layout original e o layout gráfico</span>\r\n                                            </div>\r\n                                        </a>\r\n                                    </div>\r\n                                </div>\r\n                            </div>\r\n                        </div>\r\n                    </section>\r\n                    <section id=\"experience-sub-nav\" class=\"pls-nav-dropdown solution-part\" style=\"visibility: hidden; height: 0px; display: none;\">\r\n                        <div class=\"d-flex nav-drown-con\" style=\"height: 0px; transition: transform 0.2s ease-in-out 0s,opacity 0.2s ease-in-out 0s; opacity: 0; transform: translate(0px, -100%);\">\r\n                            <div class=\"prdocu-sub-left\">\r\n                                <div class=\"mr-auto\">\r\n                                    <picture>\r\n                                        <source srcset=\"site/image/icon/p_4.png.webp\" type=\"image/webp\" />\r\n                                        <img class=\"p-icon\" src=\"site/image/icon/p_4.png\" alt=\"Ícone do Centro de experiência de OCR online\" aria-hidden=\"true\" loading=\"lazy\" />\r\n                                    </picture>\r\n                                    <div class=\"mt-4\">\r\n                                        <h3 class=\"h6\">Assistente de reconhecimento de texto OCR</h3>\r\n                                        <p class=\"color-gray fn14 mt-3 sub-left-desc\">Texto, tabelas, fórmulas, documentos, traduções</p>\r\n                                        <p class=\"color-gray fn14 mt-3 sub-left-desc\">&nbsp;</p>\r\n                                        <p class=\"color-gray fn14 mt-3 sub-left-desc\">Conclua todas as suas necessidades de processamento de texto em três etapas</p>\r\n                                        <p class=\"color-gray fn14 mt-3 sub-left-desc\">Captura de tela → Identificar aplicativos →</p>\r\n                                        <p class=\"color-gray fn14 mt-3 sub-left-desc\">Aumente a eficiência do trabalho em 300%</p>\r\n                                        <a class=\"link block disa fn14 contact-business shake\" href=\"Ocr.aspx\">Experimente agora<span class=\"iconfont4 icon-xiangzuo gt\"></span></a>\r\n                                    </div>\r\n                                </div>\r\n                            </div>\r\n                            <div class=\"bg-white prdocu-sub-right\">\r\n                                <div>\r\n                                    <div class=\"mt-4 d-flex flex-wrap\">\r\n                                        <div style=\"width: 100%; margin-bottom: 15px;\">\r\n                                            <h4 class=\"h6\" style=\"color: #007cfa; margin-bottom: 10px;\">Experiência da função OCR</h4>\r\n                                        </div>\r\n                                        <a href=\"Ocr.aspx?type=index\" class=\"pro-item color-default\">\r\n                                            <div class=\"letter-wrap\">\r\n                                                <h3 class=\"h6\">Funcionalidade completa</h3>\r\n                                                <span class=\"color-gray fn14\">Experimente todos os recursos inteligentes de OCR em um só lugar para encontrar rapidamente a melhor solução para suas necessidades</span>\r\n                                            </div>\r\n                                        </a>\r\n                                        <a href=\"Ocr.aspx?type=text_recognize\" class=\"pro-item color-default\">\r\n                                            <div class=\"letter-wrap\">\r\n                                                <h3 class=\"h6\">Reconhecimento universal de caracteres</h3>\r\n                                                <span class=\"color-gray fn14\">Extração inteligente de texto multilíngue de alta precisão, suportando reconhecimento de imagem complexa impressa e multicena</span>\r\n                                            </div>\r\n                                        </a>\r\n                                        <a href=\"Ocr.aspx?type=table\" class=\"pro-item color-default\">\r\n                                            <div class=\"letter-wrap\">\r\n                                                <h3 class=\"h6\">Identificação universal da tabela</h3>\r\n                                                <span class=\"color-gray fn14\">Conversão inteligente de imagens de tabela em arquivos Excel, processamento automático de estruturas de tabela complexas e células mescladas</span>\r\n                                            </div>\r\n                                        </a>\r\n                                        <a href=\"Ocr.aspx?type=handwritten_ocr\" class=\"pro-item color-default\">\r\n                                            <div class=\"letter-wrap\">\r\n                                                <h3 class=\"h6\">Reconhecimento de manuscrito</h3>\r\n                                                <span class=\"color-gray fn14\">Reconhecimento inteligente de conteúdo manuscrito em chinês e inglês, notas de suporte em sala de aula, registros médicos e outros cenários</span>\r\n                                            </div>\r\n                                        </a>\r\n                                        <a href=\"Ocr.aspx?type=pdf2word\" class=\"pro-item color-default\">\r\n                                            <div class=\"letter-wrap\">\r\n                                                <h3 class=\"h6\">PDF para Word</h3>\r\n                                                <span class=\"color-gray fn14\">Os documentos PDF são rapidamente convertidos para o formato Word, preservando perfeitamente o layout original e o layout gráfico</span>\r\n                                            </div>\r\n                                        </a>\r\n                                        <a href=\"Ocr.aspx?type=pdf2markdown\" class=\"pro-item color-default\">\r\n                                            <div class=\"letter-wrap\">\r\n                                                <h3 class=\"h6\">PDF para Markdown</h3>\r\n                                                <span class=\"color-gray fn14\">Os documentos PDF são convertidos de forma inteligente para o formato MD e os blocos de código e as estruturas de texto são otimizados automaticamente para processamento</span>\r\n                                            </div>\r\n                                        </a>\r\n                                        <div style=\"width: 100%; margin: 15px 0;\">\r\n                                            <h4 class=\"h6\" style=\"color: #007cfa; margin-bottom: 10px;\">Ferramentas de processamento de documentos</h4>\r\n                                        </div>\r\n                                        <a href=\"Ocr.aspx?type=word2pdf\" class=\"pro-item color-default\">\r\n                                            <div class=\"letter-wrap\">\r\n                                                <h3 class=\"h6\">Word para PDF</h3>\r\n                                                <span class=\"color-gray fn14\">Os documentos do Word são convertidos em PDF com um clique, mantendo perfeitamente o formato original, adequado para arquivamento e compartilhamento de documentos oficiais</span>\r\n                                            </div>\r\n                                        </a>\r\n                                        <a href=\"Ocr.aspx?type=word2jpg\" class=\"pro-item color-default\">\r\n                                            <div class=\"letter-wrap\">\r\n                                                <h3 class=\"h6\">Palavra à imagem</h3>\r\n                                                <span class=\"color-gray fn14\">Conversão inteligente de documento do Word para imagem JPG, suporta processamento de várias páginas, fácil de compartilhar nas mídias sociais</span>\r\n                                            </div>\r\n                                        </a>\r\n                                        <a href=\"Ocr.aspx?type=pdf2jpg\" class=\"pro-item color-default\">\r\n                                            <div class=\"letter-wrap\">\r\n                                                <h3 class=\"h6\">PDF para imagem</h3>\r\n                                                <span class=\"color-gray fn14\">Converta documentos PDF em imagens JPG em alta definição, suporte a processamento em lote e resolução personalizada</span>\r\n                                            </div>\r\n                                        </a>\r\n                                        <a href=\"Ocr.aspx?type=image2pdf\" class=\"pro-item color-default\">\r\n                                            <div class=\"letter-wrap\">\r\n                                                <h3 class=\"h6\">Imagem para PDF</h3>\r\n                                                <span class=\"color-gray fn14\">Mescle várias imagens em documentos PDF, suporte à classificação e configuração de página</span>\r\n                                            </div>\r\n                                        </a>\r\n                                        <div style=\"width: 100%; margin: 15px 0;\">\r\n                                            <h4 class=\"h6\" style=\"color: #007cfa; margin-bottom: 10px;\">Ferramentas de desenvolvedor</h4>\r\n                                        </div>\r\n                                        <a href=\"Tool.aspx?type=Json\" class=\"pro-item color-default\">\r\n                                            <div class=\"letter-wrap\">\r\n                                                <h3 class=\"h6\">Formatação JSON</h3>\r\n                                                <span class=\"color-gray fn14\">Embelezar de forma inteligente a estrutura do código JSON, dar suporte à compactação e expansão e facilitar o desenvolvimento e a depuração</span>\r\n                                            </div>\r\n                                        </a>\r\n                                        <a href=\"Tool.aspx?type=Regex\" class=\"pro-item color-default\">\r\n                                            <div class=\"letter-wrap\">\r\n                                                <h3 class=\"h6\">expressão regular</h3>\r\n                                                <span class=\"color-gray fn14\">Verifique os efeitos de correspondência de expressões regulares em tempo real, com uma biblioteca integrada de padrões comuns</span>\r\n                                            </div>\r\n                                        </a>\r\n                                        <a href=\"Tool.aspx?type=Encode\" class=\"pro-item color-default\">\r\n                                            <div class=\"letter-wrap\">\r\n                                                <h3 class=\"h6\">Conversão de codificação de texto</h3>\r\n                                                <span class=\"color-gray fn14\">Ele suporta a conversão de vários formatos de codificação, como Base64, URL e Unicode</span>\r\n                                            </div>\r\n                                        </a>\r\n                                        <a href=\"Tool.aspx?type=Diff\" class=\"pro-item color-default\">\r\n                                            <div class=\"letter-wrap\">\r\n                                                <h3 class=\"h6\">Correspondência e mesclagem de texto</h3>\r\n                                                <span class=\"color-gray fn14\">Destaque as diferenças de texto e suporte à comparação linha por linha e mesclagem inteligente</span>\r\n                                            </div>\r\n                                        </a>\r\n                                        <a href=\"Tool.aspx?type=Color\" class=\"pro-item color-default\">\r\n                                            <div class=\"letter-wrap\">\r\n                                                <h3 class=\"h6\">Ferramenta Cor</h3>\r\n                                                <span class=\"color-gray fn14\">Conversão de cores RGB/HEX, seletor de cores online, uma ferramenta obrigatória para desenvolvimento front-end</span>\r\n                                            </div>\r\n                                        </a>\r\n                                        <a href=\"Tool.aspx?type=Count\" class=\"pro-item color-default\">\r\n                                            <div class=\"letter-wrap\">\r\n                                                <h3 class=\"h6\">Contagem de palavras</h3>\r\n                                                <span class=\"color-gray fn14\">Contagem inteligente de caracteres, vocabulário e parágrafos e otimização automática do layout do texto</span>\r\n                                            </div>\r\n                                        </a>\r\n                                        <a href=\"Tool.aspx?type=Timespan\" class=\"pro-item color-default\">\r\n                                            <div class=\"letter-wrap\">\r\n                                                <h3 class=\"h6\">Conversão de carimbo de data/hora</h3>\r\n                                                <span class=\"color-gray fn14\">A hora é convertida de e para carimbos de data/hora Unix e vários formatos e configurações de fuso horário são suportados</span>\r\n                                            </div>\r\n                                        </a>\r\n                                        <a href=\"Tool.aspx?type=Calc\" class=\"pro-item color-default\">\r\n                                            <div class=\"letter-wrap\">\r\n                                                <h3 class=\"h6\">Ferramenta de calculadora</h3>\r\n                                                <span class=\"color-gray fn14\">Calculadora científica online com suporte para operações básicas e cálculos avançados de funções matemáticas</span>\r\n                                            </div>\r\n                                        </a>\r\n                                    </div>\r\n                                </div>\r\n                            </div>\r\n                        </div>\r\n                    </section>\r\n                    <section id=\"articles-sub-nav\" class=\"pls-nav-dropdown solution-part\" style=\"visibility: hidden; height: 0px; display: none;\">\r\n                        <div class=\"d-flex nav-drown-con\" style=\"height: 0px; transition: transform 0.2s ease-in-out 0s,opacity 0.2s ease-in-out 0s; opacity: 0; transform: translate(0px, -100%);\">\r\n                            <div class=\"prdocu-sub-left\">\r\n                                <div class=\"mr-auto\">\r\n                                    <picture>\r\n                                        <source srcset=\"site/image/icon/p_4.png.webp\" type=\"image/webp\" />\r\n                                        <img class=\"p-icon\" src=\"site/image/icon/p_4.png\" alt=\"Ícone do Centro de Compartilhamento de Tecnologia\" aria-hidden=\"true\" loading=\"lazy\" />\r\n                                    </picture>\r\n                                    <div class=\"mt-4\">\r\n                                        <h3 class=\"h6\">Compartilhamento de tecnologia OCR</h3>\r\n                                        <p class=\"color-gray fn14 mt-3 sub-left-desc\">Tutoriais técnicos, casos de aplicação, recomendações de ferramentas</p>\r\n                                        <p class=\"color-gray fn14 mt-3 sub-left-desc\">&nbsp;</p>\r\n                                        <p class=\"color-gray fn14 mt-3 sub-left-desc\">Um caminho de aprendizagem completo do iniciante ao domínio</p>\r\n                                        <p class=\"color-gray fn14 mt-3 sub-left-desc\">Casos práticos → análise técnica → aplicações de ferramentas</p>\r\n                                        <p class=\"color-gray fn14 mt-3 sub-left-desc\">Capacite seu caminho para a melhoria da tecnologia OCR</p>\r\n                                        <a class=\"link block disa fn14 contact-business shake\" href=\"Articles.aspx\">Procurar artigos<span class=\"iconfont4 icon-xiangzuo gt\"></span></a>\r\n                                    </div>\r\n                                </div>\r\n                            </div>\r\n                            <div class=\"bg-white prdocu-sub-right\">\r\n                                <div>\r\n                                    <h2 class=\"text-base-color h6\">Compartilhamento de tecnologia</h2>\r\n                                    <div class=\"mt-4 d-flex flex-wrap\">\r\n                                        <a href=\"Articles.aspx\" class=\"pro-item color-default\" title=\"Ver todos os artigos técnicos de OCR\">\r\n                                            <div class=\"letter-wrap\">\r\n                                                <h3 class=\"h6\">Todos os artigos</h3>\r\n                                                <span class=\"color-gray fn14\">Navegue por todos os artigos técnicos de OCR cobrindo um corpo completo de conhecimento, do básico ao avançado</span>\r\n                                            </div>\r\n                                        </a>\r\n                                        \r\n                                        <a href=\"Articles.aspx?category=2\" class=\"pro-item color-default\" title=\"Tutoriais técnicos de OCR e guias de introdução\">\r\n                                            <div class=\"letter-wrap\">\r\n                                                <h3 class=\"h6\">Guia Avançado</h3>\r\n                                                <span class=\"color-gray fn14\">De tutoriais técnicos de OCR introdutórios a proficientes, guias de instruções detalhados e orientações práticas</span>\r\n                                            </div>\r\n                                        </a>\r\n                                        \r\n                                        <a href=\"Articles.aspx?category=3\" class=\"pro-item color-default\" title=\"Princípios, algoritmos e aplicações da tecnologia OCR\">\r\n                                            <div class=\"letter-wrap\">\r\n                                                <h3 class=\"h6\">Exploração tecnológica</h3>\r\n                                                <span class=\"color-gray fn14\">Explore as fronteiras da tecnologia OCR, dos princípios às aplicações, e analise profundamente os principais algoritmos</span>\r\n                                            </div>\r\n                                        </a>\r\n                                        \r\n                                        <a href=\"Articles.aspx?category=4\" class=\"pro-item color-default\" title=\"Os últimos desenvolvimentos e tendências de desenvolvimento na indústria de OCR\">\r\n                                            <div class=\"letter-wrap\">\r\n                                                <h3 class=\"h6\">Tendências da indústria</h3>\r\n                                                <span class=\"color-gray fn14\">Insights detalhados sobre tendências de desenvolvimento de tecnologia OCR, análise de mercado, dinâmica do setor e perspectivas futuras</span>\r\n                                            </div>\r\n                                        </a>\r\n                                        \r\n                                        <a href=\"Articles.aspx?category=5\" class=\"pro-item color-default\" title=\"Casos de aplicação da tecnologia OCR em vários setores\">\r\n                                            <div class=\"letter-wrap\">\r\n                                                <h3 class=\"h6\">Casos de uso:</h3>\r\n                                                <span class=\"color-gray fn14\">Casos de aplicação do mundo real, soluções e melhores práticas da tecnologia OCR em vários setores são compartilhados</span>\r\n                                            </div>\r\n                                        </a>\r\n                                        \r\n                                        <a href=\"Articles.aspx?category=6\" class=\"pro-item color-default\" title=\"Avaliações profissionais, análises comparativas e diretrizes recomendadas para o uso de ferramentas de software de OCR\">\r\n                                            <div class=\"letter-wrap\">\r\n                                                <h3 class=\"h6\">Revisão da ferramenta</h3>\r\n                                                <span class=\"color-gray fn14\">Avalie vários softwares e ferramentas de reconhecimento de texto OCR e forneça sugestões detalhadas de comparação e seleção de funções</span>\r\n                                            </div>\r\n                                        </a>\r\n                                        \r\n                                    </div>\r\n                                </div>\r\n                            </div>\r\n                        </div>\r\n                    </section>\r\n                    <section id=\"vip-sub-nav\" class=\"pls-nav-dropdown solution-part\" style=\"visibility: hidden; height: 0px; display: none;\">\r\n                        <div class=\"d-flex nav-drown-con\" style=\"height: 0px; transition: transform 0.2s ease-in-out 0s,opacity 0.2s ease-in-out 0s; opacity: 0; transform: translate(0px, -100%);\">\r\n                            <div class=\"prdocu-sub-left\">\r\n                                <div class=\"mr-auto\">\r\n                                    <picture>\r\n                                        <source class=\"p-icon\" srcset=\"site/image/icon/p_1.png.webp\" type=\"image/webp\" alt=\"\" aria-hidden=\"true\" />\r\n                                        <img class=\"p-icon\" src=\"site/image/icon/p_1.png\" alt=\"Ícone do serviço de upgrade de associação\" aria-hidden=\"true\" loading=\"lazy\" />\r\n                                    </picture>\r\n                                    <div class=\"mt-4\">\r\n                                        <h3 class=\"h6\">Serviço de upgrade de associação</h3>\r\n                                        <p class=\"color-gray fn14 mt-3 sub-left-desc\">Desbloqueie todos os recursos premium e desfrute de serviços exclusivos</p>\r\n                                        <p class=\"color-gray fn14 mt-3 sub-left-desc\">&nbsp;</p>\r\n                                        <p class=\"color-gray fn14 mt-3 sub-left-desc\">Reconhecimento offline, processamento em lote, uso ilimitado</p>\r\n                                        <p class=\"color-gray fn14 mt-3 sub-left-desc\">Pro → Ultimate → Enterprise</p>\r\n                                        <p class=\"color-gray fn14 mt-3 sub-left-desc\">Há algo para atender às suas necessidades</p>\r\n                                        <a class=\"link block disa fn14 contact-business shake\" href=\"Version.aspx\">Ver detalhes<span class=\"iconfont4 icon-xiangzuo gt\"></span></a>\r\n                                    </div>\r\n                                </div>\r\n                            </div>\r\n                            <div class=\"bg-white prdocu-sub-right\">\r\n                                <div>\r\n                                    <h2 class=\"text-base-color h6\" style=\"margin: inherit;\">Upgrades de associação</h2>\r\n                                    <div class=\"mt-4 d-flex flex-wrap\">\r\n                                        <a href=\"Version.aspx\" class=\"pro-item color-default\">\r\n                                            <div class=\"letter-wrap\">\r\n                                                <h3 class=\"h6\">Privilégios de associação</h3>\r\n                                                <span class=\"color-gray fn14\">Saiba mais sobre as diferenças entre as edições e escolha o nível de associação que melhor se adapta a você</span>\r\n                                            </div>\r\n                                        </a>\r\n                                        <a href=\"Upgrade.aspx\" class=\"pro-item color-default\">\r\n                                            <div class=\"letter-wrap\">\r\n                                                <h3 class=\"h6\">Atualize agora</h3>\r\n                                                <span class=\"color-gray fn14\">Atualize rapidamente sua assinatura VIP para desbloquear mais recursos premium e serviços exclusivos</span>\r\n                                            </div>\r\n                                        </a>\r\n                                        <a href=\"User.aspx\" class=\"pro-item color-default\">\r\n                                            <div class=\"letter-wrap\">\r\n                                                <h3 class=\"h6\">Minha Conta</h3>\r\n                                                <span class=\"color-gray fn14\">Gerencie informações da conta, status da assinatura e histórico de uso para personalizar as configurações</span>\r\n                                            </div>\r\n                                        </a>\r\n                                    </div>\r\n                                </div>\r\n                            </div>\r\n                        </div>\r\n                    </section>\r\n                    <section id=\"support-sub-nav\" class=\"pls-nav-dropdown solution-part\" style=\"visibility: hidden; height: 0px; display: none;\">\r\n                        <div class=\"d-flex nav-drown-con\" style=\"height: 0px; transition: transform 0.2s ease-in-out 0s,opacity 0.2s ease-in-out 0s; opacity: 0; transform: translate(0px, -100%);\">\r\n                            <div class=\"prdocu-sub-left\">\r\n                                <div class=\"mr-auto\">\r\n                                    <picture>\r\n                                        <source class=\"p-icon\" srcset=\"site/image/icon/p_4.png.webp\" type=\"image/webp\" alt=\"\" aria-hidden=\"true\" />\r\n                                        <img class=\"p-icon\" src=\"site/image/icon/p_4.png\" alt=\"Ícone de suporte da Central de Ajuda\" aria-hidden=\"true\" loading=\"lazy\" />\r\n                                    </picture>\r\n                                    <div class=\"mt-4\">\r\n                                        <h3 class=\"h6\">Central de Ajuda</h3>\r\n                                        <p class=\"color-gray fn14 mt-3 sub-left-desc\">Atendimento ao cliente profissional, documentação detalhada e resposta rápida</p>\r\n                                        <p class=\"color-gray fn14 mt-3 sub-left-desc\">&nbsp;</p>\r\n                                        <p class=\"color-gray fn14 mt-3 sub-left-desc\">Não entre em pânico quando encontrar problemas</p>\r\n                                        <p class=\"color-gray fn14 mt-3 sub-left-desc\">Problema → encontrar → resolvido</p>\r\n                                        <p class=\"color-gray fn14 mt-3 sub-left-desc\">Torne sua experiência mais tranquila</p>\r\n                                        <a class=\"link block disa fn14 contact-business shake\" href=\"FAQ.aspx\">Obter ajuda<span class=\"iconfont4 icon-xiangzuo gt\"></span></a>\r\n                                    </div>\r\n                                </div>\r\n                            </div>\r\n                            <div class=\"bg-white prdocu-sub-right\">\r\n                                <div>\r\n                                    <h2 class=\"text-base-color h6\" style=\"margin: inherit;\">Central de Ajuda</h2>\r\n                                    <div class=\"mt-4 d-flex flex-wrap\">\r\n                                        <a href=\"FAQ.aspx\" class=\"pro-item color-default\">\r\n                                            <div class=\"letter-wrap\">\r\n                                                <h3 class=\"h6\">Perguntas frequentes</h3>\r\n                                                <span class=\"color-gray fn14\">Responda rapidamente a perguntas comuns dos usuários e forneça guias de uso detalhados e suporte técnico</span>\r\n                                            </div>\r\n                                        </a>\r\n                                        <a href=\"AboutUs.aspx\" class=\"pro-item color-default\">\r\n                                            <div class=\"letter-wrap\">\r\n                                                <h3 class=\"h6\">Quem somos</h3>\r\n                                                <span class=\"color-gray fn14\">Saiba mais sobre o histórico de desenvolvimento, as principais funções e os conceitos de serviço do assistente de reconhecimento de texto OCR</span>\r\n                                            </div>\r\n                                        </a>\r\n                                        <a href=\"UserAgreement.aspx\" class=\"pro-item color-default\">\r\n                                            <div class=\"letter-wrap\">\r\n                                                <h3 class=\"h6\">Contrato do Usuário</h3>\r\n                                                <span class=\"color-gray fn14\">Termos de serviço detalhados e direitos e obrigações do usuário</span>\r\n                                            </div>\r\n                                        </a>\r\n                                        <a href=\"PrivacyPolicy.aspx\" class=\"pro-item color-default\">\r\n                                            <div class=\"letter-wrap\">\r\n                                                <h3 class=\"h6\">Acordo de Privacidade</h3>\r\n                                                <span class=\"color-gray fn14\">Política de proteção de informações pessoais e medidas de segurança de dados</span>\r\n                                            </div>\r\n                                        </a>\r\n                                        <a href=\"Status.aspx\" class=\"pro-item color-default\">\r\n                                            <div class=\"letter-wrap\">\r\n                                                <h3 class=\"h6\">Status do sistema</h3>\r\n                                                <span class=\"color-gray fn14\">Monitore o status de operação dos nós de identificação global em tempo real e visualize os dados de desempenho do sistema</span>\r\n                                            </div>\r\n                                        </a>\r\n                                        <a href=\"javascript:void(0);\" class=\"pro-item color-default\" onclick=\"if(window.openCustomerService) window.openCustomerService(); else alert('Clique no ícone da janela flutuante à direita para entrar em contato com o atendimento ao cliente');\">\r\n                                            <div class=\"letter-wrap\">\r\n                                                <h3 class=\"h6\">Entre em contato com o atendimento ao cliente</h3>\r\n                                                <span class=\"color-gray fn14\">Suporte de atendimento ao cliente online para responder rapidamente às suas perguntas e necessidades</span>\r\n                                            </div>\r\n                                        </a>\r\n                                    </div>\r\n                                </div>\r\n                            </div>\r\n                        </div>\r\n                    </section>\r\n                </div>\r\n            </div>\r\n            <div class=\"header_pc_right\" style=\"display: flex; align-items: center; justify-content: flex-end;\">\r\n                <div id=\"translate\"></div>\r\n                <div id=\"user-login-container\" data-url=\"User.ashx?op=userinfo\" style=\"white-space: nowrap; padding-right: 20px;\"></div>\r\n            </div>\r\n        </header>\r\n        <header class=\"v4_header_mob\">\r\n            <div class=\"container\">\r\n                <div class=\"row no-gutters justify-content-between align-items-center\">\r\n                    <a href=\"Default.aspx\" title=\"Início | Reconhecimento de texto inteligente AI\" class=\"mob-logo-wrap\">\r\n                        <picture>\r\n                            <source srcset=\"site/image/logo-s.png.webp\" type=\"image/webp\" />\r\n                            <img src=\"site/image/logo-s.png\" class=\"pc-logo\" style=\"height: 32px; width: 32px\" alt=\"Logotipo móvel do assistente de reconhecimento de texto OCR\" />\r\n                        </picture>\r\n                        <span style=\"font-size: 18px; font-weight: bold; color: #333;\">Assistente de reconhecimento de texto OCR</span>\r\n                    </a>\r\n                    <div class=\"right-menu\" role=\"button\" tabindex=\"0\" aria-label=\"Abra o menu de navegação\" data-toggle=\"collapse\" data-target=\"#mob-header-collapse\" aria-expanded=\"false\" aria-controls=\"mob-header-collapse\">\r\n                        <nav-products id=\"nav_products\">\r\n                            <nav-button class=\"submail-mdi-nav-btn\" style=\"width: 60px\">\r\n                                <label id=\"mdi-submail-nav-btn\">\r\n                                    <span class=\"mdi-nav-products-1\">\r\n                                        <span class=\"nav-products-icon\"></span>\r\n                                    </span>\r\n                                    <span class=\"mdi-nav-products-2\">\r\n                                        <span class=\"nav-products-icon\"></span>\r\n                                    </span>\r\n                                    <span class=\"mdi-nav-products-3\">\r\n                                        <span class=\"nav-products-icon\"></span>\r\n                                    </span>\r\n                                </label>\r\n                            </nav-button>\r\n                        </nav-products>\r\n                    </div>\r\n                </div>\r\n            </div>\r\n            <div class=\"mob-nav-content\">\r\n                <div class=\"sidebar-fix\">\r\n                    <div class=\"container\">\r\n                        <div class=\"row\">\r\n                            <div class=\"col-4 bg-white sidebar-fix-left\" style=\"border-right: 1px solid #e1e6ed\">\r\n                                <div class=\"mob-nav-item active\" data=\"0\">\r\n                                    <div class=\"nav-header\"><a href=\"Default.aspx\" class=\"nav-header-letter px-3\"><span>Casa</span></a></div>\r\n                                </div>\r\n                                <div class=\"mob-nav-item\" data=\"1\">\r\n                                    <div class=\"nav-header\"><a href=\"Detail.aspx\" class=\"nav-header-letter px-3\"><span>função</span></a></div>\r\n                                </div>\r\n                                <div class=\"mob-nav-item\" data=\"2\">\r\n                                    <div class=\"nav-header\"><a href=\"Ocr.aspx\" class=\"nav-header-letter px-3\"><span>experiência</span></a></div>\r\n                                </div>\r\n                                <div class=\"mob-nav-item\" data=\"3\">\r\n                                    <div class=\"nav-header\"><a href=\"Version.aspx\" class=\"nav-header-letter px-3\"><span>membro</span></a></div>\r\n                                </div>\r\n                                <div class=\"mob-nav-item\" data=\"4\">\r\n                                    <div class=\"nav-header\"><a href=\"Download.aspx\" class=\"nav-header-letter px-3\"><span>Baixar</span></a></div>\r\n                                </div>\r\n                                <div class=\"mob-nav-item\" data=\"5\">\r\n                                    <div class=\"nav-header\"><a href=\"Articles.aspx\" class=\"nav-header-letter px-3\"><span>Compartilhar</span></a></div>\r\n                                </div>\r\n                                <div class=\"mob-nav-item\" data=\"6\">\r\n                                    <div class=\"nav-header\"><a href=\"FAQ.aspx\" class=\"nav-header-letter px-3\"><span>Ajuda</span></a></div>\r\n                                </div>\r\n                            </div>\r\n                            <div class=\"col-8 bg-white sidebar-fix-rigth\">\r\n                                <div class=\"sub-nav\">\r\n                                    <div class=\"sub-nav-item py-2 pb-3 px-4\">\r\n                                        <h2 class=\"h5\" style=\"margin: inherit;\">Ferramentas de produtividade eficientes</h2>\r\n                                        <p class=\"color-gray fn14 mt-3 sub-left-desc\">Reconhecimento inteligente, processamento de alta velocidade e saída precisa</p>\r\n                                        <p class=\"color-gray fn14 mt-3 sub-left-desc\">&nbsp;</p>\r\n                                        <p class=\"color-gray fn14 mt-3 sub-left-desc\">Reconheça uma página inteira de documentos em 3 segundos</p>\r\n                                        <p class=\"color-gray fn14 mt-3 sub-left-desc\">98%+ precisão de reconhecimento</p>\r\n                                        <p class=\"color-gray fn14 mt-3 sub-left-desc\">Processamento multilíngue em tempo real sem demora</p>\r\n                                        <a class=\"link block disa fn14 contact-business shake\" href=\"Download.aspx\">Baixe a experiência agora<span class=\"iconfont4 icon-xiangzuo gt\"></span></a>\r\n                                    </div>\r\n                                </div>\r\n                                <div class=\"sub-nav\">\r\n                                    <div class=\"sub-nav-item py-3 px-4\">\r\n                                        <h2 class=\"h5\" style=\"margin: inherit;\">Características do produto:</h2>\r\n                                        <p class=\"color-gray fn14 mt-3 sub-left-desc\">Identificação inteligente de IA, solução completa</p>\r\n                                    </div>\r\n                                    <div class=\"sub-nav-item py-3 px-4\"><a href=\"Detail.aspx\">Introdução da função</a></div>\r\n                                    <div class=\"sub-nav-item py-3 px-4\"><a href=\"Download.aspx\">Download de software</a></div>\r\n                                    <div class=\"sub-nav-item py-3 px-4\"><a href=\"Version.aspx\">Comparação de versões</a></div>\r\n                                    <div class=\"sub-nav-item py-3 px-4\"><a href=\"Ocr.aspx\">Experiência online</a></div>\r\n                                    <div class=\"sub-nav-item py-3 px-4\"><a href=\"Status.aspx\">Status do sistema</a></div>\r\n                                </div>\r\n                                <div class=\"sub-nav\">\r\n                                    <div class=\"sub-nav-item py-3 px-4\">\r\n                                        <h2 class=\"h5\" style=\"margin: inherit;\">Experiência online</h2>\r\n                                        <p class=\"color-gray fn14 mt-3 sub-left-desc\">Experiência online gratuita com a função OCR</p>\r\n                                    </div>\r\n                                    <div class=\"sub-nav-item py-3 px-4\"><a href=\"Ocr.aspx\">Funcionalidade completa</a></div>\r\n                                    <div class=\"sub-nav-item py-3 px-4\"><a href=\"Ocr.aspx?type=text_recognize\">Reconhecimento de palavras</a></div>\r\n                                    <div class=\"sub-nav-item py-3 px-4\"><a href=\"Ocr.aspx?type=table\">Identificação da tabela</a></div>\r\n                                    <div class=\"sub-nav-item py-3 px-4\"><a href=\"Ocr.aspx?type=pdf2word\">PDF para Word</a></div>\r\n                                </div>\r\n                                <div class=\"sub-nav\">\r\n                                    <div class=\"sub-nav-item py-3 px-4\">\r\n                                        <h2 class=\"h5\" style=\"margin: inherit;\">Upgrades de associação</h2>\r\n                                        <p class=\"color-gray fn14 mt-3 sub-left-desc\">Desbloqueie todos os recursos e desfrute de serviços exclusivos</p>\r\n                                    </div>\r\n                                    <div class=\"sub-nav-item py-3 px-4\"><a href=\"Version.aspx\">Benefícios da associação</a></div>\r\n                                    <div class=\"sub-nav-item py-3 px-4\"><a href=\"Upgrade.aspx\">Ative imediatamente</a></div>\r\n                                </div>\r\n                                <div class=\"sub-nav\">\r\n                                    <div class=\"sub-nav-item py-3 px-4\">\r\n                                        <h2 class=\"h5\" style=\"margin: inherit;\">Download de software</h2>\r\n                                        <p class=\"color-gray fn14 mt-3 sub-left-desc\">Baixe o software profissional de OCR gratuitamente</p>\r\n                                    </div>\r\n                                    <div class=\"sub-nav-item py-3 px-4\"><a href=\"Download.aspx\">Faça o download agora</a></div>\r\n                                    <div class=\"sub-nav-item py-3 px-4\"><a href=\"Version.aspx\">Comparação de versões</a></div>\r\n                                </div>\r\n                                <div class=\"sub-nav\">\r\n                                    <div class=\"sub-nav-item py-3 px-4\">\r\n                                        <h2 class=\"h5\" style=\"margin: inherit;\">Compartilhamento de tecnologia</h2>\r\n                                        <p class=\"color-gray fn14 mt-3 sub-left-desc\">Artigos técnicos de OCR e compartilhamento de conhecimento</p>\r\n                                    </div>\r\n                                    <div class=\"sub-nav-item py-3 px-4\"><a href=\"Articles.aspx\">Todos os artigos</a></div>\r\n                                    \r\n                                    <div class=\"sub-nav-item py-3 px-4\"><a href=\"Articles.aspx?category=2\">Guia Avançado</a></div>\r\n                                    \r\n                                    <div class=\"sub-nav-item py-3 px-4\"><a href=\"Articles.aspx?category=3\">Exploração tecnológica</a></div>\r\n                                    \r\n                                    <div class=\"sub-nav-item py-3 px-4\"><a href=\"Articles.aspx?category=4\">Tendências da indústria</a></div>\r\n                                    \r\n                                    <div class=\"sub-nav-item py-3 px-4\"><a href=\"Articles.aspx?category=5\">Casos de uso:</a></div>\r\n                                    \r\n                                    <div class=\"sub-nav-item py-3 px-4\"><a href=\"Articles.aspx?category=6\">Revisão da ferramenta</a></div>\r\n                                    \r\n                                </div>\r\n                                <div class=\"sub-nav\">\r\n                                    <div class=\"sub-nav-item py-3 px-4\">\r\n                                        <h2 class=\"h5\" style=\"margin: inherit;\">Central de Ajuda</h2>\r\n                                        <p class=\"color-gray fn14 mt-3 sub-left-desc\">Atendimento ao cliente profissional, serviço íntimo</p>\r\n                                    </div>\r\n                                    <div class=\"sub-nav-item py-3 px-4\"><a href=\"FAQ.aspx\">Usar ajuda</a></div>\r\n                                    <div class=\"sub-nav-item py-3 px-4\"><a href=\"AboutUs.aspx\">Quem somos</a></div>\r\n                                    <div class=\"sub-nav-item py-3 px-4\"><a href=\"mailto:<EMAIL>\">Entre em contato com o atendimento ao cliente</a></div>\r\n                                    <div class=\"sub-nav-item py-3 px-4\"><a href=\"UserAgreement.aspx\">Termos de Serviço</a></div>\r\n                                </div>\r\n                            </div>\r\n                        </div>\r\n                    </div>\r\n                </div>\r\n            </div>\r\n        </header>\r\n        <form method=\"post\" action=\"./Article.aspx?id=82&amp;slug=attention-mechanism-principles&amp;lang=zh-Hans&amp;_translation_source=true\" id=\"aspnetForm\">\r\n<div class=\"aspNetHidden\">\r\n<input type=\"hidden\" name=\"__VIEWSTATE\" id=\"__VIEWSTATE\" value=\"BqrhS+P8MW5VC3DOBfGwj6vOm3+QcH2E0p5DXnNjlyGuctPQR14YJZeSsWbjw86X57yDpdIFIGGGE04n8DnmgHdYirvyFNNsxJ81r0FsIZo=\" />\r\n</div>\r\n\r\n<div class=\"aspNetHidden\">\r\n\r\n\t<input type=\"hidden\" name=\"__VIEWSTATEGENERATOR\" id=\"__VIEWSTATEGENERATOR\" value=\"2173C2F0\" />\r\n</div>\r\n            <div>\r\n                \n\n<div class=\"reading-progress\" id=\"readingProgress\"></div>\n<div class=\"article-detail-container\">\n<div class=\"container\">\n<div class=\"row\">\n<div class=\"col-lg-8 col-xl-9 col-md-12\">\n                    \n                    <article class=\"article-detail\" id=\"articleDetail\" data-article-id=\"82\">\n                        <header class=\"article-header\">\n                            <h1 class=\"article-title\">【Série OCR de Aprendizado Profundo·5】Princípio e Implementação do Mecanismo de Atenção</h1>\n                            <div class=\"article-meta\">\n                                <div class=\"meta-item\">\n                                    <span class=\"meta-icon\">📅</span>\n                                    <span>Horário da postagem: 2025-08-19</span>\n                                </div>\n                                <div class=\"meta-item\">\n                                    <span class=\"meta-icon\">👁️</span>\n                                    <span>Leitura:<span class=\"view-count\">1250</span></span>\n                                </div>\n                                <div class=\"meta-item\">\n                                    <span class=\"meta-icon\">⏱️</span>\n                                    <span>Aprox. 58 minutos (11464 palavras)</span>\n                                </div>\n                                \n                                <div class=\"meta-item\">\n                                    <span class=\"meta-icon\">📁</span>\n                                    <span>Categoria: Guias Avançados</span>\n                                </div>\n                                \n                            </div>\n                            <div class=\"article-summary\"><p>Aprofunde-se nos princípios matemáticos dos mecanismos de atenção, atenção com várias cabeças, mecanismos de autoatenção e aplicações específicas em OCR. Análise detalhada de cálculos de peso de atenção, codificação de posição e estratégias de otimização de desempenho.</p></div>\n                        </header>\n                        <div class=\"article-content\" id=\"articleContent\">## Introdução\r\n\r\nO Mecanismo de Atenção é uma inovação importante no campo do aprendizado profundo, que simula a atenção seletiva nos processos cognitivos humanos. Em tarefas de OCR, o mecanismo de atenção pode ajudar o modelo a se concentrar dinamicamente em áreas importantes da imagem, melhorando significativamente a precisão e a eficiência do reconhecimento de texto. Este artigo se aprofundará nos fundamentos teóricos, princípios matemáticos, métodos de implementação e aplicações específicas de mecanismos de atenção em OCR, fornecendo aos leitores uma compreensão técnica abrangente e orientação prática.\r\n\r\n## Implicações biológicas dos mecanismos de atenção\r\n\r\n### Sistema de Atenção Visual Humana\r\n\r\nO sistema visual humano tem uma forte capacidade de prestar atenção seletivamente, o que nos permite extrair informações úteis com eficiência em ambientes visuais complexos. Quando lemos um pedaço de texto, os olhos se concentram automaticamente no personagem que está sendo reconhecido no momento, com supressão moderada das informações ao redor.\r\n\r\n**Características da Atenção Humana**:\r\n- Seletividade: Capacidade de selecionar seções importantes de uma grande quantidade de informações\r\n- Dinâmico: os focos de atenção se ajustam dinamicamente com base nas demandas da tarefa\r\n- Hierarquia: A atenção pode ser distribuída em diferentes níveis de abstração\r\n- Paralelismo: Várias regiões relacionadas podem ser focadas simultaneamente\r\n- Sensibilidade ao contexto: A alocação de atenção é influenciada por informações contextuais\r\n\r\n**Mecanismos neurais da atenção visual**:\r\nNa pesquisa em neurociência, a atenção visual envolve o trabalho coordenado de várias regiões do cérebro:\r\n- Córtex parietal: responsável pelo controle da atenção espacial\r\n- Córtex pré-frontal: responsável pelo controle da atenção orientado a objetivos\r\n- Visual Cortex: Responsável pela detecção e representação de recursos\r\n- Tálamo: serve como uma estação retransmissora para informações de atenção\r\n\r\n### Requisitos do modelo computacional\r\n\r\nAs redes neurais tradicionais normalmente compactam todas as informações de entrada em um vetor de comprimento fixo ao processar dados de sequência. Essa abordagem tem gargalos de informação óbvios, especialmente ao lidar com sequências longas, onde as informações iniciais são facilmente substituídas por informações subsequentes.\r\n\r\n**Limitações dos métodos tradicionais**:\r\n- Gargalos de informação: vetores codificados de comprimento fixo lutam para armazenar todas as informações importantes\r\n- Dependências de longa distância: dificuldade em modelar relacionamentos entre elementos distantes em uma sequência de entrada\r\n- Eficiência computacional: Toda a sequência precisa ser processada para obter o resultado final\r\n- Explicabilidade: Dificuldade em entender o processo de tomada de decisão do modelo\r\n- Flexibilidade: incapaz de ajustar dinamicamente as estratégias de processamento de informações com base nas demandas da tarefa\r\n\r\n**Soluções para mecanismos de atenção**:\r\nO mecanismo de atenção permite que o modelo se concentre seletivamente em diferentes partes da entrada enquanto processa cada saída, introduzindo um mecanismo dinâmico de alocação de peso:\r\n- Seleção dinâmica: selecione dinamicamente informações relevantes com base nos requisitos atuais da tarefa\r\n- Acesso global: acesso direto a qualquer local da sequência de entrada\r\n- Computação paralela: Suporta processamento paralelo para melhorar a eficiência computacional\r\n- Explicabilidade: os pesos de atenção fornecem uma explicação visual das decisões do modelo\r\n\r\n## Princípios matemáticos dos mecanismos de atenção\r\n\r\n### Modelo de Atenção Básica\r\n\r\nA ideia central do mecanismo de atenção é atribuir um peso a cada elemento da sequência de entrada, o que reflete a importância desse elemento para a tarefa em questão.\r\n\r\n**Representação Matemática**:\r\nDada a sequência de entrada X = {x₁, x₂, ..., xn} e o vetor de consulta q, o mecanismo de atenção calcula o peso de atenção para cada elemento de entrada:\r\n\r\nα_i = f(q, x_i) # Função de pontuação de atenção\r\nα̃_i = softmax(α_i) = exp(α_i) / Σj exp(αj) # Peso normalizado\r\n\r\nO vetor de contexto final é obtido por soma ponderada:\r\nc = Σi α̃_i · x_i\r\n\r\n**Componentes dos Mecanismos de Atenção**:\r\n1. Consulta: Indica as informações que precisam ser observadas no momento\r\n2. Chave: As informações de referência usadas para calcular o peso da atenção\r\n3. Valor: Informação que realmente participa da soma ponderada\r\n4. **Função de atenção**: Uma função que calcula a semelhança entre consultas e chaves\r\n\r\n### Explicação detalhada da função de pontuação de atenção\r\n\r\nA função de pontuação de atenção determina como a correlação entre a consulta e a entrada é calculada. Diferentes funções de pontuação são adequadas para diferentes cenários de aplicação.\r\n\r\n**1. Atenção ao produto ponto**:\r\nα_i = q^T · x_i\r\n\r\nEsse é o mecanismo de atenção mais simples e é computacionalmente eficiente, mas requer que as consultas e entradas tenham as mesmas dimensões.\r\n\r\n**Mérito**:\r\n- Cálculos simples e alta eficiência\r\n- Pequeno número de parâmetros e sem necessidade de parâmetros adicionais que possam ser aprendidos\r\n- Distinguir efetivamente entre vetores semelhantes e diferentes no espaço de alta dimensão\r\n\r\n**Deficiência**:\r\n- Exigir que consultas e chaves tenham as mesmas dimensões\r\n- A instabilidade numérica pode ocorrer no espaço de alta dimensão\r\n- Falta de capacidade de aprendizado para se adaptar a relações complexas de similaridade\r\n\r\n**2. Atenção ao produto escalonado **:\r\nα_i = (q^T · x_i) / √d\r\n\r\nonde d é a dimensão do vetor. O fator de escala evita o desaparecimento do gradiente\r\n\r\n**A necessidade de dimensionamento**:\r\nQuando a dimensão d é grande, a variância do produto escalar aumenta, fazendo com que a função softmax entre na região de saturação e o gradiente se torne pequeno. Ao dividir por √d, a variância do produto escalar pode ser mantida estável.\r\n\r\n**Derivação Matemática**:\r\nAssumindo que os elementos q e k são variáveis aleatórias independentes, com uma média de 0 e uma variância de 1, então:\r\n- q ^ T · A variância de k é d\r\n- A variância de (q ^ T · k ) / √d é 1\r\n\r\n**3. Atenção aditiva**:\r\nα_i = v^T · tanh(W_q · q + W_x · x_i)\r\n\r\nConsultas e entradas são mapeadas para o mesmo espaço por meio de uma matriz de parâmetros que pode ser aprendida W_q e W_x e, em seguida, a similaridade é calculada.\r\n\r\n**Análise de Vantagem**:\r\n- Flexibilidade: Pode lidar com consultas e chaves em diferentes dimensões\r\n- Capacidades de aprendizagem: Adapte-se a relações complexas de similaridade com parâmetros que podem ser aprendidos\r\n- Recursos de expressão: transformações não lineares fornecem recursos de expressão aprimorados\r\n\r\n**Análise de parâmetros**:\r\n- W_q ∈ R^{d_h×d_q}: Consultar a matriz de projeção\r\n- W_x ∈ R^{d_h×d_x}: Matriz de projeção de chave\r\n- v ∈ R^{d_h}: Vetor de peso de atenção\r\n- d_h: Dimensões da camada oculta\r\n\r\n**4. Atenção MLP**:\r\nα_i = MLP([q; x_i])\r\n\r\nUse perceptrons multicamadas para aprender funções de correlação entre consultas e entradas diretamente.\r\n\r\n**Estrutura de rede**:\r\nOs MLPs normalmente contêm 2-3 camadas totalmente conectadas:\r\n- Camada de entrada: consultas de emenda e vetores de chave\r\n- Camada oculta: Ative funções usando ReLU ou tanh\r\n- Camada de saída: gera pontuações de atenção escalar\r\n\r\n**Análise de prós e contras**:\r\nMérito:\r\n- Habilidades expressivas mais fortes\r\n- Relações não lineares complexas podem ser aprendidas\r\n- Sem restrições nas dimensões de entrada\r\n\r\nDeficiência:\r\n- Grande número de parâmetros e fácil sobreajuste\r\n- Alta complexidade computacional\r\n- Longo tempo de treinamento\r\n\r\n### Mecanismo de atenção de cabeça múltipla\r\n\r\nA atenção de várias cabeças é um componente central da arquitetura do transformador, permitindo que os modelos prestem atenção a diferentes tipos de informações em paralelo em diferentes subespaços de representação.\r\n\r\n**Definição matemática**:\r\nMultiHead(Q, K, V) = Concat(head₁, head₂, ..., headh) · W^O\r\n\r\nonde cada cabeça de atenção é definida como:\r\nheadi = Atenção(Q· W_i^Q, K· W_i^K, V·W_i^V)\r\n\r\n**Matriz de parâmetros**:\r\n- W_i^Q ∈ R^{d_model×d_k}: A matriz de projeção de consulta do i-ésimo cabeçalho\r\n- W_i^K ∈ R^{d_model×d_k}: a matriz de projeção chave do i-ésimo cabeçalho\r\n- W_i^V ∈ R^{d_model×d_v}: Matriz de projeção de valor para a i-ésima cabeça\r\n- W^O ∈ R^{h·d_v×d_model}: Matriz de projeção de saída\r\n\r\n**Vantagens da atenção do touro**:\r\n1. **Diversidade**: Diferentes cabeças podem se concentrar em diferentes tipos de características\r\n2. **Paralelismo**: Vários cabeçotes podem ser calculados em paralelo, melhorando a eficiência\r\n3. **Capacidade de expressão**: Aprimorada a capacidade de aprendizado de representação do modelo\r\n4. ** Estabilidade **: O efeito de integração de várias cabeças é mais estável\r\n5. **Especialização**: Cada chefe pode se especializar em tipos específicos de relacionamentos\r\n\r\n**Considerações para seleção de cabeça**:\r\n- Poucas cabeças: pode não capturar diversidade de informações suficiente\r\n- Número excessivo de funcionários: aumenta a complexidade computacional, potencialmente levando ao sobreajuste\r\n- Opções comuns: 8 ou 16 cabeçotes, ajustados de acordo com o tamanho do modelo e a complexidade da tarefa\r\n\r\n**Estratégia de alocação de dimensão**:\r\nNormalmente, defina d_k = d_v = d_model / h para garantir que a quantidade total de parâmetros seja razoável:\r\n- Manter o volume computacional total relativamente estável\r\n- Cada cabeça tem capacidade de representação suficiente\r\n- Evite a perda de informações causada por dimensões muito pequenas\r\n\r\n## Mecanismo de auto-atenção\r\n\r\n### O conceito de auto-atenção\r\n\r\nA autoatenção é uma forma especial de mecanismo de atenção em que consultas, chaves e valores vêm da mesma sequência de entrada. Esse mecanismo permite que cada elemento da sequência se concentre em todos os outros elementos da sequência.\r\n\r\n**Representação Matemática**:\r\nPara a sequência de entrada X = {x₁, x₂, ..., xn}:\r\n- Matriz de consulta: Q = X · W^Q\r\n- Matriz de chaves: K = X · W ^ K  \r\n- Matriz de valores: V = X · W ^ V\r\n\r\nSaída de atenção:\r\nAtenção(Q, K, V) = softmax(QK^T / √d_k) · V\r\n\r\n**Processo de cálculo da autoatenção**:\r\n1. **Transformação Linear**: A sequência de entrada é obtida por três transformações lineares diferentes para obter Q, K e V\r\n2. **Cálculo de similaridade**: Calcule a matriz de similaridade entre todos os pares de posições\r\n3. **Normalização de peso**: Use a função softmax para normalizar os pesos de atenção\r\n4. **Soma ponderada**: Soma ponderada de vetores de valor com base em pesos de atenção\r\n\r\n### Vantagens da auto-atenção\r\n\r\n**1. Modelagem de dependência de longa distância**:\r\nA autoatenção pode modelar diretamente a relação entre quaisquer duas posições em uma sequência, independentemente da distância. Isso é especialmente importante para tarefas de OCR, onde o reconhecimento de caracteres geralmente requer a consideração de informações contextuais à distância.\r\n\r\n**Análise de complexidade de tempo**:\r\n- RNN: Cálculo da sequência O(n), difícil de paralelizar\r\n- CNN: O(log n) para cobrir toda a sequência\r\n- Autoatenção: O comprimento do caminho de O(1) se conecta diretamente a qualquer local\r\n\r\n**2. Computação paralela**:\r\nAo contrário dos RNNs, o cálculo da autoatenção pode ser totalmente paralelizado, melhorando muito a eficiência do treinamento.\r\n\r\n**Vantagens da paralelização**:\r\n- Os pesos de atenção para todas as posições podem ser calculados simultaneamente\r\n- As operações de matriz podem aproveitar ao máximo o poder de computação paralela das GPUs\r\n- O tempo de treinamento é significativamente reduzido em comparação com o RNN\r\n\r\n**3. Interpretabilidade**:\r\nA matriz de peso de atenção fornece uma explicação visual das decisões do modelo, facilitando a compreensão de como o modelo funciona.\r\n\r\n**Análise Visual**:\r\n- Mapa de calor de atenção: mostra quanta atenção cada local presta aos outros\r\n- Padrões de atenção: analise padrões de atenção de diferentes cabeças\r\n- Análise hierárquica: observe mudanças nos padrões de atenção em diferentes níveis\r\n\r\n**4. Flexibilidade**:\r\nEle pode ser facilmente estendido para sequências de diferentes comprimentos sem modificar a arquitetura do modelo.\r\n\r\n### Codificação de posição\r\n\r\nComo o mecanismo de autoatenção em si não contém informações de posição, é necessário fornecer ao modelo informações de posição dos elementos na sequência por meio da codificação de posição.\r\n\r\n**A necessidade de codificação de posição**:\r\nO mecanismo de autoatenção é imutável, ou seja, alterar a ordem da sequência de entrada não afeta a saída. Mas em tarefas de OCR, as informações de localização dos personagens são cruciais.\r\n\r\n**Codificação da posição do seno**:\r\nPE(pos, 2i) = sin(pos / 10000^(2i/d_model))\r\nPE(pos, 2i+1) = cos(pos / 10000^(2i/d_model))\r\n\r\nNisso:\r\n- pos: Índice de localização\r\n- i: Índice de dimensão\r\n- d_model: Dimensão do modelo\r\n\r\n**Vantagens da codificação da posição senoidal**:\r\n- Determinístico: Não é necessário aprendizado, reduzindo a quantidade de parâmetros\r\n- Extrapolação: Pode lidar com sequências mais longas do que quando treinado\r\n- Periodicidade: Tem uma boa natureza periódica, o que é conveniente para o modelo aprender relações de posição relativa\r\n\r\n**Codificação de posição que pode ser aprendida**:\r\nA codificação de posição é usada como um parâmetro que pode ser aprendido e a representação da posição ideal é aprendida automaticamente durante o processo de treinamento.\r\n\r\n**Método de implementação**:\r\n- Atribua um vetor que pode ser aprendido a cada posição\r\n- Some com as incorporações de entrada para obter a entrada final\r\n- Atualizar o código de posição com retropropagação\r\n\r\n**Prós e contras da codificação de posição que pode ser aprendida**:\r\nMérito:\r\n- Adaptável para aprender representações posicionais específicas da tarefa\r\n- O desempenho geralmente é um pouco melhor do que a codificação de posição fixa\r\n\r\nDeficiência:\r\n- Aumente a quantidade de parâmetros\r\n- Incapacidade de processar sequências além da duração do treinamento\r\n- Mais dados de treinamento são necessários\r\n\r\n**Codificação de posição relativa**:\r\nEle não codifica diretamente a posição absoluta, mas codifica relações de posição relativa.\r\n\r\n**Princípio de implementação**:\r\n- Adicionando viés de posição relativa aos cálculos de atenção\r\n- Concentre-se apenas na distância relativa entre os elementos, não em sua posição absoluta\r\n- Melhor capacidade de generalização\r\n\r\n## Atenção Aplicações em OCR\r\n\r\n### Atenção sequência a sequência\r\n\r\nA aplicação mais comum em tarefas de OCR é o uso de mecanismos de atenção em modelos sequência a sequência. O codificador codifica a imagem de entrada em uma sequência de recursos e o decodificador se concentra na parte relevante do codificador por meio de um mecanismo de atenção à medida que gera cada caractere.\r\n\r\n**Arquitetura do codificador-decodificador**:\r\n1. **Codificador**: CNN extrai recursos de imagem, RNN codifica como representação de sequência\r\n2. **Módulo de Atenção**: Calcule o peso de atenção do estado do decodificador e a saída do codificador\r\n3. **Decodificador**: Gere sequências de caracteres com base em vetores de contexto ponderados por atenção\r\n\r\n**Atenção Processo de Cálculo**:\r\nNo momento de decodificação t, o estado do decodificador é s_t e a saída do codificador é H = {h₁, h₂, ..., hn}:\r\n\r\ne_ti = a(s_t, h_i) # Pontuação de atenção\r\nα_ti = softmax(e_ti) # Peso de atenção\r\nc_t = Σi α_ti · h_i # Vetor de contexto\r\n\r\n**Seleção de funções de atenção**:\r\nAs funções de atenção comumente usadas incluem:\r\n- Atenção acumulada: e_ti = s_t^T · h_i\r\n- Atenção aditiva: e_ti = v ^ T · tanh(W_s · s_t + W_h · h_i)\r\n- Atenção bilinear: e_ti = s_t^T · W · h_i\r\n\r\n### Módulo de Atenção Visual\r\n\r\nA atenção visual aplica mecanismos de atenção diretamente no mapa de recursos da imagem, permitindo que o modelo se concentre em áreas importantes da imagem.\r\n\r\n**Atenção Espacial**:\r\nCalcule os pesos de atenção para cada posição espacial do mapa de recursos:\r\nA(i,j) = σ(W_a · [F(i,j); g])\r\n\r\nNisso:\r\n- F(i,j): autovetor de posição (i,j).\r\n- g: Informações de contexto global\r\n- W_a: Matriz de peso que pode ser aprendida\r\n- σ: função de ativação sigmóide\r\n\r\n**Passos para alcançar a atenção espacial**:\r\n1. **Extração de recursos**: Use a CNN para extrair mapas de recursos de imagem\r\n2. **Agregação Global de Informações**: Obtenha recursos globais por meio de agrupamento médio global ou agrupamento máximo global\r\n3. **Cálculo de atenção**: Calcule os pesos de atenção com base em recursos locais e globais\r\n4. **Aprimoramento de recursos**: Aprimore o recurso original com pesos de atenção\r\n\r\n**Atenção do canal**:\r\nOs pesos de atenção são calculados para cada canal do gráfico de recursos:\r\nA_c = σ(W_c · LACUNA (F_c))\r\n\r\nNisso:\r\n- GAP: Agrupamento médio global\r\n- F_c: Mapa de recursos do canal c\r\n- W_c: A matriz de peso da atenção do canal\r\n\r\n**Princípios de Atenção do Canal**:\r\n- Diferentes canais capturam diferentes tipos de recursos\r\n- Seleção de canais de recursos importantes por meio de mecanismos de atenção\r\n- Suprimir recursos irrelevantes e aprimorar os úteis\r\n\r\n**Atenção mista**:\r\nCombine atenção espacial e atenção de canal:\r\nF_output = F ⊙ A_spatial ⊙ A_channel\r\n\r\nonde ⊙ representa a multiplicação no nível do elemento.\r\n\r\n**Vantagens da atenção mista**:\r\n- Considere a importância das dimensões espaciais e de passagem\r\n- Recursos de seleção de recursos mais refinados\r\n- Melhor desempenho\r\n\r\n### Atenção multiescala\r\n\r\nO texto na tarefa de OCR tem escalas diferentes e o mecanismo de atenção em várias escalas pode prestar atenção a informações relevantes em diferentes resoluções.\r\n\r\n**Atenção característica da pirâmide**:\r\nO mecanismo de atenção é aplicado aos mapas de recursos de diferentes escalas e, em seguida, os resultados de atenção de várias escalas são fundidos.\r\n\r\n**Arquitetura de implementação**:\r\n1. **Extração de recursos em várias escalas**: Use redes de pirâmide de recursos para extrair recursos em diferentes escalas\r\n2. **Atenção específica da balança**: Calcule os pesos de atenção independentemente em cada balança\r\n3. **Fusão entre escalas**: Integre resultados de atenção de diferentes escalas\r\n4. **Previsão Final**: Faça uma previsão final com base nos recursos fundidos\r\n\r\n**Seleção de escala adaptável**:\r\nDe acordo com as necessidades da tarefa de reconhecimento atual, a escala de recursos mais adequada é selecionada dinamicamente.\r\n\r\n**Estratégia de Seleção**:\r\n- Seleção baseada em conteúdo: seleciona automaticamente a escala apropriada com base no conteúdo da imagem\r\n- Seleção baseada em tarefas: Selecione a escala com base nas características da tarefa identificada\r\n- Alocação dinâmica de peso: Atribua pesos dinâmicos a diferentes balanças\r\n\r\n## Variações dos mecanismos de atenção\r\n\r\n### Atenção esparsa\r\n\r\nA complexidade computacional do mecanismo de auto-atenção padrão é O(n²), que é computacionalmente caro para sequências longas. A atenção esparsa reduz a complexidade computacional, limitando o alcance da atenção.\r\n\r\n**Atenção Local**:\r\nCada local se concentra apenas no local dentro da janela fixa ao seu redor.\r\n\r\n**Representação Matemática**:\r\nPara a posição i, apenas o peso de atenção dentro da faixa de posição [i-w, i+w] é calculado, onde w é o tamanho da janela.\r\n\r\n**Análise de prós e contras**:\r\nMérito:\r\n- Complexidade computacional reduzida a O(n·w)\r\n- As informações de contexto local são mantidas\r\n- Adequado para lidar com sequências longas\r\n\r\nDeficiência:\r\n- Não é possível capturar dependências de longa distância\r\n- O tamanho da janela precisa ser cuidadosamente ajustado\r\n- Perda potencial de informações globais importantes\r\n\r\n**Atenção em Fragmentação**:\r\nDivida a sequência em partes, cada uma focando apenas no resto dentro do mesmo bloco.\r\n\r\n**Método de implementação**:\r\n1. Divida a sequência de comprimento n em n / b blocos, cada um dos quais é um tamanho b\r\n2. Calcule a atenção completa dentro de cada bloco\r\n3. Sem cálculo de atenção entre blocos\r\n\r\nComplexidade computacional: O(n·b), onde b << n\r\n\r\n**Atenção aleatória**:\r\nCada posição seleciona aleatoriamente uma parte do local para o cálculo da atenção.\r\n\r\n**Estratégia de seleção aleatória**:\r\n- Aleatório fixo: padrões de conexão aleatórios predeterminados\r\n- Dynamic Random: selecione conexões dinamicamente durante o treinamento\r\n- Aleatório estruturado: combina conexões locais e aleatórias\r\n\r\n### Atenção linear\r\n\r\nA atenção linear reduz a complexidade dos cálculos de atenção de O(n²) para O(n) por meio de transformações matemáticas.\r\n\r\n**Atenção Nucleada**:\r\nAproximando operações softmax usando funções de kernel:\r\nAtenção (Q, K, V) ≈ φ (Q) · (φ(K)^T · V)\r\n\r\nφ delas são funções de mapeamento de recursos.\r\n\r\n**Funções comuns do kernel**:\r\n- Núcleo ReLU: φ(x) = ReLU(x)\r\n- Kernel LEU: φ(x) = ELU(x) + 1\r\n- Kernels de recursos aleatórios: Use recursos aleatórios de Fourier\r\n\r\n**Vantagens da atenção linear**:\r\n- A complexidade computacional aumenta linearmente\r\n- Os requisitos de memória são significativamente reduzidos\r\n- Adequado para lidar com sequências muito longas\r\n\r\n**Trade-offs de desempenho**:\r\n- Precisão: Normalmente um pouco abaixo da atenção padrão\r\n- Eficiência: melhora significativamente a eficiência computacional\r\n- Aplicabilidade: Adequado para cenários com recursos limitados\r\n\r\n### Atenção cruzada\r\n\r\nEm tarefas multimodais, a atenção cruzada permite a interação de informações entre diferentes modalidades.\r\n\r\n**Atenção cruzada imagem-texto**:\r\nOs recursos de texto são usados como consultas e os recursos de imagem são usados como chaves e valores para perceber a atenção do texto às imagens.\r\n\r\n**Representação Matemática**:\r\nCrossAttention(Q_text, K_image, V_image) = softmax(Q_text · K_image ^ T / √d) · V_image\r\n\r\n**Cenários de aplicação**:\r\n- Geração de descrição de imagem\r\n- Perguntas e respostas visuais\r\n- Compreensão de documentos multimodais\r\n\r\n**Atenção cruzada bidirecional**:\r\nCalcule a atenção de imagem para texto e de texto para imagem.\r\n\r\n**Método de implementação**:\r\n1. Imagem para texto: atenção (Q_image, K_text, V_text)\r\n2. Texto para imagem: atenção (Q_text, K_image, V_image)\r\n3. Fusão de recursos: mesclar resultados de atenção em ambas as direções\r\n\r\n## Estratégias de treinamento e otimização\r\n\r\n### Supervisão de Atenção\r\n\r\nGuie o modelo para aprender os padrões de atenção corretos, fornecendo sinais supervisionados de atenção.\r\n\r\n**Perda de alinhamento de atenção**:\r\nL_align = || A - A_gt|| ²\r\n\r\nNisso:\r\n- A: Matriz de peso de atenção prevista\r\n- A_gt: Tags de atenção autênticas\r\n\r\n**Aquisição de sinal supervisionada**:\r\n- Anotação manual: especialistas marcam áreas importantes\r\n- Heurística: Gere rótulos de atenção com base em regras\r\n- Supervisão fraca: use sinais de supervisão de baixa granularidade\r\n\r\n**Regularização de atenção**:\r\nIncentive a dispersão ou suavidade dos pesos de atenção:\r\nL_reg = λ₁ · || UMA|| ₁ + λ₂ · || ∇A|| ²\r\n\r\nNisso:\r\n- || UMA|| ₁: Regularização L1 para incentivar a esparsidade\r\n- || ∇A|| ²: Regularização da suavidade, incentivando pesos de atenção semelhantes em posições adjacentes\r\n\r\n**Aprendizagem multitarefa**:\r\nA previsão da atenção é usada como uma tarefa secundária e treinada em conjunto com a tarefa principal.\r\n\r\n**Projeto da função de perda**:\r\nL_total = L_main + α · L_attention + β · L_reg\r\n\r\nonde α e β são os hiperparâmetros que equilibram diferentes termos de perda.\r\n\r\n### Visualização da atenção\r\n\r\nA visualização de pesos de atenção ajuda a entender como o modelo funciona e a depurar problemas do modelo.\r\n\r\n**Visualização do mapa de calor**:\r\nMapeie os pesos de atenção como um mapa de calor, sobrepondo-os na imagem original para mostrar a área de interesse do modelo.\r\n\r\n**Etapas de implementação**:\r\n1. Extraia a matriz de peso de atenção\r\n2. Mapeie os valores de peso para o espaço de cores\r\n3. Ajuste o tamanho do mapa de calor para corresponder à imagem original\r\n4. Sobreposição ou lado a lado\r\n\r\n**Trajetória de Atenção**:\r\nExibe a trajetória de movimento do foco de atenção durante a decodificação, auxiliando na compreensão do processo de reconhecimento do modelo.\r\n\r\n**Análise de trajetória**:\r\n- A ordem em que a atenção se move\r\n- Habitação de atenção\r\n- Padrão de saltos de atenção\r\n- Identificação de comportamento anormal de atenção\r\n\r\n**Visualização de atenção de várias cabeças**:\r\nA distribuição de peso de diferentes cabeças de atenção é visualizada separadamente e o grau de especialização de cada cabeça é analisado.\r\n\r\n**Dimensões analíticas**:\r\n- Diferenças frente a frente: Diferenças regionais de preocupação para diferentes cabeças\r\n- Especialização da cabeça: Algumas cabeças se especializam em tipos específicos de recursos\r\n- Importância das Cabeças: A contribuição de diferentes cabeças para o resultado final\r\n\r\n### Otimização Computacional\r\n\r\n**Otimização de memória**:\r\n- Pontos de verificação de gradiente: use pontos de verificação de gradiente no treinamento de sequência longa para reduzir o volume de memória\r\n- Precisão mista: reduz os requisitos de memória com o treinamento FP16\r\n- Cache de atenção: Armazena em cache pesos de atenção calculados\r\n\r\n**Aceleração Computacional**:\r\n- Fragmentação de matrizes: Calcule matrizes grandes em partes para reduzir picos de memória\r\n- Cálculos esparsos: acelere os cálculos com a dispersão dos pesos de atenção\r\n- Otimização de hardware: Otimize os cálculos de atenção para hardware específico\r\n\r\n**Estratégia de paralelização**:\r\n- Paralelismo de dados: processe diferentes amostras em paralelo em várias GPUs\r\n- Paralelismo de modelos: distribua cálculos de atenção em vários dispositivos\r\n- Paralelização de pipeline: pipeline de diferentes camadas de computação\r\n\r\n## Avaliação e análise de desempenho\r\n\r\n### Avaliação da Qualidade da Atenção\r\n\r\n**Precisão de atenção**:\r\nMeça o alinhamento dos pesos de atenção com anotações manuais.\r\n\r\nFórmula de cálculo:\r\nPrecisão = (Número de posições focadas corretamente) / (Total de posições)\r\n\r\n**Concentração**:\r\nA concentração da distribuição da atenção é medida usando entropia ou o coeficiente de Gini.\r\n\r\nCálculo de entropia:\r\nH(A) = -Σi αi · log(αi)\r\n\r\nonde αi é o peso de atenção da i-ésima posição.\r\n\r\n**Estabilidade de atenção**:\r\nAvalie a consistência dos padrões de atenção sob entradas semelhantes.\r\n\r\nIndicadores de estabilidade:\r\nEstabilidade = 1 - || A₁ - A₂|| ₂ / 2\r\n\r\nonde A₁ e A₂ são as matrizes de peso de atenção de entradas semelhantes.\r\n\r\n### Análise de Eficiência Computacional\r\n\r\n**Complexidade do tempo**:\r\nAnalise a complexidade computacional e o tempo real de execução de diferentes mecanismos de atenção.\r\n\r\nComparação de complexidade:\r\n- Atenção padrão: O(n²d)\r\n- Atenção esparsa: O(n·k·d), k<< n\r\n- Atenção linear: O(n·d²)\r\n\r\n**Uso de memória**:\r\nAvalie a demanda por memória de GPU para mecanismos de atenção.\r\n\r\nAnálise de memória:\r\n- Matriz de peso de atenção: O(n²)\r\n- Resultado do cálculo intermediário: O(n·d)\r\n- Armazenamento de gradiente: O(n²d)\r\n\r\n**Análise de Consumo de Energia**:\r\nAvaliar o impacto do consumo de energia dos mecanismos de atenção em dispositivos móveis.\r\n\r\nFatores de consumo de energia:\r\n- Força de cálculo: Número de operações de ponto flutuante\r\n- Acesso à memória: sobrecarga de transferência de dados\r\n- Utilização de hardware: uso eficiente de recursos de computação\r\n\r\n## Casos de aplicação do mundo real\r\n\r\n### Reconhecimento de texto manuscrito\r\n\r\nNo reconhecimento de texto manuscrito, o mecanismo de atenção ajuda o modelo a se concentrar no caractere que está reconhecendo no momento, ignorando outras informações que distraem.\r\n\r\n**Efeitos de aplicação**:\r\n- Precisão de reconhecimento aumentada em 15-20%\r\n- Robustez aprimorada para planos de fundo complexos\r\n- Capacidade aprimorada de lidar com texto organizado irregularmente\r\n\r\n**Implementação Técnica**:\r\n1. **Atenção Espacial**: Preste atenção à área espacial onde o personagem está localizado\r\n2. **Atenção Temporal**: Utilize a relação temporal entre os personagens\r\n3. **Atenção em várias escalas**: Manipule personagens de tamanhos diferentes\r\n\r\n**Estudo de caso**:\r\nEm tarefas de reconhecimento de palavras manuscritas em inglês, os mecanismos de atenção podem:\r\n- Localize com precisão a posição de cada personagem\r\n- Lidar com o fenômeno de traços contínuos entre personagens\r\n- Utilizar o conhecimento do modelo de linguagem no nível da palavra\r\n\r\n### Reconhecimento de texto de cena\r\n\r\nEm cenas naturais, o texto geralmente é incorporado em planos de fundo complexos e os mecanismos de atenção podem separar efetivamente o texto do plano de fundo.\r\n\r\n**Características técnicas**:\r\n- Atenção multiescala para trabalhar com texto de diferentes tamanhos\r\n- Atenção espacial para localizar áreas de texto\r\n- Seleção de recursos úteis de atenção do canal\r\n\r\n**Desafios e Soluções**:\r\n1. **Distração de fundo**: Filtre o ruído de fundo com atenção espacial\r\n2. **Mudanças de iluminação**: Adapte-se a diferentes condições de iluminação através da atenção do canal\r\n3. **Deformação Geométrica**: Incorpora mecanismos de correção geométrica e atenção\r\n\r\n**Melhorias de desempenho**:\r\n- Melhoria de 10 a 15% na precisão dos conjuntos de dados ICDAR\r\n- Adaptabilidade significativamente aprimorada a cenários complexos\r\n- A velocidade de raciocínio é mantida dentro de limites aceitáveis\r\n\r\n### Análise de documentos\r\n\r\nEm tarefas de análise de documentos, os mecanismos de atenção ajudam os modelos a entender a estrutura e as relações hierárquicas dos documentos.\r\n\r\n**Cenários de aplicação**:\r\n- Identificação da tabela: Concentre-se na estrutura da coluna da tabela\r\n- Análise de layout: identifique elementos como títulos, corpo, imagens e muito mais\r\n- Extração de informações: localize a localização das principais informações\r\n\r\n**Inovação Tecnológica**:\r\n1. **Atenção Hierárquica**: Aplique a atenção em diferentes níveis\r\n2. **Atenção Estruturada**: Considere as informações estruturadas do documento\r\n3. **Atenção Multimodal**: Misturando texto e informações visuais\r\n\r\n**Resultados práticos**:\r\n- Aumente a precisão do reconhecimento de tabelas em mais de 20%\r\n- Poder de processamento significativamente aumentado para layouts complexos\r\n- A precisão da extração de informações foi bastante melhorada\r\n\r\n## Tendências futuras de desenvolvimento\r\n\r\n### Mecanismo de atenção eficiente\r\n\r\nÀ medida que o comprimento da sequência aumenta, o custo computacional do mecanismo de atenção torna-se um gargalo. As direções futuras da pesquisa incluem:\r\n\r\n**Otimização de algoritmo**:\r\n- Modo de atenção esparsa mais eficiente\r\n- Melhorias nos métodos de cálculo aproximados\r\n- Design de atenção amigável ao hardware\r\n\r\n**Inovação arquitetônica**:\r\n- Mecanismo de atenção hierárquica\r\n- Roteamento dinâmico de atenção\r\n- Gráficos de cálculo adaptáveis\r\n\r\n**Avanço teórico**:\r\n- Análise teórica do mecanismo de atenção\r\n- Prova matemática de padrões de atenção ideais\r\n- Teoria unificada da atenção e outros mecanismos\r\n\r\n### Atenção multimodal\r\n\r\nOs futuros sistemas de OCR integrarão mais informações de várias modalidades:\r\n\r\n**Fusão Visual-Linguagem**:\r\n- Atenção conjunta de imagens e texto\r\n- Transmissão de informações entre modalidades\r\n- Representação multimodal unificada\r\n\r\n**Fusão de Informações Temporais**:\r\n- Cronometrando a atenção no OCR de vídeo\r\n- Rastreamento de texto para cenas dinâmicas\r\n- Modelagem conjunta do espaço-tempo\r\n\r\n**Fusão multissensor**:\r\n- Atenção 3D combinada com informações de profundidade\r\n- Mecanismos de atenção para imagens multiespectrais\r\n- Modelagem conjunta de dados de sensores\r\n\r\n### Aprimoramento da interpretabilidade\r\n\r\nMelhorar a interpretabilidade dos mecanismos de atenção é uma importante direção de pesquisa:\r\n\r\n**Explicação de atenção**:\r\n- Métodos de visualização mais intuitivos\r\n- Explicação semântica dos padrões de atenção\r\n- Ferramentas de análise e depuração de erros\r\n\r\n**Raciocínio Causal**:\r\n- Análise causal da atenção\r\n- Métodos de raciocínio contrafactual\r\n- Tecnologia de verificação de robustez\r\n\r\n**Interativo**:\r\n- Ajustes de atenção interativos\r\n- Incorporação de feedback do usuário\r\n- Modo de atenção personalizado\r\n\r\n## Resumo\r\n\r\nComo parte importante do aprendizado profundo, o mecanismo de atenção desempenha um papel cada vez mais importante no campo do OCR. Da atenção básica de sequência a sequência à autoatenção complexa de várias cabeças, da atenção espacial à atenção em várias escalas, o desenvolvimento dessas tecnologias melhorou muito o desempenho dos sistemas de OCR.\r\n\r\n**Principais conclusões**:\r\n- O mecanismo de atenção simula a capacidade da atenção seletiva humana e resolve o problema de gargalos de informação\r\n- Os princípios matemáticos são baseados na soma ponderada, permitindo a seleção de informações aprendendo pesos de atenção\r\n- Atenção com várias cabeças e autoatenção são as principais técnicas dos mecanismos de atenção modernos\r\n- As aplicações em OCR incluem modelagem de sequência, atenção visual, processamento em várias escalas e muito mais\r\n- As direções de desenvolvimento futuras incluem otimização de eficiência, fusão multimodal, aprimoramento da interpretabilidade, etc\r\n\r\n**Conselhos práticos**:\r\n- Escolha o mecanismo de atenção apropriado para a tarefa específica\r\n- Preste atenção ao equilíbrio entre eficiência computacional e desempenho\r\n- Faça pleno uso da interpretabilidade da atenção para depuração de modelos\r\n- Fique de olho nos últimos avanços de pesquisa e desenvolvimentos tecnológicos\r\n\r\nÀ medida que a tecnologia continua a evoluir, os mecanismos de atenção continuarão a evoluir, fornecendo ferramentas ainda mais poderosas para OCR e outras aplicações de IA. Compreender e dominar os princípios e aplicações dos mecanismos de atenção é crucial para os técnicos envolvidos na pesquisa e desenvolvimento de OCR.</div>\n                        \n                        <div class=\"article-tags\">\n                            <h6>Etiqueta:</h6>\n                            <div class=\"tags-list\">\n                                \n                                <span class=\"tag\">Mecanismo de atenção</span>\n                                \n                                <span class=\"tag\">Atenção do touro</span>\n                                \n                                <span class=\"tag\">Auto-atenção</span>\n                                \n                                <span class=\"tag\">Codificação de posição</span>\n                                \n                                <span class=\"tag\">Atenção cruzada</span>\n                                \n                                <span class=\"tag\">Atenção esparsa</span>\n                                \n                                <span class=\"tag\">OCR</span>\n                                \n                                <span class=\"tag\">Transformer</span>\n                                \n                            </div>\n                        </div>\n                        \n                    </article>\n                    \n                    <footer class=\"article-footer\" id=\"articleFooter\">\n                        <div class=\"article-share\">\n                            <h6 style=\"color: #1a1c1f; margin-bottom: 1rem;\">Compartilhe e opere:</h6>\n                            <div class=\"share-buttons\">\n                                <a href=\"javascript:void(0)\" onclick=\"shareToWeibo()\" class=\"btn btn-weibo\">📱 Weibo compartilhou</a>\n                                <a href=\"javascript:void(0)\" onclick=\"copyUrl()\" class=\"btn btn-copy\">🔗 Copiar link</a>\n                                <button type=\"button\" class=\"btn btn-print\" onclick=\"printArticle()\">🖨️ Imprimir o artigo</button>\n                            </div>\n                        </div>\n                    </footer>\n                    \n                </div>\n                <div class=\"col-lg-4 col-xl-3 col-md-12\">\n                    <div class=\"sidebar\">\n                        <div class=\"sidebar-card toc-card\" id=\"tocCard\" style=\"display: none;\"><h5>Índice</h5><div id=\"tableOfContents\" class=\"toc-list\"></div></div>\n                        <div class=\"sidebar-card\">\n                            <h5>Leitura recomendada</h5>\n                            <div id=\"recommendedArticles\">\n                                \n                                <div class=\"recommended-item\"><h6><a href=\"Article.aspx?slug=document-intelligence-future-outlook\">【Série de Processamento Inteligente de Documentos·20】Perspectivas de desenvolvimento da tecnologia de processamento inteligente de documentos</a></h6><p class=\"text-muted small\">2025-08-19 • 1203 Próxima leitura</p></div>\n                                \n                                <div class=\"recommended-item\"><h6><a href=\"Article.aspx?slug=document-intelligence-quality-assurance\">【Série de Processamento Inteligente de Documentos·19】Sistema de Garantia de Qualidade de Processamento Inteligente de Documentos</a></h6><p class=\"text-muted small\">2025-08-19 • 1200 Próxima leitura</p></div>\n                                \n                                <div class=\"recommended-item\"><h6><a href=\"Article.aspx?slug=large-scale-document-processing-optimization\">【Série de processamento inteligente de documentos·18】 Otimização do desempenho do processamento de documentos em larga escala</a></h6><p class=\"text-muted small\">2025-08-19 • 1201 Próxima leitura</p></div>\n                                \n                            </div>\n                        </div>\n                    </div>\n</div>\n</div>\n</div>\n</div>\n<script>\nvar articleId,viewCountKey,now=new Date().getTime();\ndocument.addEventListener('DOMContentLoaded',function(){var articleDetail=document.getElementById('articleDetail');if(articleDetail&&articleDetail.querySelector('.article-content')){formatContent();initReadingProgress();optimizeImages();updateViewCount();}});\nfunction updateViewCount(){articleId=getArticleId();if(!articleId)return;viewCountKey='article_view_'+articleId;var lastViewTime=localStorage.getItem(viewCountKey);if(lastViewTime&&(now-parseInt(lastViewTime))<3600000)return;var xhr=new XMLHttpRequest();xhr.open('POST','/Article.ashx',true);xhr.setRequestHeader('Content-Type','application/x-www-form-urlencoded');xhr.onreadystatechange=function(){if(xhr.readyState===4&&xhr.status===200){try{var response=JSON.parse(xhr.responseText);if(response.success){updateViewCountDisplay(response.viewCount);localStorage.setItem(viewCountKey,now.toString());}}catch(e){}}};xhr.send('action=updateviewcount&articleId='+encodeURIComponent(articleId));}\nfunction getArticleId(){var articleDetail=document.getElementById('articleDetail');if(articleDetail){var id=articleDetail.getAttribute('data-article-id');if(id)return id;}var urlParams=new URLSearchParams(window.location.search);return urlParams.get('id');}\nfunction updateViewCountDisplay(newCount){document.querySelectorAll('.view-count').forEach(function(element){element.textContent=newCount;});}\nfunction decodeHtmlEntities(text){var textArea=document.createElement('textarea');textArea.innerHTML=text;return textArea.value;}\nfunction formatContent(){var contentDiv=document.getElementById('articleContent');if(!contentDiv)return;var content=contentDiv.textContent||contentDiv.innerText;if(!content)return;content=decodeHtmlEntities(content);content=formatMarkdownContent(content);content=formatIndentation(content);content=formatLists(content);content=formatParagraphs(content);content=formatLinks(content);contentDiv.innerHTML=content;initTableOfContents();}\nfunction formatIndentation(content){var lines=content.split('\\n'),result=[];for(var i=0;i<lines.length;i++){var line=lines[i];if(line.trim().match(/^<[^>]+>/)){result.push(line);continue;}line=line.replace(/^\\t+/g,function(match){return '    '.repeat(match.length);});if(line.match(/^  +[^\\s]/)){var indentMatch=line.match(/^( +)(.*)$/);if(indentMatch){var indentLevel=Math.floor(indentMatch[1].length/2);line='<span class=\"text-indent-'+indentLevel+'\">'+indentMatch[2]+'</span>';}}result.push(line);}return result.join('\\n');}\nfunction formatMarkdownContent(content){content=content.replace(/^\\s*##### (.+)$/gm,'<h5>$1</h5>').replace(/^\\s*#### (.+)$/gm,'<h4>$1</h4>').replace(/^\\s*### (.+)$/gm,'<h3>$1</h3>').replace(/^\\s*## (.+)$/gm,'<h2>$1</h2>').replace(/^\\s*# (.+)$/gm,'<h1>$1</h1>').replace(/^> (.+)$/gm,'<blockquote><p>$1</p></blockquote>').replace(/\\*\\*([^*]+?)\\*\\*/g,'<strong>$1</strong>').replace(/\\*([^*\\n]+?)\\*/g,'<em>$1</em>').replace(/`([^`]+?)`/g,'<code class=\"inline-code\">$1</code>').replace(/^(\\*\\*[^*]+?\\*\\*)：/gm,'<div class=\"label-item\"><span class=\"label\">$1</span></div>').replace(/^([^：\\n]+?)：$/gm,'<div class=\"section-label\">$1：</div>').replace(/^(\\*\\*[^*]+?\\*\\*)：(.+)$/gm,'<div class=\"definition-item\"><span class=\"definition-label\">$1：</span><span class=\"definition-content\">$2</span></div>').replace(/^(nota|nota|nota):(.+)$/gm,'<div class=\"note-item\"><span class=\"note-label\">$1：</span><span class=\"note-content\">$2</span></div>');content=formatLists(content);content=formatMathSymbols(content);content=formatParagraphs(content);return content;}\nfunction formatLists(content){var lines=content.split('\\n'),result=[],inOrderedList=false,inUnorderedList=false;for(var i=0;i<lines.length;i++){var line=lines[i],trimmed=line.trim();var orderedMatch=line.match(/^(\\d+)\\.\\s*(.+)$/);var unorderedMatch=line.match(/^[-*+]\\s*(.+)$/);if(orderedMatch){if(!inOrderedList){if(inUnorderedList){result.push('</ul>');inUnorderedList=false;}result.push('<ol class=\"formatted-list ordered\">');inOrderedList=true;}var listContent=orderedMatch[2].replace(/^\\*\\*([^*]+?)\\*\\*：(.+)$/,'<strong class=\"list-title\">$1：</strong><span class=\"list-content\">$2</span>');result.push('<li>'+listContent+'</li>');}else if(unorderedMatch){if(!inUnorderedList){if(inOrderedList){result.push('</ol>');inOrderedList=false;}result.push('<ul class=\"formatted-list unordered\">');inUnorderedList=true;}var listContent=unorderedMatch[1].replace(/^\\*\\*([^*]+?)\\*\\*：(.+)$/,'<strong class=\"list-title\">$1：</strong><span class=\"list-content\">$2</span>');result.push('<li>'+listContent+'</li>');}else{if(trimmed===''){if(i+1<lines.length){var nextLine=lines[i+1].trim();if(!nextLine.match(/^\\d+\\.\\s/)&&!nextLine.match(/^[-*+]\\s/)){if(inOrderedList){result.push('</ol>');inOrderedList=false;}if(inUnorderedList){result.push('</ul>');inUnorderedList=false;}}}result.push(line);}else{if(inOrderedList){result.push('</ol>');inOrderedList=false;}if(inUnorderedList){result.push('</ul>');inUnorderedList=false;}result.push(line);}}}if(inOrderedList)result.push('</ol>');if(inUnorderedList)result.push('</ul>');return result.join('\\n');}\nfunction formatMathSymbols(content){var mathSymbols={'α':'&alpha;','β':'&beta;','γ':'&gamma;','δ':'&delta;','ε':'&epsilon;','ζ':'&zeta;','η':'&eta;','θ':'&theta;','λ':'&lambda;','μ':'&mu;','π':'&pi;','σ':'&sigma;','∑':'&sum;','∏':'&prod;','∫':'&int;','∂':'&part;','∇':'&nabla;','∞':'&infin;','≤':'&le;','≥':'&ge;','≠':'&ne;','≈':'&asymp;','±':'&plusmn;','×':'&times;','÷':'&divide;','→':'&rarr;','←':'&larr;','↑':'&uarr;','↓':'&darr;','⇒':'&rArr;','⇐':'&lArr;'};var subMap={'₀':'0','₁':'1','₂':'2','₃':'3','₄':'4','₅':'5','₆':'6','₇':'7','₈':'8','₉':'9','ₖ':'k','ₜ':'t','ₛ':'s','₊':'+','₋':'-'};var supMap={'⁰':'0','¹':'1','²':'2','³':'3','⁴':'4','⁵':'5','⁶':'6','⁷':'7','⁸':'8','⁹':'9','ᵏ':'k','ᵗ':'t','ˢ':'s','⁺':'+','⁻':'-'};for(var symbol in mathSymbols){content=content.replace(new RegExp(escapeRegExp(symbol),'g'),mathSymbols[symbol]);}content=content.replace(/([a-zA-Z])₍([0-9]+)₎/g,'$1<sub>$2</sub>').replace(/([a-zA-Z])⁽([0-9]+)⁾/g,'$1<sup>$2</sup>').replace(/([a-zA-Z])([₀₁₂₃₄₅₆₇₈₉ₖₜₛ₊₋]+)/g,function(match,base,subscript){var converted=subscript.replace(/[₀₁₂₃₄₅₆₇₈₉ₖₜₛ₊₋]/g,function(char){return subMap[char]||char;});return base+'<sub>'+converted+'</sub>';}).replace(/([a-zA-Z])([⁰¹²³⁴⁵⁶⁷⁸⁹ᵏᵗˢ⁺⁻]+)/g,function(match,base,superscript){var converted=superscript.replace(/[⁰¹²³⁴⁵⁶⁷⁸⁹ᵏᵗˢ⁺⁻]/g,function(char){return supMap[char]||char;});return base+'<sup>'+converted+'</sup>';}).replace(/\\b([A-Za-z])_\\{([^}]+)\\}/g,'$1<sub>$2</sub>').replace(/\\b([A-Za-z])\\^\\{([^}]+)\\}/g,'$1<sup>$2</sup>');return content;}\nfunction formatParagraphs(content){var lines=content.split('\\n'),result=[],inParagraph=false,currentParagraph=[];for(var i=0;i<lines.length;i++){var originalLine=lines[i],line=originalLine.trim();if(line.match(/^<[^>]+>/)||line===''){if(inParagraph&&currentParagraph.length>0){var paragraphContent=currentParagraph.join(' ').trim();if(paragraphContent)result.push('<p class=\"content-paragraph indent-paragraph\">'+paragraphContent+'</p>');currentParagraph=[];inParagraph=false;}result.push(originalLine);continue;}if(line.match(/^#{1,6}\\s/)||line.match(/^\\d+\\.\\s/)||line.match(/^[-*+]\\s/)||line.match(/^>/)){if(inParagraph&&currentParagraph.length>0){var paragraphContent=currentParagraph.join(' ').trim();if(paragraphContent)result.push('<p class=\"content-paragraph indent-paragraph\">'+paragraphContent+'</p>');currentParagraph=[];inParagraph=false;}result.push(originalLine);continue;}if(line){currentParagraph.push(line);inParagraph=true;}}if(inParagraph&&currentParagraph.length>0){var paragraphContent=currentParagraph.join(' ').trim();if(paragraphContent)result.push('<p class=\"content-paragraph indent-paragraph\">'+paragraphContent+'</p>');}return result.join('\\n');}\nfunction escapeRegExp(string){return string.replace(/[.*+?^${}()|[\\]\\\\]/g,'\\\\$&');}\nfunction formatLinks(content){return content.replace(/<a\\s+href=\"(https?:\\/\\/[^\"]+)\"([^>]*)>/gi,function(match,url,attrs){return !attrs.includes('target=')?'<a href=\"'+url+'\"'+attrs+' target=\"_blank\" rel=\"noopener noreferrer\">':match;});}\nfunction escapeHtml(text){var div=document.createElement('div');div.textContent=text;return div.innerHTML;}\nfunction addTocClickEvents(){document.querySelectorAll('#tableOfContents a').forEach(function(link){link.addEventListener('click',function(e){e.preventDefault();var targetElement=document.getElementById(this.getAttribute('data-target'));if(targetElement){targetElement.scrollIntoView({behavior:'smooth',block:'start'});updateActiveTocItem(this);}});});}\nfunction addScrollHighlight(headings){var throttleTimer=null;function updateActiveHeading(){var windowHeight=window.innerHeight,activeHeading=null;for(var i=headings.length-1;i>=0;i--){var rect=headings[i].getBoundingClientRect();if(rect.top<=windowHeight*0.3){activeHeading=headings[i];break;}}if(activeHeading){var activeLink=document.querySelector('#tableOfContents a[data-target=\"'+activeHeading.id+'\"]');if(activeLink)updateActiveTocItem(activeLink);}}window.addEventListener('scroll',function(){if(throttleTimer)clearTimeout(throttleTimer);throttleTimer=setTimeout(updateActiveHeading,100);});updateActiveHeading();}\nfunction updateActiveTocItem(activeLink){document.querySelectorAll('#tableOfContents a').forEach(function(link){link.classList.remove('active');});if(activeLink){activeLink.classList.add('active');scrollTocToActiveItem(activeLink);}}\nfunction scrollTocToActiveItem(activeLink){var tocCard=document.getElementById('tocCard');if(!activeLink||!tocCard)return;var cardRect=tocCard.getBoundingClientRect();var activeRect=activeLink.getBoundingClientRect();var relativeTop=activeRect.top-cardRect.top;var relativeBottom=activeRect.bottom-cardRect.bottom;var cardStyle=window.getComputedStyle(tocCard);var paddingTop=parseFloat(cardStyle.paddingTop)||0;var titleHeight=tocCard.querySelector('h5')?tocCard.querySelector('h5').offsetHeight:0;var contentTop=paddingTop+titleHeight;if(relativeTop<contentTop){tocCard.scrollTop+=(relativeTop-contentTop-10);}else if(relativeBottom>0){tocCard.scrollTop+=(relativeBottom+10);}}\nfunction optimizeImages(){document.querySelectorAll('.article-content img').forEach(function(img){img.loading='lazy';img.onerror=function(){this.style.display='none';};if(!img.alt)img.alt='Artigo com fotos';});}\nfunction initReadingProgress(){var progressBar=document.getElementById('readingProgress');function updateProgress(){var scrollTop=window.pageYOffset||document.documentElement.scrollTop;var docHeight=document.documentElement.scrollHeight-window.innerHeight;progressBar.style.width=Math.min((scrollTop/docHeight)*100,100)+'%';}window.addEventListener('scroll',updateProgress);updateProgress();}\nfunction initTableOfContents(){var content=document.querySelector('.article-content');if(!content)return;var headings=content.querySelectorAll('h1, h2, h3, h4, h5, h6');if(headings.length<=1)return;var tocCard=document.getElementById('tocCard');var tocList=document.getElementById('tableOfContents');var html='',counters=[0,0,0,0,0,0];headings.forEach(function(heading,index){var id='heading-'+index;heading.id=id;var level=parseInt(heading.tagName.substring(1));var indent=(level-1)*15;counters[level-1]++;for(var i=level;i<counters.length;i++)counters[i]=0;var numberText='';for(var i=0;i<level;i++){if(counters[i]>0)numberText+=(numberText?'.':'')+counters[i];}numberText+='.';html+='<a href=\"#'+id+'\" class=\"toc-item toc-level-'+level+'\" style=\"padding-left: '+indent+'px;\" data-target=\"'+id+'\"><span class=\"toc-number\">'+numberText+'</span> <span class=\"toc-text\">'+heading.textContent+'</span></a>';});tocList.innerHTML=html;tocCard.style.display='block';addTocClickEvents();addScrollHighlight(headings);tocList.addEventListener('click',function(e){if(e.target.tagName==='A'){e.preventDefault();var targetElement=document.getElementById(e.target.getAttribute('data-target'));if(targetElement){targetElement.scrollIntoView({behavior:'smooth',block:'start'});targetElement.style.transition='all 0.3s ease';targetElement.style.backgroundColor='#e3f2fd';targetElement.style.padding='10px';targetElement.style.borderRadius='6px';setTimeout(function(){targetElement.style.backgroundColor='';targetElement.style.padding='';targetElement.style.borderRadius='';},2000);}}});}\nfunction shareToWeibo(){window.open('https://service.weibo.com/share/share.php?url='+encodeURIComponent(window.location.href)+'&title='+encodeURIComponent(document.title),'_blank');}\nfunction copyUrl(){navigator.clipboard.writeText(window.location.href).then(function(){alert('O link foi copiado para a área de transferência');}).catch(function(){var textArea=document.createElement('textarea');textArea.value=window.location.href;textArea.style.cssText='position:fixed;left:-999999px;top:-999999px';document.body.appendChild(textArea);textArea.focus();textArea.select();try{alert(document.execCommand('copy')?'O link foi copiado para a área de transferência':'Se a cópia falhar, copie o link manualmente');}catch(err){alert('Se a cópia falhar, copie o link manualmente');}document.body.removeChild(textArea);});}\nfunction printArticle(){var printWindow=window.open('','_blank');var articleContent=document.querySelector('.article-detail').innerHTML;var articleTitle=document.querySelector('.article-title').textContent;printWindow.document.write('<!DOCTYPE html><html lang=\"pt\"><title>'+articleTitle+'</title><style>body{font-family:-apple-system,BlinkMacSystemFont,\"Segoe UI\",Roboto,\"Helvetica Neue\",Arial,sans-serif;line-height:1.6;color:#333;max-width:800px;margin:0 auto;padding:20px}h1,h2,h3,h4,h5,h6{color:#1a1c1f;margin-top:1.5em;margin-bottom:0.5em}h1{font-size:2em;border-bottom:2px solid #e9ecef;padding-bottom:0.3em}h2{font-size:1.5em;border-bottom:1px solid #e9ecef;padding-bottom:0.3em}h3{font-size:1.25em}p{margin-bottom:1em}pre{background:#f8f9fa;border:1px solid #e9ecef;border-radius:4px;padding:1em;overflow-x:auto;font-family:Consolas,Monaco,\"Courier New\",monospace}code{background:#f1f3f4;padding:2px 4px;border-radius:3px;font-family:Consolas,Monaco,\"Courier New\",monospace}blockquote{border-left:4px solid #1764ff;background:#f8f9fa;padding:1em;margin:1em 0;border-radius:0 4px 4px 0}img{max-width:100%;height:auto}table{width:100%;border-collapse:collapse;margin:1em 0}th,td{border:1px solid #ddd;padding:8px;text-align:left}th{background-color:#f2f2f2}.article-meta,.article-footer,.share-buttons{display:none}</style><body>'+articleContent+'</body></html>');printWindow.document.close();printWindow.focus();setTimeout(function(){printWindow.print();printWindow.close();},250);}\nfunction improvedStickyPosition(){var sidebarCards=document.querySelectorAll('.sidebar-card');var originalPositions=[],isFixed=[];sidebarCards.forEach(function(card,index){var rect=card.getBoundingClientRect();originalPositions[index]=rect.top+window.pageYOffset;isFixed[index]=false;var placeholder=document.createElement('div');placeholder.style.cssText='height:'+card.offsetHeight+'px;display:none';placeholder.className='sticky-placeholder';card.parentNode.insertBefore(placeholder,card.nextSibling);});function updateStickyPosition(){var scrollTop=window.pageYOffset||document.documentElement.scrollTop;sidebarCards.forEach(function(card,index){var originalTop=originalPositions[index];var stickyTop=index>0?(document.querySelector('.toc-card').style.position==='fixed'?60+document.querySelector('.toc-card').offsetHeight+8:80):60;var placeholder=card.nextSibling;if(scrollTop>originalTop-stickyTop){if(!isFixed[index]){var colContainer=card.closest('[class*=\"col-\"]');var colRect=colContainer.getBoundingClientRect();var colStyle=window.getComputedStyle(colContainer);var paddingLeft=parseFloat(colStyle.paddingLeft)||0;var paddingRight=parseFloat(colStyle.paddingRight)||0;card.style.cssText='position:fixed;top:'+stickyTop+'px;left:'+(colRect.left+paddingLeft)+'px;width:'+(colRect.width-paddingLeft-paddingRight)+'px;z-index:9999;box-sizing:border-box';card.classList.add('fixed');placeholder.style.display='block';isFixed[index]=true;}}else if(isFixed[index]){card.style.cssText='';card.classList.remove('fixed');placeholder.style.display='none';isFixed[index]=false;}});}window.addEventListener('scroll',updateStickyPosition);window.addEventListener('resize',function(){setTimeout(function(){sidebarCards.forEach(function(card,index){if(isFixed[index]){var colContainer=card.closest('[class*=\"col-\"]');var colRect=colContainer.getBoundingClientRect();var colStyle=window.getComputedStyle(colContainer);var paddingLeft=parseFloat(colStyle.paddingLeft)||0;var paddingRight=parseFloat(colStyle.paddingRight)||0;card.style.left=(colRect.left+paddingLeft)+'px';card.style.width=(colRect.width-paddingLeft-paddingRight)+'px';}else{originalPositions[index]=card.getBoundingClientRect().top+window.pageYOffset;}});},100);});updateStickyPosition();}\ndocument.addEventListener('DOMContentLoaded',function(){setTimeout(improvedStickyPosition,1000);});\n    </script>\n\r\n            </div>\r\n        </form>\r\n\r\n        <div class=\"fixed-icon\">\r\n            <div style=\"width: 120px; position: relative; left: 1rem; top: -12px;\">\r\n            </div>\r\n            <a id=\"webchat\" class=\"wx-con disa\" href=\"http://wpa.qq.com/msgrd?v=3&uin=365833440&site=qq&menu=yes\" target=\"_blank\">\r\n                <img class=\"wechat-ico\" src=\"site/image/icon/qq.svg\" alt=\"Assistente de OCR QQ atendimento ao cliente online\" />\r\n                <div class=\"wx-text\">Atendimento ao Cliente QQ (365833440)</div>\r\n                <label class=\"badge badge-danger\" style=\"background: rgb(255, 34, 122); display: inline-block;\">1</label>\r\n            </a>\r\n\r\n            <a class=\"wx-con\" href=\"https://qm.qq.com/cgi-bin/qm/qr?k=bohNy9GMdgD5bsYrB-fcIjy-s6Ot3zOg&jump_from=webapi&qr=1\" target=\"_blank\">\r\n                <img class=\"wechat-ico\" src=\"site/image/icon/qq.svg\" alt=\"Grupo de comunicação do usuário do assistente de OCR QQ\" />\r\n                <div class=\"wx-text\">Grupo QQ (100029010)</div>\r\n                <label class=\"badge badge-danger\" style=\"background: rgb(255, 34, 122); display: inline-block;\">2</label>\r\n            </a>\r\n            <a href=\"mailto:<EMAIL>\" class=\"wx-con\">\r\n                <img class=\"wechat-ico\" src=\"site/image/icon/im.svg\" alt=\"Assistente de OCR entre em contato com o atendimento ao cliente por e-mail\" />\r\n                <div class=\"wx-text\" style=\"width: 240px; text-align: left; left: -15.3rem; padding: 10px 20px;\">\r\n                    <div style=\"font-size: 15px\">E-mail: <EMAIL></div>\r\n                    <hr style=\"background: #c1c1c1; margin: 10px 10px 6px 0;\" />\r\n                    <span style=\"font-size: 13px; color: #c1c1c1\">Obrigado por seus comentários e sugestões!</span>\r\n                </div>\r\n                <label class=\"badge badge-danger\" style=\"background: rgb(255, 34, 122); display: inline-block;\">3</label>\r\n            </a>\r\n        </div>\r\n\r\n        <style>\r\n            .content {\r\n                width: 100%;\r\n                padding: 0 !important;\r\n                margin: 0 auto\r\n            }\r\n\r\n            h3.h6 {\r\n                font-size: 1rem !important;\r\n                font-weight: bold !important;\r\n                line-height: 1.2 !important;\r\n                margin-bottom: .5rem;\r\n                font-family: inherit\r\n            }\r\n        </style>\r\n        <footer class=\"page-footer\">\r\n            <div class=\"container\">\r\n                <div class=\"row foot-cont pt-4 no-gutters\" style=\"border-top: 1px solid #e1e6ed; max-width: 100%\">\r\n                    <div class=\"col-lg-12 hu-an p-0 mb-3 mt-3 text-l color-gray\" style=\"text-align: center;\">\r\n                        Assistente de reconhecimento de texto OCR&nbsp;©️ 2025 ALL RIGHTS RESERVED. Todos os direitos reservados&nbsp;&nbsp;&nbsp;|&nbsp;&nbsp;&nbsp;\r\n                        <a href=\"PrivacyPolicy.aspx\" style=\"text-decoration: underline;\">Acordo de Privacidade</a>&nbsp;&nbsp;&nbsp;|&nbsp;&nbsp;&nbsp;\r\n                        <a href=\"UserAgreement.aspx\" style=\"text-decoration: underline;\">Contrato do Usuário</a>&nbsp;&nbsp;&nbsp;|&nbsp;&nbsp;&nbsp;\r\n                        <a href=\"Status.aspx\" style=\"text-decoration: underline;\">Status do serviço</a>&nbsp;&nbsp;&nbsp;|&nbsp;&nbsp;&nbsp;\r\n                        <a class=\"mb-2 color-gray\" href=\"https://beian.miit.gov.cn/\" target=\"_blank\" style=\"text-decoration: underline;\">E Preparação do PIC n.º 2021012692</a>\r\n                    </div>\r\n                </div>\r\n            </div>\r\n        </footer>\r\n        <style>\r\n            #translate > .translateSelectLanguage {\r\n                right: 2rem;\r\n                font-size: 1rem;\r\n                width: 150px;\r\n                padding: .3rem;\r\n                margin-right: 20px;\r\n                border: 1px solid #C9C9C9;\r\n                background: #fff;\r\n                color: #555\r\n            }\r\n        </style>\r\n    </div>\r\n    <script>\r\n        document.addEventListener('DOMContentLoaded', function () {\r\n            var setScroll = function (active) {\r\n                forEach(qa('.v4_header_pc,.v4_header_mob'), function (e) {\r\n                    if (active) addClass(e, 'scrollActive'); else removeClass(e, 'scrollActive');\r\n                });\r\n            };\r\n            if (getScrollY() > 60) setScroll(1);\r\n            window.addEventListener('scroll', function () { setScroll(getScrollY() > 60); });\r\n\r\n            forEach(qa('.v4_header_pc'), function (h) {\r\n                h.addEventListener('mouseenter', function () { addClass(h, 'active'); });\r\n                h.addEventListener('mouseleave', function () { removeClass(h, 'active'); });\r\n            });\r\n\r\n            var menuTimer;\r\n            var hideMenu = function (menuId) {\r\n                var m = document.getElementById(menuId);\r\n                if (m) {\r\n                    m.style.cssText = 'visibility:hidden;height:0;display:none';\r\n                    var n = q('.nav-drown-con', m);\r\n                    if (n) { n.style.cssText = 'height:0;opacity:0;transform:translate(0,-100%)'; }\r\n                }\r\n            };\r\n\r\n            forEach(qa('ul.top-nav>li'), function (i) {\r\n                i.addEventListener('mouseenter', function () {\r\n                    clearTimeout(menuTimer);\r\n                    var l = this.querySelector('a'), t = this.querySelector('.triangle'), s = l ? l.getAttribute('data-sub') : null;\r\n                    if (l) addClass(l, 'active'); if (t) addClass(t, 'active');\r\n                    if (s) {\r\n                        var m = document.getElementById(s);\r\n                        if (m) {\r\n                            m.style.cssText = 'display:block;visibility:visible';\r\n                            var siblings = m.parentNode ? toArray(m.parentNode.children) : [];\r\n                            forEach(siblings, function (c) { if (c !== m) c.style.display = 'none'; });\r\n                            var n = q('.nav-drown-con', m);\r\n                            if (n) { n.style.cssText = 'height:100%;opacity:1;transform:translate(0,0)'; }\r\n\r\n                            m.addEventListener('mouseenter', function () { clearTimeout(menuTimer); });\r\n                            m.addEventListener('mouseleave', function () { menuTimer = setTimeout(function () { hideMenu(s); }, 100); });\r\n                        }\r\n                    }\r\n                });\r\n                i.addEventListener('mouseleave', function () {\r\n                    var l = this.querySelector('a'), t = this.querySelector('.triangle'), s = l ? l.getAttribute('data-sub') : null;\r\n                    if (l) removeClass(l, 'active'); if (t) removeClass(t, 'active');\r\n                    if (s) { menuTimer = setTimeout(function () { hideMenu(s); }, 100); }\r\n                });\r\n            });\r\n\r\n            var r = q('.v4_header_mob .right-menu');\r\n            if (r) r.addEventListener('click', function () {\r\n                var arr = [r, q('.v4_header_mob'), q('#nav_products'), q('.mob-nav-content .sidebar-fix')];\r\n                for (var i = 0; i < arr.length; i++) {\r\n                    var e = arr[i];\r\n                    if (e) {\r\n                        var name = (i === 0 ? 'active' : (i === 1 ? 'active' : (i === 2 ? 'active1' : 'fixedActiveBg')));\r\n                        toggleClass(e, name);\r\n                    }\r\n                }\r\n                var f = q('.mob-nav-content .sidebar-fix');\r\n                if (f) {\r\n                    toggleClass(f, 'show');\r\n                    document.body.style.overflow = hasClass(f, 'show') ? 'hidden' : 'scroll';\r\n                }\r\n            });\r\n\r\n            document.addEventListener('click', function (e) {\r\n                e = e || window.event;\r\n                var target = e.target || e.srcElement;\r\n                var m = closest(target, '.sidebar-fix-left .mob-nav-item');\r\n                if (m) {\r\n                    var p = m.parentNode;\r\n                    if (p) forEach(toArray(p.children), function (c) { removeClass(c, 'active'); });\r\n                    addClass(m, 'active');\r\n                    var d = m.getAttribute('data');\r\n                    if (d) forEach(qa('.sidebar-fix-rigth .sub-nav'), function (n, i) { n.style.display = (i == d ? 'block' : 'none'); });\r\n                }\r\n            });\r\n        });\r\n    </script>\r\n</body>\r\n</html>\r\n\r\n\t            <script type=\"text/javascript\">\r\n                    var _hmt = _hmt || [];\r\n                    (function () {\r\n                        var hm = document.createElement('script');hm.async=1;\r\n                        hm.src = 'https://hm.baidu.com/hm.js?5ee048d7caaf0d439cf16309a1687754';\r\n                        var s = document.getElementsByTagName('script')[0];s.parentNode.insertBefore(hm, s);\r\n                    })();\r\n                </script><div id=\"translate\" class=\"ignore\" style=\"display: none\"></div><script src=\"/static/js/translate.js\"></script>"
﻿"<!DOCTYPE html>\r\n<html xmlns=\"http://www.w3.org/1999/xhtml\" lang=\"ta\">\r\n<head><link rel=\"canonical\" href=\"https://ocr.oldfish.cn/ta/Article.aspx?id=81&slug=rnn-sequence-modeling\" /><link rel=\"alternate\" href=\"https://ocr.oldfish.cn/Article.aspx?id=81&slug=rnn-sequence-modeling\" hreflang=\"x-default\" />\r\n<link rel=\"alternate\" href=\"https://ocr.oldfish.cn/Article.aspx?id=81&slug=rnn-sequence-modeling\" hreflang=\"zh\" />\r\n<link rel=\"alternate\" href=\"https://ocr.oldfish.cn/Article.aspx?id=81&slug=rnn-sequence-modeling\" hreflang=\"zh\" />\r\n<link rel=\"alternate\" href=\"https://ocr.oldfish.cn/zh-Hant/Article.aspx?id=81&slug=rnn-sequence-modeling\" hreflang=\"zh-tw\" />\r\n<link rel=\"alternate\" href=\"https://ocr.oldfish.cn/yue/Article.aspx?id=81&slug=rnn-sequence-modeling\" hreflang=\"zh-yue\" />\r\n<link rel=\"alternate\" href=\"https://ocr.oldfish.cn/en/Article.aspx?id=81&slug=rnn-sequence-modeling\" hreflang=\"en\" />\r\n<link rel=\"alternate\" href=\"https://ocr.oldfish.cn/es/Article.aspx?id=81&slug=rnn-sequence-modeling\" hreflang=\"es\" />\r\n<link rel=\"alternate\" href=\"https://ocr.oldfish.cn/hi/Article.aspx?id=81&slug=rnn-sequence-modeling\" hreflang=\"hi\" />\r\n<link rel=\"alternate\" href=\"https://ocr.oldfish.cn/ar/Article.aspx?id=81&slug=rnn-sequence-modeling\" hreflang=\"ar\" />\r\n<link rel=\"alternate\" href=\"https://ocr.oldfish.cn/pt/Article.aspx?id=81&slug=rnn-sequence-modeling\" hreflang=\"pt\" />\r\n<link rel=\"alternate\" href=\"https://ocr.oldfish.cn/bn/Article.aspx?id=81&slug=rnn-sequence-modeling\" hreflang=\"bn\" />\r\n<link rel=\"alternate\" href=\"https://ocr.oldfish.cn/ru/Article.aspx?id=81&slug=rnn-sequence-modeling\" hreflang=\"ru\" />\r\n<link rel=\"alternate\" href=\"https://ocr.oldfish.cn/ja/Article.aspx?id=81&slug=rnn-sequence-modeling\" hreflang=\"ja\" />\r\n<link rel=\"alternate\" href=\"https://ocr.oldfish.cn/pa/Article.aspx?id=81&slug=rnn-sequence-modeling\" hreflang=\"pa\" />\r\n<link rel=\"alternate\" href=\"https://ocr.oldfish.cn/de/Article.aspx?id=81&slug=rnn-sequence-modeling\" hreflang=\"de\" />\r\n<link rel=\"alternate\" href=\"https://ocr.oldfish.cn/fr/Article.aspx?id=81&slug=rnn-sequence-modeling\" hreflang=\"fr\" />\r\n<link rel=\"alternate\" href=\"https://ocr.oldfish.cn/mr/Article.aspx?id=81&slug=rnn-sequence-modeling\" hreflang=\"mr\" />\r\n<link rel=\"alternate\" href=\"https://ocr.oldfish.cn/te/Article.aspx?id=81&slug=rnn-sequence-modeling\" hreflang=\"te\" />\r\n<link rel=\"alternate\" href=\"https://ocr.oldfish.cn/vi/Article.aspx?id=81&slug=rnn-sequence-modeling\" hreflang=\"vi\" />\r\n<link rel=\"alternate\" href=\"https://ocr.oldfish.cn/ko/Article.aspx?id=81&slug=rnn-sequence-modeling\" hreflang=\"ko\" />\r\n<link rel=\"alternate\" href=\"https://ocr.oldfish.cn/ta/Article.aspx?id=81&slug=rnn-sequence-modeling\" hreflang=\"ta\" />\r\n<link rel=\"alternate\" href=\"https://ocr.oldfish.cn/ur/Article.aspx?id=81&slug=rnn-sequence-modeling\" hreflang=\"ur\" />\r\n<link rel=\"alternate\" href=\"https://ocr.oldfish.cn/tr/Article.aspx?id=81&slug=rnn-sequence-modeling\" hreflang=\"tr\" />\r\n<link rel=\"alternate\" href=\"https://ocr.oldfish.cn/it/Article.aspx?id=81&slug=rnn-sequence-modeling\" hreflang=\"it\" />\r\n<link rel=\"alternate\" href=\"https://ocr.oldfish.cn/gu/Article.aspx?id=81&slug=rnn-sequence-modeling\" hreflang=\"gu\" />\r\n<link rel=\"alternate\" href=\"https://ocr.oldfish.cn/pl/Article.aspx?id=81&slug=rnn-sequence-modeling\" hreflang=\"pl\" />\r\n<link rel=\"alternate\" href=\"https://ocr.oldfish.cn/uk/Article.aspx?id=81&slug=rnn-sequence-modeling\" hreflang=\"uk\" />\r\n<link rel=\"alternate\" href=\"https://ocr.oldfish.cn/ms/Article.aspx?id=81&slug=rnn-sequence-modeling\" hreflang=\"ms\" />\r\n<link rel=\"alternate\" href=\"https://ocr.oldfish.cn/id/Article.aspx?id=81&slug=rnn-sequence-modeling\" hreflang=\"id\" />\r\n<link rel=\"alternate\" href=\"https://ocr.oldfish.cn/ml/Article.aspx?id=81&slug=rnn-sequence-modeling\" hreflang=\"ml\" />\r\n<link rel=\"alternate\" href=\"https://ocr.oldfish.cn/kn/Article.aspx?id=81&slug=rnn-sequence-modeling\" hreflang=\"kn\" />\r\n<link rel=\"alternate\" href=\"https://ocr.oldfish.cn/fa/Article.aspx?id=81&slug=rnn-sequence-modeling\" hreflang=\"fa\" />\r\n<link rel=\"alternate\" href=\"https://ocr.oldfish.cn/nl/Article.aspx?id=81&slug=rnn-sequence-modeling\" hreflang=\"nl\" />\r\n<link rel=\"alternate\" href=\"https://ocr.oldfish.cn/th/Article.aspx?id=81&slug=rnn-sequence-modeling\" hreflang=\"th\" />\r\n<link rel=\"alternate\" href=\"https://ocr.oldfish.cn/sw/Article.aspx?id=81&slug=rnn-sequence-modeling\" hreflang=\"sw\" />\r\n<link rel=\"alternate\" href=\"https://ocr.oldfish.cn/ro/Article.aspx?id=81&slug=rnn-sequence-modeling\" hreflang=\"ro\" />\r\n<link rel=\"alternate\" href=\"https://ocr.oldfish.cn/my/Article.aspx?id=81&slug=rnn-sequence-modeling\" hreflang=\"my\" />\r\n<link rel=\"alternate\" href=\"https://ocr.oldfish.cn/or/Article.aspx?id=81&slug=rnn-sequence-modeling\" hreflang=\"or\" />\r\n<link rel=\"alternate\" href=\"https://ocr.oldfish.cn/he/Article.aspx?id=81&slug=rnn-sequence-modeling\" hreflang=\"he\" />\r\n<link rel=\"alternate\" href=\"https://ocr.oldfish.cn/am/Article.aspx?id=81&slug=rnn-sequence-modeling\" hreflang=\"am\" />\r\n<link rel=\"alternate\" href=\"https://ocr.oldfish.cn/fil/Article.aspx?id=81&slug=rnn-sequence-modeling\" hreflang=\"fil\" />\r\n<link rel=\"alternate\" href=\"https://ocr.oldfish.cn/sv/Article.aspx?id=81&slug=rnn-sequence-modeling\" hreflang=\"sv\" />\r\n<link rel=\"alternate\" href=\"https://ocr.oldfish.cn/el/Article.aspx?id=81&slug=rnn-sequence-modeling\" hreflang=\"el\" />\r\n<link rel=\"alternate\" href=\"https://ocr.oldfish.cn/cs/Article.aspx?id=81&slug=rnn-sequence-modeling\" hreflang=\"cs\" />\r\n<link rel=\"alternate\" href=\"https://ocr.oldfish.cn/hu/Article.aspx?id=81&slug=rnn-sequence-modeling\" hreflang=\"hu\" />\r\n<link rel=\"alternate\" href=\"https://ocr.oldfish.cn/be/Article.aspx?id=81&slug=rnn-sequence-modeling\" hreflang=\"be\" />\r\n<link rel=\"alternate\" href=\"https://ocr.oldfish.cn/si/Article.aspx?id=81&slug=rnn-sequence-modeling\" hreflang=\"si\" />\r\n<link rel=\"alternate\" href=\"https://ocr.oldfish.cn/ne/Article.aspx?id=81&slug=rnn-sequence-modeling\" hreflang=\"ne\" />\r\n<link rel=\"alternate\" href=\"https://ocr.oldfish.cn/km/Article.aspx?id=81&slug=rnn-sequence-modeling\" hreflang=\"km\" />\r\n<link rel=\"alternate\" href=\"https://ocr.oldfish.cn/sk/Article.aspx?id=81&slug=rnn-sequence-modeling\" hreflang=\"sk\" />\r\n<link rel=\"alternate\" href=\"https://ocr.oldfish.cn/bg/Article.aspx?id=81&slug=rnn-sequence-modeling\" hreflang=\"bg\" />\r\n<link rel=\"alternate\" href=\"https://ocr.oldfish.cn/fr-ca/Article.aspx?id=81&slug=rnn-sequence-modeling\" hreflang=\"fr-ca\" />\r\n<link rel=\"alternate\" href=\"https://ocr.oldfish.cn/ha/Article.aspx?id=81&slug=rnn-sequence-modeling\" hreflang=\"ha\" />\r\n<link rel=\"alternate\" href=\"https://ocr.oldfish.cn/yo/Article.aspx?id=81&slug=rnn-sequence-modeling\" hreflang=\"yo\" />\r\n<link rel=\"alternate\" href=\"https://ocr.oldfish.cn/ig/Article.aspx?id=81&slug=rnn-sequence-modeling\" hreflang=\"ig\" />\r\n<link rel=\"alternate\" href=\"https://ocr.oldfish.cn/ku/Article.aspx?id=81&slug=rnn-sequence-modeling\" hreflang=\"ku\" />\r\n<link rel=\"alternate\" href=\"https://ocr.oldfish.cn/rw/Article.aspx?id=81&slug=rnn-sequence-modeling\" hreflang=\"rw\" />\r\n<link rel=\"alternate\" href=\"https://ocr.oldfish.cn/ca/Article.aspx?id=81&slug=rnn-sequence-modeling\" hreflang=\"ca\" />\r\n<link rel=\"alternate\" href=\"https://ocr.oldfish.cn/da/Article.aspx?id=81&slug=rnn-sequence-modeling\" hreflang=\"da\" />\r\n<link rel=\"alternate\" href=\"https://ocr.oldfish.cn/fi/Article.aspx?id=81&slug=rnn-sequence-modeling\" hreflang=\"fi\" />\r\n<link rel=\"alternate\" href=\"https://ocr.oldfish.cn/nb/Article.aspx?id=81&slug=rnn-sequence-modeling\" hreflang=\"nb\" />\r\n<link rel=\"alternate\" href=\"https://ocr.oldfish.cn/hr/Article.aspx?id=81&slug=rnn-sequence-modeling\" hreflang=\"hr\" />\r\n<link rel=\"alternate\" href=\"https://ocr.oldfish.cn/sr-Cyrl/Article.aspx?id=81&slug=rnn-sequence-modeling\" hreflang=\"sr-cyrl\" />\r\n<link rel=\"alternate\" href=\"https://ocr.oldfish.cn/sr-Latn/Article.aspx?id=81&slug=rnn-sequence-modeling\" hreflang=\"sr-latn\" />\r\n<link rel=\"alternate\" href=\"https://ocr.oldfish.cn/sq/Article.aspx?id=81&slug=rnn-sequence-modeling\" hreflang=\"sq\" />\r\n<link rel=\"alternate\" href=\"https://ocr.oldfish.cn/so/Article.aspx?id=81&slug=rnn-sequence-modeling\" hreflang=\"so\" />\r\n<link rel=\"alternate\" href=\"https://ocr.oldfish.cn/zu/Article.aspx?id=81&slug=rnn-sequence-modeling\" hreflang=\"zu\" />\r\n<link rel=\"alternate\" href=\"https://ocr.oldfish.cn/ka/Article.aspx?id=81&slug=rnn-sequence-modeling\" hreflang=\"ka\" />\r\n<meta http-equiv=\"Content-Type\" content=\"text/html; charset=UTF-8\" /><meta http-equiv=\"X-UA-Compatible\" content=\"IE=Edge,chrome=1\" /><meta name=\"renderer\" content=\"webkit\" /><meta name=\"viewport\" content=\"width=device-width, initial-scale=1.0, maximum-scale=5.0\" /><link rel=\"icon\" href=\"/favicon.ico\" type=\"image/x-icon\" /><meta name=\"robots\" content=\"all\" /><link rel=\"preconnect\" href=\"https://scm-file-new-1301864755.cos.ap-beijing.myqcloud.com\" /><link rel=\"preconnect\" href=\"https://cdn.oldfish.cn\" /><link rel=\"dns-prefetch\" href=\"https://hm.baidu.com\" /><link rel=\"dns-prefetch\" href=\"https://cdn.oldfish.cn\" /><link rel=\"preload\" href=\"/site/css/site.css\" as=\"style\" />\r\n<script>function toArray(l){var a=[];for(var i=0;i<(l?l.length:0);i++)a.push(l[i]);return a;}function forEach(l,f){for(var i=0,n=(l?l.length:0);i<n;i++)f(l[i],i);}function hasClass(e,c){if(!e||!c)return false;return(' '+e.className+' ').indexOf(' '+c+' ')>-1;}function addClass(e,c){if(!e||!c||hasClass(e,c))return;e.className=(e.className?e.className+' ':'')+c;}function removeClass(e,c){if(!e||!c)return;var u=' '+e.className+' ';while(u.indexOf(' '+c+' ')>-1){u=u.replace(' '+c+' ',' ');}e.className=u.replace(/^\\s+|\\s+$/g,'');}function toggleClass(e,c){if(!e||!c)return;if(hasClass(e,c))removeClass(e,c);else addClass(e,c);}function matchesSelector(e,s){if(!e||e.nodeType!==1)return false;if(e.matches)return e.matches(s);var p=e.parentNode||document;var m=toArray(p.querySelectorAll(s));for(var i=0;i<m.length;i++)if(m[i]===e)return true;return false;}function closest(e,s){var n=e;while(n&&n.nodeType===1){if(matchesSelector(n,s))return n;n=n.parentNode;}return null;}function safeRemove(e){if(e&&e.parentNode)e.parentNode.removeChild(e);}function qa(s,r){return toArray((r||document).querySelectorAll(s));}function q(s,r){return(r||document).querySelector(s);}function xhrGet(u,o,e){try{var x=new XMLHttpRequest();x.open('GET',u,true);x.onreadystatechange=function(){if(x.readyState===4){if(x.status>=200&&x.status<300)o&&o(x.responseText);else e&&e(x);}};x.send(null);}catch(r){e&&e(r);}}function getScrollY(){return window.pageYOffset||document.documentElement.scrollTop||document.body.scrollTop||0;}function getOrigin(){return window.location.protocol+'//'+window.location.host;}</script>\r\n<script>var isWebP=(function(){try{return document.createElement('canvas').toDataURL('image/webp').indexOf('data:image/webp')===0;}catch(e){return false;}})();document.addEventListener('DOMContentLoaded',function(){var c=document.getElementById('user-login-container');if(c){var u=c.getAttribute('data-url');if(u){xhrGet(u,function(h){c.innerHTML=h;forEach(qa('a',c),function(l){l.style.display='inline-block';l.style.marginLeft='10px';});});}}});</script>\r\n    \n    \n\n    <meta name=\"description\" content=\"OCR இல் RNN, LSTM, GRU இன் பயன்பாட்டில் முழுக்கு. வரிசை மாதிரியாக்கத்தின் கொள்கைகள், சாய்வு சிக்கல்களுக்கான தீர்வுகள் மற்றும் இருதிசை RNNகளின் நன்மைகள் பற்றிய விரிவான பகுப்பாய்வு.\" />\n    <meta name=\"keywords\" content=\"RNN, LSTM, GRU, வரிசை மாடலிங், சாய்வு மறைந்துவிடும், இருதிசை RNN, கவனம் பொறிமுறை, CRNN, OCR, OCR உரை அங்கீகாரம், படத்திலிருந்து உரை, OCR தொழில்நுட்பம்\" />\n    <meta property=\"og:title\" content=\"【ஆழமான கற்றல் OCR தொடர்·4】மீண்டும் மீண்டும் நரம்பியல் நெட்வொர்க்குகள் மற்றும் வரிசை மாடலிங்\" />\n    <meta property=\"og:description\" content=\"OCR இல் RNN, LSTM, GRU இன் பயன்பாட்டில் முழுக்கு. வரிசை மாதிரியாக்கத்தின் கொள்கைகள், சாய்வு சிக்கல்களுக்கான தீர்வுகள் மற்றும் இருதிசை RNNகளின் நன்மைகள் பற்றிய விரிவான பகுப்பாய்வு.\" />\n    <meta property=\"og:type\" content=\"article\" />\n    <meta property=\"og:url\" content=\"https://ocr.oldfish.cn/Article.aspx?id=81&slug=rnn-sequence-modeling&lang=zh-Hans&_translation_source=true\" />\n    <meta property=\"og:site_name\" content=\"OCR உரை அங்கீகார உதவியாளர்\" />\n    <meta name=\"twitter:card\" content=\"summary\" />\n    <meta name=\"twitter:title\" content=\"【ஆழமான கற்றல் OCR தொடர்·4】மீண்டும் மீண்டும் நரம்பியல் நெட்வொர்க்குகள் மற்றும் வரிசை மாடலிங்\" />\n    <meta name=\"twitter:description\" content=\"OCR இல் RNN, LSTM, GRU இன் பயன்பாட்டில் முழுக்கு. வரிசை மாதிரியாக்கத்தின் கொள்கைகள், சாய்வு சிக்கல்களுக்கான தீர்வுகள் மற்றும் இருதிசை RNNகளின் நன்மைகள் பற்றிய விரிவான பகுப்பாய்வு.\" />\n\n    \n    <script type=\"application/ld+json\">\n    {\n        \"@context\": \"https://schema.org\",\n        \"@type\": \"Article\",\n        \"headline\": \"【ஆழமான கற்றல் OCR தொடர் 4] மீண்டும் மீண்டும் நரம்பியல் நெட்வொர்க் மற்றும் வரிசை மாடலிங்\",\n        \"description\": \"OCR இல் RNN, LSTM, GRU இன் பயன்பாட்டில் முழுக்கு. வரிசை மாதிரியாக்கத்தின் கொள்கைகள், சாய்வு சிக்கல்களுக்கான தீர்வுகள் மற்றும் இருதிசை RNNகளின் நன்மைகள் பற்றிய விரிவான பகுப்பாய்வு。\",\n        \"publisher\": {\n            \"@type\": \"Organization\",\n            \"name\": \"OCR உரை அங்கீகார உதவி குழு\"\n        },\n        \"datePublished\": \"2025-08-19T06:32:05Z\",\n        \"dateModified\": \"2025-08-19T06:32:05Z\",\n        \"mainEntityOfPage\": {\n            \"@type\": \"WebPage\",\n            \"@id\": \"https://ocr.oldfish.cn/Article.aspx?id=81&slug=rnn-sequence-modeling&lang=zh-Hans&_translation_source=true\"\n        }\n    }\n    </script>\n    \n    <style>\n        .reading-progress { position: fixed; top: 0; left: 0; width: 0%; height: 3px; background: linear-gradient(90deg, #1764ff, #0d47a1); z-index: 9999; transition: width 0.2s ease; box-shadow: 0 1px 3px rgba(23, 100, 255, 0.3); }\n        .home-container.root { overflow: visible !important; }\n        .article-detail-container { background: #f5f7fc; min-height: calc(100vh - 60px); padding: 80px 0 60px; position: relative; overflow: visible; }\n        .article-detail-container .row { margin: 0 -12px; align-items: flex-start !important; min-height: 100vh !important; }\n        .article-detail-container .row > [class*=\"col-\"] { padding: 0 12px; box-sizing: border-box; min-width: 0; }\n        .article-detail-container .row > .col-lg-8, .article-detail-container .row > .col-xl-9 { overflow: hidden; }\n        .article-detail-container .row > .col-lg-4, .article-detail-container .row > .col-xl-3 { overflow: visible !important; height: 100% !important; min-height: 100% !important; position: static !important; display: block !important; transform: none !important; will-change: auto !important; contain: none !important; perspective: none !important; filter: none !important; }\n        .article-detail { background: #fff; border-radius: 8px; padding: 1.5rem; margin-bottom: 2rem; box-shadow: 0 2px 10px rgba(0,0,0,0.1); width: 100%; box-sizing: border-box; }\n        .article-title { font-size: 1.7rem; color: #1a1c1f; line-height: 1.3; margin-bottom: 1.5rem; font-weight: 600; }\n        .article-meta { display: flex; align-items: center; flex-wrap: nowrap; gap: 1.2rem; color: #6c757d; font-size: 0.88rem; margin-bottom: 2rem; padding: 1.2rem 1.4rem; background: linear-gradient(135deg, #f8f9fa 0%, #ffffff 100%); border-radius: 12px; border-left: 4px solid #1764ff; box-shadow: 0 2px 8px rgba(0,0,0,0.06); overflow-x: hidden; transition: all 0.3s ease; }\n        .article-meta:hover { box-shadow: 0 4px 16px rgba(0,0,0,0.1); transform: translateY(-1px); }\n        .article-meta .meta-item { display: flex; align-items: center; gap: 0.25rem; font-weight: 500; color: #495057; white-space: nowrap; flex-shrink: 1; transition: color 0.2s ease; cursor: default; }\n        .article-meta .meta-item:hover { color: #1764ff; }\n        .article-meta .meta-item:hover .meta-icon { transform: scale(1.1); filter: drop-shadow(0 2px 6px rgba(13, 71, 161, 0.25)); }\n        .meta-icon { display: inline-block; font-size: 16px; flex-shrink: 0; vertical-align: middle; transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1); margin-right: 4px; opacity: 0.9; }\n        .meta-item:hover .meta-icon { opacity: 1; }\n        .meta-item:hover { color: #1764ff; }\n        .meta-item { position: relative; }\n        .meta-item::after { content: ''; position: absolute; bottom: -2px; left: 0; width: 0; height: 2px; background: linear-gradient(90deg, #1764ff, #00d4ff); transition: width 0.3s ease; }\n        .meta-item:hover::after { width: 100%; }\n        .article-category, .read-time { display: inline-block; padding: 6px 12px; border-radius: 16px; font-size: 0.8rem; font-weight: 600; }\n        .article-category { background: linear-gradient(135deg, #1764ff, #0d47a1); color: white; text-decoration: none; text-transform: uppercase; letter-spacing: 0.5px; transition: all 0.3s ease; }\n        .article-category:hover { background: linear-gradient(135deg, #0d47a1, #1764ff); color: white; text-decoration: none; transform: translateY(-1px); box-shadow: 0 4px 12px rgba(23, 100, 255, 0.3); }\n        .read-time { background: #e3f2fd; color: #1565c0; border: 1px solid #90caf9; }\n        .article-tags { margin: 20px 0 1.5rem; }\n        .article-tags .tag { display: inline-block; background: #1764ff; color: #fff; padding: 4px 12px; border-radius: 12px; font-size: 0.8rem; margin: 0 8px 4px 0; }\n        .article-summary { background: #e3f2fd; border-left: 4px solid #1764ff; padding: 1rem 1.5rem; margin-bottom: 2rem; border-radius: 0 4px 4px 0; }\n        .article-content { line-height: 1.8; font-size: 16px; color: #333; word-wrap: break-word; }\n        .article-content h1, .article-content h2, .article-content h3, .article-content h4, .article-content h5, .article-content h6 { margin: 2rem 0 1rem; font-weight: 600; line-height: 1.25; color: #1a1c1f; }\n        .article-content h1 { font-size: 2rem; border-bottom: 2px solid #e9ecef; padding-bottom: 0.5rem; }\n        .article-content h2 { font-size: 1.5rem; border-bottom: 1px solid #e9ecef; padding-bottom: 0.3rem; }\n        .article-content h3 { font-size: 1.25rem; }\n        .article-content h4 { font-size: 1.1rem; }\n        .article-content h5, .article-content h6 { font-size: 1rem; }\n        .article-content ul, .article-content ol { margin: 1rem 0; padding-left: 2rem; }\n        .article-content ul ul, .article-content ol ol, .article-content ul ol, .article-content ol ul { margin: 0.5rem 0; }\n        .article-content blockquote { margin: 1rem 0; padding: 0.5rem 1rem; border-left: 4px solid #1764ff; background-color: #f8f9fa; color: #6c757d; font-style: italic; }\n        .article-content pre { background-color: #f8f9fa; border: 1px solid #e9ecef; border-radius: 6px; padding: 1rem; margin: 1rem 0; overflow-x: auto; font-family: 'Courier New', Courier, monospace; font-size: 0.9em; line-height: 1.4; }\n        .article-content pre code { background: none; border: none; padding: 0; color: inherit; }\n        .article-content p { margin: 1rem 0; }\n        .article-content p:first-child { margin-top: 0; }\n        .article-content p:last-child { margin-bottom: 0; }\n        .article-footer { margin-top: 4rem; padding: 2.5rem; background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%); border-radius: 12px; border: 1px solid #e9ecef; position: relative; overflow: hidden; }\n        .article-footer::before { content: ''; position: absolute; top: 0; left: 0; right: 0; height: 3px; background: linear-gradient(90deg, #1764ff, #17a2b8, #28a745); }\n        .article-share h6 { color: #495057; font-weight: 600; margin-bottom: 1.5rem; font-size: 16px; }\n        .share-buttons { display: flex; gap: 12px; flex-wrap: wrap; }\n        .share-buttons .btn { padding: 10px 20px; border-radius: 25px; font-weight: 500; font-size: 14px; border: none; transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1); box-shadow: 0 2px 8px rgba(0,0,0,0.1); display: inline-flex; align-items: center; gap: 8px; text-decoration: none; }\n        .btn-weibo { background: linear-gradient(135deg, #e6162d, #c41230); color: white; }\n        .btn-copy { background: #1764ff; color: white; }\n        .btn-print { background: linear-gradient(135deg, #17a2b8, #138496); color: white; }\n        .btn-weibo:hover { background: linear-gradient(135deg, #c41230, #a00e26); box-shadow: 0 4px 16px rgba(230, 22, 45, 0.3); }\n        .btn-copy:hover { background: #0d47a1; box-shadow: 0 4px 16px rgba(23, 100, 255, 0.3); }\n        .btn-print:hover { background: linear-gradient(135deg, #138496, #117a8b); box-shadow: 0 4px 16px rgba(23, 162, 184, 0.3); }\n        .btn-weibo:hover, .btn-copy:hover, .btn-print:hover { transform: translateY(-2px); color: white; }\n        .article-actions { display: flex; justify-content: flex-end; align-items: center; }\n        .related-articles { margin-top: 3rem; }\n        .related-article-item { background: #fff; border-radius: 8px; padding: 1.5rem; margin-bottom: 1rem; box-shadow: 0 2px 8px rgba(0,0,0,0.1); transition: transform 0.3s ease; }\n        .related-article-item:hover { transform: translateY(-2px); }\n        .sidebar-card { background: linear-gradient(135deg, rgba(255,255,255,0.9) 0%, rgba(255,255,255,0.8) 100%); backdrop-filter: blur(20px); border: 1px solid rgba(255,255,255,0.3); border-radius: 16px; padding: 1rem; margin-bottom: 0.5rem; box-shadow: 0 4px 20px rgba(0,0,0,0.08), inset 0 1px 0 rgba(255,255,255,0.2); overflow: visible; box-sizing: border-box; width: 100%; z-index: 10; }\n        .sidebar-card.fixed { box-shadow: 0 8px 32px rgba(0,0,0,0.12); }\n        .sidebar-card::before { content: ''; position: absolute; top: 0; left: 0; right: 0; height: 3px; background: linear-gradient(90deg, #00d4ff, #1764ff, #9c27b0); border-radius: 20px 20px 0 0; }\n        .sidebar-card h5 { color: #1a202c; padding-bottom: 0.8rem; border-bottom: 2px solid rgba(23, 100, 255, 0.2); font-weight: 700; font-size: 1.1rem; }\n        .sidebar { min-height: 100vh; position: relative !important; display: block !important; flex-direction: column !important; gap: 1rem; }\n        .toc-card { flex: 0 1 auto; max-height: 60vh; overflow-y: auto; }\n        .sidebar-card:not(.toc-card) { flex: 1 1 auto; overflow: visible; }\n        .recommended-item { padding: 0.75rem 0; border-bottom: 1px solid rgba(0,0,0,0.05); }\n        .recommended-item:last-child { border-bottom: none; }\n        .recommended-item h6 { margin: 0 0 0.5rem 0; font-size: 0.9rem; line-height: 1.4; }\n        .recommended-item h6 a { color: #1a1c1f; text-decoration: none; transition: color 0.3s ease; }\n        .recommended-item h6 a:hover { color: #1764ff; }\n        .recommended-item .text-muted { font-size: 0.75rem; margin: 0; }\n        .toc-list { list-style: none; padding: 0; margin: 0; max-height: none; overflow-y: visible; overflow-x: hidden; }\n        .toc-list::-webkit-scrollbar { width: 4px; }\n        .toc-list::-webkit-scrollbar-track { background: rgba(0,0,0,0.1); border-radius: 2px; }\n        .toc-list::-webkit-scrollbar-thumb { background: linear-gradient(135deg, #00d4ff, #1764ff); border-radius: 2px; }\n        .toc-list::-webkit-scrollbar-thumb:hover { background: linear-gradient(135deg, #1764ff, #0d47a1); }\n        .toc-list a { color: #4a5568; text-decoration: none; font-size: 13px; font-weight: 500; display: block; padding: 8px 12px; border-radius: 8px; transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1); position: relative; margin-bottom: 4px; border: 1px solid transparent; }\n        .toc-list a::before { content: ''; position: absolute; left: 0; top: 0; bottom: 0; width: 3px; background: linear-gradient(135deg, #00d4ff, #1764ff); border-radius: 0 12px 12px 0; transform: scaleY(0); transition: transform 0.3s ease; }\n        .toc-list a:hover { color: #1764ff; background: linear-gradient(135deg, rgba(0, 212, 255, 0.1), rgba(23, 100, 255, 0.1)); border-color: rgba(23, 100, 255, 0.2); transform: translateX(8px); box-shadow: 0 4px 12px rgba(0, 212, 255, 0.2); }\n        .toc-list a:hover::before, .toc-list a.active::before { transform: scaleY(1); }\n        .toc-list a.active { color: #1764ff; background: linear-gradient(135deg, rgba(0, 212, 255, 0.15), rgba(23, 100, 255, 0.15)); font-weight: 700; border-color: rgba(23, 100, 255, 0.3); transform: translateX(4px); box-shadow: 0 6px 16px rgba(0, 212, 255, 0.25); }\n        .toc-number { color: #1764ff; font-weight: 600; margin-right: 0.5rem; font-size: 0.9em; }\n        .toc-text { flex: 1; }\n        .toc-level-1 { font-weight: 700; font-size: 15px; }\n        .toc-level-2 { font-size: 14px; }\n        .toc-level-3 { font-size: 13px; opacity: 0.9; }\n        .toc-level-4, .toc-level-5, .toc-level-6 { font-size: 12px; opacity: 0.8; }\n        .article-content .formatted-list { margin: 1.5rem 0; padding-left: 0; list-style: none; }\n        .article-content .formatted-list.ordered { counter-reset: list-counter; }\n        .article-content .formatted-list.ordered li { counter-increment: list-counter; position: relative; margin: 1rem 0; padding: 1rem 1rem 1rem 3rem; background: linear-gradient(135deg, rgba(23, 100, 255, 0.03), rgba(0, 212, 255, 0.03)); border-left: 4px solid #1764ff; border-radius: 0 8px 8px 0; transition: all 0.3s ease; }\n        .article-content .formatted-list.ordered li::before { content: counter(list-counter); position: absolute; left: 0.8rem; top: 1rem; background: linear-gradient(135deg, #1764ff, #00d4ff); color: white; width: 1.5rem; height: 1.5rem; border-radius: 50%; display: flex; align-items: center; justify-content: center; font-weight: 600; font-size: 0.9rem; }\n        .article-content .formatted-list.ordered li:hover { background: linear-gradient(135deg, rgba(23, 100, 255, 0.06), rgba(0, 212, 255, 0.06)); transform: translateX(4px); box-shadow: 0 2px 8px rgba(23, 100, 255, 0.15); }\n        .article-content .formatted-list.unordered li { position: relative; margin: 0.8rem 0; padding: 0.8rem 1rem 0.8rem 2.5rem; background: linear-gradient(135deg, rgba(40, 167, 69, 0.03), rgba(25, 135, 84, 0.03)); border-left: 4px solid #28a745; border-radius: 0 6px 6px 0; transition: all 0.3s ease; }\n        .article-content .formatted-list.unordered li::before { content: \"•\"; position: absolute; left: 1rem; top: 0.8rem; color: #28a745; font-size: 1.2rem; font-weight: bold; }\n        .article-content .formatted-list.unordered li:hover { background: linear-gradient(135deg, rgba(40, 167, 69, 0.06), rgba(25, 135, 84, 0.06)); transform: translateX(4px); box-shadow: 0 2px 8px rgba(40, 167, 69, 0.15); }\n        .article-content .list-title { color: #1764ff; font-weight: 600; display: inline-block; margin-bottom: 0.3rem; }\n        .article-content .list-content { color: #2c3e50; line-height: 1.6; display: block; }\n        .article-content .inline-code { background: #f8f9fa; border: 1px solid #e9ecef; border-radius: 4px; padding: 2px 6px; font-family: 'Consolas', 'Monaco', 'Courier New', monospace; font-size: 0.9em; color: #e83e8c; }\n        .article-content .label-item { margin: 0.8rem 0; }\n        .article-content .label { color: #1764ff; font-weight: 600; }\n        .article-content .section-label { color: #2c3e50; font-weight: 600; margin: 1rem 0 0.5rem 0; font-size: 1.1em; border-left: 4px solid #1764ff; padding-left: 0.8rem; }\n        .article-content p, .article-content .content-paragraph { margin: 1.2rem 0; line-height: 1.8; text-align: justify; color: #2c3e50; font-size: 1rem; }\n        .article-content .content-paragraph { text-indent: 0; margin: 1rem 0; }\n        .article-content .indent-paragraph { text-indent: 2em; margin: 1.2rem 0; }\n        .article-content .text-indent-1 { text-indent: 2em; display: block; }\n        .article-content .text-indent-2 { text-indent: 4em; display: block; }\n        .article-content .text-indent-3 { text-indent: 6em; display: block; }\n        .article-content .network-config { background: linear-gradient(135deg, #f8f9fa, #e9ecef); border: 1px solid #dee2e6; border-radius: 6px; padding: 0.8rem 1rem; margin: 0.5rem 0; font-family: 'Consolas', 'Monaco', 'Courier New', monospace; font-size: 0.9rem; box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05); }\n        .article-content .config-label { color: #1764ff; font-weight: 600; }\n        .article-content .config-desc { color: #495057; }\n        .article-content .algorithm-step { background: linear-gradient(135deg, rgba(23, 100, 255, 0.05), rgba(0, 212, 255, 0.05)); border-left: 4px solid #1764ff; padding: 0.8rem 1rem; margin: 0.5rem 0; border-radius: 0 6px 6px 0; transition: all 0.3s ease; }\n        .article-content .step-number { color: #1764ff; font-weight: 700; margin-right: 0.5rem; }\n        .article-content .step-desc { color: #2c3e50; line-height: 1.5; }\n        .article-content table { width: 100%; border-collapse: collapse; margin: 1.5rem 0; background: white; border-radius: 8px; overflow: hidden; box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1); }\n        .article-content table th { background: linear-gradient(135deg, #1764ff, #00d4ff); color: white; padding: 1rem; text-align: left; font-weight: 600; }\n        .article-content table td { padding: 0.8rem 1rem; border-bottom: 1px solid #e9ecef; }\n        .article-content table tr:hover { background: rgba(23, 100, 255, 0.05); }\n        .article-content strong { color: #1764ff; font-weight: 600; }\n        .article-content em { color: #6c757d; font-style: italic; }\n        .article-content a { color: #1764ff; text-decoration: none; border-bottom: 1px solid transparent; transition: all 0.3s ease; }\n        .article-content a:hover { border-bottom-color: #1764ff; color: #0056b3; }\n        .article-content .definition-item { background: linear-gradient(135deg, rgba(40, 167, 69, 0.05), rgba(25, 135, 84, 0.05)); border-left: 4px solid #28a745; padding: 1rem; margin: 1rem 0; border-radius: 0 8px 8px 0; }\n        .article-content .definition-label { color: #28a745; font-weight: 600; display: block; margin-bottom: 0.5rem; }\n        .article-content .definition-content { color: #2c3e50; line-height: 1.6; }\n        .article-content .note-item { background: linear-gradient(135deg, rgba(255, 193, 7, 0.1), rgba(255, 193, 7, 0.05)); border: 1px solid rgba(255, 193, 7, 0.3); border-radius: 8px; padding: 1rem; margin: 1rem 0; position: relative; }\n        .article-content .note-item::before { content: \"💡\"; position: absolute; left: 1rem; top: 1rem; font-size: 1.2em; }\n        .article-content .note-label { color: #856404; font-weight: 600; margin-left: 2rem; }\n        .article-content .note-content { color: #2c3e50; line-height: 1.6; margin-left: 2rem; display: block; margin-top: 0.3rem; }\n        .article-content .math-formula { background: #f8f9fa; border: 1px solid #e9ecef; border-radius: 6px; padding: 1rem; margin: 1rem 0; text-align: center; font-family: 'Times New Roman', serif; font-size: 1.1em; color: #2c3e50; }\n        @media (min-width: 1400px) { .article-content { font-size: 17px; line-height: 1.8; } .article-detail { padding: 2.5rem; } }\n        @media (max-width: 1199px) and (min-width: 992px) { .article-detail { padding: 1.5rem; } .sidebar-card { padding: 1rem; } .toc-list a { font-size: 12px; padding: 6px 8px; } }\n        @media (max-width: 768px) {\n            .article-detail-container { padding: 60px 15px; }\n            .article-content .network-config, .article-content .algorithm-step, .article-content .definition-item, .article-content .note-item { margin: 0.5rem -1rem; border-radius: 0; }\n            .article-content .note-item::before { left: 0.5rem; }\n            .article-content .note-label, .article-content .note-content { margin-left: 1.5rem; }\n            .article-detail { padding: 1.5rem; }\n            .article-title { font-size: 1.6rem; line-height: 1.3; }\n            .article-meta { flex-wrap: wrap; gap: 1rem; padding: 1rem; font-size: 0.8rem; }\n            .article-meta .meta-item { flex-shrink: 1; }\n            .meta-icon { font-size: 14px; margin-right: 3px; }\n            .article-content { font-size: 15px; }\n            .article-content h1 { font-size: 1.6rem; }\n            .article-content h2 { font-size: 1.4rem; }\n            .article-content h3 { font-size: 1.2rem; }\n            .article-content h4 { font-size: 1.1rem; }\n            .article-content pre { padding: 0.75rem; font-size: 0.85em; overflow-x: auto; }\n            .article-content code { font-size: 0.85em; padding: 1px 4px; }\n            .sidebar { margin-top: 2rem; }\n            .sidebar-card { position: static; }\n            .toc-card { max-height: none; overflow-y: visible; }\n            .share-buttons { flex-direction: column; gap: 0.5rem; }\n            .share-buttons .btn { width: 100%; justify-content: center; }\n        }\n        @media print { body * { visibility: hidden; } .article-detail, .article-detail * { visibility: visible; } .article-detail { position: absolute; left: 0; top: 0; width: 100%; background: white; box-shadow: none; border: none; margin: 0; padding: 20px; } .article-meta { border: none; background: none; padding: 0; margin-bottom: 20px; } .article-footer, .reading-progress, .back-to-top { display: none !important; } .article-content { font-size: 12pt; line-height: 1.6; } .article-content h1, .article-content h2, .article-content h3, .article-content h4, .article-content h5, .article-content h6 { page-break-after: avoid; margin-top: 20pt; margin-bottom: 10pt; } .article-content pre, .article-content blockquote { page-break-inside: avoid; } }\n    </style>\n\n<script type=\"application/ld+json\">{\"@context\":\"https://schema.org\",\"@type\":\"BreadcrumbList\",\"itemListElement\":[{\"@type\":\"ListItem\",\"position\":1,\"name\":\"இல்லம்\",\"item\":\"https://ocr.oldfish.cn/default.aspx\"},{\"@type\":\"ListItem\",\"position\":2,\"name\":\"தொழில்நுட்ப கட்டுரைகள்\",\"item\":\"https://ocr.oldfish.cn/Articles.aspx\"},{\"@type\":\"ListItem\",\"position\":3,\"name\":\"கட்டுரை விவரங்கள்\",\"item\":\"https://ocr.oldfish.cn/Article.aspx?id=81&slug=rnn-sequence-modeling&lang=zh-Hans&_translation_source=true\"}]}</script>\n<link rel=\"stylesheet\" href=\"/site/css/site.css\" type=\"text/css\" /><title>【ஆழமான கற்றல் OCR தொடர்·4】மீண்டும் மீண்டும் நரம்பியல் நெட்வொர்க்குகள் மற்றும் வரிசை மாடலிங்</title><meta http-equiv=\"Content-Language\" content=\"ta\" />\n</head>\r\n<body>\r\n    <div class=\"home-container root\">\r\n        <header class=\"v4_header_pc\">\r\n            <div class=\"header_pc_left\">\r\n                <a href=\"Default.aspx\" title=\"முகப்பு | AI அறிவார்ந்த உரை அங்கீகாரம்\" class=\"pc-logo-wrap ml-4 mr-5\">\r\n                    <picture>\r\n                        <source srcset=\"site/image/logo-s.png.webp\" type=\"image/webp\" />\r\n                        <img src=\"site/image/logo-s.png\" class=\"pc-logo\" style=\"height: 100%\" alt=\"OCR உரை அங்கீகாரம் உதவி அதிகாரப்பூர்வ இணையதள லோகோ - AI நுண்ணறிவு உரை அங்கீகார தளம்\" />\r\n                    </picture>\r\n                    <span style=\"font-size: 18px; font-weight: bold; color: #333;\">OCR உரை அங்கீகார உதவியாளர்</span>\r\n                </a>\r\n                <ul class=\"top-nav\" role=\"navigation\" aria-label=\"முக்கிய வழிசெலுத்தல்\">\r\n                    <li><a class=\"color-default\" href=\"Default.aspx\" title=\"OCR உரை அங்கீகார உதவியாளர் முகப்புப்பக்கம்\">இல்லம்</a></li>\r\n                    <li><a class=\"color-default\" data-sub=\"product-sub-nav\" href=\"Detail.aspx\" title=\"OCR தயாரிப்பு செயல்பாடு அறிமுகம்\">பொருளின் பண்புகள்:</a><div class=\"triangle\"></div>\r\n                    </li>\r\n                    <li><a class=\"color-default\" data-sub=\"experience-sub-nav\" href=\"Ocr.aspx\" title=\"OCR அம்சங்களை ஆன்லைனில் அனுபவிக்கவும்\">ஆன்லைன் அனுபவம்</a><div class=\"triangle\"></div>\r\n                    </li>\r\n                    <li><a class=\"color-default\" data-sub=\"vip-sub-nav\" href=\"Version.aspx\" title=\"OCR உறுப்பினர் மேம்படுத்தல் சேவை\">உறுப்பினர் மேம்பாடுகள்</a><div class=\"triangle\"></div>\r\n                    </li>\r\n                    <li><a class=\"color-default\" href=\"Download.aspx\" title=\"OCR உரை அங்கீகார உதவியாளரை இலவசமாகப் பதிவிறக்கவும்\">இலவச பதிவிறக்க</a></li>\r\n                    <li><a class=\"color-default\" data-sub=\"articles-sub-nav\" href=\"Articles.aspx\" title=\"OCR தொழில்நுட்ப கட்டுரைகள் மற்றும் அறிவு பகிர்வு\">தொழில்நுட்ப பகிர்வு</a><div class=\"triangle\"></div>\r\n                    </li>\r\n                    <li><a class=\"color-default\" data-sub=\"support-sub-nav\" href=\"FAQ.aspx\" title=\"OCR பயன்பாட்டு உதவி மற்றும் தொழில்நுட்ப ஆதரவு\">உதவி மையம்</a><div class=\"triangle\"></div>\r\n                    </li>\r\n                </ul>\r\n                <div class=\"product-con\">\r\n                    <section id=\"product-sub-nav\" class=\"pls-nav-dropdown\" style=\"visibility: hidden; height: 0px; display: none;\">\r\n                        <div class=\"d-flex nav-drown-con\" style=\"height: 0px; transition: transform 0.2s ease-in-out 0s,opacity 0.2s ease-in-out 0s; opacity: 0; transform: translate(0px, -100%);\">\r\n                            <div class=\"prdocu-sub-left\">\r\n                                <div class=\"mr-auto\">\r\n                                    <picture>\r\n                                        <source class=\"p-icon\" srcset=\"site/image/icon/p_1.png.webp\" type=\"image/webp\" alt=\"\" aria-hidden=\"true\" />\r\n                                        <img class=\"p-icon\" src=\"site/image/icon/p_1.png\" alt=\"OCR தயாரிப்பு செயல்பாடு ஐகான்\" aria-hidden=\"true\" loading=\"lazy\" />\r\n                                    </picture>\r\n                                    <div class=\"mt-4\">\r\n                                        <h3 class=\"h6\">OCR உரை அங்கீகார உதவியாளர்</h3>\r\n                                        <p class=\"color-gray fn14 mt-3 sub-left-desc\">செயல்திறனை மேம்படுத்தவும், செலவுகளைக் குறைக்கவும், மதிப்பை உருவாக்கவும்</p>\r\n                                        <p class=\"color-gray fn14 mt-3 sub-left-desc\">&nbsp;</p>\r\n                                        <p class=\"color-gray fn14 mt-3 sub-left-desc\">அறிவார்ந்த அங்கீகாரம், அதிவேக செயலாக்கம் மற்றும் துல்லியமான வெளியீடு</p>\r\n                                        <p class=\"color-gray fn14 mt-3 sub-left-desc\">உரை முதல் அட்டவணைகள் வரை, சூத்திரங்கள் முதல் மொழிபெயர்ப்புகள் வரை</p>\r\n                                        <p class=\"color-gray fn14 mt-3 sub-left-desc\">ஒவ்வொரு சொல் செயலாக்கத்தையும் மிகவும் எளிதாக்குங்கள்</p>\r\n                                        <a class=\"link block disa fn14 contact-business shake\" href=\"Detail.aspx\">அம்சங்களைப் பற்றி அறிக<span class=\"iconfont4 icon-xiangzuo gt\"></span></a>\r\n                                    </div>\r\n                                </div>\r\n                            </div>\r\n                            <div class=\"bg-white prdocu-sub-right\">\r\n                                <div>\r\n                                    <h2 class=\"text-base-color h6\">பொருளின் பண்புகள்:</h2>\r\n                                    <div class=\"mt-4 d-flex flex-wrap\">\r\n                                        <a href=\"Detail.aspx\" class=\"pro-item color-default\" title=\"OCR உதவியாளரின் முக்கிய செயல்பாடுகளின் விவரங்களைப் பார்க்கவும்\">\r\n                                            <div class=\"letter-wrap\">\r\n                                                <h3 class=\"h6\">முக்கிய அம்சங்கள்:</h3>\r\n                                                <span class=\"color-gray fn14\">98%+ அங்கீகார விகிதத்துடன், OCR உதவியாளரின் முக்கிய அம்சங்கள் மற்றும் தொழில்நுட்ப நன்மைகளைப் பற்றி மேலும் அறிக</span>\r\n                                            </div>\r\n                                        </a>\r\n                                        <a href=\"Version.aspx\" class=\"pro-item color-default\" title=\"OCR உதவியாளர் பதிப்புகளுக்கு இடையிலான வேறுபாடுகளை ஒப்பிடுக\">\r\n                                            <div class=\"letter-wrap\">\r\n                                                <h3 class=\"h6\">பதிப்பு ஒப்பீடு</h3>\r\n                                                <span class=\"color-gray fn14\">இலவச பதிப்பு, தனிப்பட்ட பதிப்பு, தொழில்முறை பதிப்பு மற்றும் இறுதி பதிப்பின் செயல்பாட்டு வேறுபாடுகளை விரிவாக ஒப்பிடுக</span>\r\n                                            </div>\r\n                                        </a>\r\n                                        <a href=\"FAQ.aspx\" class=\"pro-item color-default\" title=\"OCR உதவியாளரைப் பார்க்கவும் அடிக்கடி கேட்கப்படும் கேள்விகள்\">\r\n                                            <div class=\"letter-wrap\">\r\n                                                <h3 class=\"h6\">தயாரிப்பு கேள்வி பதில்</h3>\r\n                                                <span class=\"color-gray fn14\">தயாரிப்பு அம்சங்கள், பயன்பாட்டு முறைகள் மற்றும் அடிக்கடி கேட்கப்படும் கேள்விகளுக்கான விரிவான பதில்கள் பற்றி விரைவாகத் தெரிந்துகொள்ளுங்கள்</span>\r\n                                            </div>\r\n                                        </a>\r\n                                        <a href=\"Download.aspx\" class=\"pro-item color-default\" title=\"OCR உரை அங்கீகார உதவியாளரை இலவசமாகப் பதிவிறக்கவும்\">\r\n                                            <div class=\"letter-wrap\">\r\n                                                <h3 class=\"h6\">இதை இலவசமாக முயற்சிக்கவும்</h3>\r\n                                                <span class=\"color-gray fn14\">பதிவிறக்கி நிறுவவும் OCR உதவியாளர் இப்போது சக்திவாய்ந்த உரை அங்கீகார செயல்பாட்டை இலவசமாக அனுபவிக்க</span>\r\n                                            </div>\r\n                                        </a>\r\n                                        <div style=\"width: 100%; margin: 15px 0;\">\r\n                                            <h4 class=\"h6\" style=\"color: #007cfa; margin-bottom: 10px;\">ஆன்லைன் OCR அங்கீகாரம்</h4>\r\n                                        </div>\r\n                                        <a href=\"Ocr.aspx?type=text_recognize\" class=\"pro-item color-default\" title=\"உலகளாவிய உரை அறிதலை ஆன்லைனில் அனுபவிக்கவும்\">\r\n                                            <div class=\"letter-wrap\">\r\n                                                <h3 class=\"h6\">யுனிவர்சல் கேரக்டர் அங்கீகாரம்</h3>\r\n                                                <span class=\"color-gray fn14\">பன்மொழி உயர் துல்லியமான உரையின் அறிவார்ந்த பிரித்தெடுத்தல், அச்சிடப்பட்ட மற்றும் பல காட்சி சிக்கலான பட அங்கீகாரத்தை ஆதரிக்கிறது</span>\r\n                                            </div>\r\n                                        </a>\r\n                                        <a href=\"Ocr.aspx?type=table\" class=\"pro-item color-default\">\r\n                                            <div class=\"letter-wrap\">\r\n                                                <h3 class=\"h6\">யுனிவர்சல் அட்டவணை அடையாளம்</h3>\r\n                                                <span class=\"color-gray fn14\">அட்டவணை படங்களை எக்செல் கோப்புகளாக அறிவார்ந்த மாற்றம், சிக்கலான அட்டவணை கட்டமைப்புகள் மற்றும் இணைக்கப்பட்ட கலங்களின் தானியங்கி செயலாக்கம்</span>\r\n                                            </div>\r\n                                        </a>\r\n                                        <a href=\"Ocr.aspx?type=handwritten_ocr\" class=\"pro-item color-default\">\r\n                                            <div class=\"letter-wrap\">\r\n                                                <h3 class=\"h6\">கையெழுத்து அங்கீகாரம்</h3>\r\n                                                <span class=\"color-gray fn14\">சீன மற்றும் ஆங்கில கையால் எழுதப்பட்ட உள்ளடக்கத்தின் புத்திசாலித்தனமான அங்கீகாரம், வகுப்பறை குறிப்புகள், மருத்துவ பதிவுகள் மற்றும் பிற காட்சிகளை ஆதரிக்கிறது</span>\r\n                                            </div>\r\n                                        </a>\r\n                                        <a href=\"Ocr.aspx?type=pdf2word\" class=\"pro-item color-default\">\r\n                                            <div class=\"letter-wrap\">\r\n                                                <h3 class=\"h6\">வார்த்தைக்கு PDF</h3>\r\n                                                <span class=\"color-gray fn14\">PDF ஆவணங்கள் விரைவாக வேர்ட் வடிவத்திற்கு மாற்றப்பட்டு, அசல் தளவமைப்பு மற்றும் கிராஃபிக் தளவமைப்பை முழுமையாகப் பாதுகாக்கின்றன</span>\r\n                                            </div>\r\n                                        </a>\r\n                                    </div>\r\n                                </div>\r\n                            </div>\r\n                        </div>\r\n                    </section>\r\n                    <section id=\"experience-sub-nav\" class=\"pls-nav-dropdown solution-part\" style=\"visibility: hidden; height: 0px; display: none;\">\r\n                        <div class=\"d-flex nav-drown-con\" style=\"height: 0px; transition: transform 0.2s ease-in-out 0s,opacity 0.2s ease-in-out 0s; opacity: 0; transform: translate(0px, -100%);\">\r\n                            <div class=\"prdocu-sub-left\">\r\n                                <div class=\"mr-auto\">\r\n                                    <picture>\r\n                                        <source srcset=\"site/image/icon/p_4.png.webp\" type=\"image/webp\" />\r\n                                        <img class=\"p-icon\" src=\"site/image/icon/p_4.png\" alt=\"ஆன்லைன் OCR அனுபவ மைய ஐகான்\" aria-hidden=\"true\" loading=\"lazy\" />\r\n                                    </picture>\r\n                                    <div class=\"mt-4\">\r\n                                        <h3 class=\"h6\">OCR உரை அங்கீகார உதவியாளர்</h3>\r\n                                        <p class=\"color-gray fn14 mt-3 sub-left-desc\">உரை, அட்டவணைகள், சூத்திரங்கள், ஆவணங்கள், மொழிபெயர்ப்புகள்</p>\r\n                                        <p class=\"color-gray fn14 mt-3 sub-left-desc\">&nbsp;</p>\r\n                                        <p class=\"color-gray fn14 mt-3 sub-left-desc\">உங்கள் சொல் செயலாக்க தேவைகள் அனைத்தையும் மூன்று படிகளில் முடிக்கவும்</p>\r\n                                        <p class=\"color-gray fn14 mt-3 sub-left-desc\">ஸ்கிரீன்ஷாட் → → பயன்பாடுகளை அடையாளம் காணவும்</p>\r\n                                        <p class=\"color-gray fn14 mt-3 sub-left-desc\">வேலை திறனை 300% அதிகரிக்கவும்</p>\r\n                                        <a class=\"link block disa fn14 contact-business shake\" href=\"Ocr.aspx\">இப்போது முயற்சி செய்<span class=\"iconfont4 icon-xiangzuo gt\"></span></a>\r\n                                    </div>\r\n                                </div>\r\n                            </div>\r\n                            <div class=\"bg-white prdocu-sub-right\">\r\n                                <div>\r\n                                    <div class=\"mt-4 d-flex flex-wrap\">\r\n                                        <div style=\"width: 100%; margin-bottom: 15px;\">\r\n                                            <h4 class=\"h6\" style=\"color: #007cfa; margin-bottom: 10px;\">OCR செயல்பாட்டு அனுபவம்</h4>\r\n                                        </div>\r\n                                        <a href=\"Ocr.aspx?type=index\" class=\"pro-item color-default\">\r\n                                            <div class=\"letter-wrap\">\r\n                                                <h3 class=\"h6\">முழு செயல்பாடு</h3>\r\n                                                <span class=\"color-gray fn14\">உங்கள் தேவைகளுக்கு சிறந்த தீர்வை விரைவாகக் கண்டறிய அனைத்து OCR ஸ்மார்ட் அம்சங்களையும் ஒரே இடத்தில் அனுபவிக்கவும்</span>\r\n                                            </div>\r\n                                        </a>\r\n                                        <a href=\"Ocr.aspx?type=text_recognize\" class=\"pro-item color-default\">\r\n                                            <div class=\"letter-wrap\">\r\n                                                <h3 class=\"h6\">யுனிவர்சல் கேரக்டர் அங்கீகாரம்</h3>\r\n                                                <span class=\"color-gray fn14\">பன்மொழி உயர் துல்லியமான உரையின் அறிவார்ந்த பிரித்தெடுத்தல், அச்சிடப்பட்ட மற்றும் பல காட்சி சிக்கலான பட அங்கீகாரத்தை ஆதரிக்கிறது</span>\r\n                                            </div>\r\n                                        </a>\r\n                                        <a href=\"Ocr.aspx?type=table\" class=\"pro-item color-default\">\r\n                                            <div class=\"letter-wrap\">\r\n                                                <h3 class=\"h6\">யுனிவர்சல் அட்டவணை அடையாளம்</h3>\r\n                                                <span class=\"color-gray fn14\">அட்டவணை படங்களை எக்செல் கோப்புகளாக அறிவார்ந்த மாற்றம், சிக்கலான அட்டவணை கட்டமைப்புகள் மற்றும் இணைக்கப்பட்ட கலங்களின் தானியங்கி செயலாக்கம்</span>\r\n                                            </div>\r\n                                        </a>\r\n                                        <a href=\"Ocr.aspx?type=handwritten_ocr\" class=\"pro-item color-default\">\r\n                                            <div class=\"letter-wrap\">\r\n                                                <h3 class=\"h6\">கையெழுத்து அங்கீகாரம்</h3>\r\n                                                <span class=\"color-gray fn14\">சீன மற்றும் ஆங்கில கையால் எழுதப்பட்ட உள்ளடக்கத்தின் புத்திசாலித்தனமான அங்கீகாரம், வகுப்பறை குறிப்புகள், மருத்துவ பதிவுகள் மற்றும் பிற காட்சிகளை ஆதரிக்கிறது</span>\r\n                                            </div>\r\n                                        </a>\r\n                                        <a href=\"Ocr.aspx?type=pdf2word\" class=\"pro-item color-default\">\r\n                                            <div class=\"letter-wrap\">\r\n                                                <h3 class=\"h6\">வார்த்தைக்கு PDF</h3>\r\n                                                <span class=\"color-gray fn14\">PDF ஆவணங்கள் விரைவாக வேர்ட் வடிவத்திற்கு மாற்றப்பட்டு, அசல் தளவமைப்பு மற்றும் கிராஃபிக் தளவமைப்பை முழுமையாகப் பாதுகாக்கின்றன</span>\r\n                                            </div>\r\n                                        </a>\r\n                                        <a href=\"Ocr.aspx?type=pdf2markdown\" class=\"pro-item color-default\">\r\n                                            <div class=\"letter-wrap\">\r\n                                                <h3 class=\"h6\">மார்க்டவுன் செய்ய PDF</h3>\r\n                                                <span class=\"color-gray fn14\">PDF ஆவணங்கள் புத்திசாலித்தனமாக MD வடிவத்திற்கு மாற்றப்படுகின்றன, மேலும் குறியீடு தொகுதிகள் மற்றும் உரை கட்டமைப்புகள் தானாகவே செயலாக்கத்திற்கு உகந்ததாக இருக்கும்</span>\r\n                                            </div>\r\n                                        </a>\r\n                                        <div style=\"width: 100%; margin: 15px 0;\">\r\n                                            <h4 class=\"h6\" style=\"color: #007cfa; margin-bottom: 10px;\">ஆவண செயலாக்க கருவிகள்</h4>\r\n                                        </div>\r\n                                        <a href=\"Ocr.aspx?type=word2pdf\" class=\"pro-item color-default\">\r\n                                            <div class=\"letter-wrap\">\r\n                                                <h3 class=\"h6\">PDF க்கு வார்த்தை</h3>\r\n                                                <span class=\"color-gray fn14\">வேர்ட் ஆவணங்கள் ஒரே கிளிக்கில் PDF ஆக மாற்றப்படுகின்றன, அசல் வடிவமைப்பைத் தக்கவைத்துக் கொள்கின்றன, காப்பகத்திற்கும் அதிகாரப்பூர்வ ஆவணப் பகிர்வுக்கும் ஏற்றது</span>\r\n                                            </div>\r\n                                        </a>\r\n                                        <a href=\"Ocr.aspx?type=word2jpg\" class=\"pro-item color-default\">\r\n                                            <div class=\"letter-wrap\">\r\n                                                <h3 class=\"h6\">வார்த்தைக்கு படம்</h3>\r\n                                                <span class=\"color-gray fn14\">Word ஆவணம் JPG படத்திற்கு அறிவார்ந்த மாற்றம், பல பக்க செயலாக்கத்தை ஆதரிக்கிறது, சமூக ஊடகங்களில் பகிர எளிதானது</span>\r\n                                            </div>\r\n                                        </a>\r\n                                        <a href=\"Ocr.aspx?type=pdf2jpg\" class=\"pro-item color-default\">\r\n                                            <div class=\"letter-wrap\">\r\n                                                <h3 class=\"h6\">படத்திற்கு PDF</h3>\r\n                                                <span class=\"color-gray fn14\">PDF ஆவணங்களை JPG படங்களாக உயர் வரையறையில் மாற்றவும், தொகுதி செயலாக்கம் மற்றும் தனிப்பயன் தீர்மானத்தை ஆதரிக்கவும்</span>\r\n                                            </div>\r\n                                        </a>\r\n                                        <a href=\"Ocr.aspx?type=image2pdf\" class=\"pro-item color-default\">\r\n                                            <div class=\"letter-wrap\">\r\n                                                <h3 class=\"h6\">PDF க்கு படம்</h3>\r\n                                                <span class=\"color-gray fn14\">பல படங்களை PDF ஆவணங்களில் ஒன்றிணைத்தல், வரிசையாக்கம் மற்றும் பக்க அமைப்பை ஆதரிக்கவும்</span>\r\n                                            </div>\r\n                                        </a>\r\n                                        <div style=\"width: 100%; margin: 15px 0;\">\r\n                                            <h4 class=\"h6\" style=\"color: #007cfa; margin-bottom: 10px;\">டெவலப்பர் கருவிகள்</h4>\r\n                                        </div>\r\n                                        <a href=\"Tool.aspx?type=Json\" class=\"pro-item color-default\">\r\n                                            <div class=\"letter-wrap\">\r\n                                                <h3 class=\"h6\">JSON வடிவமைப்பு</h3>\r\n                                                <span class=\"color-gray fn14\">JSON குறியீடு கட்டமைப்பை புத்திசாலித்தனமாக அழகுபடுத்தவும், சுருக்க மற்றும் விரிவாக்கத்தை ஆதரிக்கவும், வளர்ச்சி மற்றும் பிழைத்திருத்தத்தை எளிதாக்கவும்</span>\r\n                                            </div>\r\n                                        </a>\r\n                                        <a href=\"Tool.aspx?type=Regex\" class=\"pro-item color-default\">\r\n                                            <div class=\"letter-wrap\">\r\n                                                <h3 class=\"h6\">வழக்கமான வெளிப்பாடு</h3>\r\n                                                <span class=\"color-gray fn14\">பொதுவான வடிவங்களின் உள்ளமைக்கப்பட்ட நூலகத்துடன் உண்மையான நேரத்தில் வழக்கமான வெளிப்பாடு பொருந்தக்கூடிய விளைவுகளை சரிபார்க்கவும்</span>\r\n                                            </div>\r\n                                        </a>\r\n                                        <a href=\"Tool.aspx?type=Encode\" class=\"pro-item color-default\">\r\n                                            <div class=\"letter-wrap\">\r\n                                                <h3 class=\"h6\">உரை குறியாக்க மாற்றம்</h3>\r\n                                                <span class=\"color-gray fn14\">இது Base64, URL மற்றும் யூனிகோட் போன்ற பல குறியாக்க வடிவங்களை மாற்றுவதை ஆதரிக்கிறது</span>\r\n                                            </div>\r\n                                        </a>\r\n                                        <a href=\"Tool.aspx?type=Diff\" class=\"pro-item color-default\">\r\n                                            <div class=\"letter-wrap\">\r\n                                                <h3 class=\"h6\">உரை பொருத்தம் மற்றும் ஒன்றிணைத்தல்</h3>\r\n                                                <span class=\"color-gray fn14\">உரை வேறுபாடுகளை முன்னிலைப்படுத்தவும் மற்றும் வரிக்கு வரி ஒப்பீடு மற்றும் புத்திசாலித்தனமான ஒன்றிணைப்பை ஆதரிக்கவும்</span>\r\n                                            </div>\r\n                                        </a>\r\n                                        <a href=\"Tool.aspx?type=Color\" class=\"pro-item color-default\">\r\n                                            <div class=\"letter-wrap\">\r\n                                                <h3 class=\"h6\">வண்ண கருவி</h3>\r\n                                                <span class=\"color-gray fn14\">RGB/HEX வண்ண மாற்றம், ஆன்லைன் கலர் பிக்கர், முன்-இறுதி வளர்ச்சிக்கு கட்டாயம் இருக்க வேண்டிய கருவி</span>\r\n                                            </div>\r\n                                        </a>\r\n                                        <a href=\"Tool.aspx?type=Count\" class=\"pro-item color-default\">\r\n                                            <div class=\"letter-wrap\">\r\n                                                <h3 class=\"h6\">சொல் எண்ணிக்கை</h3>\r\n                                                <span class=\"color-gray fn14\">எழுத்துக்கள், சொற்களஞ்சியம் மற்றும் பத்திகளின் புத்திசாலித்தனமான எண்ணுதல் மற்றும் உரை தளவமைப்பை தானாக மேம்படுத்துதல்</span>\r\n                                            </div>\r\n                                        </a>\r\n                                        <a href=\"Tool.aspx?type=Timespan\" class=\"pro-item color-default\">\r\n                                            <div class=\"letter-wrap\">\r\n                                                <h3 class=\"h6\">நேர முத்திரை மாற்றம்</h3>\r\n                                                <span class=\"color-gray fn14\">நேரம் யூனிக்ஸ் நேர முத்திரைகளுக்கு மாற்றப்படுகிறது, மேலும் பல வடிவங்கள் மற்றும் நேர மண்டல அமைப்புகள் ஆதரிக்கப்படுகின்றன</span>\r\n                                            </div>\r\n                                        </a>\r\n                                        <a href=\"Tool.aspx?type=Calc\" class=\"pro-item color-default\">\r\n                                            <div class=\"letter-wrap\">\r\n                                                <h3 class=\"h6\">கால்குலேட்டர் கருவி</h3>\r\n                                                <span class=\"color-gray fn14\">அடிப்படை செயல்பாடுகள் மற்றும் மேம்பட்ட கணித செயல்பாடு கணக்கீடுகளுக்கான ஆதரவுடன் ஆன்லைன் அறிவியல் கால்குலேட்டர்</span>\r\n                                            </div>\r\n                                        </a>\r\n                                    </div>\r\n                                </div>\r\n                            </div>\r\n                        </div>\r\n                    </section>\r\n                    <section id=\"articles-sub-nav\" class=\"pls-nav-dropdown solution-part\" style=\"visibility: hidden; height: 0px; display: none;\">\r\n                        <div class=\"d-flex nav-drown-con\" style=\"height: 0px; transition: transform 0.2s ease-in-out 0s,opacity 0.2s ease-in-out 0s; opacity: 0; transform: translate(0px, -100%);\">\r\n                            <div class=\"prdocu-sub-left\">\r\n                                <div class=\"mr-auto\">\r\n                                    <picture>\r\n                                        <source srcset=\"site/image/icon/p_4.png.webp\" type=\"image/webp\" />\r\n                                        <img class=\"p-icon\" src=\"site/image/icon/p_4.png\" alt=\"தொழில்நுட்ப பகிர்வு மைய ஐகான்\" aria-hidden=\"true\" loading=\"lazy\" />\r\n                                    </picture>\r\n                                    <div class=\"mt-4\">\r\n                                        <h3 class=\"h6\">OCR தொழில்நுட்ப பகிர்வு</h3>\r\n                                        <p class=\"color-gray fn14 mt-3 sub-left-desc\">தொழில்நுட்ப பயிற்சிகள், பயன்பாட்டு வழக்குகள், கருவி பரிந்துரைகள்</p>\r\n                                        <p class=\"color-gray fn14 mt-3 sub-left-desc\">&nbsp;</p>\r\n                                        <p class=\"color-gray fn14 mt-3 sub-left-desc\">தொடக்கத்திலிருந்து தேர்ச்சி வரை ஒரு முழுமையான கற்றல் பாதை</p>\r\n                                        <p class=\"color-gray fn14 mt-3 sub-left-desc\">நடைமுறை வழக்குகள் → தொழில்நுட்ப பகுப்பாய்வு → கருவி பயன்பாடுகள்</p>\r\n                                        <p class=\"color-gray fn14 mt-3 sub-left-desc\">OCR தொழில்நுட்ப மேம்பாட்டிற்கான உங்கள் பாதையை மேம்படுத்துங்கள்</p>\r\n                                        <a class=\"link block disa fn14 contact-business shake\" href=\"Articles.aspx\">கட்டுரைகளை உலாவுக<span class=\"iconfont4 icon-xiangzuo gt\"></span></a>\r\n                                    </div>\r\n                                </div>\r\n                            </div>\r\n                            <div class=\"bg-white prdocu-sub-right\">\r\n                                <div>\r\n                                    <h2 class=\"text-base-color h6\">தொழில்நுட்ப பகிர்வு</h2>\r\n                                    <div class=\"mt-4 d-flex flex-wrap\">\r\n                                        <a href=\"Articles.aspx\" class=\"pro-item color-default\" title=\"அனைத்து OCR தொழில்நுட்ப கட்டுரைகளையும் காண்க\">\r\n                                            <div class=\"letter-wrap\">\r\n                                                <h3 class=\"h6\">அனைத்து கட்டுரைகள்</h3>\r\n                                                <span class=\"color-gray fn14\">அடிப்படை முதல் மேம்பட்ட வரை முழுமையான அறிவை உள்ளடக்கிய அனைத்து OCR தொழில்நுட்பக் கட்டுரைகளையும் உலாவவும்</span>\r\n                                            </div>\r\n                                        </a>\r\n                                        \r\n                                        <a href=\"Articles.aspx?category=2\" class=\"pro-item color-default\" title=\"OCR தொழில்நுட்ப பயிற்சிகள் மற்றும் தொடங்குதல் வழிகாட்டிகள்\">\r\n                                            <div class=\"letter-wrap\">\r\n                                                <h3 class=\"h6\">மேம்பட்ட வழிகாட்டி</h3>\r\n                                                <span class=\"color-gray fn14\">அறிமுகம் முதல் திறமையான OCR தொழில்நுட்ப பயிற்சிகள், எப்படி செய்வது என்பது பற்றிய விரிவான வழிகாட்டிகள் மற்றும் நடைமுறை ஒத்திகைகள்</span>\r\n                                            </div>\r\n                                        </a>\r\n                                        \r\n                                        <a href=\"Articles.aspx?category=3\" class=\"pro-item color-default\" title=\"OCR தொழில்நுட்பக் கொள்கைகள், வழிமுறைகள் மற்றும் பயன்பாடுகள்\">\r\n                                            <div class=\"letter-wrap\">\r\n                                                <h3 class=\"h6\">தொழில்நுட்ப ஆய்வு</h3>\r\n                                                <span class=\"color-gray fn14\">OCR தொழில்நுட்பத்தின் எல்லைகளை, கொள்கைகள் முதல் பயன்பாடுகள் வரை ஆராயுங்கள் மற்றும் முக்கிய வழிமுறைகளை ஆழமாக பகுப்பாய்வு செய்யுங்கள்</span>\r\n                                            </div>\r\n                                        </a>\r\n                                        \r\n                                        <a href=\"Articles.aspx?category=4\" class=\"pro-item color-default\" title=\"OCR துறையில் சமீபத்திய முன்னேற்றங்கள் மற்றும் மேம்பாட்டு போக்குகள்\">\r\n                                            <div class=\"letter-wrap\">\r\n                                                <h3 class=\"h6\">தொழில் போக்குகள்</h3>\r\n                                                <span class=\"color-gray fn14\">OCR தொழில்நுட்ப மேம்பாட்டு போக்குகள், சந்தை பகுப்பாய்வு, தொழில் இயக்கவியல் மற்றும் எதிர்கால வாய்ப்புகள் பற்றிய ஆழமான நுண்ணறிவு</span>\r\n                                            </div>\r\n                                        </a>\r\n                                        \r\n                                        <a href=\"Articles.aspx?category=5\" class=\"pro-item color-default\" title=\"பல்வேறு தொழில்களில் OCR தொழில்நுட்பத்தின் பயன்பாட்டு வழக்குகள்\">\r\n                                            <div class=\"letter-wrap\">\r\n                                                <h3 class=\"h6\">பயன்பாட்டு வழக்குகள்:</h3>\r\n                                                <span class=\"color-gray fn14\">நிஜ உலக பயன்பாட்டு வழக்குகள், தீர்வுகள் மற்றும் பல்வேறு தொழில்களில் OCR தொழில்நுட்பத்தின் சிறந்த நடைமுறைகள் பகிரப்படுகின்றன</span>\r\n                                            </div>\r\n                                        </a>\r\n                                        \r\n                                        <a href=\"Articles.aspx?category=6\" class=\"pro-item color-default\" title=\"தொழில்முறை மதிப்புரைகள், ஒப்பீட்டு பகுப்பாய்வு மற்றும் OCR மென்பொருள் கருவிகளைப் பயன்படுத்துவதற்கான பரிந்துரைக்கப்பட்ட வழிகாட்டுதல்கள்\">\r\n                                            <div class=\"letter-wrap\">\r\n                                                <h3 class=\"h6\">கருவி மதிப்பாய்வு</h3>\r\n                                                <span class=\"color-gray fn14\">பல்வேறு OCR உரை அங்கீகார மென்பொருள் மற்றும் கருவிகளை மதிப்பீடு செய்து, விரிவான செயல்பாட்டு ஒப்பீடு மற்றும் தேர்வு பரிந்துரைகளை வழங்கவும்</span>\r\n                                            </div>\r\n                                        </a>\r\n                                        \r\n                                    </div>\r\n                                </div>\r\n                            </div>\r\n                        </div>\r\n                    </section>\r\n                    <section id=\"vip-sub-nav\" class=\"pls-nav-dropdown solution-part\" style=\"visibility: hidden; height: 0px; display: none;\">\r\n                        <div class=\"d-flex nav-drown-con\" style=\"height: 0px; transition: transform 0.2s ease-in-out 0s,opacity 0.2s ease-in-out 0s; opacity: 0; transform: translate(0px, -100%);\">\r\n                            <div class=\"prdocu-sub-left\">\r\n                                <div class=\"mr-auto\">\r\n                                    <picture>\r\n                                        <source class=\"p-icon\" srcset=\"site/image/icon/p_1.png.webp\" type=\"image/webp\" alt=\"\" aria-hidden=\"true\" />\r\n                                        <img class=\"p-icon\" src=\"site/image/icon/p_1.png\" alt=\"உறுப்பினர் மேம்படுத்தல் சேவை ஐகான்\" aria-hidden=\"true\" loading=\"lazy\" />\r\n                                    </picture>\r\n                                    <div class=\"mt-4\">\r\n                                        <h3 class=\"h6\">உறுப்பினர் மேம்படுத்தல் சேவை</h3>\r\n                                        <p class=\"color-gray fn14 mt-3 sub-left-desc\">அனைத்து பிரீமியம் அம்சங்களையும் பூட்டுநீக்கி பிரத்யேக சேவைகளை அனுபவியுங்கள்</p>\r\n                                        <p class=\"color-gray fn14 mt-3 sub-left-desc\">&nbsp;</p>\r\n                                        <p class=\"color-gray fn14 mt-3 sub-left-desc\">ஆஃப்லைன் அங்கீகாரம், தொகுதி செயலாக்கம், வரம்பற்ற பயன்பாடு</p>\r\n                                        <p class=\"color-gray fn14 mt-3 sub-left-desc\">புரோ → அல்டிமேட் → எண்டர்பிரைஸ்</p>\r\n                                        <p class=\"color-gray fn14 mt-3 sub-left-desc\">உங்கள் தேவைகளுக்கு ஏற்ப ஏதாவது இருக்கிறது</p>\r\n                                        <a class=\"link block disa fn14 contact-business shake\" href=\"Version.aspx\">விவரங்களைக் காட்டு<span class=\"iconfont4 icon-xiangzuo gt\"></span></a>\r\n                                    </div>\r\n                                </div>\r\n                            </div>\r\n                            <div class=\"bg-white prdocu-sub-right\">\r\n                                <div>\r\n                                    <h2 class=\"text-base-color h6\" style=\"margin: inherit;\">உறுப்பினர் மேம்பாடுகள்</h2>\r\n                                    <div class=\"mt-4 d-flex flex-wrap\">\r\n                                        <a href=\"Version.aspx\" class=\"pro-item color-default\">\r\n                                            <div class=\"letter-wrap\">\r\n                                                <h3 class=\"h6\">உறுப்பினர் சலுகைகள்</h3>\r\n                                                <span class=\"color-gray fn14\">பதிப்புகளுக்கு இடையிலான வேறுபாடுகளைப் பற்றி மேலும் அறிக மற்றும் உங்களுக்கு மிகவும் பொருத்தமான உறுப்பினர் அடுக்கைத் தேர்வுசெய்க</span>\r\n                                            </div>\r\n                                        </a>\r\n                                        <a href=\"Upgrade.aspx\" class=\"pro-item color-default\">\r\n                                            <div class=\"letter-wrap\">\r\n                                                <h3 class=\"h6\">இப்போதே மேம்படுத்தவும்</h3>\r\n                                                <span class=\"color-gray fn14\">மேலும் பிரீமியம் அம்சங்கள் மற்றும் பிரத்யேக சேவைகளைத் திறக்க உங்கள் விஐபி மெம்பர்ஷிப்பை விரைவாக மேம்படுத்துங்கள்</span>\r\n                                            </div>\r\n                                        </a>\r\n                                        <a href=\"User.aspx\" class=\"pro-item color-default\">\r\n                                            <div class=\"letter-wrap\">\r\n                                                <h3 class=\"h6\">எனது கணக்கு</h3>\r\n                                                <span class=\"color-gray fn14\">அமைப்புகளைத் தனிப்பயனாக்க கணக்குத் தகவல், சந்தா நிலை மற்றும் பயன்பாட்டு வரலாற்றை நிர்வகிக்கவும்</span>\r\n                                            </div>\r\n                                        </a>\r\n                                    </div>\r\n                                </div>\r\n                            </div>\r\n                        </div>\r\n                    </section>\r\n                    <section id=\"support-sub-nav\" class=\"pls-nav-dropdown solution-part\" style=\"visibility: hidden; height: 0px; display: none;\">\r\n                        <div class=\"d-flex nav-drown-con\" style=\"height: 0px; transition: transform 0.2s ease-in-out 0s,opacity 0.2s ease-in-out 0s; opacity: 0; transform: translate(0px, -100%);\">\r\n                            <div class=\"prdocu-sub-left\">\r\n                                <div class=\"mr-auto\">\r\n                                    <picture>\r\n                                        <source class=\"p-icon\" srcset=\"site/image/icon/p_4.png.webp\" type=\"image/webp\" alt=\"\" aria-hidden=\"true\" />\r\n                                        <img class=\"p-icon\" src=\"site/image/icon/p_4.png\" alt=\"உதவி மைய உதவிப் படவுரு\" aria-hidden=\"true\" loading=\"lazy\" />\r\n                                    </picture>\r\n                                    <div class=\"mt-4\">\r\n                                        <h3 class=\"h6\">உதவி மையம்</h3>\r\n                                        <p class=\"color-gray fn14 mt-3 sub-left-desc\">தொழில்முறை வாடிக்கையாளர் சேவை, விரிவான ஆவணங்கள் மற்றும் விரைவான பதில்</p>\r\n                                        <p class=\"color-gray fn14 mt-3 sub-left-desc\">&nbsp;</p>\r\n                                        <p class=\"color-gray fn14 mt-3 sub-left-desc\">நீங்கள் பிரச்சினைகளை எதிர்கொள்ளும்போது பீதி அடைய வேண்டாம்</p>\r\n                                        <p class=\"color-gray fn14 mt-3 sub-left-desc\">சிக்கல் → → தீர்க்கப்பட்டது</p>\r\n                                        <p class=\"color-gray fn14 mt-3 sub-left-desc\">உங்கள் அனுபவத்தை மென்மையாக்குங்கள்</p>\r\n                                        <a class=\"link block disa fn14 contact-business shake\" href=\"FAQ.aspx\">உதவி பெறுங்கள்<span class=\"iconfont4 icon-xiangzuo gt\"></span></a>\r\n                                    </div>\r\n                                </div>\r\n                            </div>\r\n                            <div class=\"bg-white prdocu-sub-right\">\r\n                                <div>\r\n                                    <h2 class=\"text-base-color h6\" style=\"margin: inherit;\">உதவி மையம்</h2>\r\n                                    <div class=\"mt-4 d-flex flex-wrap\">\r\n                                        <a href=\"FAQ.aspx\" class=\"pro-item color-default\">\r\n                                            <div class=\"letter-wrap\">\r\n                                                <h3 class=\"h6\">அடிக்கடி கேட்கப்படும் கேள்விகள்</h3>\r\n                                                <span class=\"color-gray fn14\">பொதுவான பயனர் கேள்விகளுக்கு விரைவாக பதிலளித்து, விரிவான பயன்பாட்டு வழிகாட்டிகள் மற்றும் தொழில்நுட்ப ஆதரவை வழங்கவும்</span>\r\n                                            </div>\r\n                                        </a>\r\n                                        <a href=\"AboutUs.aspx\" class=\"pro-item color-default\">\r\n                                            <div class=\"letter-wrap\">\r\n                                                <h3 class=\"h6\">எங்களைப் பற்றி</h3>\r\n                                                <span class=\"color-gray fn14\">OCR உரை அங்கீகார உதவியாளரின் வளர்ச்சி வரலாறு, முக்கிய செயல்பாடுகள் மற்றும் சேவை கருத்துக்கள் பற்றி அறிக</span>\r\n                                            </div>\r\n                                        </a>\r\n                                        <a href=\"UserAgreement.aspx\" class=\"pro-item color-default\">\r\n                                            <div class=\"letter-wrap\">\r\n                                                <h3 class=\"h6\">பயனர் ஒப்பந்தம்</h3>\r\n                                                <span class=\"color-gray fn14\">விரிவான சேவை விதிமுறைகள் மற்றும் பயனர் உரிமைகள் மற்றும் கடமைகள்</span>\r\n                                            </div>\r\n                                        </a>\r\n                                        <a href=\"PrivacyPolicy.aspx\" class=\"pro-item color-default\">\r\n                                            <div class=\"letter-wrap\">\r\n                                                <h3 class=\"h6\">தனியுரிமை ஒப்பந்தம்</h3>\r\n                                                <span class=\"color-gray fn14\">தனிப்பட்ட தகவல் பாதுகாப்பு கொள்கை மற்றும் தரவு பாதுகாப்பு நடவடிக்கைகள்</span>\r\n                                            </div>\r\n                                        </a>\r\n                                        <a href=\"Status.aspx\" class=\"pro-item color-default\">\r\n                                            <div class=\"letter-wrap\">\r\n                                                <h3 class=\"h6\">கணினி நிலை</h3>\r\n                                                <span class=\"color-gray fn14\">உலகளாவிய அடையாள முனைகளின் செயல்பாட்டு நிலையை உண்மையான நேரத்தில் கண்காணிக்கவும் மற்றும் view கணினி செயல்திறன் தரவு</span>\r\n                                            </div>\r\n                                        </a>\r\n                                        <a href=\"javascript:void(0);\" class=\"pro-item color-default\" onclick=\"if(window.openCustomerService) window.openCustomerService(); else alert('வாடிக்கையாளர் சேவையைத் தொடர்புகொள்ள வலதுபுறத்தில் உள்ள மிதக்கும் சாளர ஐகானைக் கிளிக் செய்யவும்');\">\r\n                                            <div class=\"letter-wrap\">\r\n                                                <h3 class=\"h6\">வாடிக்கையாளர் சேவையைத் தொடர்பு கொள்ளவும்</h3>\r\n                                                <span class=\"color-gray fn14\">உங்கள் கேள்விகள் மற்றும் தேவைகளுக்கு விரைவாக பதிலளிக்க ஆன்லைன் வாடிக்கையாளர் சேவை ஆதரவு</span>\r\n                                            </div>\r\n                                        </a>\r\n                                    </div>\r\n                                </div>\r\n                            </div>\r\n                        </div>\r\n                    </section>\r\n                </div>\r\n            </div>\r\n            <div class=\"header_pc_right\" style=\"display: flex; align-items: center; justify-content: flex-end;\">\r\n                <div id=\"translate\"></div>\r\n                <div id=\"user-login-container\" data-url=\"User.ashx?op=userinfo\" style=\"white-space: nowrap; padding-right: 20px;\"></div>\r\n            </div>\r\n        </header>\r\n        <header class=\"v4_header_mob\">\r\n            <div class=\"container\">\r\n                <div class=\"row no-gutters justify-content-between align-items-center\">\r\n                    <a href=\"Default.aspx\" title=\"முகப்பு | AI அறிவார்ந்த உரை அங்கீகாரம்\" class=\"mob-logo-wrap\">\r\n                        <picture>\r\n                            <source srcset=\"site/image/logo-s.png.webp\" type=\"image/webp\" />\r\n                            <img src=\"site/image/logo-s.png\" class=\"pc-logo\" style=\"height: 32px; width: 32px\" alt=\"OCR உரை அங்கீகார உதவியாளர் மொபைல் லோகோ\" />\r\n                        </picture>\r\n                        <span style=\"font-size: 18px; font-weight: bold; color: #333;\">OCR உரை அங்கீகார உதவியாளர்</span>\r\n                    </a>\r\n                    <div class=\"right-menu\" role=\"button\" tabindex=\"0\" aria-label=\"வழிசெலுத்தல் மெனுவைத் திறக்கவும்\" data-toggle=\"collapse\" data-target=\"#mob-header-collapse\" aria-expanded=\"false\" aria-controls=\"mob-header-collapse\">\r\n                        <nav-products id=\"nav_products\">\r\n                            <nav-button class=\"submail-mdi-nav-btn\" style=\"width: 60px\">\r\n                                <label id=\"mdi-submail-nav-btn\">\r\n                                    <span class=\"mdi-nav-products-1\">\r\n                                        <span class=\"nav-products-icon\"></span>\r\n                                    </span>\r\n                                    <span class=\"mdi-nav-products-2\">\r\n                                        <span class=\"nav-products-icon\"></span>\r\n                                    </span>\r\n                                    <span class=\"mdi-nav-products-3\">\r\n                                        <span class=\"nav-products-icon\"></span>\r\n                                    </span>\r\n                                </label>\r\n                            </nav-button>\r\n                        </nav-products>\r\n                    </div>\r\n                </div>\r\n            </div>\r\n            <div class=\"mob-nav-content\">\r\n                <div class=\"sidebar-fix\">\r\n                    <div class=\"container\">\r\n                        <div class=\"row\">\r\n                            <div class=\"col-4 bg-white sidebar-fix-left\" style=\"border-right: 1px solid #e1e6ed\">\r\n                                <div class=\"mob-nav-item active\" data=\"0\">\r\n                                    <div class=\"nav-header\"><a href=\"Default.aspx\" class=\"nav-header-letter px-3\"><span>இல்லம்</span></a></div>\r\n                                </div>\r\n                                <div class=\"mob-nav-item\" data=\"1\">\r\n                                    <div class=\"nav-header\"><a href=\"Detail.aspx\" class=\"nav-header-letter px-3\"><span>நிகழ்ச்சி</span></a></div>\r\n                                </div>\r\n                                <div class=\"mob-nav-item\" data=\"2\">\r\n                                    <div class=\"nav-header\"><a href=\"Ocr.aspx\" class=\"nav-header-letter px-3\"><span>அனுபவம்</span></a></div>\r\n                                </div>\r\n                                <div class=\"mob-nav-item\" data=\"3\">\r\n                                    <div class=\"nav-header\"><a href=\"Version.aspx\" class=\"nav-header-letter px-3\"><span>உறுப்பினர்</span></a></div>\r\n                                </div>\r\n                                <div class=\"mob-nav-item\" data=\"4\">\r\n                                    <div class=\"nav-header\"><a href=\"Download.aspx\" class=\"nav-header-letter px-3\"><span>பதிவிறக்க</span></a></div>\r\n                                </div>\r\n                                <div class=\"mob-nav-item\" data=\"5\">\r\n                                    <div class=\"nav-header\"><a href=\"Articles.aspx\" class=\"nav-header-letter px-3\"><span>பங்கு</span></a></div>\r\n                                </div>\r\n                                <div class=\"mob-nav-item\" data=\"6\">\r\n                                    <div class=\"nav-header\"><a href=\"FAQ.aspx\" class=\"nav-header-letter px-3\"><span>உதவி</span></a></div>\r\n                                </div>\r\n                            </div>\r\n                            <div class=\"col-8 bg-white sidebar-fix-rigth\">\r\n                                <div class=\"sub-nav\">\r\n                                    <div class=\"sub-nav-item py-2 pb-3 px-4\">\r\n                                        <h2 class=\"h5\" style=\"margin: inherit;\">திறமையான உற்பத்தித்திறன் கருவிகள்</h2>\r\n                                        <p class=\"color-gray fn14 mt-3 sub-left-desc\">அறிவார்ந்த அங்கீகாரம், அதிவேக செயலாக்கம் மற்றும் துல்லியமான வெளியீடு</p>\r\n                                        <p class=\"color-gray fn14 mt-3 sub-left-desc\">&nbsp;</p>\r\n                                        <p class=\"color-gray fn14 mt-3 sub-left-desc\">ஆவணங்களின் முழு பக்கத்தை 3 வினாடிகளில் அடையாளம் காணவும்</p>\r\n                                        <p class=\"color-gray fn14 mt-3 sub-left-desc\">98%+ அங்கீகார துல்லியம்</p>\r\n                                        <p class=\"color-gray fn14 mt-3 sub-left-desc\">தாமதமின்றி பன்மொழி நிகழ்நேர செயலாக்கம்</p>\r\n                                        <a class=\"link block disa fn14 contact-business shake\" href=\"Download.aspx\">அனுபவத்தை இப்போது பதிவிறக்கவும்<span class=\"iconfont4 icon-xiangzuo gt\"></span></a>\r\n                                    </div>\r\n                                </div>\r\n                                <div class=\"sub-nav\">\r\n                                    <div class=\"sub-nav-item py-3 px-4\">\r\n                                        <h2 class=\"h5\" style=\"margin: inherit;\">பொருளின் பண்புகள்:</h2>\r\n                                        <p class=\"color-gray fn14 mt-3 sub-left-desc\">AI அறிவார்ந்த அடையாளம், ஒரு நிறுத்த தீர்வு</p>\r\n                                    </div>\r\n                                    <div class=\"sub-nav-item py-3 px-4\"><a href=\"Detail.aspx\">செயல்பாடு அறிமுகம்</a></div>\r\n                                    <div class=\"sub-nav-item py-3 px-4\"><a href=\"Download.aspx\">மென்பொருள் பதிவிறக்கம்</a></div>\r\n                                    <div class=\"sub-nav-item py-3 px-4\"><a href=\"Version.aspx\">பதிப்பு ஒப்பீடு</a></div>\r\n                                    <div class=\"sub-nav-item py-3 px-4\"><a href=\"Ocr.aspx\">ஆன்லைன் அனுபவம்</a></div>\r\n                                    <div class=\"sub-nav-item py-3 px-4\"><a href=\"Status.aspx\">கணினி நிலை</a></div>\r\n                                </div>\r\n                                <div class=\"sub-nav\">\r\n                                    <div class=\"sub-nav-item py-3 px-4\">\r\n                                        <h2 class=\"h5\" style=\"margin: inherit;\">ஆன்லைன் அனுபவம்</h2>\r\n                                        <p class=\"color-gray fn14 mt-3 sub-left-desc\">இலவச ஆன்லைன் OCR செயல்பாட்டு அனுபவம்</p>\r\n                                    </div>\r\n                                    <div class=\"sub-nav-item py-3 px-4\"><a href=\"Ocr.aspx\">முழு செயல்பாடு</a></div>\r\n                                    <div class=\"sub-nav-item py-3 px-4\"><a href=\"Ocr.aspx?type=text_recognize\">சொல் அறிதல்</a></div>\r\n                                    <div class=\"sub-nav-item py-3 px-4\"><a href=\"Ocr.aspx?type=table\">அட்டவணை அடையாளம்</a></div>\r\n                                    <div class=\"sub-nav-item py-3 px-4\"><a href=\"Ocr.aspx?type=pdf2word\">வார்த்தைக்கு PDF</a></div>\r\n                                </div>\r\n                                <div class=\"sub-nav\">\r\n                                    <div class=\"sub-nav-item py-3 px-4\">\r\n                                        <h2 class=\"h5\" style=\"margin: inherit;\">உறுப்பினர் மேம்பாடுகள்</h2>\r\n                                        <p class=\"color-gray fn14 mt-3 sub-left-desc\">அனைத்து அம்சங்களையும் திறந்து பிரத்யேக சேவைகளை அனுபவிக்கவும்</p>\r\n                                    </div>\r\n                                    <div class=\"sub-nav-item py-3 px-4\"><a href=\"Version.aspx\">உறுப்பினர் நன்மைகள்</a></div>\r\n                                    <div class=\"sub-nav-item py-3 px-4\"><a href=\"Upgrade.aspx\">உடனடியாக செயல்படுத்தவும்</a></div>\r\n                                </div>\r\n                                <div class=\"sub-nav\">\r\n                                    <div class=\"sub-nav-item py-3 px-4\">\r\n                                        <h2 class=\"h5\" style=\"margin: inherit;\">மென்பொருள் பதிவிறக்கம்</h2>\r\n                                        <p class=\"color-gray fn14 mt-3 sub-left-desc\">தொழில்முறை OCR மென்பொருளை இலவசமாகப் பதிவிறக்கவும்</p>\r\n                                    </div>\r\n                                    <div class=\"sub-nav-item py-3 px-4\"><a href=\"Download.aspx\">இப்போதே பதிவிறக்கு</a></div>\r\n                                    <div class=\"sub-nav-item py-3 px-4\"><a href=\"Version.aspx\">பதிப்பு ஒப்பீடு</a></div>\r\n                                </div>\r\n                                <div class=\"sub-nav\">\r\n                                    <div class=\"sub-nav-item py-3 px-4\">\r\n                                        <h2 class=\"h5\" style=\"margin: inherit;\">தொழில்நுட்ப பகிர்வு</h2>\r\n                                        <p class=\"color-gray fn14 mt-3 sub-left-desc\">OCR தொழில்நுட்ப கட்டுரைகள் மற்றும் அறிவு பகிர்வு</p>\r\n                                    </div>\r\n                                    <div class=\"sub-nav-item py-3 px-4\"><a href=\"Articles.aspx\">அனைத்து கட்டுரைகள்</a></div>\r\n                                    \r\n                                    <div class=\"sub-nav-item py-3 px-4\"><a href=\"Articles.aspx?category=2\">மேம்பட்ட வழிகாட்டி</a></div>\r\n                                    \r\n                                    <div class=\"sub-nav-item py-3 px-4\"><a href=\"Articles.aspx?category=3\">தொழில்நுட்ப ஆய்வு</a></div>\r\n                                    \r\n                                    <div class=\"sub-nav-item py-3 px-4\"><a href=\"Articles.aspx?category=4\">தொழில் போக்குகள்</a></div>\r\n                                    \r\n                                    <div class=\"sub-nav-item py-3 px-4\"><a href=\"Articles.aspx?category=5\">பயன்பாட்டு வழக்குகள்:</a></div>\r\n                                    \r\n                                    <div class=\"sub-nav-item py-3 px-4\"><a href=\"Articles.aspx?category=6\">கருவி மதிப்பாய்வு</a></div>\r\n                                    \r\n                                </div>\r\n                                <div class=\"sub-nav\">\r\n                                    <div class=\"sub-nav-item py-3 px-4\">\r\n                                        <h2 class=\"h5\" style=\"margin: inherit;\">உதவி மையம்</h2>\r\n                                        <p class=\"color-gray fn14 mt-3 sub-left-desc\">தொழில்முறை வாடிக்கையாளர் சேவை, நெருக்கமான சேவை</p>\r\n                                    </div>\r\n                                    <div class=\"sub-nav-item py-3 px-4\"><a href=\"FAQ.aspx\">உதவியைப் பயன்படுத்து</a></div>\r\n                                    <div class=\"sub-nav-item py-3 px-4\"><a href=\"AboutUs.aspx\">எங்களைப் பற்றி</a></div>\r\n                                    <div class=\"sub-nav-item py-3 px-4\"><a href=\"mailto:<EMAIL>\">வாடிக்கையாளர் சேவையைத் தொடர்பு கொள்ளவும்</a></div>\r\n                                    <div class=\"sub-nav-item py-3 px-4\"><a href=\"UserAgreement.aspx\">சேவை விதிமுறைகள்</a></div>\r\n                                </div>\r\n                            </div>\r\n                        </div>\r\n                    </div>\r\n                </div>\r\n            </div>\r\n        </header>\r\n        <form method=\"post\" action=\"./Article.aspx?id=81&amp;slug=rnn-sequence-modeling&amp;lang=zh-Hans&amp;_translation_source=true\" id=\"aspnetForm\">\r\n<div class=\"aspNetHidden\">\r\n<input type=\"hidden\" name=\"__VIEWSTATE\" id=\"__VIEWSTATE\" value=\"Q9uUeUlUJtaRG68B9ZsvqxMA0nWMrG97wrg6XH6pF/PALpTNvdWvXETFldXabtYfiX/3jguNbdKAEx4O02Rz9ETQIoNHodxI3/t/Hx/oGKM=\" />\r\n</div>\r\n\r\n<div class=\"aspNetHidden\">\r\n\r\n\t<input type=\"hidden\" name=\"__VIEWSTATEGENERATOR\" id=\"__VIEWSTATEGENERATOR\" value=\"2173C2F0\" />\r\n</div>\r\n            <div>\r\n                \n\n<div class=\"reading-progress\" id=\"readingProgress\"></div>\n<div class=\"article-detail-container\">\n<div class=\"container\">\n<div class=\"row\">\n<div class=\"col-lg-8 col-xl-9 col-md-12\">\n                    \n                    <article class=\"article-detail\" id=\"articleDetail\" data-article-id=\"81\">\n                        <header class=\"article-header\">\n                            <h1 class=\"article-title\">【ஆழமான கற்றல் OCR தொடர்·4】மீண்டும் மீண்டும் நரம்பியல் நெட்வொர்க்குகள் மற்றும் வரிசை மாடலிங்</h1>\n                            <div class=\"article-meta\">\n                                <div class=\"meta-item\">\n                                    <span class=\"meta-icon\">📅</span>\n                                    <span>இடுகை நேரம்: 2025-08-19</span>\n                                </div>\n                                <div class=\"meta-item\">\n                                    <span class=\"meta-icon\">👁️</span>\n                                    <span>வாசித்தல்:<span class=\"view-count\">1200</span></span>\n                                </div>\n                                <div class=\"meta-item\">\n                                    <span class=\"meta-icon\">⏱️</span>\n                                    <span>தோராயமாக 50 நிமிடங்கள் (9819 வார்த்தைகள்)</span>\n                                </div>\n                                \n                                <div class=\"meta-item\">\n                                    <span class=\"meta-icon\">📁</span>\n                                    <span>வகை: மேம்பட்ட வழிகாட்டிகள்</span>\n                                </div>\n                                \n                            </div>\n                            <div class=\"article-summary\"><p>OCR இல் RNN, LSTM, GRU இன் பயன்பாட்டில் முழுக்கு. வரிசை மாதிரியாக்கத்தின் கொள்கைகள், சாய்வு சிக்கல்களுக்கான தீர்வுகள் மற்றும் இருதிசை RNNகளின் நன்மைகள் பற்றிய விரிவான பகுப்பாய்வு.</p></div>\n                        </header>\n                        <div class=\"article-content\" id=\"articleContent\">## அறிமுகம்\r\n\r\nமீண்டும் மீண்டும் நரம்பியல் நெட்வொர்க் (ஆர்.என்.என்) என்பது ஆழ்ந்த கற்றலில் ஒரு நரம்பியல் நெட்வொர்க் கட்டமைப்பாகும், இது வரிசை தரவை செயலாக்குவதில் நிபுணத்துவம் பெற்றது. OCR பணிகளில், உரை அங்கீகாரம் என்பது அடிப்படையில் ஒரு வரிசை-க்கு-வரிசை மாற்ற சிக்கலாகும்: பட அம்சங்களின் வரிசையை உரை எழுத்து வரிசையாக மாற்றுகிறது. இந்த கட்டுரை RNN எவ்வாறு செயல்படுகிறது, அதன் முக்கிய வகைகள் மற்றும் OCR இல் அதன் குறிப்பிட்ட பயன்பாடுகளை ஆராயும், இது வாசகர்களுக்கு ஒரு விரிவான தத்துவார்த்த அடித்தளத்தையும் நடைமுறை வழிகாட்டுதலையும் வழங்குகிறது.\r\n\r\n## ஆர்.என்.என் அடிப்படைகள்\r\n\r\n### பாரம்பரிய நரம்பியல் நெட்வொர்க்குகளின் வரம்புகள்\r\n\r\nபாரம்பரிய ஃபீட்ஃபார்வர்டு நியூரல் நெட்வொர்க்குகள் வரிசை தரவை செயலாக்குவதில் அடிப்படை வரம்புகளைக் கொண்டுள்ளன. இந்த நெட்வொர்க்குகள் உள்ளீட்டு தரவு சுயாதீனமானது மற்றும் ஹோமோடிஸ்ட்ரிபியூட் என்று கருதுகின்றன, மேலும் வரிசையில் உள்ள கூறுகளுக்கு இடையிலான தற்காலிக சார்புகளைப் பிடிக்க முடியாது.\r\n\r\n**ஃபீட்ஃபார்வர்டு நெட்வொர்க் சிக்கல்கள்**:\r\n- நிலையான உள்ளீடு மற்றும் வெளியீட்டு நீளம்: மாறி நீள வரிசைகளை கையாள முடியாது\r\n- நினைவக திறன் இல்லாமை: வரலாற்று தகவல்களைப் பயன்படுத்த இயலாமை\r\n- அளவுரு பகிர்வில் சிரமம்: ஒரே மாதிரியை வெவ்வேறு இடங்களில் மீண்டும் மீண்டும் கற்றுக்கொள்ள வேண்டும்\r\n- நிலை உணர்திறன்: உள்ளீடுகளின் வரிசையை மாற்றுவது முற்றிலும் மாறுபட்ட வெளியீடுகளுக்கு வழிவகுக்கும்\r\n\r\nOCR பணிகளில் இந்த வரம்புகள் குறிப்பாக கவனிக்கத்தக்கவை. உரை வரிசைகள் மிகவும் சூழல் சார்ந்தவை, மேலும் முந்தைய கதாபாத்திரத்தின் அங்கீகார முடிவுகள் பெரும்பாலும் அடுத்தடுத்த எழுத்துக்களின் சாத்தியத்தை தீர்மானிக்க உதவுகின்றன. எடுத்துக்காட்டாக, ஆங்கில வார்த்தையான \"the\" ஐ அடையாளம் காணும்போது, \"th\" ஏற்கனவே அங்கீகரிக்கப்பட்டிருந்தால், அடுத்த எழுத்து \"e\" ஆக இருக்க வாய்ப்புள்ளது.\r\n\r\n### ஆர்.என்.என் இன் மையக் கருத்து\r\n\r\nRNN மடக்கு இணைப்புகளை அறிமுகப்படுத்துவதன் மூலம் வரிசை மாதிரியாக்கத்தின் சிக்கலைத் தீர்க்கிறது. நெட்வொர்க்கில் ஒரு \"நினைவகம்\" பொறிமுறையைச் சேர்ப்பதே முக்கிய யோசனை, இதனால் நெட்வொர்க் முந்தைய தருணங்களிலிருந்து தகவல்களை சேமித்து பயன்படுத்த முடியும்.\r\n\r\n**RNN இன் கணித பிரதிநிதித்துவம்**:\r\nt தருணத்தில், RNN இன் மறைக்கப்பட்ட h_t நிலை தற்போதைய உள்ளீடு x_t மற்றும் முந்தைய கணத்தின் மறைக்கப்பட்ட நிலை h_{t-1} ஆகியவற்றால் தீர்மானிக்கப்படுகிறது:\r\n\r\nh_t = f(W_hh * h_{t-1} + W_xh * x_t + b_h)\r\n\r\nஅதில்:\r\n- W_hh என்பது மறைக்கப்பட்ட நிலையிலிருந்து மறைக்கப்பட்ட நிலைக்கு எடை அணி\r\n- W_xh எடை அணி மறைக்கப்பட்ட நிலையில் உள்ளிடப்பட்டுள்ளது  \r\n- b_h ஒரு சார்பு திசையன்\r\n- f என்பது செயல்படுத்தும் செயல்பாடு (பொதுவாக tanh அல்லது ReLU)\r\n\r\nவெளியீட்டு y_t தற்போதைய மறைக்கப்பட்ட நிலையில் இருந்து கணக்கிடப்படுகிறது:\r\ny_t = W_hy * h_t + b_y\r\n\r\n**RNN களின் நன்மைகள் **:\r\n- அளவுரு பகிர்வு: ஒரே எடைகள் எல்லா நேரங்களிலும் பகிரப்படுகின்றன\r\n- மாறி நீள வரிசை செயலாக்கம்: தன்னிச்சையான நீளத்தின் உள்ளீட்டு வரிசைகளைக் கையாள முடியும்\r\n- நினைவக திறன்: மறைக்கப்பட்ட நிலைகள் பிணையத்தின் \"நினைவுகளாக\" செயல்படுகின்றன\r\n- நெகிழ்வான உள்ளீடு மற்றும் வெளியீடு: ஒன்றிலிருந்து ஒன்று, ஒன்றிலிருந்து பல, பலவற்றிலிருந்து ஒன்று, பலவற்றிலிருந்து பல முறைகள் மற்றும் பலவற்றை ஆதரிக்கிறது\r\n\r\n### ஆர்.என்.என் விரிவாக்கப்பட்ட பார்வை\r\n\r\nஆர்.என்.என் கள் எவ்வாறு செயல்படுகின்றன என்பதை நன்கு புரிந்து கொள்ள, அவற்றை தற்காலிக பரிமாணத்தில் விரிவாக்கலாம். விரிவாக்கப்பட்ட ஆர்.என்.என் ஒரு ஆழமான பின்னூட்ட நெட்வொர்க் போல் தெரிகிறது, ஆனால் எல்லா நேர படிகளும் ஒரே அளவுருக்களைப் பகிர்ந்து கொள்கின்றன.\r\n\r\n**காலம் விரிவதின் முக்கியத்துவம்**:\r\n- தகவல் ஓட்டத்தைப் புரிந்துகொள்வது எளிது: நேர படிகளுக்கு இடையில் தகவல் எவ்வாறு அனுப்பப்படுகிறது என்பதை தெளிவாகக் காண முடியும்\r\n- சாய்வு கணக்கீடு: சாய்வுகள் டைம் பேக்ப்ரோபகேஷன் (BPTT) அல்காரிதம் மூலம் கணக்கிடப்படுகின்றன\r\n- இணை பரிசீலனைகள்: ஆர்.என்.என் கள் இயல்பாகவே தொடர்ச்சியாக இருக்கும்போது, சில செயல்பாடுகளை இணையாக செய்யலாம்\r\n\r\n**விரிவடைதல் செயல்முறையின் கணித விளக்கம்**:\r\nநீளம் T வரிசைகளுக்கு, RNN பின்வருமாறு விரிவடைகிறது:\r\nh_1 = f(W_xh * x_1 + b_h)\r\nh_2 = f(W_hh * h_1 + W_xh * x_2 + b_h)\r\nh_3 = f(W_hh * h_2 + W_xh * x_3 + b_h)\r\n...\r\nh_T = f(W_hh * h_{T-1} + W_xh * x_T + b_h)\r\n\r\nஇந்த மடிக்கப்பட்ட படிவம் நேர படிகளுக்கு இடையில் தகவல் எவ்வாறு அனுப்பப்படுகிறது மற்றும் எல்லா நேர படிகளிலும் அளவுருக்கள் எவ்வாறு பகிரப்படுகின்றன என்பதை தெளிவாகக் காட்டுகிறது.\r\n\r\n## சாய்வு மறைதல் மற்றும் வெடிப்பு சிக்கல்\r\n\r\n### பிரச்சினையின் ஆணிவேர்\r\n\r\nRNN களைப் பயிற்றுவிக்கும் போது, Backpropagation Through Time (BPTT) அல்காரிதத்தைப் பயன்படுத்துகிறோம். அல்காரிதம் ஒவ்வொரு டைம்ஸ்டெப் அளவுருவுக்கும் இழப்பு செயல்பாட்டின் சாய்வை கணக்கிட வேண்டும்.\r\n\r\n**சாய்வு கணக்கீட்டிற்கான சங்கிலி விதி**:\r\nவரிசை நீளமாக இருக்கும்போது, சாய்வு பல நேர படிகள் மூலம் பின்னோக்கி செல்ல வேண்டும். சங்கிலி விதியின் படி, ஒரு சாய்வு எடை மேட்ரிக்ஸின் பல பெருக்கல்களைக் கொண்டிருக்கும்:\r\n\r\n∂L/∂W = Σ_t (∂L/∂y_t) * (∂y_t/∂h_t) * (∂h_t/∂W)\r\n\r\nஇங்கு ∂h_t/∂W என்பது கணம் t முதல் கணம் 1 வரையிலான அனைத்து இடைநிலை நிலைகளின் பெருக்கலை உள்ளடக்கியது.\r\n\r\n**சாய்வு மறைவு பற்றிய கணித பகுப்பாய்வு**:\r\nநேர படிகளுக்கு இடையில் சாய்வுகளின் பரவலைக் கவனியுங்கள்:\r\n∂h_t/∂h_{t-1} = diag(f_prime(W_hh * h_{t-1} + W_xh * x_t + b_h)) * W_hh\r\n\r\nவரிசை நீளம் T ஆக இருக்கும்போது, சாய்வு T-1 போன்ற தயாரிப்பு காலத்தைக் கொண்டுள்ளது. W_hh இன் பெரும ஐஜன் மதிப்பு 1 க்கும் குறைவாக இருந்தால், தொடர்ச்சியான அணி பெருக்கல் சாய்வு அடுக்கு சிதைவை ஏற்படுத்தும்.\r\n\r\n**சாய்வு வெடிப்புகளின் கணித பகுப்பாய்வு**:\r\nமாறாக, W_hh இன் அதிகபட்ச ஐஜென்மதிப்பு 1 ஐ விட அதிகமாக இருக்கும்போது, சாய்வு அதிவேகமாக அதிகரிக்கிறது:\r\n|| ∂h_t/∂h_1|| ≈ || W_hh|| ^{டி-1}\r\n\r\nஇது நிலையற்ற பயிற்சி மற்றும் அதிகப்படியான அளவுரு புதுப்பிப்புகளுக்கு வழிவகுக்கிறது.\r\n\r\n### தீர்வு பற்றிய விரிவான விளக்கம்\r\n\r\nசாய்வு கிளிப்பிங்:\r\nசாய்வு கிளிப்பிங் என்பது சாய்வு வெடிப்புகளைத் தீர்க்க மிகவும் நேரடியான வழியாகும். சாய்வு விதிமுறை ஒரு நிர்ணயிக்கப்பட்ட வரம்பை மீறும்போது, சாய்வு வாசல் அளவிற்கு அளவிடப்படுகிறது. இந்த முறை எளிமையானது மற்றும் பயனுள்ளது, ஆனால் வரம்புகளை கவனமாக தேர்வு செய்ய வேண்டும். மிகச் சிறியதாக இருக்கும் ஒரு வாசல் கற்றல் திறனைக் கட்டுப்படுத்தும், மேலும் மிகப் பெரியதாக இருக்கும் ஒரு வாசல் சாய்வு வெடிப்பை திறம்பட தடுக்காது.\r\n\r\n**எடை துவக்க உத்தி**:\r\nசரியான எடை துவக்கம் சாய்வு சிக்கல்களைத் தணிக்கும்:\r\n- சேவியர் துவக்கம்: எடை மாறுபாடு 1/n ஆகும், அங்கு n என்பது உள்ளீட்டு பரிமாணமாகும்\r\n- அவர் துவக்கம்: எடை மாறுபாடு 2/n ஆகும், இது ReLU செயல்படுத்தும் செயல்பாடுகளுக்கு ஏற்றது\r\n- ஆர்த்தோகனல் துவக்கம்: எடை அணியை ஒரு ஆர்த்தோகனல் மேட்ரிக்ஸாக துவக்குகிறது\r\n\r\n**செயல்படுத்தும் செயல்பாடுகளின் தேர்வு**:\r\nவெவ்வேறு செயல்படுத்தும் செயல்பாடுகள் சாய்வு பரவலில் வெவ்வேறு விளைவுகளைக் கொண்டுள்ளன:\r\n- TANH: வெளியீட்டு வரம்பு [-1,1], சாய்வு அதிகபட்ச மதிப்பு 1\r\n- ReLU: சாய்வு மறைவைத் தணிக்க முடியும், ஆனால் நரம்பியல் மரணத்தை ஏற்படுத்தக்கூடும்\r\n- கசிவு ReLU: ReLU இன் நரம்பியல் இறப்பு சிக்கலை தீர்க்கிறது\r\n\r\n**கட்டிடக்கலை மேம்பாடுகள்**:\r\nமிக அடிப்படையான தீர்வு RNN கட்டமைப்பை மேம்படுத்துவதாகும், இது LSTM மற்றும் GRU ஆகியவற்றின் தோற்றத்திற்கு வழிவகுத்தது. இந்த கட்டமைப்புகள் கேட்டிங் வழிமுறைகள் மற்றும் சிறப்பு தகவல் ஓட்ட வடிவமைப்புகள் மூலம் சாய்வுகளை நிவர்த்தி செய்கின்றன.\r\n\r\n## LSTM: நீண்ட குறுகிய கால நினைவக நெட்வொர்க்\r\n\r\n### LSTM க்கான வடிவமைப்பு உந்துதல்\r\n\r\nLSTM (நீண்ட குறுகிய கால நினைவகம்) என்பது 1997 இல் Hochreiter மற்றும் Schmidhuber ஆகியோரால் முன்மொழியப்பட்ட RNN மாறுபாடாகும், இது குறிப்பாக சாய்வு மறைந்து போகும் மற்றும் நீண்ட தூர சார்பு கற்றல் சிரமங்களின் சிக்கலை தீர்க்க வடிவமைக்கப்பட்டுள்ளது.\r\n\r\n**LSTM இன் முக்கிய கண்டுபிடிப்புகள் **:\r\n- செல் நிலை: தகவலுக்கான \"நெடுஞ்சாலை\" ஆக செயல்படுகிறது, இது நேர படிகளுக்கு இடையில் நேரடியாக தகவல்களை பாய அனுமதிக்கிறது\r\n- கேட்டிங் மெக்கானிசம்: தகவலின் வருகை, தக்கவைத்தல் மற்றும் வெளியீடு ஆகியவற்றின் மீது துல்லியமான கட்டுப்பாடு\r\n- விலகிய நினைவக வழிமுறைகள்: குறுகிய கால நினைவகம் (மறைக்கப்பட்ட நிலை) மற்றும் நீண்ட கால நினைவகம் (செல்லுலார் நிலை) ஆகியவற்றை வேறுபடுத்துகின்றன\r\n\r\n**LSTM சாய்வு சிக்கல்களை எவ்வாறு தீர்க்கிறது **:\r\nஎல்.எஸ்.டி.எம் பெருக்கல் செயல்பாடுகளை விட சேர்க்கை மூலம் செல் நிலையை புதுப்பிக்கிறது, இது சாய்வுகளை முந்தைய நேர படிகளுக்கு எளிதாக பாய அனுமதிக்கிறது. செல் நிலைக்கான புதுப்பிக்கப்பட்ட சூத்திரம்:\r\n\r\nC_t = f_t ⊙ C_{t-1} + i_t ⊙ C_tilde_t\r\n\r\nபாரம்பரிய RNN களில் தொடர்ச்சியான அணி பெருக்கத்தைத் தவிர்த்து, உறுப்பு-நிலை கூட்டல் இங்கே பயன்படுத்தப்படுகிறது.\r\n\r\n### LSTM கட்டிடக்கலை பற்றிய விரிவான விளக்கம்\r\n\r\nLSTM மூன்று கேட்டிங் அலகுகள் மற்றும் ஒரு செல் நிலையைக் கொண்டுள்ளது:\r\n\r\n**1. வாயிலை மறந்துவிடுங்கள்**:\r\nசெல் நிலையிலிருந்து எந்த தகவலை நிராகரிக்க வேண்டும் என்பதை மறதியின் வாயில் தீர்மானிக்கிறது:\r\nf_t = σ(W_f · [h_{t-1}, x_t] + b_f)\r\n\r\nமறதி வாயிலின் வெளியீடு 0 மற்றும் 1 க்கு இடையிலான மதிப்பாகும், 0 \"முற்றிலும் மறக்கப்படுகிறது\" மற்றும் 1 \"முழுமையாக தக்கவைக்கப்படுகிறது\". இந்த வாயில் எல்.எஸ்.டி.எம் முக்கியமற்ற வரலாற்றுத் தகவல்களைத் தேர்ந்தெடுத்து மறக்க அனுமதிக்கிறது.\r\n\r\n**2. உள்ளீட்டு வாயில்**:\r\nசெல் நிலையில் என்ன புதிய தகவல்கள் சேமிக்கப்படுகின்றன என்பதை உள்ளீட்டு வாயில் தீர்மானிக்கிறது:\r\ni_t = σ(W_i · [h_{t-1}, x_t] + b_i)\r\nC_tilde_t = tanh(W_C · [h_{t-1}, x_t] + b_C)\r\n\r\nஉள்ளீட்டு வாயில் இரண்டு பகுதிகளைக் கொண்டுள்ளது: சிக்மாய்டு அடுக்கு எந்த மதிப்புகளை புதுப்பிக்க வேண்டும் என்பதை தீர்மானிக்கிறது, மேலும் tanh அடுக்கு வேட்பாளர் மதிப்பு திசையன்களை உருவாக்குகிறது.\r\n\r\n**3. செல் நிலை புதுப்பிப்பு **:\r\nசெல் நிலையைப் புதுப்பிக்க மறதி வாயில் மற்றும் உள்ளீட்டு வாயிலின் வெளியீடுகளை இணைக்கவும்:\r\nC_t = f_t ⊙ C_{t-1} + i_t ⊙ C_tilde_t\r\n\r\nஇந்த சூத்திரம் LSTM இன் இதயத்தில் உள்ளது: உறுப்பு-நிலை பெருக்கல் மற்றும் கூட்டல் செயல்பாடுகள் மூலம் தகவல்களைத் தேர்ந்தெடுத்தல் மற்றும் புதுப்பித்தல்.\r\n\r\n**4. வெளியீடு வாயில்**:\r\nமின்கலத்தின் எந்தெந்த பகுதிகள் வெளியீடு என்பதை வெளியீட்டு வாயில் தீர்மானிக்கிறது:\r\no_t = σ(W_o · [h_{t-1}, x_t] + b_o)\r\nh_t = o_t ⊙ tanh(C_t)\r\n\r\nமின்கலத்தின் நிலையின் எந்தப் பகுதிகள் மின்னோட்ட வெளியீட்டைப் பாதிக்கின்றன என்பதை வெளியீட்டு வாயில் கட்டுப்படுத்துகிறது.\r\n\r\n### LSTM வகைகள்\r\n\r\n**பீப்ஹோல் LSTM **:\r\nநிலையான LSTM ஐ உருவாக்கி, Peephole LSTM கேட்டிங் யூனிட்டை செல் நிலையைக் காண அனுமதிக்கிறது:\r\nf_t = σ(W_f · [C_{t-1}, h_{t-1}, x_t] + b_f)\r\ni_t = σ(W_i · [C_{t-1}, h_{t-1}, x_t] + b_i)\r\no_t = σ(W_o · [C_t, h_{t-1}, x_t] + b_o)\r\n\r\n**இணைக்கப்பட்ட LSTM **:\r\nமறந்துபோன வாயிலை உள்ளீட்டு வாயிலுடன் இணைக்கவும், மறந்துபோன தகவலின் அளவு உள்ளிடப்பட்ட தகவலின் அளவிற்கு சமமாக இருப்பதை உறுதிசெய்க:\r\nf_t = σ(W_f · [h_{t-1}, x_t] + b_f)\r\ni_t = 1 - f_t\r\n\r\nஇந்த வடிவமைப்பு LSTM இன் முக்கிய செயல்பாட்டை பராமரிக்கும் போது அளவுருக்களின் எண்ணிக்கையை குறைக்கிறது.\r\n\r\n## GRU: கேட்டட் லூப் யூனிட்\r\n\r\n### GRU இன் எளிமைப்படுத்தப்பட்ட வடிவமைப்பு\r\n\r\nGRU (Gated Recurrent Unit) என்பது LSTM இன் எளிமைப்படுத்தப்பட்ட பதிப்பாகும். GRU LSTM இன் மூன்று வாயில்களை இரண்டு வாயில்களுக்கு எளிதாக்குகிறது மற்றும் செல்லுலார் நிலை மற்றும் மறைக்கப்பட்ட நிலையை ஒன்றிணைக்கிறது.\r\n\r\n**GRU இன் வடிவமைப்பு தத்துவம்**:\r\n- எளிமைப்படுத்தப்பட்ட அமைப்பு: கதவுகளின் எண்ணிக்கையைக் குறைக்கிறது மற்றும் கணக்கீடுகளின் சிக்கலைக் குறைக்கிறது\r\n- செயல்திறனை பராமரிக்கவும்: LSTM- ஒப்பிடக்கூடிய செயல்திறனை பராமரிக்கும் போது எளிதாக்கவும்\r\n- செயல்படுத்த எளிதானது: எளிமையான கட்டுமானம் எளிதாக செயல்படுத்தவும் ஆணையிடவும் அனுமதிக்கிறது\r\n\r\n### GRU இன் கேட்டிங் மெக்கானிசம்\r\n\r\n**1. வாயிலை மீட்டமை **:\r\nr_t = σ(W_r · [h_{t-1}, x_t] + b_r)\r\n\r\nபுதிய உள்ளீட்டை முந்தைய நினைவகத்துடன் எவ்வாறு இணைப்பது என்பதை மீட்டமை வாயில் தீர்மானிக்கிறது. மீட்டமைப்பு வாயில் 0 ஐ நெருங்கும்போது, மாதிரி முந்தைய மறைக்கப்பட்ட நிலையை புறக்கணிக்கிறது.\r\n\r\n**2. புதுப்பிப்பு வாயில்**:\r\nz_t = σ(W_z · [h_{t-1}, x_t] + b_z)\r\n\r\nபுதுப்பிப்பு வாயில் எவ்வளவு கடந்தகால தகவல்களை வைத்திருக்க வேண்டும் மற்றும் எவ்வளவு புதிய தகவல்களைச் சேர்க்க வேண்டும் என்பதை தீர்மானிக்கிறது. இது மறதி மற்றும் உள்ளீடு இரண்டையும் கட்டுப்படுத்துகிறது, இது LSTM இல் மறதி மற்றும் உள்ளீட்டு வாயில்களின் கலவையைப் போன்றது.\r\n\r\n**3. வேட்பாளர் மறைக்கப்பட்ட நிலை**:\r\nh_tilde_t = tanh(W_h · [r_t ⊙ h_{t-1}, x_t] + b_h)\r\n\r\nவேட்பாளர் மறைக்கப்பட்ட மாநிலங்கள் முந்தைய மறைக்கப்பட்ட நிலையின் விளைவுகளைக் கட்டுப்படுத்த மீட்டமைப்பு வாயிலைப் பயன்படுத்துகின்றன.\r\n\r\n**4. இறுதி மறைக்கப்பட்ட நிலை**:\r\nh_t = (1 - z_t) ⊙ h_{t-1} + z_t ⊙ h_tilde_t\r\n\r\nஇறுதி மறைக்கப்பட்ட நிலை என்பது முந்தைய மறைக்கப்பட்ட நிலை மற்றும் வேட்பாளர் மறைக்கப்பட்ட நிலை ஆகியவற்றின் எடையிடப்பட்ட சராசரியாகும்.\r\n\r\n### GRU vs LSTM ஆழமான ஒப்பீடு\r\n\r\n**அளவுருக்களின் எண்ணிக்கையின் ஒப்பீடு**:\r\n- LSTM: 4 எடை அணிகள் (கேட், இன்புட் கேட், கேண்டிடேட் மதிப்பு, வெளியீட்டு வாயில் மறந்துவிடுதல்)\r\n- GRU: 3 எடை மெட்ரிக்குகள் (மீட்டமை வாயில், புதுப்பிப்பு வாயில், வேட்பாளர் மதிப்பு)\r\n- GRU இன் அளவுருக்களின் எண்ணிக்கை LSTM இல் தோராயமாக 75% ஆகும்\r\n\r\n**கணக்கீட்டு சிக்கலான ஒப்பீடு **:\r\n- LSTM: 4 கேட் வெளியீடுகள் மற்றும் செல் நிலை புதுப்பிப்புகளின் கணக்கீடு தேவைப்படுகிறது\r\n- GRU: 2 வாயில்கள் மற்றும் மறைக்கப்பட்ட நிலை புதுப்பிப்புகளின் வெளியீட்டைக் கணக்கிடுங்கள்\r\n- GRU பொதுவாக LSTM ஐ விட 20-30% வேகமாக இருக்கும்\r\n\r\n**செயல்திறன் ஒப்பீடு**:\r\n- பெரும்பாலான பணிகளில், GRU மற்றும் LSTM ஒப்பிடத்தக்க வகையில் செயல்படுகின்றன\r\n- சில நீண்ட வரிசை பணிகளில் LSTM GRU ஐ விட சற்று சிறப்பாக இருக்கலாம்\r\n- கணினி வளங்கள் குறைவாக இருக்கும் சந்தர்ப்பங்களில் GRU ஒரு சிறந்த தேர்வாகும்\r\n\r\n## இருதிசை ஆர்.என்.என்.கள்\r\n\r\n### இருவழி செயலாக்கத்தின் அவசியம்\r\n\r\nபல வரிசை மாடலிங் பணிகளில், தற்போதைய தருணத்தின் வெளியீடு கடந்த காலத்தை மட்டுமல்ல, எதிர்கால தகவல்களையும் நம்பியுள்ளது. OCR பணிகளில் இது மிகவும் முக்கியமானது, அங்கு எழுத்து அங்கீகாரம் பெரும்பாலும் முழு சொல் அல்லது வாக்கியத்தின் சூழலைக் கருத்தில் கொள்ள வேண்டும்.\r\n\r\n**ஒரு வழி RNNகளின் வரம்புகள்**:\r\n- வரலாற்றுத் தகவல்களை மட்டுமே பயன்படுத்த முடியும், எதிர்கால சூழலைப் பெற முடியாது\r\n- சில பணிகளில் வரையறுக்கப்பட்ட செயல்திறன், குறிப்பாக உலகளாவிய தகவல் தேவைப்படுபவை\r\n- தெளிவற்ற எழுத்துக்களின் வரையறுக்கப்பட்ட அங்கீகாரம்\r\n\r\n**இருதிசை செயலாக்கத்தின் நன்மைகள்**:\r\n- முழுமையான சூழ்நிலை தகவல்: கடந்த கால மற்றும் எதிர்கால தகவல்களைப் பயன்படுத்துங்கள்\r\n- சிறந்த தெளிவுபடுத்தல்: சூழல் தகவலுடன் தெளிவுபடுத்துதல்\r\n- மேம்படுத்தப்பட்ட அங்கீகார துல்லியம்: பெரும்பாலான வரிசை சிறுகுறிப்பு பணிகளில் சிறப்பாக செயல்படுகிறது\r\n\r\n### இருதிசை LSTM கட்டமைப்பு\r\n\r\nஇருதிசை LSTM இரண்டு LSTM அடுக்குகளைக் கொண்டுள்ளது:\r\n- முன்னோக்கி LSTM: இடமிருந்து வலமாக செயல்முறை காட்சிகள்\r\n- பின்தங்கிய LSTM: வலமிருந்து இடமாக செயல்முறை காட்சிகள்\r\n\r\n**கணித பிரதிநிதித்துவம்**:\r\nh_forward_t = LSTM_forward(x_t, h_forward_{t-1})\r\nh_backward_t = LSTM_backward(x_t, h_backward_{t+1})\r\nh_t = [h_forward_t; h_backward_t] # முன்னும் பின்னுமாக மறைக்கப்பட்ட நிலைகளைத் தைத்தல்\r\n\r\n**பயிற்சி செயல்முறை**:\r\n1. முன்னோக்கி டுளுகூஎம் வரிசைகளை சாதாரண வரிசையில் செயல்படுத்துகிறது\r\n2. பின்தங்கிய டுளுகூஆ வரிசையை தலைகீழ் வரிசையில் செயல்படுத்துகிறது\r\n3. ஒவ்வொரு நேர படியில், இரு திசைகளிலும் மறைக்கப்பட்ட நிலைகளை இணைக்கவும்\r\n4. கணிக்க பிளவுபட்ட நிலையைப் பயன்படுத்தவும்\r\n\r\n**நன்மைகள் மற்றும் தீமைகள் **:\r\nநன்மை:\r\n- முழு சூழல் தகவல்\r\n- சிறந்த செயல்திறன்\r\n- சமச்சீர் சிகிச்சை\r\n\r\nதாழ்ந்த நிலை:\r\n- கணக்கீடுகளின் சிக்கலை இரட்டிப்பாக்கவும்\r\n- நிகழ்நேரத்தில் செயலாக்க முடியாது (முழு வரிசை தேவை)\r\n- அதிகரித்த நினைவக தேவைகள்\r\n\r\n## OCR இல் வரிசை மாடலிங் பயன்பாடுகள்\r\n\r\n### உரை வரி அங்கீகாரம் பற்றிய விரிவான விளக்கம்\r\n\r\nOCR அமைப்புகளில், உரை வரி அங்கீகாரம் என்பது வரிசை மாதிரியாக்கத்தின் பொதுவான பயன்பாடாகும். இந்த செயல்முறையில் பட அம்சங்களின் வரிசையை எழுத்துக்களின் வரிசையாக மாற்றுவது அடங்கும்.\r\n\r\n**சிக்கல் மாடலிங்**:\r\n- உள்ளீடு: பட அம்சம் வரிசை X = {x_1, x_2, ..., x_T}\r\n- வெளியீடு: எழுத்து வரிசை Y = {y_1, y_2, ..., y_S}\r\n- சவால்: உள்ளீட்டு வரிசை நீளம் T மற்றும் வெளியீட்டு வரிசை நீளம் S பெரும்பாலும் சமமாக இருக்காது\r\n\r\n**உரை வரி அங்கீகாரத்தில் CRNN கட்டமைப்பின் பயன்பாடு**:\r\nCRNN (Convolutional Recurrent Neural Network) என்பது OCR இல் மிகவும் வெற்றிகரமான கட்டமைப்புகளில் ஒன்றாகும்:\r\n\r\n1. **CNN அம்சம் பிரித்தெடுத்தல் அடுக்கு **:\r\n   - convolutional நரம்பியல் நெட்வொர்க்குகள் பயன்படுத்தி படத்தை அம்சங்கள் பிரித்தெடுக்க\r\n   - 2D பட அம்சங்களை 1D அம்ச காட்சிகளாக மாற்றவும்\r\n   - நேரத் தகவலின் தொடர்ச்சியை பராமரிக்கவும்\r\n\r\n2. **RNN வரிசை மாடலிங் அடுக்கு**:\r\n   - இருதிசை LSTMகளைப் பயன்படுத்தி மாதிரி அம்ச காட்சிகள்\r\n   - எழுத்துக்கள் இடையே சூழ்நிலை சார்புகள் கைப்பற்ற\r\n   - ஒவ்வொரு நேர படிநிலைக்கும் வெளியீட்டு எழுத்து நிகழ்தகவு விநியோகம்\r\n\r\n3. **CTC சீரமைப்பு அடுக்கு**:\r\n   - முகவரிகள் உள்ளீடு / வெளியீடு வரிசை நீளம் பொருந்தவில்லை\r\n   - எழுத்து-நிலை சீரமைப்பு பரிமாணங்கள் தேவையில்லை\r\n   - இறுதி முதல் இறுதி பயிற்சி\r\n\r\n**அம்சம் பிரித்தெடுத்தலை வரிசைக்கு மாற்றுதல்**:\r\nசி.என்.என் பிரித்தெடுத்த அம்ச வரைபடம் ஆர்.என்.என் செயலாக்கக்கூடிய வரிசை வடிவமாக மாற்றப்பட வேண்டும்:\r\n- அம்ச வரைபடத்தை நெடுவரிசைகளாகப் பிரிக்கவும், ஒவ்வொரு நெடுவரிசையும் நேர படியாகும்\r\n- இடஞ்சார்ந்த தகவல்களின் காலவரிசையை பராமரிக்கவும்\r\n- அம்ச வரிசையின் நீளம் படத்தின் அகலத்திற்கு விகிதாசாரமாக இருப்பதை உறுதிசெய்யவும்\r\n\r\n### OCR இல் கவனம் பொறிமுறையின் பயன்பாடு\r\n\r\nநீண்ட வரிசைகளைக் கையாளும் போது பாரம்பரிய ஆர்.என்.என் கள் இன்னும் தகவல் தடைகளைக் கொண்டுள்ளன. கவனம் வழிமுறைகளின் அறிமுகம் வரிசை மாதிரியாக்கத்தின் திறன்களை மேலும் மேம்படுத்துகிறது.\r\n\r\n**கவன வழிமுறைகளின் கோட்பாடுகள்**:\r\nஒவ்வொரு வெளியீட்டையும் உருவாக்கும் போது உள்ளீட்டு வரிசையின் வெவ்வேறு பகுதிகளில் கவனம் செலுத்த மாதிரியை கவனம் செலுத்த வழிமுறை அனுமதிக்கிறது:\r\n- நிலையான நீள குறியிடப்பட்ட திசையன்கள் தகவல் தடை தீர்க்கப்பட்டது\r\n- மாதிரி முடிவுகளின் விளக்கத்தை வழங்குகிறது\r\n- நீண்ட காட்சிகளின் மேம்படுத்தப்பட்ட செயலாக்கம்\r\n\r\n**OCR இல் குறிப்பிட்ட பயன்பாடுகள் **:\r\n\r\n1. **கேரக்டர்-லெவல் கவனம்**:\r\n   - ஒவ்வொரு பாத்திரத்தையும் அடையாளம் காணும்போது தொடர்புடைய படப் பகுதிகளில் கவனம் செலுத்துங்கள்\r\n   - பறக்கும்போது கவனத்தை எடைகளை சரிசெய்யவும்\r\n   - சிக்கலான பின்னணிக்கு வலிமையை மேம்படுத்தவும்\r\n\r\n2. **வார்த்தை நிலை கவனம்**:\r\n   - சொல்லகராதி மட்டத்தில் சூழ்நிலை தகவலைக் கவனியுங்கள்\r\n   - மொழி மாதிரி அறிவைப் பயன்படுத்துங்கள்\r\n   - முழு சொல் அங்கீகாரத்தின் துல்லியத்தை மேம்படுத்தவும்\r\n\r\n3. **பல அளவிலான கவனம்**:\r\n   - வெவ்வேறு தீர்மானங்களில் கவனம் வழிமுறைகளைப் பயன்படுத்துதல்\r\n   - வெவ்வேறு அளவுகளில் உரை கையாள\r\n   - மாற்றங்களை அளவிடுவதற்கான தகவமைப்பு மேம்படுத்தவும்\r\n\r\n**கவன பொறிமுறையின் கணித பிரதிநிதித்துவம்**:\r\nகுறியாக்கி வெளியீட்டு வரிசைக்கு H = {h_1, h_2, ..., h_T} மற்றும் டிகோடர் நிலை s_t:\r\n\r\ne_{t,i} = a(s_t, h_i) # கவனம் மதிப்பெண்\r\nα_{t,i} = softmax(e_{t,i}) # கவனம் எடை\r\nc_t = Σ_i α_{t,i} * h_i # சூழல் திசையன்\r\n\r\n## பயிற்சி உத்திகள் மற்றும் உகப்பாக்கம்\r\n\r\n### வரிசைக்கு வரிசை பயிற்சி உத்தி\r\n\r\n**ஆசிரியர் வற்புறுத்தல்**:\r\nபயிற்சி கட்டத்தின் போது, உண்மையான இலக்கு வரிசையை குறிவிலக்கியின் உள்ளீடாகப் பயன்படுத்தவும்:\r\n- நன்மை: வேகமான பயிற்சி வேகம், நிலையான ஒருங்கிணைப்பு\r\n- பாதகம்: சீரற்ற பயிற்சி மற்றும் அனுமான கட்டங்கள், பிழைகள் குவிவதற்கு வழிவகுக்கிறது\r\n\r\n**திட்டமிடப்பட்ட மாதிரி**:\r\nபயிற்சியின் போது ஆசிரியரின் சொந்த கணிப்புகளைப் பயன்படுத்துவதற்கு ஆசிரியர் கட்டாயத்திலிருந்து படிப்படியாக மாறுங்கள்:\r\n- ஆரம்ப கட்டத்தில் உண்மையான லேபிள்களையும், பிற்கால கட்டங்களில் மாதிரி கணிப்புகளையும் பயன்படுத்தவும்\r\n- பயிற்சி மற்றும் பகுத்தறிவில் உள்ள வேறுபாடுகளைக் குறைத்தல்\r\n- மாதிரி வலிமையை மேம்படுத்தவும்\r\n\r\n**பாடத்திட்ட கற்றல்**:\r\nஎளிய மாதிரிகளுடன் தொடங்கி படிப்படியாக மாதிரிகளின் சிக்கலை அதிகரிக்கும்:\r\n- குறுகிய முதல் நீண்ட வரிசைகள்: முதலில் குறுகிய உரைகளைப் பயிற்றுவிக்கவும், பின்னர் நீண்ட உரைகளைப் பயிற்றுவிக்கவும்\r\n- மங்கலான படங்களுக்கு தெளிவு: படிப்படியாக படத்தின் சிக்கலை அதிகரிக்கவும்\r\n- சிக்கலான எழுத்துருக்களுக்கு எளிமையானது: அச்சிடப்பட்ட முதல் கையெழுத்து வரை\r\n\r\n### முறைப்படுத்தும் நுட்பங்கள்\r\n\r\n**ஆர்.என்.என் படிப்பை பாதியில் நிறுத்துவதற்கான விண்ணப்பம்**:\r\nRNN இல் இடைநிற்றலுக்கு விண்ணப்பிக்க சிறப்பு கவனம் தேவை:\r\n- லூப் இணைப்புகளில் டிராப்அவுட்டைப் பயன்படுத்த வேண்டாம்\r\n- உள்ளீடு மற்றும் வெளியீட்டு அடுக்குகளில் டிராப்அவுட் பயன்படுத்தப்படலாம்\r\n- மாறுபட்ட இடைநிற்றல்: எல்லா நேர படிகளிலும் ஒரே டிராப்அவுட் முகமூடியைப் பயன்படுத்தவும்\r\n\r\n**எடை சிதைவு**:\r\nL2 ஒழுங்குபடுத்தல் அதிகப்படியான பொருத்தத்தைத் தடுக்கிறது:\r\nஇழப்பு = குறுக்கு என்ட்ரோபி + λ * || டபிள்யூ|| ²\r\n\r\nஇங்கு λ என்பது ஒழுங்குபடுத்தல் குணகம், இது சரிபார்ப்பு தொகுப்பால் உகந்ததாக இருக்க வேண்டும்.\r\n\r\n**சாய்வு பயிர் செய்தல்**:\r\nசாய்வு வெடிப்புகளைத் தடுக்க ஒரு சிறந்த வழி. சாய்வு விதிமுறை வரம்பை மீறும்போது, சாய்வு திசையை மாற்றாமல் வைத்திருக்க சாய்வை விகிதாசாரமாக அளவிடவும்.\r\n\r\n**முன்கூட்டியே நிறுத்துதல்**:\r\nசெயல்திறன் இனி மேம்படாதபோது சரிபார்ப்பு செயல்திறனை அமைத்து பயிற்சியை நிறுத்துங்கள்:\r\n- அதிகப்படியான பொருத்தத்தைத் தடுக்கவும்\r\n- கணினி வளங்களை சேமிக்கவும்\r\n- உகந்த மாதிரியைத் தேர்ந்தெடுக்கவும்\r\n\r\n### ஹைப்பர் அளவுரு ட்யூனிங்\r\n\r\n**கற்றல் விகித திட்டமிடல்**:\r\n- ஆரம்ப கற்றல் விகிதம்: பொதுவாக 0.001-0.01 ஆக அமைக்கப்படும்\r\n- கற்றல் விகித சிதைவு: அதிவேக சிதைவு அல்லது ஏணி சிதைவு\r\n- தகவமைப்பு கற்றல் விகிதம்: Adam, RMSprop போன்ற ஆப்டிமைசர்களைப் பயன்படுத்தவும்\r\n\r\n**தொகுதி அளவு தேர்வு**:\r\n- சிறிய தொகுதிகள்: சிறந்த பொதுமைப்படுத்தல் செயல்திறன் ஆனால் நீண்ட பயிற்சி நேரம்\r\n- அதிக அளவு: பயிற்சி வேகமானது ஆனால் பொதுமைப்படுத்தலை பாதிக்கலாம்\r\n- 16-128 க்கு இடையிலான தொகுதி அளவுகள் பொதுவாக தேர்ந்தெடுக்கப்படுகின்றன\r\n\r\n**வரிசை நீள செயலாக்கம்**:\r\n- நிலையான நீளம்: நிலையான நீளத்திற்கு வரிசைகளை ட்ரன்கேட் அல்லது நிரப்பவும்\r\n- டைனமிக் நீளம்: மாறி நீள வரிசைகளைக் கையாள திணிப்பு மற்றும் மறைத்தலைப் பயன்படுத்தவும்\r\n- பேக்கிங் உத்தி: ஒத்த நீளம் கொண்ட குழு வரிசைகள்\r\n\r\n## செயல்திறன் மதிப்பீடு மற்றும் பகுப்பாய்வு\r\n\r\n### அளவீடுகளை மதிப்பிடுங்கள்\r\n\r\n**எழுத்து-நிலை துல்லியம்**:\r\nAccuracy_char = (சரியாக அடையாளம் காணப்பட்ட எழுத்துக்குறிகளின் எண்ணிக்கை) / (மொத்த எழுத்துக்கள்)\r\n\r\nஇது மிக அடிப்படையான மதிப்பீட்டு குறிகாட்டி மற்றும் மாதிரியின் எழுத்து அங்கீகார திறன்களை நேரடியாக பிரதிபலிக்கிறது.\r\n\r\n**வரிசை நிலை துல்லியம்**:\r\nAccuracy_seq = (சரியாக அடையாளம் காணப்பட்ட வரிசைகளின் எண்ணிக்கை) / (வரிசைகளின் மொத்த எண்ணிக்கை)\r\n\r\nஇந்த காட்டி மிகவும் கடுமையானது, மேலும் முற்றிலும் சரியான வரிசை மட்டுமே சரியானது என்று கருதப்படுகிறது.\r\n\r\n**எடிட்டிங் தூரம் (லெவன்ஷ்டைன் தூரம்)**:\r\nகணிக்கப்பட்ட மற்றும் உண்மைத் தொடருக்கு இடையிலான வேறுபாட்டை அளவிடவும்:\r\n- செருகுதல், அகற்றுதல் மற்றும் மாற்று செயல்பாடுகளின் குறைந்தபட்ச எண்ணிக்கை\r\n- தரப்படுத்தப்பட்ட எடிட்டிங் தூரம்: எடிட்டிங் தூரம் / வரிசை நீளம்\r\n- BLEU மதிப்பெண்: பொதுவாக இயந்திர மொழிபெயர்ப்பில் பயன்படுத்தப்படுகிறது மற்றும் OCR மதிப்பீட்டிற்கும் பயன்படுத்தலாம்\r\n\r\n### பிழை பகுப்பாய்வு\r\n\r\n**பொதுவான பிழை வகைகள்**:\r\n1. **கேரக்டர் குழப்பம்**: ஒத்த கதாபாத்திரங்களை தவறாக அடையாளம் காணுதல்\r\n   - எண் 0 மற்றும் எழுத்து O\r\n   - எண் 1 மற்றும் எழுத்து l\r\n   - M மற்றும் N எழுத்துக்கள்\r\n\r\n2. **வரிசைப் பிழை**: எழுத்துக்களின் வரிசையில் பிழை\r\n   - எழுத்து நிலைகள் தலைகீழாக உள்ளன\r\n   - எழுத்துக்களின் நகலெடுத்தல் அல்லது தவிர்த்தல்\r\n\r\n3. **நீளப் பிழை**: வரிசை நீளத்தை கணிப்பதில் பிழை\r\n   - மிக நீண்டது: இல்லாத எழுத்துக்கள் செருகப்பட்டன\r\n   - மிகக் குறுகியது: இருக்கும் எழுத்துக்கள் இல்லை\r\n\r\n**பகுப்பாய்வு முறை**:\r\n1. **குழப்ப அணி **: எழுத்து-நிலை பிழை வடிவங்களை பகுப்பாய்வு செய்கிறது\r\n2. **கவனம் காட்சிப்படுத்தல்**: மாதிரியின் கவலைகளைப் புரிந்து கொள்ளுங்கள்\r\n3. **சாய்வு பகுப்பாய்வு**: சாய்வு ஓட்டத்தை சரிபார்க்கவும்\r\n4. **செயல்படுத்தல் பகுப்பாய்வு **: நெட்வொர்க்கின் அடுக்குகளில் செயல்படுத்தும் வடிவங்களைக் கவனியுங்கள்\r\n\r\n### மாதிரி கண்டறிதல்\r\n\r\n**ஓவர்ஃபிட் கண்டறிதல்**:\r\n- பயிற்சி இழப்புகள் தொடர்ந்து குறைந்து வருகின்றன, சரிபார்ப்பு இழப்புகள் அதிகரிக்கின்றன\r\n- பயிற்சி துல்லியம் சரிபார்ப்பு துல்லியத்தை விட மிக அதிகமாக உள்ளது\r\n- தீர்வு: வழக்கமான தன்மையை அதிகரிக்கவும் மற்றும் மாதிரி சிக்கலைக் குறைக்கவும்\r\n\r\n**அண்டர்ஃபிட் கண்டறிதல்**:\r\n- பயிற்சி மற்றும் சரிபார்ப்பு இழப்புகள் இரண்டும் அதிகம்\r\n- பயிற்சி தொகுப்பில் மாதிரி சிறப்பாக செயல்படவில்லை\r\n- தீர்வு: மாதிரி சிக்கலை அதிகரிக்கவும் மற்றும் கற்றல் விகிதத்தை சரிசெய்யவும்\r\n\r\n**சாய்வு சிக்கல் கண்டறிதல்**:\r\n- சாய்வு இழப்பு: சாய்வு மதிப்பு மிகவும் சிறியது, மெதுவான கற்றல்\r\n- சாய்வு வெடிப்பு: அதிகப்படியான சாய்வு மதிப்புகள் நிலையற்ற பயிற்சிக்கு வழிவகுக்கும்\r\n- தீர்வு: LSTM/GRU ஐப் பயன்படுத்துதல், சாய்வு பயிர்\r\n\r\n## நிஜ உலக விண்ணப்ப வழக்குகள்\r\n\r\n### கையால் எழுதப்பட்ட எழுத்து அங்கீகார அமைப்பு\r\n\r\n**விண்ணப்ப காட்சிகள் **:\r\n- கையால் எழுதப்பட்ட குறிப்புகளை டிஜிட்டல் மயமாக்குங்கள்: காகித குறிப்புகளை மின்னணு ஆவணங்களாக மாற்றவும்\r\n- படிவம் தானாக நிரப்பு: கையால் எழுதப்பட்ட படிவம் உள்ளடக்கத்தை தானாக அங்கீகரிக்கிறது\r\n- வரலாற்று ஆவண அடையாளம்: பண்டைய புத்தகங்கள் மற்றும் வரலாற்று ஆவணங்களை டிஜிட்டல் மயமாக்குதல்\r\n\r\n**தொழில்நுட்ப அம்சங்கள் **:\r\n- பெரிய எழுத்து மாறுபாடுகள்: கையால் எழுதப்பட்ட உரை அதிக அளவு தனிப்பயனாக்கத்தைக் கொண்டுள்ளது\r\n- தொடர்ச்சியான பேனா செயலாக்கம்: எழுத்துக்களுக்கு இடையிலான இணைப்புகள் கையாளப்பட வேண்டும்\r\n- சூழல்-முக்கியமான: அங்கீகாரத்தை மேம்படுத்த மொழி மாதிரிகளைப் பயன்படுத்தவும்\r\n\r\n**கணினி கட்டமைப்பு**:\r\n1. **சிகிச்சைக்கு முந்தைய தொகுதி**:\r\n   - படத்தை நீக்குதல் மற்றும் மேம்படுத்துதல்\r\n   - சாய்வு திருத்தம்\r\n   - உரை வரி பிரித்தல்\r\n\r\n2. **அம்சம் பிரித்தெடுத்தல் தொகுதி **:\r\n   - சி.என்.என் காட்சி அம்சங்களை பிரித்தெடுக்கிறது\r\n   - பல அளவிலான அம்ச இணைவு\r\n   - சிறப்புத் தொடர்\r\n\r\n3. **வரிசை மாடலிங் தொகுதி **:\r\n   - இருதிசை LSTM மாடலிங்\r\n   - கவனம் வழிமுறைகள்\r\n   - சூழ்நிலை குறியாக்கம்\r\n\r\n4. **டிகோடிங் தொகுதி**:\r\n   - CTC டிகோடிங் அல்லது கவனம் டிகோடிங்\r\n   - மொழி மாதிரி பிந்தைய செயலாக்க\r\n   - நம்பிக்கை மதிப்பீடு\r\n\r\n### அச்சிடப்பட்ட ஆவண அறிதல் அமைப்பு\r\n\r\n**விண்ணப்ப காட்சிகள் **:\r\n- ஆவண டிஜிட்டல்மயமாக்கல்: காகித ஆவணங்களை திருத்தக்கூடிய வடிவங்களாக மாற்றுதல்\r\n- பில் அங்கீகாரம்: விலைப்பட்டியல்கள், ரசீதுகள் மற்றும் பிற பில்களை தானாகவே செயலாக்கவும்\r\n- சிக்னேஜ் அங்கீகாரம்: சாலை அடையாளங்கள், கடை அடையாளங்கள் மற்றும் பலவற்றை அடையாளம் காணவும்\r\n\r\n**தொழில்நுட்ப அம்சங்கள் **:\r\n- வழக்கமான எழுத்துரு: கையால் எழுதப்பட்ட உரையை விட வழக்கமானது\r\n- அச்சுக்கலை விதிகள்: தளவமைப்பு தகவலைப் பயன்படுத்தலாம்\r\n- உயர் துல்லியத் தேவைகள்: வணிக பயன்பாடுகளுக்கு கடுமையான துல்லியத் தேவைகள் உள்ளன\r\n\r\n**உகப்பாக்கம் உத்தி**:\r\n1. **பல எழுத்துரு பயிற்சி **: பல எழுத்துருக்களிலிருந்து பயிற்சி தரவைப் பயன்படுத்துகிறது\r\n2. **தரவு மேம்பாடு **: சுழற்றுதல், அளவு, இரைச்சல் சேர்த்தல்\r\n3. **பிந்தைய செயலாக்க தேர்வுமுறை **: எழுத்துப்பிழை சரிபார்ப்பு, இலக்கண திருத்தம்\r\n4. **நம்பிக்கை மதிப்பீடு**: அங்கீகார முடிவுகளுக்கான நம்பகத்தன்மை மதிப்பெண்ணை வழங்குகிறது\r\n\r\n### காட்சி உரை அறிதல் அமைப்பு\r\n\r\n**விண்ணப்ப காட்சிகள் **:\r\n- Street View உரை அறிதல்: Google Street Viewவில் உரை அறிதல்\r\n- தயாரிப்பு லேபிள் அங்கீகாரம்: பல்பொருள் அங்காடி தயாரிப்புகளின் தானியங்கி அடையாளம்\r\n- போக்குவரத்து அடையாளம் அங்கீகாரம்: நுண்ணறிவு போக்குவரத்து அமைப்புகளின் பயன்பாடுகள்\r\n\r\n**தொழில்நுட்ப சவால்கள்**:\r\n- சிக்கலான பின்னணிகள்: சிக்கலான இயற்கை காட்சிகளில் உரை உட்பொதிக்கப்பட்டுள்ளது\r\n- கடுமையான சிதைவு: முன்னோக்கு சிதைவு, வளைக்கும் சிதைவு\r\n- நிகழ்நேர தேவைகள்: மொபைல் பயன்பாடுகள் பதிலளிக்க வேண்டும்\r\n\r\n**தீர்வு**:\r\n1. **வலுவான அம்சம் பிரித்தெடுத்தல் **: ஆழமான சி.என்.என் நெட்வொர்க்குகளைப் பயன்படுத்துகிறது\r\n2. **பல அளவிலான செயலாக்கம் **: வெவ்வேறு அளவுகளின் உரையைக் கையாளவும்\r\n3. **வடிவியல் திருத்தம் **: வடிவியல் சிதைவுகளை தானாகவே சரிசெய்கிறது\r\n4. **மாதிரி சுருக்க **: மொபைலுக்கான மாதிரியை மேம்படுத்தவும்\r\n\r\n## சுருக்கம்\r\n\r\nமீண்டும் மீண்டும் நரம்பியல் நெட்வொர்க்குகள் OCR இல் வரிசை மாடலிங்கிற்கான சக்திவாய்ந்த கருவியை வழங்குகின்றன. அடிப்படை RNNகள் முதல் மேம்படுத்தப்பட்ட LSTMகள் மற்றும் GRUகள் வரை இருதிசை செயலாக்கம் மற்றும் கவனம் வழிமுறைகள் வரை, இந்த தொழில்நுட்பங்களின் வளர்ச்சி OCR அமைப்புகளின் செயல்திறனை பெரிதும் மேம்படுத்தியுள்ளது.\r\n\r\n**முக்கிய எடுத்துக்காட்டுகள்**:\r\n- RNNகள் லூப் இணைப்புகள் மூலம் வரிசை மாதிரியாக்கத்தை செயல்படுத்துகின்றன, ஆனால் ஒரு சாய்வு காணாமல் பிரச்சனை உள்ளது\r\n- LSTM மற்றும் GRU ஆகியவை கேட்டிங் வழிமுறைகள் மூலம் நீண்ட தூர சார்பு கற்றல் சிக்கலை தீர்க்கின்றன\r\n- இருதிசை RNNகள் முழு சூழல் தகவல்களையும் பயன்படுத்த முடியும்\r\n- கவனம் வழிமுறைகள் வரிசை மாதிரியாக்கத்தின் திறனை மேலும் மேம்படுத்துகின்றன\r\n- மாதிரி செயல்திறனுக்கு பொருத்தமான பயிற்சி உத்திகள் மற்றும் ஒழுங்குபடுத்தல் நுட்பங்கள் முக்கியம்\r\n\r\n**எதிர்கால அபிவிருத்தி திசைகள்**:\r\n- மின்மாற்றி கட்டமைப்புகளுடன் ஒருங்கிணைப்பு\r\n- வரிசை மாதிரியாக்கத்திற்கு மிகவும் திறமையான அணுகுமுறை\r\n- எண்ட்-டு-எண்ட் மல்டிமோடல் கற்றல்\r\n- நிகழ்நேர மற்றும் துல்லியத்தின் சமநிலை\r\n\r\nதொழில்நுட்பம் தொடர்ந்து உருவாகி வருவதால், வரிசை மாடலிங் நுட்பங்கள் இன்னும் உருவாகி வருகின்றன. OCR துறையில் RNN கள் மற்றும் அவற்றின் மாறுபாடுகளால் திரட்டப்பட்ட அனுபவம் மற்றும் தொழில்நுட்பம் மிகவும் மேம்பட்ட வரிசை மாடலிங் முறைகளைப் புரிந்துகொள்வதற்கும் வடிவமைப்பதற்கும் ஒரு உறுதியான அடித்தளத்தை அமைத்துள்ளது.</div>\n                        \n                        <div class=\"article-tags\">\n                            <h6>சிட்டை:</h6>\n                            <div class=\"tags-list\">\n                                \n                                <span class=\"tag\">RNN</span>\n                                \n                                <span class=\"tag\">LSTM</span>\n                                \n                                <span class=\"tag\">GRU</span>\n                                \n                                <span class=\"tag\">வரிசை மாதிரியாக்கம்</span>\n                                \n                                <span class=\"tag\">சாய்வு மறைந்துவிடும்</span>\n                                \n                                <span class=\"tag\">இருதிசை RNN</span>\n                                \n                                <span class=\"tag\">கவன ஈர்ப்பு பொறிமுறை</span>\n                                \n                                <span class=\"tag\">CRNN</span>\n                                \n                                <span class=\"tag\">OCR</span>\n                                \n                            </div>\n                        </div>\n                        \n                    </article>\n                    \n                    <footer class=\"article-footer\" id=\"articleFooter\">\n                        <div class=\"article-share\">\n                            <h6 style=\"color: #1a1c1f; margin-bottom: 1rem;\">பகிர்தல் மற்றும் இயக்குதல்:</h6>\n                            <div class=\"share-buttons\">\n                                <a href=\"javascript:void(0)\" onclick=\"shareToWeibo()\" class=\"btn btn-weibo\">📱 Weibo பகிர்ந்துள்ளார்</a>\n                                <a href=\"javascript:void(0)\" onclick=\"copyUrl()\" class=\"btn btn-copy\">🔗 இணைப்பை நகலெடு</a>\n                                <button type=\"button\" class=\"btn btn-print\" onclick=\"printArticle()\">🖨️ கட்டுரையை அச்சிடுங்கள்</button>\n                            </div>\n                        </div>\n                    </footer>\n                    \n                </div>\n                <div class=\"col-lg-4 col-xl-3 col-md-12\">\n                    <div class=\"sidebar\">\n                        <div class=\"sidebar-card toc-card\" id=\"tocCard\" style=\"display: none;\"><h5>பொருளடக்கம்</h5><div id=\"tableOfContents\" class=\"toc-list\"></div></div>\n                        <div class=\"sidebar-card\">\n                            <h5>பரிந்துரைக்கப்பட்ட வாசிப்பு</h5>\n                            <div id=\"recommendedArticles\">\n                                \n                                <div class=\"recommended-item\"><h6><a href=\"Article.aspx?slug=document-intelligence-future-outlook\">【ஆவண நுண்ணறிவு செயலாக்கத் தொடர்·20】ஆவண நுண்ணறிவு செயலாக்க தொழில்நுட்பத்தின் வளர்ச்சி வாய்ப்புகள்</a></h6><p class=\"text-muted small\">2025-08-19 • 1203 அடுத்த வாசிப்பு</p></div>\n                                \n                                <div class=\"recommended-item\"><h6><a href=\"Article.aspx?slug=document-intelligence-quality-assurance\">【ஆவண நுண்ணறிவு செயலாக்கத் தொடர்·19】நுண்ணறிவு செயலாக்க தர உத்தரவாத அமைப்பை ஆவணப்படுத்துங்கள்</a></h6><p class=\"text-muted small\">2025-08-19 • 1200 அடுத்த வாசிப்பு</p></div>\n                                \n                                <div class=\"recommended-item\"><h6><a href=\"Article.aspx?slug=large-scale-document-processing-optimization\">【ஆவண நுண்ணறிவு செயலாக்கத் தொடர்·18】பெரிய அளவிலான ஆவண செயலாக்க செயல்திறன் தேர்வுமுறை</a></h6><p class=\"text-muted small\">2025-08-19 • 1201 அடுத்த வாசிப்பு</p></div>\n                                \n                            </div>\n                        </div>\n                    </div>\n</div>\n</div>\n</div>\n</div>\n<script>\nvar articleId,viewCountKey,now=new Date().getTime();\ndocument.addEventListener('DOMContentLoaded',function(){var articleDetail=document.getElementById('articleDetail');if(articleDetail&&articleDetail.querySelector('.article-content')){formatContent();initReadingProgress();optimizeImages();updateViewCount();}});\nfunction updateViewCount(){articleId=getArticleId();if(!articleId)return;viewCountKey='article_view_'+articleId;var lastViewTime=localStorage.getItem(viewCountKey);if(lastViewTime&&(now-parseInt(lastViewTime))<3600000)return;var xhr=new XMLHttpRequest();xhr.open('POST','/Article.ashx',true);xhr.setRequestHeader('Content-Type','application/x-www-form-urlencoded');xhr.onreadystatechange=function(){if(xhr.readyState===4&&xhr.status===200){try{var response=JSON.parse(xhr.responseText);if(response.success){updateViewCountDisplay(response.viewCount);localStorage.setItem(viewCountKey,now.toString());}}catch(e){}}};xhr.send('action=updateviewcount&articleId='+encodeURIComponent(articleId));}\nfunction getArticleId(){var articleDetail=document.getElementById('articleDetail');if(articleDetail){var id=articleDetail.getAttribute('data-article-id');if(id)return id;}var urlParams=new URLSearchParams(window.location.search);return urlParams.get('id');}\nfunction updateViewCountDisplay(newCount){document.querySelectorAll('.view-count').forEach(function(element){element.textContent=newCount;});}\nfunction decodeHtmlEntities(text){var textArea=document.createElement('textarea');textArea.innerHTML=text;return textArea.value;}\nfunction formatContent(){var contentDiv=document.getElementById('articleContent');if(!contentDiv)return;var content=contentDiv.textContent||contentDiv.innerText;if(!content)return;content=decodeHtmlEntities(content);content=formatMarkdownContent(content);content=formatIndentation(content);content=formatLists(content);content=formatParagraphs(content);content=formatLinks(content);contentDiv.innerHTML=content;initTableOfContents();}\nfunction formatIndentation(content){var lines=content.split('\\n'),result=[];for(var i=0;i<lines.length;i++){var line=lines[i];if(line.trim().match(/^<[^>]+>/)){result.push(line);continue;}line=line.replace(/^\\t+/g,function(match){return '    '.repeat(match.length);});if(line.match(/^  +[^\\s]/)){var indentMatch=line.match(/^( +)(.*)$/);if(indentMatch){var indentLevel=Math.floor(indentMatch[1].length/2);line='<span class=\"text-indent-'+indentLevel+'\">'+indentMatch[2]+'</span>';}}result.push(line);}return result.join('\\n');}\nfunction formatMarkdownContent(content){content=content.replace(/^\\s*##### (.+)$/gm,'<h5>$1</h5>').replace(/^\\s*#### (.+)$/gm,'<h4>$1</h4>').replace(/^\\s*### (.+)$/gm,'<h3>$1</h3>').replace(/^\\s*## (.+)$/gm,'<h2>$1</h2>').replace(/^\\s*# (.+)$/gm,'<h1>$1</h1>').replace(/^> (.+)$/gm,'<blockquote><p>$1</p></blockquote>').replace(/\\*\\*([^*]+?)\\*\\*/g,'<strong>$1</strong>').replace(/\\*([^*\\n]+?)\\*/g,'<em>$1</em>').replace(/`([^`]+?)`/g,'<code class=\"inline-code\">$1</code>').replace(/^(\\*\\*[^*]+?\\*\\*)：/gm,'<div class=\"label-item\"><span class=\"label\">$1</span></div>').replace(/^([^：\\n]+?)：$/gm,'<div class=\"section-label\">$1：</div>').replace(/^(\\*\\*[^*]+?\\*\\*)：(.+)$/gm,'<div class=\"definition-item\"><span class=\"definition-label\">$1：</span><span class=\"definition-content\">$2</span></div>').replace(/^(note|note|note):(.+)$/gm,'<div class=\"note-item\"><span class=\"note-label\">$1：</span><span class=\"note-content\">$2</span></div>');content=formatLists(content);content=formatMathSymbols(content);content=formatParagraphs(content);return content;}\nfunction formatLists(content){var lines=content.split('\\n'),result=[],inOrderedList=false,inUnorderedList=false;for(var i=0;i<lines.length;i++){var line=lines[i],trimmed=line.trim();var orderedMatch=line.match(/^(\\d+)\\.\\s*(.+)$/);var unorderedMatch=line.match(/^[-*+]\\s*(.+)$/);if(orderedMatch){if(!inOrderedList){if(inUnorderedList){result.push('</ul>');inUnorderedList=false;}result.push('<ol class=\"formatted-list ordered\">');inOrderedList=true;}var listContent=orderedMatch[2].replace(/^\\*\\*([^*]+?)\\*\\*：(.+)$/,'<strong class=\"list-title\">$1：</strong><span class=\"list-content\">$2</span>');result.push('<li>'+listContent+'</li>');}else if(unorderedMatch){if(!inUnorderedList){if(inOrderedList){result.push('</ol>');inOrderedList=false;}result.push('<ul class=\"formatted-list unordered\">');inUnorderedList=true;}var listContent=unorderedMatch[1].replace(/^\\*\\*([^*]+?)\\*\\*：(.+)$/,'<strong class=\"list-title\">$1：</strong><span class=\"list-content\">$2</span>');result.push('<li>'+listContent+'</li>');}else{if(trimmed===''){if(i+1<lines.length){var nextLine=lines[i+1].trim();if(!nextLine.match(/^\\d+\\.\\s/)&&!nextLine.match(/^[-*+]\\s/)){if(inOrderedList){result.push('</ol>');inOrderedList=false;}if(inUnorderedList){result.push('</ul>');inUnorderedList=false;}}}result.push(line);}else{if(inOrderedList){result.push('</ol>');inOrderedList=false;}if(inUnorderedList){result.push('</ul>');inUnorderedList=false;}result.push(line);}}}if(inOrderedList)result.push('</ol>');if(inUnorderedList)result.push('</ul>');return result.join('\\n');}\nfunction formatMathSymbols(content){var mathSymbols={'α':'&alpha;','β':'&beta;','γ':'&gamma;','δ':'&delta;','ε':'&epsilon;','ζ':'&zeta;','η':'&eta;','θ':'&theta;','λ':'&lambda;','μ':'&mu;','π':'&pi;','σ':'&sigma;','∑':'&sum;','∏':'&prod;','∫':'&int;','∂':'&part;','∇':'&nabla;','∞':'&infin;','≤':'&le;','≥':'&ge;','≠':'&ne;','≈':'&asymp;','±':'&plusmn;','×':'&times;','÷':'&divide;','→':'&rarr;','←':'&larr;','↑':'&uarr;','↓':'&darr;','⇒':'&rArr;','⇐':'&lArr;'};var subMap={'₀':'0','₁':'1','₂':'2','₃':'3','₄':'4','₅':'5','₆':'6','₇':'7','₈':'8','₉':'9','ₖ':'k','ₜ':'t','ₛ':'s','₊':'+','₋':'-'};var supMap={'⁰':'0','¹':'1','²':'2','³':'3','⁴':'4','⁵':'5','⁶':'6','⁷':'7','⁸':'8','⁹':'9','ᵏ':'k','ᵗ':'t','ˢ':'s','⁺':'+','⁻':'-'};for(var symbol in mathSymbols){content=content.replace(new RegExp(escapeRegExp(symbol),'g'),mathSymbols[symbol]);}content=content.replace(/([a-zA-Z])₍([0-9]+)₎/g,'$1<sub>$2</sub>').replace(/([a-zA-Z])⁽([0-9]+)⁾/g,'$1<sup>$2</sup>').replace(/([a-zA-Z])([₀₁₂₃₄₅₆₇₈₉ₖₜₛ₊₋]+)/g,function(match,base,subscript){var converted=subscript.replace(/[₀₁₂₃₄₅₆₇₈₉ₖₜₛ₊₋]/g,function(char){return subMap[char]||char;});return base+'<sub>'+converted+'</sub>';}).replace(/([a-zA-Z])([⁰¹²³⁴⁵⁶⁷⁸⁹ᵏᵗˢ⁺⁻]+)/g,function(match,base,superscript){var converted=superscript.replace(/[⁰¹²³⁴⁵⁶⁷⁸⁹ᵏᵗˢ⁺⁻]/g,function(char){return supMap[char]||char;});return base+'<sup>'+converted+'</sup>';}).replace(/\\b([A-Za-z])_\\{([^}]+)\\}/g,'$1<sub>$2</sub>').replace(/\\b([A-Za-z])\\^\\{([^}]+)\\}/g,'$1<sup>$2</sup>');return content;}\nfunction formatParagraphs(content){var lines=content.split('\\n'),result=[],inParagraph=false,currentParagraph=[];for(var i=0;i<lines.length;i++){var originalLine=lines[i],line=originalLine.trim();if(line.match(/^<[^>]+>/)||line===''){if(inParagraph&&currentParagraph.length>0){var paragraphContent=currentParagraph.join(' ').trim();if(paragraphContent)result.push('<p class=\"content-paragraph indent-paragraph\">'+paragraphContent+'</p>');currentParagraph=[];inParagraph=false;}result.push(originalLine);continue;}if(line.match(/^#{1,6}\\s/)||line.match(/^\\d+\\.\\s/)||line.match(/^[-*+]\\s/)||line.match(/^>/)){if(inParagraph&&currentParagraph.length>0){var paragraphContent=currentParagraph.join(' ').trim();if(paragraphContent)result.push('<p class=\"content-paragraph indent-paragraph\">'+paragraphContent+'</p>');currentParagraph=[];inParagraph=false;}result.push(originalLine);continue;}if(line){currentParagraph.push(line);inParagraph=true;}}if(inParagraph&&currentParagraph.length>0){var paragraphContent=currentParagraph.join(' ').trim();if(paragraphContent)result.push('<p class=\"content-paragraph indent-paragraph\">'+paragraphContent+'</p>');}return result.join('\\n');}\nfunction escapeRegExp(string){return string.replace(/[.*+?^${}()|[\\]\\\\]/g,'\\\\$&');}\nfunction formatLinks(content){return content.replace(/<a\\s+href=\"(https?:\\/\\/[^\"]+)\"([^>]*)>/gi,function(match,url,attrs){return !attrs.includes('target=')?'<a href=\"'+url+'\"'+attrs+' target=\"_blank\" rel=\"noopener noreferrer\">':match;});}\nfunction escapeHtml(text){var div=document.createElement('div');div.textContent=text;return div.innerHTML;}\nfunction addTocClickEvents(){document.querySelectorAll('#tableOfContents a').forEach(function(link){link.addEventListener('click',function(e){e.preventDefault();var targetElement=document.getElementById(this.getAttribute('data-target'));if(targetElement){targetElement.scrollIntoView({behavior:'smooth',block:'start'});updateActiveTocItem(this);}});});}\nfunction addScrollHighlight(headings){var throttleTimer=null;function updateActiveHeading(){var windowHeight=window.innerHeight,activeHeading=null;for(var i=headings.length-1;i>=0;i--){var rect=headings[i].getBoundingClientRect();if(rect.top<=windowHeight*0.3){activeHeading=headings[i];break;}}if(activeHeading){var activeLink=document.querySelector('#tableOfContents a[data-target=\"'+activeHeading.id+'\"]');if(activeLink)updateActiveTocItem(activeLink);}}window.addEventListener('scroll',function(){if(throttleTimer)clearTimeout(throttleTimer);throttleTimer=setTimeout(updateActiveHeading,100);});updateActiveHeading();}\nfunction updateActiveTocItem(activeLink){document.querySelectorAll('#tableOfContents a').forEach(function(link){link.classList.remove('active');});if(activeLink){activeLink.classList.add('active');scrollTocToActiveItem(activeLink);}}\nfunction scrollTocToActiveItem(activeLink){var tocCard=document.getElementById('tocCard');if(!activeLink||!tocCard)return;var cardRect=tocCard.getBoundingClientRect();var activeRect=activeLink.getBoundingClientRect();var relativeTop=activeRect.top-cardRect.top;var relativeBottom=activeRect.bottom-cardRect.bottom;var cardStyle=window.getComputedStyle(tocCard);var paddingTop=parseFloat(cardStyle.paddingTop)||0;var titleHeight=tocCard.querySelector('h5')?tocCard.querySelector('h5').offsetHeight:0;var contentTop=paddingTop+titleHeight;if(relativeTop<contentTop){tocCard.scrollTop+=(relativeTop-contentTop-10);}else if(relativeBottom>0){tocCard.scrollTop+=(relativeBottom+10);}}\nfunction optimizeImages(){document.querySelectorAll('.article-content img').forEach(function(img){img.loading='lazy';img.onerror=function(){this.style.display='none';};if(!img.alt)img.alt='படங்களுடன் கட்டுரை';});}\nfunction initReadingProgress(){var progressBar=document.getElementById('readingProgress');function updateProgress(){var scrollTop=window.pageYOffset||document.documentElement.scrollTop;var docHeight=document.documentElement.scrollHeight-window.innerHeight;progressBar.style.width=Math.min((scrollTop/docHeight)*100,100)+'%';}window.addEventListener('scroll',updateProgress);updateProgress();}\nfunction initTableOfContents(){var content=document.querySelector('.article-content');if(!content)return;var headings=content.querySelectorAll('h1, h2, h3, h4, h5, h6');if(headings.length<=1)return;var tocCard=document.getElementById('tocCard');var tocList=document.getElementById('tableOfContents');var html='',counters=[0,0,0,0,0,0];headings.forEach(function(heading,index){var id='heading-'+index;heading.id=id;var level=parseInt(heading.tagName.substring(1));var indent=(level-1)*15;counters[level-1]++;for(var i=level;i<counters.length;i++)counters[i]=0;var numberText='';for(var i=0;i<level;i++){if(counters[i]>0)numberText+=(numberText?'.':'')+counters[i];}numberText+='.';html+='<a href=\"#'+id+'\" class=\"toc-item toc-level-'+level+'\" style=\"padding-left: '+indent+'px;\" data-target=\"'+id+'\"><span class=\"toc-number\">'+numberText+'</span> <span class=\"toc-text\">'+heading.textContent+'</span></a>';});tocList.innerHTML=html;tocCard.style.display='block';addTocClickEvents();addScrollHighlight(headings);tocList.addEventListener('click',function(e){if(e.target.tagName==='A'){e.preventDefault();var targetElement=document.getElementById(e.target.getAttribute('data-target'));if(targetElement){targetElement.scrollIntoView({behavior:'smooth',block:'start'});targetElement.style.transition='all 0.3s ease';targetElement.style.backgroundColor='#e3f2fd';targetElement.style.padding='10px';targetElement.style.borderRadius='6px';setTimeout(function(){targetElement.style.backgroundColor='';targetElement.style.padding='';targetElement.style.borderRadius='';},2000);}}});}\nfunction shareToWeibo(){window.open('https://service.weibo.com/share/share.php?url='+encodeURIComponent(window.location.href)+'&title='+encodeURIComponent(document.title),'_blank');}\nfunction copyUrl(){navigator.clipboard.writeText(window.location.href).then(function(){alert('இணைப்பு கிளிப்போர்டுக்கு நகலெடுக்கப்பட்டது');}).catch(function(){var textArea=document.createElement('textarea');textArea.value=window.location.href;textArea.style.cssText='position:fixed;left:-999999px;top:-999999px';document.body.appendChild(textArea);textArea.focus();textArea.select();try{alert(document.execCommand('copy')?'இணைப்பு கிளிப்போர்டுக்கு நகலெடுக்கப்பட்டது':'நகல் தோல்வியுற்றால், இணைப்பை கைமுறையாக நகலெடுக்கவும்');}catch(err){alert('நகல் தோல்வியுற்றால், இணைப்பை கைமுறையாக நகலெடுக்கவும்');}document.body.removeChild(textArea);});}\nfunction printArticle(){var printWindow=window.open('','_blank');var articleContent=document.querySelector('.article-detail').innerHTML;var articleTitle=document.querySelector('.article-title').textContent;printWindow.document.write('<!DOCTYPE html><html lang=\"ta\"><title>'+articleTitle+'</title><style>body{font-family:-apple-system,BlinkMacSystemFont,\"Segoe UI\",Roboto,\"Helvetica Neue\",Arial,sans-serif;line-height:1.6;color:#333;max-width:800px;margin:0 auto;padding:20px}h1,h2,h3,h4,h5,h6{color:#1a1c1f;margin-top:1.5em;margin-bottom:0.5em}h1{font-size:2em;border-bottom:2px solid #e9ecef;padding-bottom:0.3em}h2{font-size:1.5em;border-bottom:1px solid #e9ecef;padding-bottom:0.3em}h3{font-size:1.25em}p{margin-bottom:1em}pre{background:#f8f9fa;border:1px solid #e9ecef;border-radius:4px;padding:1em;overflow-x:auto;font-family:Consolas,Monaco,\"Courier New\",monospace}code{background:#f1f3f4;padding:2px 4px;border-radius:3px;font-family:Consolas,Monaco,\"Courier New\",monospace}blockquote{border-left:4px solid #1764ff;background:#f8f9fa;padding:1em;margin:1em 0;border-radius:0 4px 4px 0}img{max-width:100%;height:auto}table{width:100%;border-collapse:collapse;margin:1em 0}th,td{border:1px solid #ddd;padding:8px;text-align:left}th{background-color:#f2f2f2}.article-meta,.article-footer,.share-buttons{display:none}</style><body>'+articleContent+'</body></html>');printWindow.document.close();printWindow.focus();setTimeout(function(){printWindow.print();printWindow.close();},250);}\nfunction improvedStickyPosition(){var sidebarCards=document.querySelectorAll('.sidebar-card');var originalPositions=[],isFixed=[];sidebarCards.forEach(function(card,index){var rect=card.getBoundingClientRect();originalPositions[index]=rect.top+window.pageYOffset;isFixed[index]=false;var placeholder=document.createElement('div');placeholder.style.cssText='height:'+card.offsetHeight+'px;display:none';placeholder.className='sticky-placeholder';card.parentNode.insertBefore(placeholder,card.nextSibling);});function updateStickyPosition(){var scrollTop=window.pageYOffset||document.documentElement.scrollTop;sidebarCards.forEach(function(card,index){var originalTop=originalPositions[index];var stickyTop=index>0?(document.querySelector('.toc-card').style.position==='fixed'?60+document.querySelector('.toc-card').offsetHeight+8:80):60;var placeholder=card.nextSibling;if(scrollTop>originalTop-stickyTop){if(!isFixed[index]){var colContainer=card.closest('[class*=\"col-\"]');var colRect=colContainer.getBoundingClientRect();var colStyle=window.getComputedStyle(colContainer);var paddingLeft=parseFloat(colStyle.paddingLeft)||0;var paddingRight=parseFloat(colStyle.paddingRight)||0;card.style.cssText='position:fixed;top:'+stickyTop+'px;left:'+(colRect.left+paddingLeft)+'px;width:'+(colRect.width-paddingLeft-paddingRight)+'px;z-index:9999;box-sizing:border-box';card.classList.add('fixed');placeholder.style.display='block';isFixed[index]=true;}}else if(isFixed[index]){card.style.cssText='';card.classList.remove('fixed');placeholder.style.display='none';isFixed[index]=false;}});}window.addEventListener('scroll',updateStickyPosition);window.addEventListener('resize',function(){setTimeout(function(){sidebarCards.forEach(function(card,index){if(isFixed[index]){var colContainer=card.closest('[class*=\"col-\"]');var colRect=colContainer.getBoundingClientRect();var colStyle=window.getComputedStyle(colContainer);var paddingLeft=parseFloat(colStyle.paddingLeft)||0;var paddingRight=parseFloat(colStyle.paddingRight)||0;card.style.left=(colRect.left+paddingLeft)+'px';card.style.width=(colRect.width-paddingLeft-paddingRight)+'px';}else{originalPositions[index]=card.getBoundingClientRect().top+window.pageYOffset;}});},100);});updateStickyPosition();}\ndocument.addEventListener('DOMContentLoaded',function(){setTimeout(improvedStickyPosition,1000);});\n    </script>\n\r\n            </div>\r\n        </form>\r\n\r\n        <div class=\"fixed-icon\">\r\n            <div style=\"width: 120px; position: relative; left: 1rem; top: -12px;\">\r\n            </div>\r\n            <a id=\"webchat\" class=\"wx-con disa\" href=\"http://wpa.qq.com/msgrd?v=3&uin=365833440&site=qq&menu=yes\" target=\"_blank\">\r\n                <img class=\"wechat-ico\" src=\"site/image/icon/qq.svg\" alt=\"OCR உதவியாளர் QQ ஆன்லைன் வாடிக்கையாளர் சேவை\" />\r\n                <div class=\"wx-text\">QQ வாடிக்கையாளர் சேவை (365833440)</div>\r\n                <label class=\"badge badge-danger\" style=\"background: rgb(255, 34, 122); display: inline-block;\">1</label>\r\n            </a>\r\n\r\n            <a class=\"wx-con\" href=\"https://qm.qq.com/cgi-bin/qm/qr?k=bohNy9GMdgD5bsYrB-fcIjy-s6Ot3zOg&jump_from=webapi&qr=1\" target=\"_blank\">\r\n                <img class=\"wechat-ico\" src=\"site/image/icon/qq.svg\" alt=\"OCR உதவியாளர் QQ பயனர் தொடர்பு குழு\" />\r\n                <div class=\"wx-text\">QQ குழு (100029010)</div>\r\n                <label class=\"badge badge-danger\" style=\"background: rgb(255, 34, 122); display: inline-block;\">2</label>\r\n            </a>\r\n            <a href=\"mailto:<EMAIL>\" class=\"wx-con\">\r\n                <img class=\"wechat-ico\" src=\"site/image/icon/im.svg\" alt=\"OCR உதவியாளர் வாடிக்கையாளர் சேவையை மின்னஞ்சல் மூலம் தொடர்பு கொள்ளவும்\" />\r\n                <div class=\"wx-text\" style=\"width: 240px; text-align: left; left: -15.3rem; padding: 10px 20px;\">\r\n                    <div style=\"font-size: 15px\">மின்னஞ்சல்: <EMAIL></div>\r\n                    <hr style=\"background: #c1c1c1; margin: 10px 10px 6px 0;\" />\r\n                    <span style=\"font-size: 13px; color: #c1c1c1\">உங்கள் கருத்துக்கள் மற்றும் ஆலோசனைகளுக்கு நன்றி!</span>\r\n                </div>\r\n                <label class=\"badge badge-danger\" style=\"background: rgb(255, 34, 122); display: inline-block;\">3</label>\r\n            </a>\r\n        </div>\r\n\r\n        <style>\r\n            .content {\r\n                width: 100%;\r\n                padding: 0 !important;\r\n                margin: 0 auto\r\n            }\r\n\r\n            h3.h6 {\r\n                font-size: 1rem !important;\r\n                font-weight: bold !important;\r\n                line-height: 1.2 !important;\r\n                margin-bottom: .5rem;\r\n                font-family: inherit\r\n            }\r\n        </style>\r\n        <footer class=\"page-footer\">\r\n            <div class=\"container\">\r\n                <div class=\"row foot-cont pt-4 no-gutters\" style=\"border-top: 1px solid #e1e6ed; max-width: 100%\">\r\n                    <div class=\"col-lg-12 hu-an p-0 mb-3 mt-3 text-l color-gray\" style=\"text-align: center;\">\r\n                        OCR உரை அங்கீகார உதவியாளர்&nbsp;©️ 2025 ALL RIGHTS RESERVED. அனைத்து உரிமைகளும் பாதுகாக்கப்பட்டவை&nbsp;&nbsp;&nbsp;|&nbsp;&nbsp;&nbsp;\r\n                        <a href=\"PrivacyPolicy.aspx\" style=\"text-decoration: underline;\">தனியுரிமை ஒப்பந்தம்</a>&nbsp;&nbsp;&nbsp;|&nbsp;&nbsp;&nbsp;\r\n                        <a href=\"UserAgreement.aspx\" style=\"text-decoration: underline;\">பயனர் ஒப்பந்தம்</a>&nbsp;&nbsp;&nbsp;|&nbsp;&nbsp;&nbsp;\r\n                        <a href=\"Status.aspx\" style=\"text-decoration: underline;\">சேவை நிலை</a>&nbsp;&nbsp;&nbsp;|&nbsp;&nbsp;&nbsp;\r\n                        <a class=\"mb-2 color-gray\" href=\"https://beian.miit.gov.cn/\" target=\"_blank\" style=\"text-decoration: underline;\">உ ஐசிபி தயாரிப்பு எண் 2021012692</a>\r\n                    </div>\r\n                </div>\r\n            </div>\r\n        </footer>\r\n        <style>\r\n            #translate > .translateSelectLanguage {\r\n                right: 2rem;\r\n                font-size: 1rem;\r\n                width: 150px;\r\n                padding: .3rem;\r\n                margin-right: 20px;\r\n                border: 1px solid #C9C9C9;\r\n                background: #fff;\r\n                color: #555\r\n            }\r\n        </style>\r\n    </div>\r\n    <script>\r\n        document.addEventListener('DOMContentLoaded', function () {\r\n            var setScroll = function (active) {\r\n                forEach(qa('.v4_header_pc,.v4_header_mob'), function (e) {\r\n                    if (active) addClass(e, 'scrollActive'); else removeClass(e, 'scrollActive');\r\n                });\r\n            };\r\n            if (getScrollY() > 60) setScroll(1);\r\n            window.addEventListener('scroll', function () { setScroll(getScrollY() > 60); });\r\n\r\n            forEach(qa('.v4_header_pc'), function (h) {\r\n                h.addEventListener('mouseenter', function () { addClass(h, 'active'); });\r\n                h.addEventListener('mouseleave', function () { removeClass(h, 'active'); });\r\n            });\r\n\r\n            var menuTimer;\r\n            var hideMenu = function (menuId) {\r\n                var m = document.getElementById(menuId);\r\n                if (m) {\r\n                    m.style.cssText = 'visibility:hidden;height:0;display:none';\r\n                    var n = q('.nav-drown-con', m);\r\n                    if (n) { n.style.cssText = 'height:0;opacity:0;transform:translate(0,-100%)'; }\r\n                }\r\n            };\r\n\r\n            forEach(qa('ul.top-nav>li'), function (i) {\r\n                i.addEventListener('mouseenter', function () {\r\n                    clearTimeout(menuTimer);\r\n                    var l = this.querySelector('a'), t = this.querySelector('.triangle'), s = l ? l.getAttribute('data-sub') : null;\r\n                    if (l) addClass(l, 'active'); if (t) addClass(t, 'active');\r\n                    if (s) {\r\n                        var m = document.getElementById(s);\r\n                        if (m) {\r\n                            m.style.cssText = 'display:block;visibility:visible';\r\n                            var siblings = m.parentNode ? toArray(m.parentNode.children) : [];\r\n                            forEach(siblings, function (c) { if (c !== m) c.style.display = 'none'; });\r\n                            var n = q('.nav-drown-con', m);\r\n                            if (n) { n.style.cssText = 'height:100%;opacity:1;transform:translate(0,0)'; }\r\n\r\n                            m.addEventListener('mouseenter', function () { clearTimeout(menuTimer); });\r\n                            m.addEventListener('mouseleave', function () { menuTimer = setTimeout(function () { hideMenu(s); }, 100); });\r\n                        }\r\n                    }\r\n                });\r\n                i.addEventListener('mouseleave', function () {\r\n                    var l = this.querySelector('a'), t = this.querySelector('.triangle'), s = l ? l.getAttribute('data-sub') : null;\r\n                    if (l) removeClass(l, 'active'); if (t) removeClass(t, 'active');\r\n                    if (s) { menuTimer = setTimeout(function () { hideMenu(s); }, 100); }\r\n                });\r\n            });\r\n\r\n            var r = q('.v4_header_mob .right-menu');\r\n            if (r) r.addEventListener('click', function () {\r\n                var arr = [r, q('.v4_header_mob'), q('#nav_products'), q('.mob-nav-content .sidebar-fix')];\r\n                for (var i = 0; i < arr.length; i++) {\r\n                    var e = arr[i];\r\n                    if (e) {\r\n                        var name = (i === 0 ? 'active' : (i === 1 ? 'active' : (i === 2 ? 'active1' : 'fixedActiveBg')));\r\n                        toggleClass(e, name);\r\n                    }\r\n                }\r\n                var f = q('.mob-nav-content .sidebar-fix');\r\n                if (f) {\r\n                    toggleClass(f, 'show');\r\n                    document.body.style.overflow = hasClass(f, 'show') ? 'hidden' : 'scroll';\r\n                }\r\n            });\r\n\r\n            document.addEventListener('click', function (e) {\r\n                e = e || window.event;\r\n                var target = e.target || e.srcElement;\r\n                var m = closest(target, '.sidebar-fix-left .mob-nav-item');\r\n                if (m) {\r\n                    var p = m.parentNode;\r\n                    if (p) forEach(toArray(p.children), function (c) { removeClass(c, 'active'); });\r\n                    addClass(m, 'active');\r\n                    var d = m.getAttribute('data');\r\n                    if (d) forEach(qa('.sidebar-fix-rigth .sub-nav'), function (n, i) { n.style.display = (i == d ? 'block' : 'none'); });\r\n                }\r\n            });\r\n        });\r\n    </script>\r\n</body>\r\n</html>\r\n\r\n\t            <script type=\"text/javascript\">\r\n                    var _hmt = _hmt || [];\r\n                    (function () {\r\n                        var hm = document.createElement('script');hm.async=1;\r\n                        hm.src = 'https://hm.baidu.com/hm.js?5ee048d7caaf0d439cf16309a1687754';\r\n                        var s = document.getElementsByTagName('script')[0];s.parentNode.insertBefore(hm, s);\r\n                    })();\r\n                </script><div id=\"translate\" class=\"ignore\" style=\"display: none\"></div><script src=\"/static/js/translate.js\"></script>"
﻿"<!DOCTYPE html>\r\n<html xmlns=\"http://www.w3.org/1999/xhtml\" lang=\"ka\">\r\n<head><link rel=\"canonical\" href=\"https://ocr.oldfish.cn/ka/Article.aspx?id=81&slug=rnn-sequence-modeling\" /><link rel=\"alternate\" href=\"https://ocr.oldfish.cn/Article.aspx?id=81&slug=rnn-sequence-modeling\" hreflang=\"x-default\" />\r\n<link rel=\"alternate\" href=\"https://ocr.oldfish.cn/Article.aspx?id=81&slug=rnn-sequence-modeling\" hreflang=\"zh\" />\r\n<link rel=\"alternate\" href=\"https://ocr.oldfish.cn/Article.aspx?id=81&slug=rnn-sequence-modeling\" hreflang=\"zh\" />\r\n<link rel=\"alternate\" href=\"https://ocr.oldfish.cn/zh-Hant/Article.aspx?id=81&slug=rnn-sequence-modeling\" hreflang=\"zh-tw\" />\r\n<link rel=\"alternate\" href=\"https://ocr.oldfish.cn/yue/Article.aspx?id=81&slug=rnn-sequence-modeling\" hreflang=\"zh-yue\" />\r\n<link rel=\"alternate\" href=\"https://ocr.oldfish.cn/en/Article.aspx?id=81&slug=rnn-sequence-modeling\" hreflang=\"en\" />\r\n<link rel=\"alternate\" href=\"https://ocr.oldfish.cn/es/Article.aspx?id=81&slug=rnn-sequence-modeling\" hreflang=\"es\" />\r\n<link rel=\"alternate\" href=\"https://ocr.oldfish.cn/hi/Article.aspx?id=81&slug=rnn-sequence-modeling\" hreflang=\"hi\" />\r\n<link rel=\"alternate\" href=\"https://ocr.oldfish.cn/ar/Article.aspx?id=81&slug=rnn-sequence-modeling\" hreflang=\"ar\" />\r\n<link rel=\"alternate\" href=\"https://ocr.oldfish.cn/pt/Article.aspx?id=81&slug=rnn-sequence-modeling\" hreflang=\"pt\" />\r\n<link rel=\"alternate\" href=\"https://ocr.oldfish.cn/bn/Article.aspx?id=81&slug=rnn-sequence-modeling\" hreflang=\"bn\" />\r\n<link rel=\"alternate\" href=\"https://ocr.oldfish.cn/ru/Article.aspx?id=81&slug=rnn-sequence-modeling\" hreflang=\"ru\" />\r\n<link rel=\"alternate\" href=\"https://ocr.oldfish.cn/ja/Article.aspx?id=81&slug=rnn-sequence-modeling\" hreflang=\"ja\" />\r\n<link rel=\"alternate\" href=\"https://ocr.oldfish.cn/pa/Article.aspx?id=81&slug=rnn-sequence-modeling\" hreflang=\"pa\" />\r\n<link rel=\"alternate\" href=\"https://ocr.oldfish.cn/de/Article.aspx?id=81&slug=rnn-sequence-modeling\" hreflang=\"de\" />\r\n<link rel=\"alternate\" href=\"https://ocr.oldfish.cn/fr/Article.aspx?id=81&slug=rnn-sequence-modeling\" hreflang=\"fr\" />\r\n<link rel=\"alternate\" href=\"https://ocr.oldfish.cn/mr/Article.aspx?id=81&slug=rnn-sequence-modeling\" hreflang=\"mr\" />\r\n<link rel=\"alternate\" href=\"https://ocr.oldfish.cn/te/Article.aspx?id=81&slug=rnn-sequence-modeling\" hreflang=\"te\" />\r\n<link rel=\"alternate\" href=\"https://ocr.oldfish.cn/vi/Article.aspx?id=81&slug=rnn-sequence-modeling\" hreflang=\"vi\" />\r\n<link rel=\"alternate\" href=\"https://ocr.oldfish.cn/ko/Article.aspx?id=81&slug=rnn-sequence-modeling\" hreflang=\"ko\" />\r\n<link rel=\"alternate\" href=\"https://ocr.oldfish.cn/ta/Article.aspx?id=81&slug=rnn-sequence-modeling\" hreflang=\"ta\" />\r\n<link rel=\"alternate\" href=\"https://ocr.oldfish.cn/ur/Article.aspx?id=81&slug=rnn-sequence-modeling\" hreflang=\"ur\" />\r\n<link rel=\"alternate\" href=\"https://ocr.oldfish.cn/tr/Article.aspx?id=81&slug=rnn-sequence-modeling\" hreflang=\"tr\" />\r\n<link rel=\"alternate\" href=\"https://ocr.oldfish.cn/it/Article.aspx?id=81&slug=rnn-sequence-modeling\" hreflang=\"it\" />\r\n<link rel=\"alternate\" href=\"https://ocr.oldfish.cn/gu/Article.aspx?id=81&slug=rnn-sequence-modeling\" hreflang=\"gu\" />\r\n<link rel=\"alternate\" href=\"https://ocr.oldfish.cn/pl/Article.aspx?id=81&slug=rnn-sequence-modeling\" hreflang=\"pl\" />\r\n<link rel=\"alternate\" href=\"https://ocr.oldfish.cn/uk/Article.aspx?id=81&slug=rnn-sequence-modeling\" hreflang=\"uk\" />\r\n<link rel=\"alternate\" href=\"https://ocr.oldfish.cn/ms/Article.aspx?id=81&slug=rnn-sequence-modeling\" hreflang=\"ms\" />\r\n<link rel=\"alternate\" href=\"https://ocr.oldfish.cn/id/Article.aspx?id=81&slug=rnn-sequence-modeling\" hreflang=\"id\" />\r\n<link rel=\"alternate\" href=\"https://ocr.oldfish.cn/ml/Article.aspx?id=81&slug=rnn-sequence-modeling\" hreflang=\"ml\" />\r\n<link rel=\"alternate\" href=\"https://ocr.oldfish.cn/kn/Article.aspx?id=81&slug=rnn-sequence-modeling\" hreflang=\"kn\" />\r\n<link rel=\"alternate\" href=\"https://ocr.oldfish.cn/fa/Article.aspx?id=81&slug=rnn-sequence-modeling\" hreflang=\"fa\" />\r\n<link rel=\"alternate\" href=\"https://ocr.oldfish.cn/nl/Article.aspx?id=81&slug=rnn-sequence-modeling\" hreflang=\"nl\" />\r\n<link rel=\"alternate\" href=\"https://ocr.oldfish.cn/th/Article.aspx?id=81&slug=rnn-sequence-modeling\" hreflang=\"th\" />\r\n<link rel=\"alternate\" href=\"https://ocr.oldfish.cn/sw/Article.aspx?id=81&slug=rnn-sequence-modeling\" hreflang=\"sw\" />\r\n<link rel=\"alternate\" href=\"https://ocr.oldfish.cn/ro/Article.aspx?id=81&slug=rnn-sequence-modeling\" hreflang=\"ro\" />\r\n<link rel=\"alternate\" href=\"https://ocr.oldfish.cn/my/Article.aspx?id=81&slug=rnn-sequence-modeling\" hreflang=\"my\" />\r\n<link rel=\"alternate\" href=\"https://ocr.oldfish.cn/or/Article.aspx?id=81&slug=rnn-sequence-modeling\" hreflang=\"or\" />\r\n<link rel=\"alternate\" href=\"https://ocr.oldfish.cn/he/Article.aspx?id=81&slug=rnn-sequence-modeling\" hreflang=\"he\" />\r\n<link rel=\"alternate\" href=\"https://ocr.oldfish.cn/am/Article.aspx?id=81&slug=rnn-sequence-modeling\" hreflang=\"am\" />\r\n<link rel=\"alternate\" href=\"https://ocr.oldfish.cn/fil/Article.aspx?id=81&slug=rnn-sequence-modeling\" hreflang=\"fil\" />\r\n<link rel=\"alternate\" href=\"https://ocr.oldfish.cn/sv/Article.aspx?id=81&slug=rnn-sequence-modeling\" hreflang=\"sv\" />\r\n<link rel=\"alternate\" href=\"https://ocr.oldfish.cn/el/Article.aspx?id=81&slug=rnn-sequence-modeling\" hreflang=\"el\" />\r\n<link rel=\"alternate\" href=\"https://ocr.oldfish.cn/cs/Article.aspx?id=81&slug=rnn-sequence-modeling\" hreflang=\"cs\" />\r\n<link rel=\"alternate\" href=\"https://ocr.oldfish.cn/hu/Article.aspx?id=81&slug=rnn-sequence-modeling\" hreflang=\"hu\" />\r\n<link rel=\"alternate\" href=\"https://ocr.oldfish.cn/be/Article.aspx?id=81&slug=rnn-sequence-modeling\" hreflang=\"be\" />\r\n<link rel=\"alternate\" href=\"https://ocr.oldfish.cn/si/Article.aspx?id=81&slug=rnn-sequence-modeling\" hreflang=\"si\" />\r\n<link rel=\"alternate\" href=\"https://ocr.oldfish.cn/ne/Article.aspx?id=81&slug=rnn-sequence-modeling\" hreflang=\"ne\" />\r\n<link rel=\"alternate\" href=\"https://ocr.oldfish.cn/km/Article.aspx?id=81&slug=rnn-sequence-modeling\" hreflang=\"km\" />\r\n<link rel=\"alternate\" href=\"https://ocr.oldfish.cn/sk/Article.aspx?id=81&slug=rnn-sequence-modeling\" hreflang=\"sk\" />\r\n<link rel=\"alternate\" href=\"https://ocr.oldfish.cn/bg/Article.aspx?id=81&slug=rnn-sequence-modeling\" hreflang=\"bg\" />\r\n<link rel=\"alternate\" href=\"https://ocr.oldfish.cn/fr-ca/Article.aspx?id=81&slug=rnn-sequence-modeling\" hreflang=\"fr-ca\" />\r\n<link rel=\"alternate\" href=\"https://ocr.oldfish.cn/ha/Article.aspx?id=81&slug=rnn-sequence-modeling\" hreflang=\"ha\" />\r\n<link rel=\"alternate\" href=\"https://ocr.oldfish.cn/yo/Article.aspx?id=81&slug=rnn-sequence-modeling\" hreflang=\"yo\" />\r\n<link rel=\"alternate\" href=\"https://ocr.oldfish.cn/ig/Article.aspx?id=81&slug=rnn-sequence-modeling\" hreflang=\"ig\" />\r\n<link rel=\"alternate\" href=\"https://ocr.oldfish.cn/ku/Article.aspx?id=81&slug=rnn-sequence-modeling\" hreflang=\"ku\" />\r\n<link rel=\"alternate\" href=\"https://ocr.oldfish.cn/rw/Article.aspx?id=81&slug=rnn-sequence-modeling\" hreflang=\"rw\" />\r\n<link rel=\"alternate\" href=\"https://ocr.oldfish.cn/ca/Article.aspx?id=81&slug=rnn-sequence-modeling\" hreflang=\"ca\" />\r\n<link rel=\"alternate\" href=\"https://ocr.oldfish.cn/da/Article.aspx?id=81&slug=rnn-sequence-modeling\" hreflang=\"da\" />\r\n<link rel=\"alternate\" href=\"https://ocr.oldfish.cn/fi/Article.aspx?id=81&slug=rnn-sequence-modeling\" hreflang=\"fi\" />\r\n<link rel=\"alternate\" href=\"https://ocr.oldfish.cn/nb/Article.aspx?id=81&slug=rnn-sequence-modeling\" hreflang=\"nb\" />\r\n<link rel=\"alternate\" href=\"https://ocr.oldfish.cn/hr/Article.aspx?id=81&slug=rnn-sequence-modeling\" hreflang=\"hr\" />\r\n<link rel=\"alternate\" href=\"https://ocr.oldfish.cn/sr-Cyrl/Article.aspx?id=81&slug=rnn-sequence-modeling\" hreflang=\"sr-cyrl\" />\r\n<link rel=\"alternate\" href=\"https://ocr.oldfish.cn/sr-Latn/Article.aspx?id=81&slug=rnn-sequence-modeling\" hreflang=\"sr-latn\" />\r\n<link rel=\"alternate\" href=\"https://ocr.oldfish.cn/sq/Article.aspx?id=81&slug=rnn-sequence-modeling\" hreflang=\"sq\" />\r\n<link rel=\"alternate\" href=\"https://ocr.oldfish.cn/so/Article.aspx?id=81&slug=rnn-sequence-modeling\" hreflang=\"so\" />\r\n<link rel=\"alternate\" href=\"https://ocr.oldfish.cn/zu/Article.aspx?id=81&slug=rnn-sequence-modeling\" hreflang=\"zu\" />\r\n<link rel=\"alternate\" href=\"https://ocr.oldfish.cn/ka/Article.aspx?id=81&slug=rnn-sequence-modeling\" hreflang=\"ka\" />\r\n<meta http-equiv=\"Content-Type\" content=\"text/html; charset=UTF-8\" /><meta http-equiv=\"X-UA-Compatible\" content=\"IE=Edge,chrome=1\" /><meta name=\"renderer\" content=\"webkit\" /><meta name=\"viewport\" content=\"width=device-width, initial-scale=1.0, maximum-scale=5.0\" /><link rel=\"icon\" href=\"/favicon.ico\" type=\"image/x-icon\" /><meta name=\"robots\" content=\"all\" /><link rel=\"preconnect\" href=\"https://scm-file-new-1301864755.cos.ap-beijing.myqcloud.com\" /><link rel=\"preconnect\" href=\"https://cdn.oldfish.cn\" /><link rel=\"dns-prefetch\" href=\"https://hm.baidu.com\" /><link rel=\"dns-prefetch\" href=\"https://cdn.oldfish.cn\" /><link rel=\"preload\" href=\"/site/css/site.css\" as=\"style\" />\r\n<script>function toArray(l){var a=[];for(var i=0;i<(l?l.length:0);i++)a.push(l[i]);return a;}function forEach(l,f){for(var i=0,n=(l?l.length:0);i<n;i++)f(l[i],i);}function hasClass(e,c){if(!e||!c)return false;return(' '+e.className+' ').indexOf(' '+c+' ')>-1;}function addClass(e,c){if(!e||!c||hasClass(e,c))return;e.className=(e.className?e.className+' ':'')+c;}function removeClass(e,c){if(!e||!c)return;var u=' '+e.className+' ';while(u.indexOf(' '+c+' ')>-1){u=u.replace(' '+c+' ',' ');}e.className=u.replace(/^\\s+|\\s+$/g,'');}function toggleClass(e,c){if(!e||!c)return;if(hasClass(e,c))removeClass(e,c);else addClass(e,c);}function matchesSelector(e,s){if(!e||e.nodeType!==1)return false;if(e.matches)return e.matches(s);var p=e.parentNode||document;var m=toArray(p.querySelectorAll(s));for(var i=0;i<m.length;i++)if(m[i]===e)return true;return false;}function closest(e,s){var n=e;while(n&&n.nodeType===1){if(matchesSelector(n,s))return n;n=n.parentNode;}return null;}function safeRemove(e){if(e&&e.parentNode)e.parentNode.removeChild(e);}function qa(s,r){return toArray((r||document).querySelectorAll(s));}function q(s,r){return(r||document).querySelector(s);}function xhrGet(u,o,e){try{var x=new XMLHttpRequest();x.open('GET',u,true);x.onreadystatechange=function(){if(x.readyState===4){if(x.status>=200&&x.status<300)o&&o(x.responseText);else e&&e(x);}};x.send(null);}catch(r){e&&e(r);}}function getScrollY(){return window.pageYOffset||document.documentElement.scrollTop||document.body.scrollTop||0;}function getOrigin(){return window.location.protocol+'//'+window.location.host;}</script>\r\n<script>var isWebP=(function(){try{return document.createElement('canvas').toDataURL('image/webp').indexOf('data:image/webp')===0;}catch(e){return false;}})();document.addEventListener('DOMContentLoaded',function(){var c=document.getElementById('user-login-container');if(c){var u=c.getAttribute('data-url');if(u){xhrGet(u,function(h){c.innerHTML=h;forEach(qa('a',c),function(l){l.style.display='inline-block';l.style.marginLeft='10px';});});}}});</script>\r\n    \n    \n\n    <meta name=\"description\" content=\"ჩაყვინთეთ RNN, LSTM, GRU გამოყენება OCR- ში. თანმიმდევრობის მოდელირების პრინციპების დეტალური ანალიზი, გრადიენტური პრობლემების გადაჭრა და ორმხრივი რნმ-ების უპირატესობები.\" />\n    <meta name=\"keywords\" content=\"RNN, LSTM, GRU, თანმიმდევრობის მოდელირება, გრადიენტის გაუჩინარება, ორმხრივი RNN, ყურადღების მექანიზმი, CRNN, OCR, OCR ტექსტის ამოცნობა, გამოსახულების ტექსტი, OCR ტექნოლოგია\" />\n    <meta property=\"og:title\" content=\"【ღრმა სწავლების OCR სერია·4】განმეორებითი ნერვული ქსელები და თანმიმდევრობის მოდელირება\" />\n    <meta property=\"og:description\" content=\"ჩაყვინთეთ RNN, LSTM, GRU გამოყენება OCR- ში. თანმიმდევრობის მოდელირების პრინციპების დეტალური ანალიზი, გრადიენტური პრობლემების გადაჭრა და ორმხრივი რნმ-ების უპირატესობები.\" />\n    <meta property=\"og:type\" content=\"article\" />\n    <meta property=\"og:url\" content=\"https://ocr.oldfish.cn/Article.aspx?id=81&slug=rnn-sequence-modeling&lang=zh-Hans&_translation_source=true\" />\n    <meta property=\"og:site_name\" content=\"OCR ტექსტის ამოცნობის ასისტენტი\" />\n    <meta name=\"twitter:card\" content=\"summary\" />\n    <meta name=\"twitter:title\" content=\"【ღრმა სწავლების OCR სერია·4】განმეორებითი ნერვული ქსელები და თანმიმდევრობის მოდელირება\" />\n    <meta name=\"twitter:description\" content=\"ჩაყვინთეთ RNN, LSTM, GRU გამოყენება OCR- ში. თანმიმდევრობის მოდელირების პრინციპების დეტალური ანალიზი, გრადიენტური პრობლემების გადაჭრა და ორმხრივი რნმ-ების უპირატესობები.\" />\n\n    \n    <script type=\"application/ld+json\">\n    {\n        \"@context\": \"https://schema.org\",\n        \"@type\": \"Article\",\n        \"headline\": \"【ღრმა სწავლების OCR სერია 4] განმეორებითი ნერვული ქსელი და თანმიმდევრობის მოდელირება\",\n        \"description\": \"ჩაყვინთეთ RNN, LSTM, GRU გამოყენება OCR- ში. თანმიმდევრობის მოდელირების პრინციპების დეტალური ანალიზი, გრადიენტური პრობლემების გადაჭრა და ორმხრივი რნმ-ების უპირატესობები。\",\n        \"publisher\": {\n            \"@type\": \"Organization\",\n            \"name\": \"OCR ტექსტის ამოცნობის ასისტენტი გუნდი\"\n        },\n        \"datePublished\": \"2025-08-19T06:32:05Z\",\n        \"dateModified\": \"2025-08-19T06:32:05Z\",\n        \"mainEntityOfPage\": {\n            \"@type\": \"WebPage\",\n            \"@id\": \"https://ocr.oldfish.cn/Article.aspx?id=81&slug=rnn-sequence-modeling&lang=zh-Hans&_translation_source=true\"\n        }\n    }\n    </script>\n    \n    <style>\n        .reading-progress { position: fixed; top: 0; left: 0; width: 0%; height: 3px; background: linear-gradient(90deg, #1764ff, #0d47a1); z-index: 9999; transition: width 0.2s ease; box-shadow: 0 1px 3px rgba(23, 100, 255, 0.3); }\n        .home-container.root { overflow: visible !important; }\n        .article-detail-container { background: #f5f7fc; min-height: calc(100vh - 60px); padding: 80px 0 60px; position: relative; overflow: visible; }\n        .article-detail-container .row { margin: 0 -12px; align-items: flex-start !important; min-height: 100vh !important; }\n        .article-detail-container .row > [class*=\"col-\"] { padding: 0 12px; box-sizing: border-box; min-width: 0; }\n        .article-detail-container .row > .col-lg-8, .article-detail-container .row > .col-xl-9 { overflow: hidden; }\n        .article-detail-container .row > .col-lg-4, .article-detail-container .row > .col-xl-3 { overflow: visible !important; height: 100% !important; min-height: 100% !important; position: static !important; display: block !important; transform: none !important; will-change: auto !important; contain: none !important; perspective: none !important; filter: none !important; }\n        .article-detail { background: #fff; border-radius: 8px; padding: 1.5rem; margin-bottom: 2rem; box-shadow: 0 2px 10px rgba(0,0,0,0.1); width: 100%; box-sizing: border-box; }\n        .article-title { font-size: 1.7rem; color: #1a1c1f; line-height: 1.3; margin-bottom: 1.5rem; font-weight: 600; }\n        .article-meta { display: flex; align-items: center; flex-wrap: nowrap; gap: 1.2rem; color: #6c757d; font-size: 0.88rem; margin-bottom: 2rem; padding: 1.2rem 1.4rem; background: linear-gradient(135deg, #f8f9fa 0%, #ffffff 100%); border-radius: 12px; border-left: 4px solid #1764ff; box-shadow: 0 2px 8px rgba(0,0,0,0.06); overflow-x: hidden; transition: all 0.3s ease; }\n        .article-meta:hover { box-shadow: 0 4px 16px rgba(0,0,0,0.1); transform: translateY(-1px); }\n        .article-meta .meta-item { display: flex; align-items: center; gap: 0.25rem; font-weight: 500; color: #495057; white-space: nowrap; flex-shrink: 1; transition: color 0.2s ease; cursor: default; }\n        .article-meta .meta-item:hover { color: #1764ff; }\n        .article-meta .meta-item:hover .meta-icon { transform: scale(1.1); filter: drop-shadow(0 2px 6px rgba(13, 71, 161, 0.25)); }\n        .meta-icon { display: inline-block; font-size: 16px; flex-shrink: 0; vertical-align: middle; transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1); margin-right: 4px; opacity: 0.9; }\n        .meta-item:hover .meta-icon { opacity: 1; }\n        .meta-item:hover { color: #1764ff; }\n        .meta-item { position: relative; }\n        .meta-item::after { content: ''; position: absolute; bottom: -2px; left: 0; width: 0; height: 2px; background: linear-gradient(90deg, #1764ff, #00d4ff); transition: width 0.3s ease; }\n        .meta-item:hover::after { width: 100%; }\n        .article-category, .read-time { display: inline-block; padding: 6px 12px; border-radius: 16px; font-size: 0.8rem; font-weight: 600; }\n        .article-category { background: linear-gradient(135deg, #1764ff, #0d47a1); color: white; text-decoration: none; text-transform: uppercase; letter-spacing: 0.5px; transition: all 0.3s ease; }\n        .article-category:hover { background: linear-gradient(135deg, #0d47a1, #1764ff); color: white; text-decoration: none; transform: translateY(-1px); box-shadow: 0 4px 12px rgba(23, 100, 255, 0.3); }\n        .read-time { background: #e3f2fd; color: #1565c0; border: 1px solid #90caf9; }\n        .article-tags { margin: 20px 0 1.5rem; }\n        .article-tags .tag { display: inline-block; background: #1764ff; color: #fff; padding: 4px 12px; border-radius: 12px; font-size: 0.8rem; margin: 0 8px 4px 0; }\n        .article-summary { background: #e3f2fd; border-left: 4px solid #1764ff; padding: 1rem 1.5rem; margin-bottom: 2rem; border-radius: 0 4px 4px 0; }\n        .article-content { line-height: 1.8; font-size: 16px; color: #333; word-wrap: break-word; }\n        .article-content h1, .article-content h2, .article-content h3, .article-content h4, .article-content h5, .article-content h6 { margin: 2rem 0 1rem; font-weight: 600; line-height: 1.25; color: #1a1c1f; }\n        .article-content h1 { font-size: 2rem; border-bottom: 2px solid #e9ecef; padding-bottom: 0.5rem; }\n        .article-content h2 { font-size: 1.5rem; border-bottom: 1px solid #e9ecef; padding-bottom: 0.3rem; }\n        .article-content h3 { font-size: 1.25rem; }\n        .article-content h4 { font-size: 1.1rem; }\n        .article-content h5, .article-content h6 { font-size: 1rem; }\n        .article-content ul, .article-content ol { margin: 1rem 0; padding-left: 2rem; }\n        .article-content ul ul, .article-content ol ol, .article-content ul ol, .article-content ol ul { margin: 0.5rem 0; }\n        .article-content blockquote { margin: 1rem 0; padding: 0.5rem 1rem; border-left: 4px solid #1764ff; background-color: #f8f9fa; color: #6c757d; font-style: italic; }\n        .article-content pre { background-color: #f8f9fa; border: 1px solid #e9ecef; border-radius: 6px; padding: 1rem; margin: 1rem 0; overflow-x: auto; font-family: 'Courier New', Courier, monospace; font-size: 0.9em; line-height: 1.4; }\n        .article-content pre code { background: none; border: none; padding: 0; color: inherit; }\n        .article-content p { margin: 1rem 0; }\n        .article-content p:first-child { margin-top: 0; }\n        .article-content p:last-child { margin-bottom: 0; }\n        .article-footer { margin-top: 4rem; padding: 2.5rem; background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%); border-radius: 12px; border: 1px solid #e9ecef; position: relative; overflow: hidden; }\n        .article-footer::before { content: ''; position: absolute; top: 0; left: 0; right: 0; height: 3px; background: linear-gradient(90deg, #1764ff, #17a2b8, #28a745); }\n        .article-share h6 { color: #495057; font-weight: 600; margin-bottom: 1.5rem; font-size: 16px; }\n        .share-buttons { display: flex; gap: 12px; flex-wrap: wrap; }\n        .share-buttons .btn { padding: 10px 20px; border-radius: 25px; font-weight: 500; font-size: 14px; border: none; transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1); box-shadow: 0 2px 8px rgba(0,0,0,0.1); display: inline-flex; align-items: center; gap: 8px; text-decoration: none; }\n        .btn-weibo { background: linear-gradient(135deg, #e6162d, #c41230); color: white; }\n        .btn-copy { background: #1764ff; color: white; }\n        .btn-print { background: linear-gradient(135deg, #17a2b8, #138496); color: white; }\n        .btn-weibo:hover { background: linear-gradient(135deg, #c41230, #a00e26); box-shadow: 0 4px 16px rgba(230, 22, 45, 0.3); }\n        .btn-copy:hover { background: #0d47a1; box-shadow: 0 4px 16px rgba(23, 100, 255, 0.3); }\n        .btn-print:hover { background: linear-gradient(135deg, #138496, #117a8b); box-shadow: 0 4px 16px rgba(23, 162, 184, 0.3); }\n        .btn-weibo:hover, .btn-copy:hover, .btn-print:hover { transform: translateY(-2px); color: white; }\n        .article-actions { display: flex; justify-content: flex-end; align-items: center; }\n        .related-articles { margin-top: 3rem; }\n        .related-article-item { background: #fff; border-radius: 8px; padding: 1.5rem; margin-bottom: 1rem; box-shadow: 0 2px 8px rgba(0,0,0,0.1); transition: transform 0.3s ease; }\n        .related-article-item:hover { transform: translateY(-2px); }\n        .sidebar-card { background: linear-gradient(135deg, rgba(255,255,255,0.9) 0%, rgba(255,255,255,0.8) 100%); backdrop-filter: blur(20px); border: 1px solid rgba(255,255,255,0.3); border-radius: 16px; padding: 1rem; margin-bottom: 0.5rem; box-shadow: 0 4px 20px rgba(0,0,0,0.08), inset 0 1px 0 rgba(255,255,255,0.2); overflow: visible; box-sizing: border-box; width: 100%; z-index: 10; }\n        .sidebar-card.fixed { box-shadow: 0 8px 32px rgba(0,0,0,0.12); }\n        .sidebar-card::before { content: ''; position: absolute; top: 0; left: 0; right: 0; height: 3px; background: linear-gradient(90deg, #00d4ff, #1764ff, #9c27b0); border-radius: 20px 20px 0 0; }\n        .sidebar-card h5 { color: #1a202c; padding-bottom: 0.8rem; border-bottom: 2px solid rgba(23, 100, 255, 0.2); font-weight: 700; font-size: 1.1rem; }\n        .sidebar { min-height: 100vh; position: relative !important; display: block !important; flex-direction: column !important; gap: 1rem; }\n        .toc-card { flex: 0 1 auto; max-height: 60vh; overflow-y: auto; }\n        .sidebar-card:not(.toc-card) { flex: 1 1 auto; overflow: visible; }\n        .recommended-item { padding: 0.75rem 0; border-bottom: 1px solid rgba(0,0,0,0.05); }\n        .recommended-item:last-child { border-bottom: none; }\n        .recommended-item h6 { margin: 0 0 0.5rem 0; font-size: 0.9rem; line-height: 1.4; }\n        .recommended-item h6 a { color: #1a1c1f; text-decoration: none; transition: color 0.3s ease; }\n        .recommended-item h6 a:hover { color: #1764ff; }\n        .recommended-item .text-muted { font-size: 0.75rem; margin: 0; }\n        .toc-list { list-style: none; padding: 0; margin: 0; max-height: none; overflow-y: visible; overflow-x: hidden; }\n        .toc-list::-webkit-scrollbar { width: 4px; }\n        .toc-list::-webkit-scrollbar-track { background: rgba(0,0,0,0.1); border-radius: 2px; }\n        .toc-list::-webkit-scrollbar-thumb { background: linear-gradient(135deg, #00d4ff, #1764ff); border-radius: 2px; }\n        .toc-list::-webkit-scrollbar-thumb:hover { background: linear-gradient(135deg, #1764ff, #0d47a1); }\n        .toc-list a { color: #4a5568; text-decoration: none; font-size: 13px; font-weight: 500; display: block; padding: 8px 12px; border-radius: 8px; transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1); position: relative; margin-bottom: 4px; border: 1px solid transparent; }\n        .toc-list a::before { content: ''; position: absolute; left: 0; top: 0; bottom: 0; width: 3px; background: linear-gradient(135deg, #00d4ff, #1764ff); border-radius: 0 12px 12px 0; transform: scaleY(0); transition: transform 0.3s ease; }\n        .toc-list a:hover { color: #1764ff; background: linear-gradient(135deg, rgba(0, 212, 255, 0.1), rgba(23, 100, 255, 0.1)); border-color: rgba(23, 100, 255, 0.2); transform: translateX(8px); box-shadow: 0 4px 12px rgba(0, 212, 255, 0.2); }\n        .toc-list a:hover::before, .toc-list a.active::before { transform: scaleY(1); }\n        .toc-list a.active { color: #1764ff; background: linear-gradient(135deg, rgba(0, 212, 255, 0.15), rgba(23, 100, 255, 0.15)); font-weight: 700; border-color: rgba(23, 100, 255, 0.3); transform: translateX(4px); box-shadow: 0 6px 16px rgba(0, 212, 255, 0.25); }\n        .toc-number { color: #1764ff; font-weight: 600; margin-right: 0.5rem; font-size: 0.9em; }\n        .toc-text { flex: 1; }\n        .toc-level-1 { font-weight: 700; font-size: 15px; }\n        .toc-level-2 { font-size: 14px; }\n        .toc-level-3 { font-size: 13px; opacity: 0.9; }\n        .toc-level-4, .toc-level-5, .toc-level-6 { font-size: 12px; opacity: 0.8; }\n        .article-content .formatted-list { margin: 1.5rem 0; padding-left: 0; list-style: none; }\n        .article-content .formatted-list.ordered { counter-reset: list-counter; }\n        .article-content .formatted-list.ordered li { counter-increment: list-counter; position: relative; margin: 1rem 0; padding: 1rem 1rem 1rem 3rem; background: linear-gradient(135deg, rgba(23, 100, 255, 0.03), rgba(0, 212, 255, 0.03)); border-left: 4px solid #1764ff; border-radius: 0 8px 8px 0; transition: all 0.3s ease; }\n        .article-content .formatted-list.ordered li::before { content: counter(list-counter); position: absolute; left: 0.8rem; top: 1rem; background: linear-gradient(135deg, #1764ff, #00d4ff); color: white; width: 1.5rem; height: 1.5rem; border-radius: 50%; display: flex; align-items: center; justify-content: center; font-weight: 600; font-size: 0.9rem; }\n        .article-content .formatted-list.ordered li:hover { background: linear-gradient(135deg, rgba(23, 100, 255, 0.06), rgba(0, 212, 255, 0.06)); transform: translateX(4px); box-shadow: 0 2px 8px rgba(23, 100, 255, 0.15); }\n        .article-content .formatted-list.unordered li { position: relative; margin: 0.8rem 0; padding: 0.8rem 1rem 0.8rem 2.5rem; background: linear-gradient(135deg, rgba(40, 167, 69, 0.03), rgba(25, 135, 84, 0.03)); border-left: 4px solid #28a745; border-radius: 0 6px 6px 0; transition: all 0.3s ease; }\n        .article-content .formatted-list.unordered li::before { content: \"•\"; position: absolute; left: 1rem; top: 0.8rem; color: #28a745; font-size: 1.2rem; font-weight: bold; }\n        .article-content .formatted-list.unordered li:hover { background: linear-gradient(135deg, rgba(40, 167, 69, 0.06), rgba(25, 135, 84, 0.06)); transform: translateX(4px); box-shadow: 0 2px 8px rgba(40, 167, 69, 0.15); }\n        .article-content .list-title { color: #1764ff; font-weight: 600; display: inline-block; margin-bottom: 0.3rem; }\n        .article-content .list-content { color: #2c3e50; line-height: 1.6; display: block; }\n        .article-content .inline-code { background: #f8f9fa; border: 1px solid #e9ecef; border-radius: 4px; padding: 2px 6px; font-family: 'Consolas', 'Monaco', 'Courier New', monospace; font-size: 0.9em; color: #e83e8c; }\n        .article-content .label-item { margin: 0.8rem 0; }\n        .article-content .label { color: #1764ff; font-weight: 600; }\n        .article-content .section-label { color: #2c3e50; font-weight: 600; margin: 1rem 0 0.5rem 0; font-size: 1.1em; border-left: 4px solid #1764ff; padding-left: 0.8rem; }\n        .article-content p, .article-content .content-paragraph { margin: 1.2rem 0; line-height: 1.8; text-align: justify; color: #2c3e50; font-size: 1rem; }\n        .article-content .content-paragraph { text-indent: 0; margin: 1rem 0; }\n        .article-content .indent-paragraph { text-indent: 2em; margin: 1.2rem 0; }\n        .article-content .text-indent-1 { text-indent: 2em; display: block; }\n        .article-content .text-indent-2 { text-indent: 4em; display: block; }\n        .article-content .text-indent-3 { text-indent: 6em; display: block; }\n        .article-content .network-config { background: linear-gradient(135deg, #f8f9fa, #e9ecef); border: 1px solid #dee2e6; border-radius: 6px; padding: 0.8rem 1rem; margin: 0.5rem 0; font-family: 'Consolas', 'Monaco', 'Courier New', monospace; font-size: 0.9rem; box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05); }\n        .article-content .config-label { color: #1764ff; font-weight: 600; }\n        .article-content .config-desc { color: #495057; }\n        .article-content .algorithm-step { background: linear-gradient(135deg, rgba(23, 100, 255, 0.05), rgba(0, 212, 255, 0.05)); border-left: 4px solid #1764ff; padding: 0.8rem 1rem; margin: 0.5rem 0; border-radius: 0 6px 6px 0; transition: all 0.3s ease; }\n        .article-content .step-number { color: #1764ff; font-weight: 700; margin-right: 0.5rem; }\n        .article-content .step-desc { color: #2c3e50; line-height: 1.5; }\n        .article-content table { width: 100%; border-collapse: collapse; margin: 1.5rem 0; background: white; border-radius: 8px; overflow: hidden; box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1); }\n        .article-content table th { background: linear-gradient(135deg, #1764ff, #00d4ff); color: white; padding: 1rem; text-align: left; font-weight: 600; }\n        .article-content table td { padding: 0.8rem 1rem; border-bottom: 1px solid #e9ecef; }\n        .article-content table tr:hover { background: rgba(23, 100, 255, 0.05); }\n        .article-content strong { color: #1764ff; font-weight: 600; }\n        .article-content em { color: #6c757d; font-style: italic; }\n        .article-content a { color: #1764ff; text-decoration: none; border-bottom: 1px solid transparent; transition: all 0.3s ease; }\n        .article-content a:hover { border-bottom-color: #1764ff; color: #0056b3; }\n        .article-content .definition-item { background: linear-gradient(135deg, rgba(40, 167, 69, 0.05), rgba(25, 135, 84, 0.05)); border-left: 4px solid #28a745; padding: 1rem; margin: 1rem 0; border-radius: 0 8px 8px 0; }\n        .article-content .definition-label { color: #28a745; font-weight: 600; display: block; margin-bottom: 0.5rem; }\n        .article-content .definition-content { color: #2c3e50; line-height: 1.6; }\n        .article-content .note-item { background: linear-gradient(135deg, rgba(255, 193, 7, 0.1), rgba(255, 193, 7, 0.05)); border: 1px solid rgba(255, 193, 7, 0.3); border-radius: 8px; padding: 1rem; margin: 1rem 0; position: relative; }\n        .article-content .note-item::before { content: \"💡\"; position: absolute; left: 1rem; top: 1rem; font-size: 1.2em; }\n        .article-content .note-label { color: #856404; font-weight: 600; margin-left: 2rem; }\n        .article-content .note-content { color: #2c3e50; line-height: 1.6; margin-left: 2rem; display: block; margin-top: 0.3rem; }\n        .article-content .math-formula { background: #f8f9fa; border: 1px solid #e9ecef; border-radius: 6px; padding: 1rem; margin: 1rem 0; text-align: center; font-family: 'Times New Roman', serif; font-size: 1.1em; color: #2c3e50; }\n        @media (min-width: 1400px) { .article-content { font-size: 17px; line-height: 1.8; } .article-detail { padding: 2.5rem; } }\n        @media (max-width: 1199px) and (min-width: 992px) { .article-detail { padding: 1.5rem; } .sidebar-card { padding: 1rem; } .toc-list a { font-size: 12px; padding: 6px 8px; } }\n        @media (max-width: 768px) {\n            .article-detail-container { padding: 60px 15px; }\n            .article-content .network-config, .article-content .algorithm-step, .article-content .definition-item, .article-content .note-item { margin: 0.5rem -1rem; border-radius: 0; }\n            .article-content .note-item::before { left: 0.5rem; }\n            .article-content .note-label, .article-content .note-content { margin-left: 1.5rem; }\n            .article-detail { padding: 1.5rem; }\n            .article-title { font-size: 1.6rem; line-height: 1.3; }\n            .article-meta { flex-wrap: wrap; gap: 1rem; padding: 1rem; font-size: 0.8rem; }\n            .article-meta .meta-item { flex-shrink: 1; }\n            .meta-icon { font-size: 14px; margin-right: 3px; }\n            .article-content { font-size: 15px; }\n            .article-content h1 { font-size: 1.6rem; }\n            .article-content h2 { font-size: 1.4rem; }\n            .article-content h3 { font-size: 1.2rem; }\n            .article-content h4 { font-size: 1.1rem; }\n            .article-content pre { padding: 0.75rem; font-size: 0.85em; overflow-x: auto; }\n            .article-content code { font-size: 0.85em; padding: 1px 4px; }\n            .sidebar { margin-top: 2rem; }\n            .sidebar-card { position: static; }\n            .toc-card { max-height: none; overflow-y: visible; }\n            .share-buttons { flex-direction: column; gap: 0.5rem; }\n            .share-buttons .btn { width: 100%; justify-content: center; }\n        }\n        @media print { body * { visibility: hidden; } .article-detail, .article-detail * { visibility: visible; } .article-detail { position: absolute; left: 0; top: 0; width: 100%; background: white; box-shadow: none; border: none; margin: 0; padding: 20px; } .article-meta { border: none; background: none; padding: 0; margin-bottom: 20px; } .article-footer, .reading-progress, .back-to-top { display: none !important; } .article-content { font-size: 12pt; line-height: 1.6; } .article-content h1, .article-content h2, .article-content h3, .article-content h4, .article-content h5, .article-content h6 { page-break-after: avoid; margin-top: 20pt; margin-bottom: 10pt; } .article-content pre, .article-content blockquote { page-break-inside: avoid; } }\n    </style>\n\n<script type=\"application/ld+json\">{\"@context\":\"https://schema.org\",\"@type\":\"BreadcrumbList\",\"itemListElement\":[{\"@type\":\"ListItem\",\"position\":1,\"name\":\"მთავარი\",\"item\":\"https://ocr.oldfish.cn/default.aspx\"},{\"@type\":\"ListItem\",\"position\":2,\"name\":\"ტექნიკური სტატიები\",\"item\":\"https://ocr.oldfish.cn/Articles.aspx\"},{\"@type\":\"ListItem\",\"position\":3,\"name\":\"სტატიის დეტალები\",\"item\":\"https://ocr.oldfish.cn/Article.aspx?id=81&slug=rnn-sequence-modeling&lang=zh-Hans&_translation_source=true\"}]}</script>\n<link rel=\"stylesheet\" href=\"/site/css/site.css\" type=\"text/css\" /><title>【ღრმა სწავლების OCR სერია·4】განმეორებითი ნერვული ქსელები და თანმიმდევრობის მოდელირება</title><meta http-equiv=\"Content-Language\" content=\"ka\" />\n</head>\r\n<body>\r\n    <div class=\"home-container root\">\r\n        <header class=\"v4_header_pc\">\r\n            <div class=\"header_pc_left\">\r\n                <a href=\"Default.aspx\" title=\"მთავარი | AI ტექსტის ინტელექტუალური ამოცნობა\" class=\"pc-logo-wrap ml-4 mr-5\">\r\n                    <picture>\r\n                        <source srcset=\"site/image/logo-s.png.webp\" type=\"image/webp\" />\r\n                        <img src=\"site/image/logo-s.png\" class=\"pc-logo\" style=\"height: 100%\" alt=\"OCR ტექსტის ამოცნობის ასისტენტი ოფიციალური ვებსაიტის ლოგო - AI ტექსტის ამოცნობის AI\" />\r\n                    </picture>\r\n                    <span style=\"font-size: 18px; font-weight: bold; color: #333;\">OCR ტექსტის ამოცნობის ასისტენტი</span>\r\n                </a>\r\n                <ul class=\"top-nav\" role=\"navigation\" aria-label=\"მთავარი ნავიგაცია\">\r\n                    <li><a class=\"color-default\" href=\"Default.aspx\" title=\"OCR ტექსტის ამოცნობის ასისტენტის მთავარი გვერდი\">მთავარი</a></li>\r\n                    <li><a class=\"color-default\" data-sub=\"product-sub-nav\" href=\"Detail.aspx\" title=\"OCR პროდუქტის ფუნქციის დანერგვა\">პროდუქტის მახასიათებლები:</a><div class=\"triangle\"></div>\r\n                    </li>\r\n                    <li><a class=\"color-default\" data-sub=\"experience-sub-nav\" href=\"Ocr.aspx\" title=\"განიცადეთ OCR ფუნქციები ინტერნეტით\">ონლაინ გამოცდილება</a><div class=\"triangle\"></div>\r\n                    </li>\r\n                    <li><a class=\"color-default\" data-sub=\"vip-sub-nav\" href=\"Version.aspx\" title=\"OCR წევრობის განახლების სერვისი\">წევრობის განახლებები</a><div class=\"triangle\"></div>\r\n                    </li>\r\n                    <li><a class=\"color-default\" href=\"Download.aspx\" title=\"ჩამოტვირთეთ OCR ტექსტის ამოცნობის ასისტენტი უფასოდ\">უფასო ჩამოტვირთვა</a></li>\r\n                    <li><a class=\"color-default\" data-sub=\"articles-sub-nav\" href=\"Articles.aspx\" title=\"OCR ტექნიკური სტატიები და ცოდნის გაზიარება\">ტექნოლოგიის გაზიარება</a><div class=\"triangle\"></div>\r\n                    </li>\r\n                    <li><a class=\"color-default\" data-sub=\"support-sub-nav\" href=\"FAQ.aspx\" title=\"OCR გამოყენების დახმარება და ტექნიკური მხარდაჭერა\">დახმარების ცენტრი</a><div class=\"triangle\"></div>\r\n                    </li>\r\n                </ul>\r\n                <div class=\"product-con\">\r\n                    <section id=\"product-sub-nav\" class=\"pls-nav-dropdown\" style=\"visibility: hidden; height: 0px; display: none;\">\r\n                        <div class=\"d-flex nav-drown-con\" style=\"height: 0px; transition: transform 0.2s ease-in-out 0s,opacity 0.2s ease-in-out 0s; opacity: 0; transform: translate(0px, -100%);\">\r\n                            <div class=\"prdocu-sub-left\">\r\n                                <div class=\"mr-auto\">\r\n                                    <picture>\r\n                                        <source class=\"p-icon\" srcset=\"site/image/icon/p_1.png.webp\" type=\"image/webp\" alt=\"\" aria-hidden=\"true\" />\r\n                                        <img class=\"p-icon\" src=\"site/image/icon/p_1.png\" alt=\"OCR პროდუქტის ფუნქციის ხატი\" aria-hidden=\"true\" loading=\"lazy\" />\r\n                                    </picture>\r\n                                    <div class=\"mt-4\">\r\n                                        <h3 class=\"h6\">OCR ტექსტის ამოცნობის ასისტენტი</h3>\r\n                                        <p class=\"color-gray fn14 mt-3 sub-left-desc\">ეფექტურობის გაუმჯობესება, ხარჯების შემცირება და ღირებულების შექმნა</p>\r\n                                        <p class=\"color-gray fn14 mt-3 sub-left-desc\">&nbsp;</p>\r\n                                        <p class=\"color-gray fn14 mt-3 sub-left-desc\">ინტელექტუალური აღიარება, მაღალსიჩქარიანი დამუშავება და ზუსტი გამომავალი</p>\r\n                                        <p class=\"color-gray fn14 mt-3 sub-left-desc\">ტექსტიდან ცხრილამდე, ფორმულებიდან თარგმანამდე</p>\r\n                                        <p class=\"color-gray fn14 mt-3 sub-left-desc\">გაამარტივეთ ყველა სიტყვის დამუშავება</p>\r\n                                        <a class=\"link block disa fn14 contact-business shake\" href=\"Detail.aspx\">შეიტყვეთ ფუნქციების შესახებ<span class=\"iconfont4 icon-xiangzuo gt\"></span></a>\r\n                                    </div>\r\n                                </div>\r\n                            </div>\r\n                            <div class=\"bg-white prdocu-sub-right\">\r\n                                <div>\r\n                                    <h2 class=\"text-base-color h6\">პროდუქტის მახასიათებლები:</h2>\r\n                                    <div class=\"mt-4 d-flex flex-wrap\">\r\n                                        <a href=\"Detail.aspx\" class=\"pro-item color-default\" title=\"გაეცანით OCR ასისტენტის ძირითადი ფუნქციების დეტალებს\">\r\n                                            <div class=\"letter-wrap\">\r\n                                                <h3 class=\"h6\">ძირითადი მახასიათებლები:</h3>\r\n                                                <span class=\"color-gray fn14\">შეიტყვეთ მეტი OCR ასისტენტის ძირითადი მახასიათებლებისა და ტექნიკური უპირატესობების შესახებ, 98% + ამოცნობის მაჩვენებლით</span>\r\n                                            </div>\r\n                                        </a>\r\n                                        <a href=\"Version.aspx\" class=\"pro-item color-default\" title=\"შეადარეთ განსხვავებები OCR ასისტენტის ვერსიებს შორის\">\r\n                                            <div class=\"letter-wrap\">\r\n                                                <h3 class=\"h6\">ვერსიის შედარება</h3>\r\n                                                <span class=\"color-gray fn14\">შეადარეთ უფასო ვერსიის, პირადი ვერსიის, პროფესიონალური ვერსიისა და საბოლოო ვერსიის ფუნქციური განსხვავებები დეტალურად</span>\r\n                                            </div>\r\n                                        </a>\r\n                                        <a href=\"FAQ.aspx\" class=\"pro-item color-default\" title=\"შეამოწმეთ OCR ასისტენტის ხშირად დასმული კითხვები\">\r\n                                            <div class=\"letter-wrap\">\r\n                                                <h3 class=\"h6\">პროდუქტის კითხვა-პასუხი</h3>\r\n                                                <span class=\"color-gray fn14\">სწრაფად გაეცანით პროდუქტის მახასიათებლებს, გამოყენების მეთოდებს და დეტალურ პასუხებს ხშირად დასმულ კითხვებზე</span>\r\n                                            </div>\r\n                                        </a>\r\n                                        <a href=\"Download.aspx\" class=\"pro-item color-default\" title=\"ჩამოტვირთეთ OCR ტექსტის ამოცნობის ასისტენტი უფასოდ\">\r\n                                            <div class=\"letter-wrap\">\r\n                                                <h3 class=\"h6\">სცადეთ უფასოდ</h3>\r\n                                                <span class=\"color-gray fn14\">ჩამოტვირთეთ და დააინსტალირეთ OCR ასისტენტი ახლა, რომ განიცადოთ ძლიერი ტექსტის ამოცნობის ფუნქცია უფასოდ</span>\r\n                                            </div>\r\n                                        </a>\r\n                                        <div style=\"width: 100%; margin: 15px 0;\">\r\n                                            <h4 class=\"h6\" style=\"color: #007cfa; margin-bottom: 10px;\">OCR ონლაინ აღიარება</h4>\r\n                                        </div>\r\n                                        <a href=\"Ocr.aspx?type=text_recognize\" class=\"pro-item color-default\" title=\"განიცადეთ ტექსტის უნივერსალური ამოცნობა ინტერნეტით\">\r\n                                            <div class=\"letter-wrap\">\r\n                                                <h3 class=\"h6\">უნივერსალური პერსონაჟების ამოცნობა</h3>\r\n                                                <span class=\"color-gray fn14\">მრავალენოვანი მაღალი სიზუსტის ტექსტის ინტელექტუალური მოპოვება, ბეჭდური და მრავალსაფეხურიანი რთული გამოსახულების ამოცნობის მხარდაჭერა</span>\r\n                                            </div>\r\n                                        </a>\r\n                                        <a href=\"Ocr.aspx?type=table\" class=\"pro-item color-default\">\r\n                                            <div class=\"letter-wrap\">\r\n                                                <h3 class=\"h6\">უნივერსალური მაგიდის იდენტიფიკაცია</h3>\r\n                                                <span class=\"color-gray fn14\">მაგიდის სურათების Excel ფაილებად გადაქცევა, რთული ცხრილის სტრუქტურების და გაერთიანებული უჯრედების ავტომატური დამუშავება</span>\r\n                                            </div>\r\n                                        </a>\r\n                                        <a href=\"Ocr.aspx?type=handwritten_ocr\" class=\"pro-item color-default\">\r\n                                            <div class=\"letter-wrap\">\r\n                                                <h3 class=\"h6\">ხელწერის ამოცნობა</h3>\r\n                                                <span class=\"color-gray fn14\">ჩინური და ინგლისური ხელნაწერი შინაარსის ინტელექტუალური აღიარება, დამხმარე საკლასო ნოტები, სამედიცინო ჩანაწერები და სხვა სცენარები</span>\r\n                                            </div>\r\n                                        </a>\r\n                                        <a href=\"Ocr.aspx?type=pdf2word\" class=\"pro-item color-default\">\r\n                                            <div class=\"letter-wrap\">\r\n                                                <h3 class=\"h6\">PDF კონვერტორიდან Word</h3>\r\n                                                <span class=\"color-gray fn14\">PDF დოკუმენტები სწრაფად გარდაიქმნება Word ფორმატში, სრულყოფილად ინარჩუნებს ორიგინალ განლაგებას და გრაფიკულ განლაგებას</span>\r\n                                            </div>\r\n                                        </a>\r\n                                    </div>\r\n                                </div>\r\n                            </div>\r\n                        </div>\r\n                    </section>\r\n                    <section id=\"experience-sub-nav\" class=\"pls-nav-dropdown solution-part\" style=\"visibility: hidden; height: 0px; display: none;\">\r\n                        <div class=\"d-flex nav-drown-con\" style=\"height: 0px; transition: transform 0.2s ease-in-out 0s,opacity 0.2s ease-in-out 0s; opacity: 0; transform: translate(0px, -100%);\">\r\n                            <div class=\"prdocu-sub-left\">\r\n                                <div class=\"mr-auto\">\r\n                                    <picture>\r\n                                        <source srcset=\"site/image/icon/p_4.png.webp\" type=\"image/webp\" />\r\n                                        <img class=\"p-icon\" src=\"site/image/icon/p_4.png\" alt=\"ონლაინ OCR გამოცდილების ცენტრის ხატი\" aria-hidden=\"true\" loading=\"lazy\" />\r\n                                    </picture>\r\n                                    <div class=\"mt-4\">\r\n                                        <h3 class=\"h6\">OCR ტექსტის ამოცნობის ასისტენტი</h3>\r\n                                        <p class=\"color-gray fn14 mt-3 sub-left-desc\">ტექსტი, ცხრილები, ფორმულები, დოკუმენტები, თარგმანები</p>\r\n                                        <p class=\"color-gray fn14 mt-3 sub-left-desc\">&nbsp;</p>\r\n                                        <p class=\"color-gray fn14 mt-3 sub-left-desc\">დაასრულეთ თქვენი სიტყვების დამუშავების ყველა საჭიროება სამი ნაბიჯით</p>\r\n                                        <p class=\"color-gray fn14 mt-3 sub-left-desc\">ეკრანის ანაბეჭდის → → აპების იდენტიფიცირება</p>\r\n                                        <p class=\"color-gray fn14 mt-3 sub-left-desc\">გაზარდეთ მუშაობის ეფექტურობა 300%-ით</p>\r\n                                        <a class=\"link block disa fn14 contact-business shake\" href=\"Ocr.aspx\">სცადეთ ახლა<span class=\"iconfont4 icon-xiangzuo gt\"></span></a>\r\n                                    </div>\r\n                                </div>\r\n                            </div>\r\n                            <div class=\"bg-white prdocu-sub-right\">\r\n                                <div>\r\n                                    <div class=\"mt-4 d-flex flex-wrap\">\r\n                                        <div style=\"width: 100%; margin-bottom: 15px;\">\r\n                                            <h4 class=\"h6\" style=\"color: #007cfa; margin-bottom: 10px;\">OCR ფუნქციის გამოცდილება</h4>\r\n                                        </div>\r\n                                        <a href=\"Ocr.aspx?type=index\" class=\"pro-item color-default\">\r\n                                            <div class=\"letter-wrap\">\r\n                                                <h3 class=\"h6\">სრული ფუნქციონირება</h3>\r\n                                                <span class=\"color-gray fn14\">განიცადეთ OCR ჭკვიანი ყველა ფუნქცია ერთ ადგილას, რათა სწრაფად იპოვოთ საუკეთესო გამოსავალი თქვენი საჭიროებისთვის</span>\r\n                                            </div>\r\n                                        </a>\r\n                                        <a href=\"Ocr.aspx?type=text_recognize\" class=\"pro-item color-default\">\r\n                                            <div class=\"letter-wrap\">\r\n                                                <h3 class=\"h6\">უნივერსალური პერსონაჟების ამოცნობა</h3>\r\n                                                <span class=\"color-gray fn14\">მრავალენოვანი მაღალი სიზუსტის ტექსტის ინტელექტუალური მოპოვება, ბეჭდური და მრავალსაფეხურიანი რთული გამოსახულების ამოცნობის მხარდაჭერა</span>\r\n                                            </div>\r\n                                        </a>\r\n                                        <a href=\"Ocr.aspx?type=table\" class=\"pro-item color-default\">\r\n                                            <div class=\"letter-wrap\">\r\n                                                <h3 class=\"h6\">უნივერსალური მაგიდის იდენტიფიკაცია</h3>\r\n                                                <span class=\"color-gray fn14\">მაგიდის სურათების Excel ფაილებად გადაქცევა, რთული ცხრილის სტრუქტურების და გაერთიანებული უჯრედების ავტომატური დამუშავება</span>\r\n                                            </div>\r\n                                        </a>\r\n                                        <a href=\"Ocr.aspx?type=handwritten_ocr\" class=\"pro-item color-default\">\r\n                                            <div class=\"letter-wrap\">\r\n                                                <h3 class=\"h6\">ხელწერის ამოცნობა</h3>\r\n                                                <span class=\"color-gray fn14\">ჩინური და ინგლისური ხელნაწერი შინაარსის ინტელექტუალური აღიარება, დამხმარე საკლასო ნოტები, სამედიცინო ჩანაწერები და სხვა სცენარები</span>\r\n                                            </div>\r\n                                        </a>\r\n                                        <a href=\"Ocr.aspx?type=pdf2word\" class=\"pro-item color-default\">\r\n                                            <div class=\"letter-wrap\">\r\n                                                <h3 class=\"h6\">PDF კონვერტორიდან Word</h3>\r\n                                                <span class=\"color-gray fn14\">PDF დოკუმენტები სწრაფად გარდაიქმნება Word ფორმატში, სრულყოფილად ინარჩუნებს ორიგინალ განლაგებას და გრაფიკულ განლაგებას</span>\r\n                                            </div>\r\n                                        </a>\r\n                                        <a href=\"Ocr.aspx?type=pdf2markdown\" class=\"pro-item color-default\">\r\n                                            <div class=\"letter-wrap\">\r\n                                                <h3 class=\"h6\">PDF კონვერტორიდან Markdown</h3>\r\n                                                <span class=\"color-gray fn14\">PDF დოკუმენტები ინტელექტუალურად გარდაიქმნება MD ფორმატში, ხოლო კოდის ბლოკები და ტექსტური სტრუქტურები ავტომატურად ოპტიმიზირებულია დამუშავებისთვის</span>\r\n                                            </div>\r\n                                        </a>\r\n                                        <div style=\"width: 100%; margin: 15px 0;\">\r\n                                            <h4 class=\"h6\" style=\"color: #007cfa; margin-bottom: 10px;\">დოკუმენტის დამუშავების ინსტრუმენტები</h4>\r\n                                        </div>\r\n                                        <a href=\"Ocr.aspx?type=word2pdf\" class=\"pro-item color-default\">\r\n                                            <div class=\"letter-wrap\">\r\n                                                <h3 class=\"h6\">Word დან PDF</h3>\r\n                                                <span class=\"color-gray fn14\">Word დოკუმენტები გარდაიქმნება PDF- ზე ერთი დაწკაპუნებით, სრულყოფილად ინარჩუნებს თავდაპირველ ფორმატს, შესაფერისია არქივირებისა და დოკუმენტის ოფიციალური გაზიარებისთვის</span>\r\n                                            </div>\r\n                                        </a>\r\n                                        <a href=\"Ocr.aspx?type=word2jpg\" class=\"pro-item color-default\">\r\n                                            <div class=\"letter-wrap\">\r\n                                                <h3 class=\"h6\">Word დან სურათი</h3>\r\n                                                <span class=\"color-gray fn14\">Word დოკუმენტის ინტელექტუალური კონვერტაცია JPG სურათზე, მრავალგვერდიანი დამუშავების მხარდაჭერა, ადვილად გაზიარება სოციალურ მედიაში</span>\r\n                                            </div>\r\n                                        </a>\r\n                                        <a href=\"Ocr.aspx?type=pdf2jpg\" class=\"pro-item color-default\">\r\n                                            <div class=\"letter-wrap\">\r\n                                                <h3 class=\"h6\">PDF კონვერტორიდან სურათი</h3>\r\n                                                <span class=\"color-gray fn14\">გადაიყვანეთ PDF დოკუმენტები JPG სურათებად მაღალი განმარტება, მხარი დაუჭირეთ სურათების დამუშავებას და საბაჟო რეზოლუციას</span>\r\n                                            </div>\r\n                                        </a>\r\n                                        <a href=\"Ocr.aspx?type=image2pdf\" class=\"pro-item color-default\">\r\n                                            <div class=\"letter-wrap\">\r\n                                                <h3 class=\"h6\">სურათი PDF-ში</h3>\r\n                                                <span class=\"color-gray fn14\">მრავალი სურათის გაერთიანება PDF დოკუმენტებში, მხარდაჭერის დახარისხება და გვერდის დაყენება</span>\r\n                                            </div>\r\n                                        </a>\r\n                                        <div style=\"width: 100%; margin: 15px 0;\">\r\n                                            <h4 class=\"h6\" style=\"color: #007cfa; margin-bottom: 10px;\">დეველოპერის ინსტრუმენტები</h4>\r\n                                        </div>\r\n                                        <a href=\"Tool.aspx?type=Json\" class=\"pro-item color-default\">\r\n                                            <div class=\"letter-wrap\">\r\n                                                <h3 class=\"h6\">JSON ფორმატირება</h3>\r\n                                                <span class=\"color-gray fn14\">ინტელექტუალურად გაალამაზეთ JSON კოდის სტრუქტურა, ხელი შეუწყეთ შეკუმშვას და გაფართოებას და ხელი შეუწყეთ განვითარებას და გამართვა</span>\r\n                                            </div>\r\n                                        </a>\r\n                                        <a href=\"Tool.aspx?type=Regex\" class=\"pro-item color-default\">\r\n                                            <div class=\"letter-wrap\">\r\n                                                <h3 class=\"h6\">რეგულარული გამოხატვა</h3>\r\n                                                <span class=\"color-gray fn14\">გადაამოწმეთ რეგულარული გამოხატვის შესატყვისი ეფექტები რეალურ დროში, საერთო შაბლონების ჩაშენებული ბიბლიოთეკით</span>\r\n                                            </div>\r\n                                        </a>\r\n                                        <a href=\"Tool.aspx?type=Encode\" class=\"pro-item color-default\">\r\n                                            <div class=\"letter-wrap\">\r\n                                                <h3 class=\"h6\">ტექსტის კოდირების კონვერტაცია</h3>\r\n                                                <span class=\"color-gray fn14\">იგი მხარს უჭერს კოდირების მრავალი ფორმატის კონვერტაციას, როგორიცაა Base64, URL და Unicode</span>\r\n                                            </div>\r\n                                        </a>\r\n                                        <a href=\"Tool.aspx?type=Diff\" class=\"pro-item color-default\">\r\n                                            <div class=\"letter-wrap\">\r\n                                                <h3 class=\"h6\">ტექსტის შესატყვისი და შერწყმა</h3>\r\n                                                <span class=\"color-gray fn14\">მონიშნეთ ტექსტის განსხვავებები და მხარი დაუჭირეთ ხაზის შედარებას და ინტელექტუალურ შერწყმას</span>\r\n                                            </div>\r\n                                        </a>\r\n                                        <a href=\"Tool.aspx?type=Color\" class=\"pro-item color-default\">\r\n                                            <div class=\"letter-wrap\">\r\n                                                <h3 class=\"h6\">ფერის ინსტრუმენტი</h3>\r\n                                                <span class=\"color-gray fn14\">RGB / HEX ფერის კონვერტაცია, ონლაინ ფერის ამომრჩევი, აუცილებელი ინსტრუმენტი წინა დონის განვითარებისთვის</span>\r\n                                            </div>\r\n                                        </a>\r\n                                        <a href=\"Tool.aspx?type=Count\" class=\"pro-item color-default\">\r\n                                            <div class=\"letter-wrap\">\r\n                                                <h3 class=\"h6\">სიტყვების რაოდენობა</h3>\r\n                                                <span class=\"color-gray fn14\">პერსონაჟების, ლექსიკისა და აბზაცების ინტელექტუალური დათვლა და ტექსტის განლაგების ავტომატურად ოპტიმიზაცია</span>\r\n                                            </div>\r\n                                        </a>\r\n                                        <a href=\"Tool.aspx?type=Timespan\" class=\"pro-item color-default\">\r\n                                            <div class=\"letter-wrap\">\r\n                                                <h3 class=\"h6\">Timestamp-ის გადაყვანა</h3>\r\n                                                <span class=\"color-gray fn14\">დრო გარდაიქმნება Unix timestamps- ში და მრავალი ფორმატისა და დროის ზონის პარამეტრებს მხარს უჭერს</span>\r\n                                            </div>\r\n                                        </a>\r\n                                        <a href=\"Tool.aspx?type=Calc\" class=\"pro-item color-default\">\r\n                                            <div class=\"letter-wrap\">\r\n                                                <h3 class=\"h6\">კალკულატორი ინსტრუმენტი</h3>\r\n                                                <span class=\"color-gray fn14\">ონლაინ სამეცნიერო კალკულატორი ძირითადი ოპერაციებისა და მოწინავე მათემატიკური ფუნქციების გამოთვლების მხარდაჭერით</span>\r\n                                            </div>\r\n                                        </a>\r\n                                    </div>\r\n                                </div>\r\n                            </div>\r\n                        </div>\r\n                    </section>\r\n                    <section id=\"articles-sub-nav\" class=\"pls-nav-dropdown solution-part\" style=\"visibility: hidden; height: 0px; display: none;\">\r\n                        <div class=\"d-flex nav-drown-con\" style=\"height: 0px; transition: transform 0.2s ease-in-out 0s,opacity 0.2s ease-in-out 0s; opacity: 0; transform: translate(0px, -100%);\">\r\n                            <div class=\"prdocu-sub-left\">\r\n                                <div class=\"mr-auto\">\r\n                                    <picture>\r\n                                        <source srcset=\"site/image/icon/p_4.png.webp\" type=\"image/webp\" />\r\n                                        <img class=\"p-icon\" src=\"site/image/icon/p_4.png\" alt=\"ტექნიკური გაზიარების ცენტრის ხატი\" aria-hidden=\"true\" loading=\"lazy\" />\r\n                                    </picture>\r\n                                    <div class=\"mt-4\">\r\n                                        <h3 class=\"h6\">OCR ტექნოლოგიის გაზიარება</h3>\r\n                                        <p class=\"color-gray fn14 mt-3 sub-left-desc\">ტექნიკური გაკვეთილები, განაცხადის შემთხვევები, ინსტრუმენტების რეკომენდაციები</p>\r\n                                        <p class=\"color-gray fn14 mt-3 sub-left-desc\">&nbsp;</p>\r\n                                        <p class=\"color-gray fn14 mt-3 sub-left-desc\">სრული სასწავლო გზა დამწყებიდან ოსტატობამდე</p>\r\n                                        <p class=\"color-gray fn14 mt-3 sub-left-desc\">პრაქტიკული შემთხვევები → ტექნიკური ანალიზის → ინსტრუმენტების პროგრამები</p>\r\n                                        <p class=\"color-gray fn14 mt-3 sub-left-desc\">გააძლიერეთ თქვენი გზა OCR ტექნოლოგიის გაუმჯობესებისკენ</p>\r\n                                        <a class=\"link block disa fn14 contact-business shake\" href=\"Articles.aspx\">სტატიების ნახვა<span class=\"iconfont4 icon-xiangzuo gt\"></span></a>\r\n                                    </div>\r\n                                </div>\r\n                            </div>\r\n                            <div class=\"bg-white prdocu-sub-right\">\r\n                                <div>\r\n                                    <h2 class=\"text-base-color h6\">ტექნოლოგიის გაზიარება</h2>\r\n                                    <div class=\"mt-4 d-flex flex-wrap\">\r\n                                        <a href=\"Articles.aspx\" class=\"pro-item color-default\" title=\"იხილეთ OCR ტექნიკური სტატიები\">\r\n                                            <div class=\"letter-wrap\">\r\n                                                <h3 class=\"h6\">ყველა სტატია</h3>\r\n                                                <span class=\"color-gray fn14\">დაათვალიერეთ OCR ტექნიკური სტატიები, რომლებიც მოიცავს ცოდნის სრულ ორგანოს ძირითადიდან მოწინავე</span>\r\n                                            </div>\r\n                                        </a>\r\n                                        \r\n                                        <a href=\"Articles.aspx?category=2\" class=\"pro-item color-default\" title=\"OCR ტექნიკური გაკვეთილები და დაწყებული სახელმძღვანელო\">\r\n                                            <div class=\"letter-wrap\">\r\n                                                <h3 class=\"h6\">დამატებითი სახელმძღვანელო</h3>\r\n                                                <span class=\"color-gray fn14\">შესავალიდან დაწყებული გამოცდილი OCR ტექნიკური გაკვეთილებით, დეტალური როგორ გიდები და პრაქტიკული გასეირნებები</span>\r\n                                            </div>\r\n                                        </a>\r\n                                        \r\n                                        <a href=\"Articles.aspx?category=3\" class=\"pro-item color-default\" title=\"OCR ტექნოლოგიის პრინციპები, ალგორითმები და პროგრამები\">\r\n                                            <div class=\"letter-wrap\">\r\n                                                <h3 class=\"h6\">ტექნოლოგიური კვლევა</h3>\r\n                                                <span class=\"color-gray fn14\">გამოიკვლიეთ OCR ტექნოლოგიის საზღვრები, პრინციპებიდან აპლიკაციებამდე და ღრმად გააანალიზეთ ძირითადი ალგორითმები</span>\r\n                                            </div>\r\n                                        </a>\r\n                                        \r\n                                        <a href=\"Articles.aspx?category=4\" class=\"pro-item color-default\" title=\"უახლესი მოვლენებისა და განვითარების ტენდენციები OCR ინდუსტრიაში\">\r\n                                            <div class=\"letter-wrap\">\r\n                                                <h3 class=\"h6\">ინდუსტრიის ტენდენციები</h3>\r\n                                                <span class=\"color-gray fn14\">სიღრმისეული ინფორმაცია OCR ტექნოლოგიის განვითარების ტენდენციების, ბაზრის ანალიზის, ინდუსტრიის დინამიკის და მომავალი პერსპექტივების შესახებ</span>\r\n                                            </div>\r\n                                        </a>\r\n                                        \r\n                                        <a href=\"Articles.aspx?category=5\" class=\"pro-item color-default\" title=\"OCR ტექნოლოგიის გამოყენების შემთხვევები სხვადასხვა ინდუსტრიაში\">\r\n                                            <div class=\"letter-wrap\">\r\n                                                <h3 class=\"h6\">გამოიყენეთ შემთხვევები:</h3>\r\n                                                <span class=\"color-gray fn14\">რეალურ სამყაროში გამოყენების შემთხვევები, გადაწყვეტილებები და OCR ტექნოლოგიის საუკეთესო პრაქტიკა სხვადასხვა ინდუსტრიაში გაზიარებულია</span>\r\n                                            </div>\r\n                                        </a>\r\n                                        \r\n                                        <a href=\"Articles.aspx?category=6\" class=\"pro-item color-default\" title=\"პროფესიონალური მიმოხილვები, შედარებითი ანალიზი და რეკომენდებული სახელმძღვანელო მითითებები OCR პროგრამული ინსტრუმენტების გამოყენებისთვის\">\r\n                                            <div class=\"letter-wrap\">\r\n                                                <h3 class=\"h6\">ინსტრუმენტის მიმოხილვა</h3>\r\n                                                <span class=\"color-gray fn14\">შეაფასეთ OCR ტექსტის ამოცნობის სხვადასხვა პროგრამული უზრუნველყოფა და ინსტრუმენტები და მიუთითეთ დეტალური ფუნქციის შედარება და შერჩევის წინადადებები</span>\r\n                                            </div>\r\n                                        </a>\r\n                                        \r\n                                    </div>\r\n                                </div>\r\n                            </div>\r\n                        </div>\r\n                    </section>\r\n                    <section id=\"vip-sub-nav\" class=\"pls-nav-dropdown solution-part\" style=\"visibility: hidden; height: 0px; display: none;\">\r\n                        <div class=\"d-flex nav-drown-con\" style=\"height: 0px; transition: transform 0.2s ease-in-out 0s,opacity 0.2s ease-in-out 0s; opacity: 0; transform: translate(0px, -100%);\">\r\n                            <div class=\"prdocu-sub-left\">\r\n                                <div class=\"mr-auto\">\r\n                                    <picture>\r\n                                        <source class=\"p-icon\" srcset=\"site/image/icon/p_1.png.webp\" type=\"image/webp\" alt=\"\" aria-hidden=\"true\" />\r\n                                        <img class=\"p-icon\" src=\"site/image/icon/p_1.png\" alt=\"წევრობის განახლების სერვისის ხატი\" aria-hidden=\"true\" loading=\"lazy\" />\r\n                                    </picture>\r\n                                    <div class=\"mt-4\">\r\n                                        <h3 class=\"h6\">წევრობის განახლების სერვისი</h3>\r\n                                        <p class=\"color-gray fn14 mt-3 sub-left-desc\">განბლოკეთ ყველა პრემიუმ ფუნქცია და ისიამოვნეთ ექსკლუზიური მომსახურებით</p>\r\n                                        <p class=\"color-gray fn14 mt-3 sub-left-desc\">&nbsp;</p>\r\n                                        <p class=\"color-gray fn14 mt-3 sub-left-desc\">ოფლაინ ამოცნობა, სურათების დამუშავება, შეუზღუდავი გამოყენება</p>\r\n                                        <p class=\"color-gray fn14 mt-3 sub-left-desc\">პრო → Ultimate → Enterprise</p>\r\n                                        <p class=\"color-gray fn14 mt-3 sub-left-desc\">თქვენს საჭიროებებზე მორგებული რამეა</p>\r\n                                        <a class=\"link block disa fn14 contact-business shake\" href=\"Version.aspx\">დეტალების ნახვა<span class=\"iconfont4 icon-xiangzuo gt\"></span></a>\r\n                                    </div>\r\n                                </div>\r\n                            </div>\r\n                            <div class=\"bg-white prdocu-sub-right\">\r\n                                <div>\r\n                                    <h2 class=\"text-base-color h6\" style=\"margin: inherit;\">წევრობის განახლებები</h2>\r\n                                    <div class=\"mt-4 d-flex flex-wrap\">\r\n                                        <a href=\"Version.aspx\" class=\"pro-item color-default\">\r\n                                            <div class=\"letter-wrap\">\r\n                                                <h3 class=\"h6\">წევრობის პრივილეგიები</h3>\r\n                                                <span class=\"color-gray fn14\">შეიტყვეთ მეტი გამოცემებს შორის განსხვავებების შესახებ და აირჩიეთ წევრობის დონე, რომელიც საუკეთესოდ მოგეწონებათ</span>\r\n                                            </div>\r\n                                        </a>\r\n                                        <a href=\"Upgrade.aspx\" class=\"pro-item color-default\">\r\n                                            <div class=\"letter-wrap\">\r\n                                                <h3 class=\"h6\">განაახლე ახლავე</h3>\r\n                                                <span class=\"color-gray fn14\">სწრაფად განაახლეთ თქვენი VIP წევრობა, რათა გახსნათ მეტი პრემიუმ ფუნქცია და ექსკლუზიური მომსახურება</span>\r\n                                            </div>\r\n                                        </a>\r\n                                        <a href=\"User.aspx\" class=\"pro-item color-default\">\r\n                                            <div class=\"letter-wrap\">\r\n                                                <h3 class=\"h6\">ჩემი ანგარიში</h3>\r\n                                                <span class=\"color-gray fn14\">მართეთ ანგარიშის ინფორმაცია, გამოწერის სტატუსი და გამოყენების ისტორია პარამეტრების პერსონალიზაციისთვის</span>\r\n                                            </div>\r\n                                        </a>\r\n                                    </div>\r\n                                </div>\r\n                            </div>\r\n                        </div>\r\n                    </section>\r\n                    <section id=\"support-sub-nav\" class=\"pls-nav-dropdown solution-part\" style=\"visibility: hidden; height: 0px; display: none;\">\r\n                        <div class=\"d-flex nav-drown-con\" style=\"height: 0px; transition: transform 0.2s ease-in-out 0s,opacity 0.2s ease-in-out 0s; opacity: 0; transform: translate(0px, -100%);\">\r\n                            <div class=\"prdocu-sub-left\">\r\n                                <div class=\"mr-auto\">\r\n                                    <picture>\r\n                                        <source class=\"p-icon\" srcset=\"site/image/icon/p_4.png.webp\" type=\"image/webp\" alt=\"\" aria-hidden=\"true\" />\r\n                                        <img class=\"p-icon\" src=\"site/image/icon/p_4.png\" alt=\"დახმარების ცენტრის მხარდაჭერის ხატი\" aria-hidden=\"true\" loading=\"lazy\" />\r\n                                    </picture>\r\n                                    <div class=\"mt-4\">\r\n                                        <h3 class=\"h6\">დახმარების ცენტრი</h3>\r\n                                        <p class=\"color-gray fn14 mt-3 sub-left-desc\">პროფესიონალური კლიენტების მომსახურება, დეტალური დოკუმენტაცია და სწრაფი რეაგირება</p>\r\n                                        <p class=\"color-gray fn14 mt-3 sub-left-desc\">&nbsp;</p>\r\n                                        <p class=\"color-gray fn14 mt-3 sub-left-desc\">ნუ პანიკაში, როდესაც პრობლემებს წააწყდებით</p>\r\n                                        <p class=\"color-gray fn14 mt-3 sub-left-desc\">პრობლემა → → მოგვარება</p>\r\n                                        <p class=\"color-gray fn14 mt-3 sub-left-desc\">გაამარტივეთ თქვენი გამოცდილება</p>\r\n                                        <a class=\"link block disa fn14 contact-business shake\" href=\"FAQ.aspx\">დახმარების მიღება<span class=\"iconfont4 icon-xiangzuo gt\"></span></a>\r\n                                    </div>\r\n                                </div>\r\n                            </div>\r\n                            <div class=\"bg-white prdocu-sub-right\">\r\n                                <div>\r\n                                    <h2 class=\"text-base-color h6\" style=\"margin: inherit;\">დახმარების ცენტრი</h2>\r\n                                    <div class=\"mt-4 d-flex flex-wrap\">\r\n                                        <a href=\"FAQ.aspx\" class=\"pro-item color-default\">\r\n                                            <div class=\"letter-wrap\">\r\n                                                <h3 class=\"h6\">ხშირად დასმული შეკითხვები</h3>\r\n                                                <span class=\"color-gray fn14\">სწრაფად უპასუხეთ მომხმარებლის საერთო კითხვებს და მიაწოდეთ დეტალური გამოყენების სახელმძღვანელო და ტექნიკური მხარდაჭერა</span>\r\n                                            </div>\r\n                                        </a>\r\n                                        <a href=\"AboutUs.aspx\" class=\"pro-item color-default\">\r\n                                            <div class=\"letter-wrap\">\r\n                                                <h3 class=\"h6\">ჩვენ შესახებ</h3>\r\n                                                <span class=\"color-gray fn14\">შეიტყვეთ OCR ტექსტის ამოცნობის ასისტენტის განვითარების ისტორიის, ძირითადი ფუნქციებისა და მომსახურების კონცეფციების შესახებ</span>\r\n                                            </div>\r\n                                        </a>\r\n                                        <a href=\"UserAgreement.aspx\" class=\"pro-item color-default\">\r\n                                            <div class=\"letter-wrap\">\r\n                                                <h3 class=\"h6\">სამომხმარებლო შეთანხმება</h3>\r\n                                                <span class=\"color-gray fn14\">მომსახურების დეტალური პირობები და მომხმარებლის უფლებები და მოვალეობები</span>\r\n                                            </div>\r\n                                        </a>\r\n                                        <a href=\"PrivacyPolicy.aspx\" class=\"pro-item color-default\">\r\n                                            <div class=\"letter-wrap\">\r\n                                                <h3 class=\"h6\">კონფიდენციალურობის ხელშეკრულება</h3>\r\n                                                <span class=\"color-gray fn14\">პერსონალური ინფორმაციის დაცვის პოლიტიკა და მონაცემთა უსაფრთხოების ზომები</span>\r\n                                            </div>\r\n                                        </a>\r\n                                        <a href=\"Status.aspx\" class=\"pro-item color-default\">\r\n                                            <div class=\"letter-wrap\">\r\n                                                <h3 class=\"h6\">სისტემის სტატუსი</h3>\r\n                                                <span class=\"color-gray fn14\">რეალურ დროში გლობალური საიდენტიფიკაციო კვანძების მუშაობის სტატუსის მონიტორინგი და სისტემის მუშაობის მონაცემების ნახვა</span>\r\n                                            </div>\r\n                                        </a>\r\n                                        <a href=\"javascript:void(0);\" class=\"pro-item color-default\" onclick=\"if(window.openCustomerService) window.openCustomerService(); else alert('გთხოვთ, დააწკაპუნოთ მარჯვნივ მდებარე ფანჯრის ხატულაზე, რათა დაუკავშირდეთ მომხმარებელთა მომსახურებას');\">\r\n                                            <div class=\"letter-wrap\">\r\n                                                <h3 class=\"h6\">დაუკავშირდით მომხმარებელთა მომსახურებას</h3>\r\n                                                <span class=\"color-gray fn14\">ონლაინ მომხმარებელთა მომსახურების მხარდაჭერა სწრაფად უპასუხებს თქვენს შეკითხვებსა და საჭიროებებს</span>\r\n                                            </div>\r\n                                        </a>\r\n                                    </div>\r\n                                </div>\r\n                            </div>\r\n                        </div>\r\n                    </section>\r\n                </div>\r\n            </div>\r\n            <div class=\"header_pc_right\" style=\"display: flex; align-items: center; justify-content: flex-end;\">\r\n                <div id=\"translate\"></div>\r\n                <div id=\"user-login-container\" data-url=\"User.ashx?op=userinfo\" style=\"white-space: nowrap; padding-right: 20px;\"></div>\r\n            </div>\r\n        </header>\r\n        <header class=\"v4_header_mob\">\r\n            <div class=\"container\">\r\n                <div class=\"row no-gutters justify-content-between align-items-center\">\r\n                    <a href=\"Default.aspx\" title=\"მთავარი | AI ტექსტის ინტელექტუალური ამოცნობა\" class=\"mob-logo-wrap\">\r\n                        <picture>\r\n                            <source srcset=\"site/image/logo-s.png.webp\" type=\"image/webp\" />\r\n                            <img src=\"site/image/logo-s.png\" class=\"pc-logo\" style=\"height: 32px; width: 32px\" alt=\"OCR ტექსტის ამოცნობის ასისტენტი მობილური ლოგო\" />\r\n                        </picture>\r\n                        <span style=\"font-size: 18px; font-weight: bold; color: #333;\">OCR ტექსტის ამოცნობის ასისტენტი</span>\r\n                    </a>\r\n                    <div class=\"right-menu\" role=\"button\" tabindex=\"0\" aria-label=\"სანავიგაციო მენიუს გახსნა\" data-toggle=\"collapse\" data-target=\"#mob-header-collapse\" aria-expanded=\"false\" aria-controls=\"mob-header-collapse\">\r\n                        <nav-products id=\"nav_products\">\r\n                            <nav-button class=\"submail-mdi-nav-btn\" style=\"width: 60px\">\r\n                                <label id=\"mdi-submail-nav-btn\">\r\n                                    <span class=\"mdi-nav-products-1\">\r\n                                        <span class=\"nav-products-icon\"></span>\r\n                                    </span>\r\n                                    <span class=\"mdi-nav-products-2\">\r\n                                        <span class=\"nav-products-icon\"></span>\r\n                                    </span>\r\n                                    <span class=\"mdi-nav-products-3\">\r\n                                        <span class=\"nav-products-icon\"></span>\r\n                                    </span>\r\n                                </label>\r\n                            </nav-button>\r\n                        </nav-products>\r\n                    </div>\r\n                </div>\r\n            </div>\r\n            <div class=\"mob-nav-content\">\r\n                <div class=\"sidebar-fix\">\r\n                    <div class=\"container\">\r\n                        <div class=\"row\">\r\n                            <div class=\"col-4 bg-white sidebar-fix-left\" style=\"border-right: 1px solid #e1e6ed\">\r\n                                <div class=\"mob-nav-item active\" data=\"0\">\r\n                                    <div class=\"nav-header\"><a href=\"Default.aspx\" class=\"nav-header-letter px-3\"><span>მთავარი</span></a></div>\r\n                                </div>\r\n                                <div class=\"mob-nav-item\" data=\"1\">\r\n                                    <div class=\"nav-header\"><a href=\"Detail.aspx\" class=\"nav-header-letter px-3\"><span>ფუნქცია</span></a></div>\r\n                                </div>\r\n                                <div class=\"mob-nav-item\" data=\"2\">\r\n                                    <div class=\"nav-header\"><a href=\"Ocr.aspx\" class=\"nav-header-letter px-3\"><span>გამოცდილება</span></a></div>\r\n                                </div>\r\n                                <div class=\"mob-nav-item\" data=\"3\">\r\n                                    <div class=\"nav-header\"><a href=\"Version.aspx\" class=\"nav-header-letter px-3\"><span>წევრი</span></a></div>\r\n                                </div>\r\n                                <div class=\"mob-nav-item\" data=\"4\">\r\n                                    <div class=\"nav-header\"><a href=\"Download.aspx\" class=\"nav-header-letter px-3\"><span>ჩამოტვირთვა</span></a></div>\r\n                                </div>\r\n                                <div class=\"mob-nav-item\" data=\"5\">\r\n                                    <div class=\"nav-header\"><a href=\"Articles.aspx\" class=\"nav-header-letter px-3\"><span>გაზიარება</span></a></div>\r\n                                </div>\r\n                                <div class=\"mob-nav-item\" data=\"6\">\r\n                                    <div class=\"nav-header\"><a href=\"FAQ.aspx\" class=\"nav-header-letter px-3\"><span>დახმარება</span></a></div>\r\n                                </div>\r\n                            </div>\r\n                            <div class=\"col-8 bg-white sidebar-fix-rigth\">\r\n                                <div class=\"sub-nav\">\r\n                                    <div class=\"sub-nav-item py-2 pb-3 px-4\">\r\n                                        <h2 class=\"h5\" style=\"margin: inherit;\">ეფექტური პროდუქტიულობის ინსტრუმენტები</h2>\r\n                                        <p class=\"color-gray fn14 mt-3 sub-left-desc\">ინტელექტუალური აღიარება, მაღალსიჩქარიანი დამუშავება და ზუსტი გამომავალი</p>\r\n                                        <p class=\"color-gray fn14 mt-3 sub-left-desc\">&nbsp;</p>\r\n                                        <p class=\"color-gray fn14 mt-3 sub-left-desc\">დოკუმენტების სრული გვერდის ამოცნობა 3 წამში</p>\r\n                                        <p class=\"color-gray fn14 mt-3 sub-left-desc\">98%+ ამოცნობის სიზუსტე</p>\r\n                                        <p class=\"color-gray fn14 mt-3 sub-left-desc\">მრავალენოვანი რეალურ დროში დამუშავება შეფერხების გარეშე</p>\r\n                                        <a class=\"link block disa fn14 contact-business shake\" href=\"Download.aspx\">ჩამოტვირთეთ გამოცდილება ახლავე<span class=\"iconfont4 icon-xiangzuo gt\"></span></a>\r\n                                    </div>\r\n                                </div>\r\n                                <div class=\"sub-nav\">\r\n                                    <div class=\"sub-nav-item py-3 px-4\">\r\n                                        <h2 class=\"h5\" style=\"margin: inherit;\">პროდუქტის მახასიათებლები:</h2>\r\n                                        <p class=\"color-gray fn14 mt-3 sub-left-desc\">AI ინტელექტუალური იდენტიფიკაცია, ერთჯერადი გადაწყვეტა</p>\r\n                                    </div>\r\n                                    <div class=\"sub-nav-item py-3 px-4\"><a href=\"Detail.aspx\">ფუნქციის დანერგვა</a></div>\r\n                                    <div class=\"sub-nav-item py-3 px-4\"><a href=\"Download.aspx\">პროგრამული უზრუნველყოფის ჩამოტვირთვა</a></div>\r\n                                    <div class=\"sub-nav-item py-3 px-4\"><a href=\"Version.aspx\">ვერსიის შედარება</a></div>\r\n                                    <div class=\"sub-nav-item py-3 px-4\"><a href=\"Ocr.aspx\">ონლაინ გამოცდილება</a></div>\r\n                                    <div class=\"sub-nav-item py-3 px-4\"><a href=\"Status.aspx\">სისტემის სტატუსი</a></div>\r\n                                </div>\r\n                                <div class=\"sub-nav\">\r\n                                    <div class=\"sub-nav-item py-3 px-4\">\r\n                                        <h2 class=\"h5\" style=\"margin: inherit;\">ონლაინ გამოცდილება</h2>\r\n                                        <p class=\"color-gray fn14 mt-3 sub-left-desc\">უფასო ონლაინ OCR ფუნქციის გამოცდილება</p>\r\n                                    </div>\r\n                                    <div class=\"sub-nav-item py-3 px-4\"><a href=\"Ocr.aspx\">სრული ფუნქციონირება</a></div>\r\n                                    <div class=\"sub-nav-item py-3 px-4\"><a href=\"Ocr.aspx?type=text_recognize\">სიტყვის ამოცნობა</a></div>\r\n                                    <div class=\"sub-nav-item py-3 px-4\"><a href=\"Ocr.aspx?type=table\">ცხრილის იდენტიფიკაცია</a></div>\r\n                                    <div class=\"sub-nav-item py-3 px-4\"><a href=\"Ocr.aspx?type=pdf2word\">PDF კონვერტორიდან Word</a></div>\r\n                                </div>\r\n                                <div class=\"sub-nav\">\r\n                                    <div class=\"sub-nav-item py-3 px-4\">\r\n                                        <h2 class=\"h5\" style=\"margin: inherit;\">წევრობის განახლებები</h2>\r\n                                        <p class=\"color-gray fn14 mt-3 sub-left-desc\">განბლოკეთ ყველა ფუნქცია და ისიამოვნეთ ექსკლუზიური მომსახურებით</p>\r\n                                    </div>\r\n                                    <div class=\"sub-nav-item py-3 px-4\"><a href=\"Version.aspx\">ალიანსის შეთავაზება წევრებს</a></div>\r\n                                    <div class=\"sub-nav-item py-3 px-4\"><a href=\"Upgrade.aspx\">დაუყოვნებლივ გააქტიურება</a></div>\r\n                                </div>\r\n                                <div class=\"sub-nav\">\r\n                                    <div class=\"sub-nav-item py-3 px-4\">\r\n                                        <h2 class=\"h5\" style=\"margin: inherit;\">პროგრამული უზრუნველყოფის ჩამოტვირთვა</h2>\r\n                                        <p class=\"color-gray fn14 mt-3 sub-left-desc\">ჩამოტვირთეთ პროფესიონალური OCR პროგრამა უფასოდ</p>\r\n                                    </div>\r\n                                    <div class=\"sub-nav-item py-3 px-4\"><a href=\"Download.aspx\">ჩამოტვირთვა ახლავე</a></div>\r\n                                    <div class=\"sub-nav-item py-3 px-4\"><a href=\"Version.aspx\">ვერსიის შედარება</a></div>\r\n                                </div>\r\n                                <div class=\"sub-nav\">\r\n                                    <div class=\"sub-nav-item py-3 px-4\">\r\n                                        <h2 class=\"h5\" style=\"margin: inherit;\">ტექნოლოგიის გაზიარება</h2>\r\n                                        <p class=\"color-gray fn14 mt-3 sub-left-desc\">OCR ტექნიკური სტატიები და ცოდნის გაზიარება</p>\r\n                                    </div>\r\n                                    <div class=\"sub-nav-item py-3 px-4\"><a href=\"Articles.aspx\">ყველა სტატია</a></div>\r\n                                    \r\n                                    <div class=\"sub-nav-item py-3 px-4\"><a href=\"Articles.aspx?category=2\">დამატებითი სახელმძღვანელო</a></div>\r\n                                    \r\n                                    <div class=\"sub-nav-item py-3 px-4\"><a href=\"Articles.aspx?category=3\">ტექნოლოგიური კვლევა</a></div>\r\n                                    \r\n                                    <div class=\"sub-nav-item py-3 px-4\"><a href=\"Articles.aspx?category=4\">ინდუსტრიის ტენდენციები</a></div>\r\n                                    \r\n                                    <div class=\"sub-nav-item py-3 px-4\"><a href=\"Articles.aspx?category=5\">გამოიყენეთ შემთხვევები:</a></div>\r\n                                    \r\n                                    <div class=\"sub-nav-item py-3 px-4\"><a href=\"Articles.aspx?category=6\">ინსტრუმენტის მიმოხილვა</a></div>\r\n                                    \r\n                                </div>\r\n                                <div class=\"sub-nav\">\r\n                                    <div class=\"sub-nav-item py-3 px-4\">\r\n                                        <h2 class=\"h5\" style=\"margin: inherit;\">დახმარების ცენტრი</h2>\r\n                                        <p class=\"color-gray fn14 mt-3 sub-left-desc\">პროფესიონალური კლიენტების მომსახურება, ინტიმური მომსახურება</p>\r\n                                    </div>\r\n                                    <div class=\"sub-nav-item py-3 px-4\"><a href=\"FAQ.aspx\">გამოიყენეთ დახმარება</a></div>\r\n                                    <div class=\"sub-nav-item py-3 px-4\"><a href=\"AboutUs.aspx\">ჩვენ შესახებ</a></div>\r\n                                    <div class=\"sub-nav-item py-3 px-4\"><a href=\"mailto:<EMAIL>\">დაუკავშირდით მომხმარებელთა მომსახურებას</a></div>\r\n                                    <div class=\"sub-nav-item py-3 px-4\"><a href=\"UserAgreement.aspx\">Მომსახურების პირობები</a></div>\r\n                                </div>\r\n                            </div>\r\n                        </div>\r\n                    </div>\r\n                </div>\r\n            </div>\r\n        </header>\r\n        <form method=\"post\" action=\"./Article.aspx?id=81&amp;slug=rnn-sequence-modeling&amp;lang=zh-Hans&amp;_translation_source=true\" id=\"aspnetForm\">\r\n<div class=\"aspNetHidden\">\r\n<input type=\"hidden\" name=\"__VIEWSTATE\" id=\"__VIEWSTATE\" value=\"Q9uUeUlUJtaRG68B9ZsvqxMA0nWMrG97wrg6XH6pF/PALpTNvdWvXETFldXabtYfiX/3jguNbdKAEx4O02Rz9ETQIoNHodxI3/t/Hx/oGKM=\" />\r\n</div>\r\n\r\n<div class=\"aspNetHidden\">\r\n\r\n\t<input type=\"hidden\" name=\"__VIEWSTATEGENERATOR\" id=\"__VIEWSTATEGENERATOR\" value=\"2173C2F0\" />\r\n</div>\r\n            <div>\r\n                \n\n<div class=\"reading-progress\" id=\"readingProgress\"></div>\n<div class=\"article-detail-container\">\n<div class=\"container\">\n<div class=\"row\">\n<div class=\"col-lg-8 col-xl-9 col-md-12\">\n                    \n                    <article class=\"article-detail\" id=\"articleDetail\" data-article-id=\"81\">\n                        <header class=\"article-header\">\n                            <h1 class=\"article-title\">【ღრმა სწავლების OCR სერია·4】განმეორებითი ნერვული ქსელები და თანმიმდევრობის მოდელირება</h1>\n                            <div class=\"article-meta\">\n                                <div class=\"meta-item\">\n                                    <span class=\"meta-icon\">📅</span>\n                                    <span>საფოსტო დრო: 2025-08-19</span>\n                                </div>\n                                <div class=\"meta-item\">\n                                    <span class=\"meta-icon\">👁️</span>\n                                    <span>კითხვა:<span class=\"view-count\">1200</span></span>\n                                </div>\n                                <div class=\"meta-item\">\n                                    <span class=\"meta-icon\">⏱️</span>\n                                    <span>დაახლოებით 50 წუთი (9819 სიტყვა)</span>\n                                </div>\n                                \n                                <div class=\"meta-item\">\n                                    <span class=\"meta-icon\">📁</span>\n                                    <span>კატეგორია: დამატებითი სახელმძღვანელო</span>\n                                </div>\n                                \n                            </div>\n                            <div class=\"article-summary\"><p>ჩაყვინთეთ RNN, LSTM, GRU გამოყენება OCR- ში. თანმიმდევრობის მოდელირების პრინციპების დეტალური ანალიზი, გრადიენტური პრობლემების გადაჭრა და ორმხრივი რნმ-ების უპირატესობები.</p></div>\n                        </header>\n                        <div class=\"article-content\" id=\"articleContent\">## შესავალი\r\n\r\nმორეციდივე ნერვული ქსელი (RNN) არის ნერვული ქსელის არქიტექტურა ღრმა სწავლებაში, რომელიც სპეციალიზირებულია თანმიმდევრობის მონაცემების დამუშავებაში. OCR ამოცანებში, ტექსტის ამოცნობა არსებითად თანმიმდევრობის კონვერტაციის პრობლემაა: გამოსახულების მახასიათებლების თანმიმდევრობის გადაკეთება ტექსტის პერსონაჟის თანმიმდევრობით. ეს სტატია გაეცნობს, თუ როგორ მუშაობს RNN, მისი ძირითადი ვარიანტები და მისი სპეციფიკური პროგრამები OCR– ში, რაც მკითხველს აძლევს ყოვლისმომცველ თეორიულ საფუძველს და პრაქტიკულ მითითებებს.\r\n\r\n## RNN საფუძვლები\r\n\r\n## ტრადიციული ნერვული ქსელების შეზღუდვები\r\n\r\nტრადიციული feedforward ნერვული ქსელები აქვს ფუნდამენტური შეზღუდვები დამუშავების თანმიმდევრობით მონაცემები. ეს ქსელები ვარაუდობენ, რომ შეყვანის მონაცემები დამოუკიდებელი და ჰომოგანიბუტია და არ შეუძლია თანმიმდევრობით ელემენტებს შორის დროებითი დამოკიდებულების ხელში ჩაგდება.\r\n\r\n**Feedforward ქსელის პრობლემები**:\r\n- ფიქსირებული შეყვანის და გამომავალი სიგრძე: ცვლადი სიგრძის თანმიმდევრობის დამუშავება შეუძლებელია\r\n- მეხსიერების უნარის ნაკლებობა: ისტორიული ინფორმაციის გამოყენების შეუძლებლობა\r\n- პარამეტრის გაზიარების სირთულე: იგივე ნიმუში უნდა ვისწავლოთ განმეორებით სხვადასხვა ადგილას\r\n- პოზიციური მგრძნობელობა: შეყვანის წესრიგის შეცვლამ შეიძლება გამოიწვიოს სრულიად განსხვავებული შედეგები\r\n\r\nეს შეზღუდვები განსაკუთრებით შესამჩნევია OCR ამოცანებში. ტექსტის თანმიმდევრობა ძალიან კონტექსტზეა დამოკიდებული და წინა პერსონაჟის ამოცნობის შედეგები ხშირად ხელს უწყობს შემდგომი სიმბოლოების ალბათობის დადგენას. მაგალითად, ინგლისური სიტყვის \"the\" იდენტიფიცირებისას, თუ \"th\" უკვე აღიარებულია, მაშინ შემდეგი პერსონაჟი სავარაუდოდ იქნება \"ე\".\r\n\r\n## RNN-ის ძირითადი იდეა\r\n\r\nRNN წყვეტს თანმიმდევრობის მოდელირების პრობლემას მარყუჟის შეერთების შემოღებით. ძირითადი იდეაა ქსელში \"მეხსიერების\" მექანიზმის დამატება, რათა ქსელმა შეძლოს ინფორმაციის შენახვა და გამოყენება წინა მომენტებიდან.\r\n\r\n** RNN-ის მათემატიკური წარმომადგენლობა**:\r\nამ დროისთვის, RNN- ის ფარული მდგომარეობა h_t, რომელიც განისაზღვრება მიმდინარე შეყვანით x_t და წინა მომენტის ფარული მდგომარეობით h_{t-1}:\r\n\r\nh_t = f(W_hh * h_{t-1} + W_xh * x_t + b_h)\r\n\r\nთაით):\r\n- W_hh არის წონის მატრიცა ფარული მდგომარეობიდან ფარული მდგომარეობაში\r\n- W_xh არის წონის მატრიცა ფარული მდგომარეობაში  \r\n- b_h არის მიკერძოებული ვექტორი\r\n- f არის აქტივაციის ფუნქცია (ჩვეულებრივ tanh ან ReLU)\r\n\r\nგამომავალი y_t გამოითვლება მიმდინარე ფარული მდგომარეობიდან:\r\ny_t = W_hy * h_t + b_y\r\n\r\n** RN– ების უპირატესობები **:\r\n- პარამეტრის გაზიარება: იგივე წონა გაზიარებულია ყველა დროისteps\r\n- ცვლადი სიგრძის თანმიმდევრობის დამუშავება: შეუძლია გაუმკლავდეს თვითნებური სიგრძის შეყვანის თანმიმდევრობას\r\n- მეხსიერების უნარი: ფარული სახელმწიფოები მოქმედებენ როგორც ქსელის \"მოგონებები\"\r\n- მოქნილი შეყვანა და გამომავალი: მხარს უჭერს ერთ-ერთს, ერთ-ერთს, მრავალ-ერთს, ბევრ რეჟიმს და სხვა\r\n\r\n## გაფართოებული ხედი RNN-ზე\r\n\r\nიმისათვის, რომ უკეთ გავიგოთ, თუ როგორ მუშაობს RNN, ჩვენ შეგვიძლია გავაფართოვოთ ისინი დროებით განზომილებაში. გაფართოებული RNN ჰგავს ღრმა საკვების ქსელის, მაგრამ ყველა დროის ნაბიჯები იზიარებს იგივე პარამეტრებს.\r\n\r\n**დროის მნიშვნელობა ვითარდება **:\r\n- მარტივი გასაგები ინფორმაციის ნაკადი: შესაძლებელია ნათლად დაინახოთ, თუ როგორ ხდება ინფორმაციის გადაცემა დროის ნაბიჯებს შორის\r\n- გრადიენტის გაანგარიშება: გრადიენტები გამოითვლება დროის Backpropagation (BPTT) ალგორითმის საშუალებით\r\n- პარალელიზაციის მოსაზრებები: მიუხედავად იმისა, რომ რნმ-ები არსებითად თანმიმდევრულია, გარკვეული ოპერაციების პარალელიზაცია შესაძლებელია\r\n\r\n**განვითარებული პროცესის მათემატიკური აღწერა **:\r\nსიგრძის T თანმიმდევრობისთვის, RNN აფართოებს შემდეგს:\r\nh_1 = f(W_xh * x_1 + b_h)\r\nh_2 = f(W_hh * h_1 + W_xh * x_2 + b_h)\r\nh_3 = f(W_hh * h_2 + W_xh * x_3 + b_h)\r\n...\r\nh_T = f(W_hh * h_{T-1} + W_xh * x_T + b_h)\r\n\r\nეს გაშლილი ფორმა ნათლად გვიჩვენებს, თუ როგორ ხდება ინფორმაციის გადაცემა დროის ნაბიჯებსა და როგორ ხდება პარამეტრების გაზიარება ყველა დროის ნაბიჯზე.\r\n\r\n## გრადიენტის გაუჩინარებისა და აფეთქების პრობლემა\r\n\r\n### პრობლემის ფესვი\r\n\r\nRN– ების მომზადებისას, ჩვენ ვიყენებთ Backpropagation- ს დროის (BPTT) ალგორითმის საშუალებით. ალგორითმმა უნდა გამოთვალოს ზარალის ფუნქციის გრადიენტი თითოეული დროის პარამეტრისთვის.\r\n\r\n**ჯაჭვის კანონი გრადიენტის გაანგარიშებისთვის **:\r\nროდესაც თანმიმდევრობა გრძელია, გრადიენტი უნდა იყოს უკან დახევა მრავალი დროის ნაბიჯით. ჯაჭვის წესის თანახმად, გრადიენტი შეიცავს წონის მატრიცის მრავალ გამრავლებას:\r\n\r\n∂L/∂W = Σ_t (∂L/∂y_t) * (∂y_t/∂h_t) * (∂h_t/∂W) * (∂h_t/∂W)\r\n\r\nსადაც ∂h_t/∂W მოიცავს ყველა შუალედური სახელმწიფოს პროდუქტს 1 მომენტამდე.\r\n\r\n** გრადიენტის გაუჩინარების მათემატიკური ანალიზი **:\r\nგანვიხილოთ გრადიენტების გამრავლება დროის ნაბიჯებს შორის:\r\n∂h_t/∂h_{t-1} = diag(f_prime(W_hh * h_{t-1} + W_xh * x_t + b_h)) * W_hh\r\n\r\nროდესაც თანმიმდევრობის სიგრძეა T, გრადიენტი შეიცავს T-1 ასეთი პროდუქტის ტერმინს. თუ W_hh მაქსიმალური eigenvalue 1-ზე ნაკლებია, უწყვეტი მატრიქსის გამრავლება გამოიწვევს გრადიენტურ ექსპონენციალურ დაშლას.\r\n\r\n** გრადიენტური აფეთქებების მათემატიკური ანალიზი **:\r\nპირიქით, როდესაც W_hh მაქსიმალური eigenvalue 1-ზე მეტია, გრადიენტი ექსპონენციალურად იზრდება:\r\n|| ∂h_t/∂h_1|| ≈ || W_hh|| ^{t-1}\r\n\r\nეს იწვევს არასტაბილურ ვარჯიშს და პარამეტრის გადაჭარბებულ განახლებებს.\r\n\r\n### გამოსავლის დეტალური ახსნა\r\n\r\nგრადიენტური ჩამოჭრა:\r\nგრადიენტური კლიპი არის ყველაზე პირდაპირი გზა გრადიენტური აფეთქებების გადასაჭრელად. როდესაც გრადიენტური ნორმა აღემატება დადგენილ ბარიერს, გრადიენტი მასშტაბურია ბარიერის ზომამდე. ეს მეთოდი მარტივი და ეფექტურია, მაგრამ მოითხოვს ბარიერების ფრთხილად შერჩევას. ბარიერი, რომელიც ძალიან მცირეა, შეზღუდავს სწავლის უნარს და ბარიერი, რომელიც ძალიან დიდია, ეფექტურად არ შეუშლის ხელს გრადიენტურ აფეთქებას.\r\n\r\n**წონის ინიციალიზაციის სტრატეგია **:\r\nწონის სწორად ინიციალიზაციამ შეიძლება შეამსუბუქოს გრადიენტური პრობლემები:\r\n- Xavier ინიციალიზაცია: წონის ცვალებადობა არის 1/n, სადაც n არის შეყვანის განზომილება\r\n- ის ინიციალიზაცია: წონის ცვალებადობა არის 2/n, რომელიც შესაფერისია ReLU აქტივაციის ფუნქციებისთვის\r\n- ორთოგონალური ინიციალიზაცია: ინიციალიზაცია წონის მატრიცა, როგორც ორთოგონალური მატრიცა\r\n\r\n**აქტივაციის ფუნქციების შერჩევა**:\r\nსხვადასხვა აქტივაციის ფუნქციას განსხვავებული გავლენა აქვს გრადიენტური გამრავლების შესახებ:\r\n- tanh: გამომავალი დიაპაზონი [-1,1], გრადიენტი მაქსიმალური მნიშვნელობა 1\r\n- ReLU: შეუძლია შეამსუბუქოს გრადიენტური გაუჩინარება, მაგრამ შეიძლება გამოიწვიოს ნეირონული სიკვდილი\r\n- Leaky ReLU: წყვეტს ReLU- ს ნეირონული სიკვდილის პრობლემას\r\n\r\n**არქიტექტურული გაუმჯობესება**:\r\nყველაზე ფუნდამენტური გამოსავალი იყო RNN არქიტექტურის გაუმჯობესება, რამაც გამოიწვია LSTM და GRU- ს გაჩენა. ეს არქიტექტურა მიმართავს გრადიენებს gating მექანიზმებისა და სპეციალიზებული ინფორმაციის ნაკადის დიზაინის საშუალებით.\r\n\r\n## LSTM: მეხსიერების გრძელვადიანი ქსელი\r\n\r\n### დიზაინის მოტივაცია LSTM- ისთვის\r\n\r\nLSTM (გრძელვადიანი მოკლევადიანი მეხსიერება) არის RNN ვარიანტი, რომელიც შემოთავაზებულია Hochreiter და Schmidhuber– ის მიერ 1997 წელს, სპეციალურად შექმნილია გრადიენტური გაუჩინარებისა და საქალაქთაშორისო სწავლის სირთულეების პრობლემის გადასაჭრელად.\r\n\r\n**LSTM-ის ძირითადი ინოვაციები**:\r\n- უჯრედის მდგომარეობა: ინფორმაციის \"გზატკეცილი\" ემსახურება, რაც საშუალებას აძლევს ინფორმაციას პირდაპირ დროის ნაბიჯებს შორის\r\n- Gating მექანიზმი: ინფორმაციის შემოდინების, შენახვისა და გამომუშავების ზუსტი კონტროლი\r\n- დისოციირებული მეხსიერების მექანიზმები: განასხვავებს მოკლევადიან მეხსიერებას (ფარული მდგომარეობა) და გრძელვადიან მეხსიერებას (ფიჭური მდგომარეობა)\r\n\r\n**როგორ წყვეტს LSTM გრადიენტურ პრობლემებს**:\r\nLSTM განაახლებს უჯრედის მდგომარეობას დანამატის საშუალებით და არა მულტიპლიკაციური ოპერაციებით, რაც საშუალებას აძლევს გრადიენებს უფრო ადვილად მიედინება ადრე დროის ნაბიჯებზე. განახლებული ფორმულა უჯრედის მდგომარეობისთვის:\r\n\r\nC_t = f_t ⊙ C_{t-1} + i_t ⊙ C_tilde_t\r\n\r\nაქ გამოიყენება ელემენტის დონის დამატება, ტრადიციული RNA- ებში უწყვეტი მატრიქსის გამრავლების თავიდან ასაცილებლად.\r\n\r\n### LSTM არქიტექტურის დეტალური ახსნა\r\n\r\nLSTM შეიცავს სამ გისოს ერთეულს და უჯრედულ მდგომარეობას:\r\n\r\n**1. დაივიწყე კარიბჭე **:\r\nდავიწყების კარიბჭე გადაწყვეტს, თუ რა ინფორმაცია უნდა გადააგდოთ უჯრედის მდგომარეობიდან:\r\nf_t = σ (W_f · [h_{t-1}, x_t] + b_f)\r\n\r\nOblivion კარიბჭის გამომუშავება არის მნიშვნელობა 0-დან 1-ს შორის, 0 \"სრულიად დავიწყებული\" და 1 \"მთლიანად შენარჩუნებულია\". ეს კარიბჭე საშუალებას აძლევს LSTM- ს შერჩევით დაივიწყოს უმნიშვნელო ისტორიული ინფორმაცია.\r\n\r\n**2. შეყვანის კარიბჭე **:\r\nშეყვანის კარიბჭე განსაზღვრავს რა ახალი ინფორმაცია ინახება უჯრედის მდგომარეობაში:\r\ni_t = σ(W_i · [h_{t-1}, x_t] + b_i)\r\nC_tilde_t = tanh(W_C · [h_{t-1}, x_t] + b_C)\r\n\r\nშეყვანის კარიბჭე შედგება ორი ნაწილისაგან: სიგმოიდური ფენა განსაზღვრავს რომელი მნიშვნელობების განახლებას, ხოლო ტანჰის ფენა ქმნის კანდიდატის ღირებულების ვექტორებს.\r\n\r\n**3. უჯრედის სტატუსის განახლება **:\r\nშეუთავსეთ დავიწყებული კარიბჭის შედეგები და შეყვანის კარიბჭე უჯრედის მდგომარეობის განახლებისთვის:\r\nC_t = f_t ⊙ C_{t-1} + i_t ⊙ C_tilde_t\r\n\r\nეს ფორმულა არის LSTM- ის ცენტრში: შერჩევითი შეკავება და ინფორმაციის განახლება ელემენტარული დონის გამრავლებისა და დამატებითი ოპერაციების საშუალებით.\r\n\r\n**4. გამომავალი კარიბჭე **:\r\nგამომავალი კარიბჭე განსაზღვრავს უჯრედის რომელ ნაწილებს გამოიმუშავებს:\r\no_t = σ (W_o · [h_{t-1}, x_t] + b_o)\r\nh_t = o_t ⊙ tanh(C_t)\r\n\r\nგამომავალი კარიბჭე აკონტროლებს უჯრედის მდგომარეობის რომელ ნაწილებს ახდენს მიმდინარე გამომავალზე.\r\n\r\n### LSTM ვარიანტები\r\n\r\n**Peephole LSTM**:\r\nსტანდარტული LSTM- ის საფუძველზე, Peephole LSTM საშუალებას აძლევს gating ერთეულს ნახოს უჯრედის მდგომარეობა:\r\nf_t = σ (W_f · [C_{t-1}, h_{t-1}, x_t] + b_f)\r\ni_t = σ(W_i · [C_{t-1}, h_{t-1}, x_t] + b_i)\r\no_t = σ (W_o · [C_t, h_{t-1}, x_t] + b_o)\r\n\r\n**შეწყვილებული LSTM**:\r\nდააწყვილეთ დავიწყებული კარიბჭე შეყვანის კარიბჭესთან, რათა უზრუნველყოთ, რომ დავიწყებული ინფორმაციის რაოდენობა ტოლია შეყვანილი ინფორმაციის რაოდენობაზე:\r\nf_t = σ (W_f · [h_{t-1}, x_t] + b_f)\r\ni_t = 1 - f_t\r\n\r\nეს დიზაინი ამცირებს პარამეტრების რაოდენობას LSTM– ის ძირითადი ფუნქციონირების შენარჩუნებისას.\r\n\r\n## GRU: კარიბჭის მარყუჟის განყოფილება\r\n\r\n## # GRU-ს გამარტივებული დიზაინი\r\n\r\nGRU (Gated Recurrent Unit) არის LSTM– ის გამარტივებული ვერსია, რომელიც შემოთავაზებულია Cho et al. 2014 წელს. GRU ამარტივებს LSTM- ის სამ კარიბჭეს ორ კარიბჭესთან და აერთიანებს ფიჭურ მდგომარეობას და ფარულ მდგომარეობას.\r\n\r\n**GRU-ს დიზაინის ფილოსოფია**:\r\n- გამარტივებული სტრუქტურა: ამცირებს კარების რაოდენობას და ამცირებს გამოთვლების სირთულეს\r\n- შეინარჩუნეთ შესრულება: გაამარტივეთ LSTM- შესადარებელი შესრულების შენარჩუნებისას\r\n- მარტივი განხორციელება: მარტივი მშენებლობა საშუალებას იძლევა მარტივი განხორციელება და ექსპლუატაციაში გაშვება\r\n\r\n## Gating მექანიზმი GRU-ში\r\n\r\n**1. კარიბჭის გადატვირთვა **:\r\nr_t = σ (W_r · [h_{t-1}, x_t] + b_r)\r\n\r\nგადატვირთვის კარიბჭე განსაზღვრავს, თუ როგორ უნდა დააკავშიროთ ახალი შეყვანა წინა მეხსიერებასთან. როდესაც გადატვირთვის კარიბჭე უახლოვდება 0-ს, მოდელი უგულებელყოფს წინა ფარულ მდგომარეობას.\r\n\r\n**2. კარიბჭის განახლება **:\r\nz_t = σ (W_z · [h_{t-1}, x_t] + b_z)\r\n\r\nგანახლების კარიბჭე განსაზღვრავს რამდენი წარსული ინფორმაციის შენახვა და რამდენი ახალი ინფორმაციის დამატება. ის აკონტროლებს როგორც დავიწყებას, ასევე შეყვანას, მსგავსია LSTM– ში დავიწყებისა და შეყვანის კარიბჭის კომბინაციის მსგავსი.\r\n\r\n**3. კანდიდატის დამალული სტატუსი**:\r\nh_tilde_t = tanh(W_h · [r_t ⊙ h_{t-1}, x_t] + b_h)\r\n\r\nკანდიდატი ფარული სახელმწიფოები იყენებენ გადატვირთვის კარიბჭეს წინა ფარული სახელმწიფოს ეფექტების გასაკონტროლებლად.\r\n\r\n**4. საბოლოო ფარული სახელმწიფო **:\r\nh_t = (1 - z_t) ⊙ h_{t-1} + z_t ⊙ h_tilde_t\r\n\r\nსაბოლოო ფარული მდგომარეობა არის წინა ფარული სახელმწიფოს საშუალო შეწონილი და კანდიდატის ფარული სახელმწიფო.\r\n\r\n### GRU vs LSTM სიღრმისეული შედარება\r\n\r\n**პარამეტრების რაოდენობის შედარება **:\r\n- LSTM: 4 წონის მატრიცები (კარიბჭის დავიწყება, შეყვანის კარიბჭე, კანდიდატის ღირებულება, გამომავალი კარიბჭე)\r\n- GRU: 3 წონის მატრიცა (გადატვირთვის კარიბჭე, განახლების კარიბჭე, კანდიდატის მნიშვნელობა)\r\n- GRU- ს პარამეტრების რაოდენობა LSTM- ის დაახლოებით 75% -ს შეადგენს\r\n\r\n**გამოთვლითი სირთულის შედარება **:\r\n- LSTM: მოითხოვს 4 კარიბჭის შედეგების გაანგარიშებას და უჯრედის მდგომარეობის განახლებებს\r\n- GRU: უბრალოდ გამოთვალეთ 2 კარიბჭის გამომუშავება და ფარული სტატუსის განახლებები\r\n- GRU, როგორც წესი, 20-30% -ით უფრო სწრაფია, ვიდრე LSTM\r\n\r\n** შესრულების შედარება **:\r\n- უმეტეს დავალებებზე, GRU და LSTM ასრულებენ შესადარებელ\r\n- LSTM შეიძლება ოდნავ უკეთესი იყოს, ვიდრე GRU გრძელი თანმიმდევრობის ამოცანებზე\r\n- GRU უკეთესი არჩევანია იმ შემთხვევებში, როდესაც კომპიუტერული რესურსები შეზღუდულია\r\n\r\n## ორმხრივი RN-ები\r\n\r\n## ორმხრივი დამუშავების აუცილებლობა\r\n\r\nმრავალი თანმიმდევრობის სამოდელო დავალებაში, დღევანდელი მომენტის გამომუშავება ეყრდნობა არა მხოლოდ წარსულს, არამედ მომავალ ინფორმაციას. ეს განსაკუთრებით მნიშვნელოვანია OCR ამოცანებში, სადაც პერსონაჟების ამოცნობა ხშირად მოითხოვს მთელი სიტყვის ან წინადადების კონტექსტის გათვალისწინებას.\r\n\r\n** ცალმხრივი RN-ების შეზღუდვები **:\r\n- შესაძლებელია მხოლოდ ისტორიული ინფორმაციის გამოყენება, მომავალი კონტექსტის მიღება არ შეიძლება\r\n- შეზღუდული შესრულება გარკვეულ ამოცანებში, განსაკუთრებით ისეთებიც, რომლებიც გლობალურ ინფორმაციას საჭიროებს\r\n- ორაზროვანი პერსონაჟების შეზღუდული აღიარება\r\n\r\n** ორმხრივი დამუშავების უპირატესობები **:\r\n- სრული კონტექსტური ინფორმაცია: გამოიყენეთ როგორც წარსული, ისე მომავალი ინფორმაცია\r\n- უკეთესი disambiguation: Disambiguation კონტექსტური ინფორმაციით\r\n- გაუმჯობესებული ამოცნობის სიზუსტე: უკეთესად შესრულებულია თანმიმდევრობის ანოტაციის დავალებების უმეტესობაზე\r\n\r\n## ბიდირციული LSTM არქიტექტურა\r\n\r\nBidirectional LSTM შედგება ორი LSTM ფენისგან:\r\n- წინ LSTM: პროცესის თანმიმდევრობა მარცხნიდან მარჯვნივ\r\n- უკან LSTM: პროცესის თანმიმდევრობა მარჯვნივ მარცხნივ\r\n\r\n**მათემატიკური წარმომადგენლობა **:\r\nh_forward_t = LSTM_forward(x_t, h_forward_{t-1})\r\nh_backward_t = LSTM_backward(x_t, h_backward_{t+1})\r\nh_t = [h_forward_t; h_backward_t] # ნაკერი წინ და უკან ფარული სახელმწიფოები\r\n\r\n**სასწავლო პროცესი**:\r\n1. Forward LSTM ამუშავებს თანმიმდევრობას ნორმალური თანმიმდევრობით\r\n2. ჩამორჩენილი LSTM ამუშავებს თანმიმდევრობას საპირისპირო მიზნით\r\n3. ყოველ ჯერზე დააკავშირეთ ფარული სახელმწიფოები ორივე მიმართულებით\r\n4. გამოიყენეთ spliced სახელმწიფო პროგნოზირებისთვის\r\n\r\n** უპირატესობები და უარყოფითი მხარეები **:\r\nუპირატესობა:\r\n- სრული კონტექსტური ინფორმაცია\r\n- უკეთესი შესრულება\r\n- სიმეტრიის მკურნალობა\r\n\r\nდაბალი პოზიცია:\r\n- გათვლების სირთულის გაორმაგება\r\n- არ შეიძლება დამუშავდეს რეალურ დროში (მოითხოვს სრულ თანმიმდევრობას)\r\n- გაზრდილი მეხსიერების მოთხოვნები\r\n\r\n# # თანმიმდევრობის მოდელირების პროგრამები OCR- ში\r\n\r\n## # ტექსტის ხაზის ამოცნობის დეტალური ახსნა\r\n\r\nOCR სისტემებში, ტექსტის ხაზის ამოცნობა არის თანმიმდევრობის მოდელირების ტიპიური პროგრამა. ეს პროცესი გულისხმობს გამოსახულების მახასიათებლების თანმიმდევრობის პერსონაჟების თანმიმდევრობით გადაქცევას.\r\n\r\n**პრობლემის მოდელირება**:\r\n- შეყვანა: სურათის ფუნქციის თანმიმდევრობა X = {x_1, x_2, ..., x_T}\r\n- გამომავალი: პერსონაჟის თანმიმდევრობა Y = {y_1, y_2, ..., y_S}\r\n- გამოწვევა: შეყვანის თანმიმდევრობის სიგრძე T და გამომავალი თანმიმდევრობის სიგრძე S ხშირად არ არის თანაბარი\r\n\r\n** CRNN არქიტექტურის გამოყენება ტექსტის ხაზის ამოცნობაში **:\r\nCRNN (კონვოლუციური მორეციდივე ნერვული ქსელი) არის ერთ-ერთი ყველაზე წარმატებული არქიტექტურა OCR- ში:\r\n\r\n1. ** CNN მხატვრული მოპოვების ფენა **:\r\n   - ამონაწერი გამოსახულების მახასიათებლები კონვოლუციური ნერვული ქსელების გამოყენებით\r\n   - 2D სურათის მახასიათებლების 1D ფუნქციის თანმიმდევრობად გადაქცევა\r\n   - დროის ინფორმაციის უწყვეტობის შენარჩუნება\r\n\r\n2. **RNN თანმიმდევრობის მოდელირების ფენა**:\r\n   - მოდელის მახასიათებლების თანმიმდევრობა ორმხრივი LSTM- ების გამოყენებით\r\n   - პერსონაჟების კონტექსტური დამოკიდებულების გადაღება\r\n   - გამომავალი პერსონაჟის ალბათობის განაწილება ყოველ ჯერზე\r\n\r\n3. ** CTC გასწორების ფენა **:\r\n   - მისამართები შეყვანის/გამომავალი თანმიმდევრობის სიგრძის შეუსაბამობები\r\n   - არ არის საჭირო პერსონაჟის დონის გასწორების ზომები\r\n   - ბოლოდან ბოლომდე ტრენინგი\r\n\r\n** ფუნქციის მოპოვების კონვერსია თანმიმდევრობით**:\r\nCNN-ის მიერ მოპოვებული მხატვრული რუკა უნდა გადაკეთდეს თანმიმდევრობის ფორმაში, რომლის დამუშავებაც RNN-ს შეუძლია:\r\n- მხატვრული რუკის სეგმენტი სვეტებად, თითოეული სვეტით, როგორც დროის ნაბიჯი\r\n- სივრცითი ინფორმაციის ქრონოლოგიის შენარჩუნება\r\n- დარწმუნდით, რომ ფუნქციის თანმიმდევრობის სიგრძე პროპორციულია გამოსახულების სიგანეზე\r\n\r\n### ყურადღების მექანიზმის გამოყენება OCR- ში\r\n\r\nტრადიციულ RN- ებს ჯერ კიდევ აქვთ ინფორმაციის შეფერხებები გრძელი თანმიმდევრობის დროს. ყურადღების მექანიზმების დანერგვა კიდევ უფრო აძლიერებს თანმიმდევრობის მოდელირების შესაძლებლობებს.\r\n\r\n** ყურადღების მექანიზმების პრინციპები **:\r\nყურადღების მექანიზმი საშუალებას აძლევს მოდელს ყურადღება გაამახვილოს შეყვანის თანმიმდევრობის სხვადასხვა ნაწილზე თითოეული გამომავალის წარმოქმნისას:\r\n- გადაწყდა ფიქსირებული სიგრძის დაშიფრული ვექტორების ინფორმაციის ჩამოსხმა\r\n- უზრუნველყოფს სამოდელო გადაწყვეტილებების ახსნას\r\n- გრძელი თანმიმდევრობის გაუმჯობესებული დამუშავება\r\n\r\n** სპეციფიკური პროგრამები OCR**-ში::\r\n\r\n1. ** პერსონაჟის დონის ყურადღება **:\r\n   - თითოეული პერსონაჟის იდენტიფიცირებისას ყურადღება გაამახვილეთ გამოსახულების შესაბამის სფეროებზე\r\n   - დაარეგულირეთ ყურადღება ფრენის დროს\r\n   - გააუმჯობესეთ სიმტკიცე რთულ ფონზე\r\n\r\n2. ** სიტყვის დონის ყურადღება**:\r\n   - განვიხილოთ კონტექსტური ინფორმაცია ლექსიკის დონეზე\r\n   - ბერკეტის ენის მოდელის ცოდნა\r\n   - გააუმჯობესეთ სიტყვის მთელი ამოცნობის სიზუსტე\r\n\r\n3. ** მრავალსამავიური ყურადღება**:\r\n   - ყურადღების მექანიზმების გამოყენება სხვადასხვა რეზოლუციებში\r\n   - გაუმკლავდეს სხვადასხვა ზომის ტექსტს\r\n   - მასშტაბის ცვლილებების ადაპტაციის გაუმჯობესება\r\n\r\n** ყურადღების მექანიზმის მათემატიკური წარმომადგენლობა **:\r\nიყიდება encoder output sequence H = {h_1, h_2, ..., h_T} და დეკოდერი სახელმწიფო s_t:\r\n\r\ne_{t,i} = a(s_t, h_i) # ყურადღების ქულა\r\nα_{t,i} = softmax(e_{t,i}) # ყურადღების წონა\r\nc_t = Σ_i α_{t,i} * h_i # context ვექტორი\r\n\r\n# # ტრენინგის სტრატეგიები და ოპტიმიზაცია\r\n\r\n## # თანმიმდევრობა-თანმიმდევრობის სასწავლო სტრატეგია\r\n\r\n** მასწავლებლის იძულება **:\r\nტრენინგის ფაზის განმავლობაში გამოიყენეთ რეალური სამიზნე თანმიმდევრობა, როგორც დეკოდირის შეყვანა:\r\n- დადებითი: სწრაფი ვარჯიშის სიჩქარე, სტაბილური კონვერგენცია\r\n- უარყოფითი მხარეები: არათანმიმდევრული ტრენინგისა და დასკვნის ფაზები, რაც იწვევს შეცდომების დაგროვებას\r\n\r\n**დაგეგმილი შერჩევა**:\r\nთანდათანობით გადასვლა მასწავლებლისგან, რომელიც აიძულებს გამოიყენოს მოდელის საკუთარი პროგნოზები ტრენინგის დროს:\r\n- გამოიყენეთ რეალური ეტიკეტები საწყის ეტაპზე და მოდელის პროგნოზები შემდგომ ეტაპებზე\r\n- შეამცირეთ განსხვავებები ტრენინგსა და მსჯელობაში\r\n- გააუმჯობესეთ მოდელის სიმტკიცე\r\n\r\n** სასწავლო გეგმის სწავლა**:\r\nდაიწყეთ მარტივი ნიმუშებით და თანდათანობით გაზარდეთ ნიმუშების სირთულე:\r\n- მოკლე და გრძელი თანმიმდევრობა: ჯერ მოკლე ტექსტების მომზადება, შემდეგ გრძელი ტექსტები\r\n- ბუნდოვანი სურათების გასუფთავება: თანდათანობით გაზარდეთ გამოსახულების სირთულე\r\n- მარტივი რთული შრიფტები: დაბეჭდილიდან ხელნაწერამდე\r\n\r\n## რეგულარიზაციის ტექნიკა\r\n\r\n**Dropout-ის გამოყენება RNN**-ში::\r\nRNN– ში ჩამოსაშლელი გამოყენება განსაკუთრებულ ყურადღებას მოითხოვს:\r\n- არ გამოიყენოთ dropout მარყუჟის კავშირებზე\r\n- Dropout შეიძლება გამოყენებულ იქნას შეყვანისა და გამომავალი ფენების დროს\r\n- ვარიაციური ვარდნა: გამოიყენეთ იგივე ჩამოსაშლელი ნიღაბი ყველა დროის ნაბიჯზე\r\n\r\n**წონის დაკლება**:\r\nL2 რეგულარიზაცია ხელს უშლის overfitting:\r\nდაკარგვა = CrossEntropy + λ * || W|| ²\r\n\r\nსადაც λ არის რეგულარიზაციის კოეფიციენტი, რომელიც უნდა იყოს ოპტიმიზირებული ვალიდაციის ნაკრებით.\r\n\r\n**გრადიენტის მოჭრა **:\r\nეფექტური გზა გრადიენტური აფეთქებების თავიდან ასაცილებლად. როდესაც გრადიენტური ნორმა აღემატება ბარიერს, მასშტაბირება გრადიენტი პროპორციულად, რათა შეინარჩუნოს გრადიენტური მიმართულება უცვლელი.\r\n\r\n** ადრეული გაჩერება **:\r\nმონიტორის ვალიდაციის კომპლექტი შესრულება და შეაჩერეთ ტრენინგი, როდესაც შესრულება აღარ გაუმჯობესდება:\r\n- თავიდან აცილება overfitting\r\n- შევინახავ კომპიუტერული რესურსები\r\n- აირჩიეთ ოპტიმალური მოდელი\r\n\r\n### ჰიპერპარამეტრის რეგულირება\r\n\r\n**სწავლის კურსის დაგეგმვა **:\r\n- საწყისი სწავლის მაჩვენებელი: როგორც წესი, მითითებულია 0.001-0.01\r\n- სწავლის სიჩქარის გაფუჭება: ექსპონენციალური დაშლა ან კიბეების დაშლა\r\n- ადაპტური სწავლის მაჩვენებელი: გამოიყენეთ ოპტიმიზატორები, როგორიცაა Adam, RMSprop და ა.შ.\r\n\r\n**სურათების ზომის შერჩევა**:\r\n- მცირე პარტიები: უკეთესი განზოგადების შესრულება, მაგრამ უფრო გრძელი ვარჯიშის დრო\r\n- მაღალი მოცულობა: ტრენინგი სწრაფია, მაგრამ შეიძლება გავლენა იქონიოს განზოგადებაზე\r\n- ჩვეულებრივ, შერჩეულია სურათების ზომები 16-128-ს შორის\r\n\r\n**თანმიმდევრობის სიგრძის დამუშავება**:\r\n- ფიქსირებული სიგრძე: Truncate ან შეავსეთ თანმიმდევრობა ფიქსირებულ სიგრძეზე\r\n- დინამიური სიგრძე: გამოიყენეთ padding და masking ცვლადი სიგრძის თანმიმდევრობის დასამუშავებლად\r\n- Bagging სტრატეგია: მსგავსი სიგრძის ჯგუფური თანმიმდევრობა\r\n\r\n# # შესრულების შეფასება და ანალიზი\r\n\r\n## შეაფასეთ მეტრიკა\r\n\r\n**პერსონაჟის დონის სიზუსტე**:\r\nAccuracy_char = (სიმბოლოების სწორად ამოცნობა) / (სულ სიმბოლოები)\r\n\r\nეს არის შეფასების ყველაზე ძირითადი მაჩვენებელი და პირდაპირ ასახავს მოდელის პერსონაჟის ამოცნობის შესაძლებლობებს.\r\n\r\n**სერიული დონის სიზუსტე**:\r\nAccuracy_seq = (თანმიმდევრობის რაოდენობა სწორად აღიარებული) / (თანმიმდევრობის საერთო რაოდენობა)\r\n\r\nეს მაჩვენებელი უფრო მკაცრია და მხოლოდ სრულიად სწორი თანმიმდევრობა ითვლება სწორად.\r\n\r\n** მანძილის რედაქტირება (Levenshtein მანძილი)**:\r\nგაზომეთ განსხვავება პროგნოზირებულ და ჭეშმარიტ სერიებს შორის:\r\n- ჩასმის, ამოღების და ჩანაცვლების ოპერაციების მინიმალური რაოდენობა\r\n- სტანდარტიზებული რედაქტირების მანძილი: რედაქტირების მანძილი / თანმიმდევრობის სიგრძე\r\n- BLEU ქულა: ჩვეულებრივ გამოიყენება მანქანური თარგმანით და ასევე შეიძლება გამოყენებულ იქნას OCR შეფასებისთვის\r\n\r\n## # შეცდომის ანალიზი\r\n\r\n**შეცდომის საერთო ტიპები**:\r\n1. ** პერსონაჟის დაბნეულობა **: მსგავსი პერსონაჟების არასწორი იდენტიფიკაცია\r\n   - ნომერი 0 და ასო O\r\n   - ნომერი 1 და ასო ლ\r\n   - წერილები M და N\r\n\r\n2. ** თანმიმდევრობის შეცდომა **: შეცდომა სიმბოლოების თანმიმდევრობით\r\n   - პერსონაჟების პოზიციები შეცვლილია\r\n   - სიმბოლოების დუბლირება ან უმოქმედობა\r\n\r\n3. ** სიგრძის შეცდომა **: თანმიმდევრობის სიგრძის პროგნოზირების შეცდომა\r\n   - ძალიან გრძელი: ჩასმული არარსებული სიმბოლოები\r\n   - ძალიან მოკლე: სიმბოლოები, რომლებიც იმყოფებიან, აკლია\r\n\r\n**ანალიზის მეთოდი**:\r\n1. ** კონფუზიური მატრიცა **: აანალიზებს პერსონაჟის დონის შეცდომის შაბლონებს\r\n2. ** ყურადღების ვიზუალიზაცია **: გაიგეთ მოდელის შეშფოთება\r\n3. ** გრადიენტის ანალიზი **: შეამოწმეთ გრადიენტური ნაკადი\r\n4. ** აქტივაციის ანალიზი **: დააკვირდით აქტივაციის ნიმუშებს ქსელის ფენებში\r\n\r\n### მოდელის დიაგნოსტიკა\r\n\r\n**ზედმეტი გამოვლენა **:\r\n- ტრენინგის დანაკარგები კვლავ მცირდება, ვალიდაციის დანაკარგები იზრდება\r\n- ტრენინგის სიზუსტე გაცილებით მაღალია, ვიდრე ვალიდაციის სიზუსტე\r\n- გადაჭრა: რეგულარობის გაზრდა და მოდელის სირთულის შემცირება\r\n\r\n**უვარგისი გამოვლენა **:\r\n- ტრენინგისა და ვალიდაციის დანაკარგები მაღალია\r\n- მოდელი კარგად არ ასრულებს სასწავლო ნაკრებს\r\n- გადაჭრა: მოდელის სირთულის გაზრდა და სწავლის ტემპის რეგულირება\r\n\r\n**გრადიენტის პრობლემის დიაგნოზი**:\r\n- გრადიენტის დაკარგვა: გრადიენტური მნიშვნელობა ძალიან მცირეა, ნელი სწავლა\r\n- გრადიენტური აფეთქება: გადაჭარბებული გრადიენტური ღირებულებები იწვევს არასტაბილურ ვარჯიშს\r\n- გამოსავალი: LSTM/GRU- ს გამოყენება, გრადიენტის მოჭრა\r\n\r\n## რეალურ სამყაროში განაცხადის შემთხვევები\r\n\r\n## # ხელნაწერი პერსონაჟების ამოცნობის სისტემა\r\n\r\n**განაცხადის სცენარები**:\r\n- ხელნაწერი შენიშვნების გაციფრულება: გადააქციეთ ქაღალდის ნოტები ელექტრონულ დოკუმენტებად\r\n- ფორმა ავტომატური შევსება: ავტომატურად ცნობს ხელნაწერი ფორმის შინაარსს\r\n- ისტორიული დოკუმენტის იდენტიფიკაცია: უძველესი წიგნების და ისტორიული დოკუმენტების გაციფრულება\r\n\r\n** ტექნიკური მახასიათებლები**:\r\n- დიდი ხასიათის ვარიაციები: ხელნაწერ ტექსტს აქვს პერსონალიზაციის მაღალი ხარისხი\r\n- კალმის უწყვეტი დამუშავება: პერსონაჟებს შორის კავშირების მოგვარება საჭიროა\r\n- კონტექსტი მნიშვნელოვანია: გამოიყენეთ ენის მოდელები აღიარების გასაუმჯობესებლად\r\n\r\n**სისტემის არქიტექტურა**:\r\n1. ** მკურნალობის მოდული **:\r\n   - სურათის დენოიზება და გაფართოება\r\n   - დახრის კორექცია\r\n   - ტექსტის ხაზის გაყოფა\r\n\r\n2. ** მხატვრული ექსტრაქციის მოდული **:\r\n   - CNN ექსტრაქტები ვიზუალური მახასიათებლები\r\n   - მრავალმასშტაბიანი ფუნქციის შერწყმა\r\n   - მხატვრული სერიალიზაცია\r\n\r\n3. ** თანმიმდევრობის მოდელირების მოდული **:\r\n   - ორმხრივი LSTM მოდელირება\r\n   - ყურადღების მექანიზმები\r\n   - კონტექსტური კოდირება\r\n\r\n4. ** დეკოდირების მოდული **:\r\n   - CTC დეკოდირება ან ყურადღების დეკოდირება\r\n   - ენის მოდელის დამუშავება\r\n   - ნდობის შეფასება\r\n\r\n## დაბეჭდილი დოკუმენტის ამოცნობის სისტემა\r\n\r\n**განაცხადის სცენარები**:\r\n- დოკუმენტის გაციფრულება: ქაღალდის დოკუმენტების რედაქტირებად ფორმატებად გადაქცევა\r\n- კანონპროექტის ამოცნობა: ავტომატურად დაამუშავებს ინვოისებს, ქვითრებს და სხვა გადასახადებს\r\n- ნიშნების ამოცნობა: საგზაო ნიშნების იდენტიფიცირება, მაღაზიის ნიშნები და სხვა\r\n\r\n** ტექნიკური მახასიათებლები**:\r\n- რეგულარული შრიფტი: უფრო რეგულარული, ვიდრე ხელნაწერი ტექსტი\r\n- ტიპოგრაფიის წესები: ინფორმაციის განლაგების გამოყენება შესაძლებელია\r\n- მაღალი სიზუსტის მოთხოვნები: კომერციულ აპლიკაციებს აქვთ მკაცრი სიზუსტის მოთხოვნები\r\n\r\n**ოპტიმიზაციის სტრატეგია **:\r\n1. ** მრავალ შრიფტის ტრენინგი **: იყენებს სასწავლო მონაცემებს მრავალი შრიფტიდან\r\n2. ** მონაცემთა გაძლიერება **: როტაცია, მასშტაბი, ხმაურის დამატება\r\n3. ** დამუშავების შემდგომი ოპტიმიზაცია **: მართლწერის შემოწმება, გრამატიკის კორექტირება\r\n4. ** ნდობის შეფასება **: უზრუნველყოფს საიმედოობის ქულას აღიარების შედეგებისთვის\r\n\r\n### სცენის ტექსტის ამოცნობის სისტემა\r\n\r\n**განაცხადის სცენარები**:\r\n- Street View ტექსტის ამოცნობა: ტექსტის ამოცნობა Google Street View-ში\r\n- პროდუქტის ეტიკეტის ამოცნობა: სუპერმარკეტების პროდუქციის ავტომატური იდენტიფიკაცია\r\n- საგზაო ნიშნის ამოცნობა: ინტელექტუალური სატრანსპორტო სისტემების პროგრამები\r\n\r\n**ტექნიკური გამოწვევები**:\r\n- რთული ფონი: ტექსტი ჩანერგილია რთულ ბუნებრივ სცენებში\r\n- მძიმე დეფორმაცია: პერსპექტიული დეფორმაცია, მოხრილი დეფორმაცია\r\n- რეალურ დროში მოთხოვნები: მობილური აპლიკაციები უნდა იყოს რეაგირება\r\n\r\n**გამოსავალი**:\r\n1. ** ძლიერი ფუნქციის მოპოვება **: იყენებს ღრმა CNN ქსელებს\r\n2. ** მრავალმასშტაბიანი დამუშავება **: გაუმკლავდეს სხვადასხვა ზომის ტექსტს\r\n3. ** გეომეტრიის კორექცია**: ავტომატურად ასწორებს გეომეტრიულ დეფორმაციებს\r\n4. ** მოდელის შეკუმშვა **: მობილური მოდელის ოპტიმიზაცია\r\n\r\n## რეზიუმე\r\n\r\nმორეციდივე ნერვული ქსელები უზრუნველყოფს მძლავრ ინსტრუმენტს OCR– ში თანმიმდევრობის მოდელირებისთვის. ძირითადი რნმ-დან დაწყებული LSTM-ებისა და GRUS-ების გაუმჯობესებით, ორმხრივი დამუშავებისა და ყურადღების მექანიზმებით, ამ ტექნოლოგიების განვითარებამ მნიშვნელოვნად გააუმჯობესა OCR სისტემების მუშაობა.\r\n\r\n**Key Takeaways**:\r\n- RNN– ები ახორციელებენ თანმიმდევრობის მოდელირებას მარყუჟის შეერთების საშუალებით, მაგრამ არსებობს გრადიენტური გაუჩინარების პრობლემა\r\n- LSTM და GRU წყვეტენ საქალაქთაშორისო სწავლის პრობლემას გისოსების მექანიზმების საშუალებით\r\n- Bidirectional RN-ებს შეუძლიათ გამოიყენონ სრული კონტექსტური ინფორმაცია\r\n- ყურადღების მექანიზმები კიდევ უფრო აძლიერებს თანმიმდევრობის მოდელირების უნარს\r\n- ტრენინგის შესაბამისი სტრატეგიები და რეგულარიზაციის ტექნიკა გადამწყვეტია მოდელის მუშაობისთვის\r\n\r\n**მომავალი განვითარების მიმართულებები**:\r\n- ტრანსფორმატორის არქიტექტურასთან ინტეგრაცია\r\n- თანმიმდევრობის მოდელირების უფრო ეფექტური მიდგომა\r\n- ბოლოდან ბოლომდე მულტიმოდალური სწავლება\r\n- რეალურ დროში და სიზუსტეზე\r\n\r\nროგორც ტექნოლოგია აგრძელებს განვითარებას, თანმიმდევრობის მოდელირების ტექნიკა კვლავ ვითარდება. RNN– ების მიერ დაგროვილი გამოცდილება და ტექნოლოგია და მათი ვარიანტები OCR– ის სფეროში მყარი საფუძველი ჩაუყარა თანმიმდევრობის მოდელირების უფრო მოწინავე მეთოდების გაგებასა და დიზაინს.</div>\n                        \n                        <div class=\"article-tags\">\n                            <h6>ლეიბლი:</h6>\n                            <div class=\"tags-list\">\n                                \n                                <span class=\"tag\">RNN</span>\n                                \n                                <span class=\"tag\">LSTM</span>\n                                \n                                <span class=\"tag\">GRU</span>\n                                \n                                <span class=\"tag\">თანმიმდევრობის მოდელირება</span>\n                                \n                                <span class=\"tag\">გრადიენტი ქრება</span>\n                                \n                                <span class=\"tag\">ორმხრივი RNN</span>\n                                \n                                <span class=\"tag\">ყურადღების მექანიზმი</span>\n                                \n                                <span class=\"tag\">CRNN</span>\n                                \n                                <span class=\"tag\">OCR</span>\n                                \n                            </div>\n                        </div>\n                        \n                    </article>\n                    \n                    <footer class=\"article-footer\" id=\"articleFooter\">\n                        <div class=\"article-share\">\n                            <h6 style=\"color: #1a1c1f; margin-bottom: 1rem;\">გაზიარება და ექსპლუატაცია:</h6>\n                            <div class=\"share-buttons\">\n                                <a href=\"javascript:void(0)\" onclick=\"shareToWeibo()\" class=\"btn btn-weibo\">📱 Weibo გაზიარებული</a>\n                                <a href=\"javascript:void(0)\" onclick=\"copyUrl()\" class=\"btn btn-copy\">🔗 ბმულის კოპირება</a>\n                                <button type=\"button\" class=\"btn btn-print\" onclick=\"printArticle()\">🖨️ სტატიის დაბეჭდვა</button>\n                            </div>\n                        </div>\n                    </footer>\n                    \n                </div>\n                <div class=\"col-lg-4 col-xl-3 col-md-12\">\n                    <div class=\"sidebar\">\n                        <div class=\"sidebar-card toc-card\" id=\"tocCard\" style=\"display: none;\"><h5>სარჩევი</h5><div id=\"tableOfContents\" class=\"toc-list\"></div></div>\n                        <div class=\"sidebar-card\">\n                            <h5>რეკომენდებული კითხვა</h5>\n                            <div id=\"recommendedArticles\">\n                                \n                                <div class=\"recommended-item\"><h6><a href=\"Article.aspx?slug=document-intelligence-future-outlook\">【დოკუმენტის ინტელექტუალური დამუშავების სერია·20】დოკუმენტის ინტელექტუალური დამუშავების ტექნოლოგიის განვითარების პერსპექტივები</a></h6><p class=\"text-muted small\">2025-08-19 • 1203 შემდეგი კითხვა</p></div>\n                                \n                                <div class=\"recommended-item\"><h6><a href=\"Article.aspx?slug=document-intelligence-quality-assurance\">【დოკუმენტის დამუშავების ინტელექტუალური სერია·19】დოკუმენტის ხარისხის უზრუნველყოფის ინტელექტუალური სისტემა</a></h6><p class=\"text-muted small\">2025-08-19 • 1200 შემდეგი კითხვა</p></div>\n                                \n                                <div class=\"recommended-item\"><h6><a href=\"Article.aspx?slug=large-scale-document-processing-optimization\">【დოკუმენტის ინტელექტუალური დამუშავების სერია·18】ფართომასშტაბიანი დოკუმენტის დამუშავების მუშაობის ოპტიმიზაცია</a></h6><p class=\"text-muted small\">2025-08-19 • 1201 შემდეგი კითხვა</p></div>\n                                \n                            </div>\n                        </div>\n                    </div>\n</div>\n</div>\n</div>\n</div>\n<script>\nvar articleId,viewCountKey,now=new Date().getTime();\ndocument.addEventListener('DOMContentLoaded',function(){var articleDetail=document.getElementById('articleDetail');if(articleDetail&&articleDetail.querySelector('.article-content')){formatContent();initReadingProgress();optimizeImages();updateViewCount();}});\nfunction updateViewCount(){articleId=getArticleId();if(!articleId)return;viewCountKey='article_view_'+articleId;var lastViewTime=localStorage.getItem(viewCountKey);if(lastViewTime&&(now-parseInt(lastViewTime))<3600000)return;var xhr=new XMLHttpRequest();xhr.open('POST','/Article.ashx',true);xhr.setRequestHeader('Content-Type','application/x-www-form-urlencoded');xhr.onreadystatechange=function(){if(xhr.readyState===4&&xhr.status===200){try{var response=JSON.parse(xhr.responseText);if(response.success){updateViewCountDisplay(response.viewCount);localStorage.setItem(viewCountKey,now.toString());}}catch(e){}}};xhr.send('action=updateviewcount&articleId='+encodeURIComponent(articleId));}\nfunction getArticleId(){var articleDetail=document.getElementById('articleDetail');if(articleDetail){var id=articleDetail.getAttribute('data-article-id');if(id)return id;}var urlParams=new URLSearchParams(window.location.search);return urlParams.get('id');}\nfunction updateViewCountDisplay(newCount){document.querySelectorAll('.view-count').forEach(function(element){element.textContent=newCount;});}\nfunction decodeHtmlEntities(text){var textArea=document.createElement('textarea');textArea.innerHTML=text;return textArea.value;}\nfunction formatContent(){var contentDiv=document.getElementById('articleContent');if(!contentDiv)return;var content=contentDiv.textContent||contentDiv.innerText;if(!content)return;content=decodeHtmlEntities(content);content=formatMarkdownContent(content);content=formatIndentation(content);content=formatLists(content);content=formatParagraphs(content);content=formatLinks(content);contentDiv.innerHTML=content;initTableOfContents();}\nfunction formatIndentation(content){var lines=content.split('\\n'),result=[];for(var i=0;i<lines.length;i++){var line=lines[i];if(line.trim().match(/^<[^>]+>/)){result.push(line);continue;}line=line.replace(/^\\t+/g,function(match){return '    '.repeat(match.length);});if(line.match(/^  +[^\\s]/)){var indentMatch=line.match(/^( +)(.*)$/);if(indentMatch){var indentLevel=Math.floor(indentMatch[1].length/2);line='<span class=\"text-indent-'+indentLevel+'\">'+indentMatch[2]+'</span>';}}result.push(line);}return result.join('\\n');}\nfunction formatMarkdownContent(content){content=content.replace(/^\\s*##### (.+)$/gm,'<h5>$1</h5>').replace(/^\\s*#### (.+)$/gm,'<h4>$1</h4>').replace(/^\\s*### (.+)$/gm,'<h3>$1</h3>').replace(/^\\s*## (.+)$/gm,'<h2>$1</h2>').replace(/^\\s*# (.+)$/gm,'<h1>$1</h1>').replace(/^> (.+)$/gm,'<blockquote><p>$1</p></blockquote>').replace(/\\*\\*([^*]+?)\\*\\*/g,'<strong>$1</strong>').replace(/\\*([^*\\n]+?)\\*/g,'<em>$1</em>').replace(/`([^`]+?)`/g,'<code class=\"inline-code\">$1</code>').replace(/^(\\*\\*[^*]+?\\*\\*)：/gm,'<div class=\"label-item\"><span class=\"label\">$1</span></div>').replace(/^([^：\\n]+?)：$/gm,'<div class=\"section-label\">$1：</div>').replace(/^(\\*\\*[^*]+?\\*\\*)：(.+)$/gm,'<div class=\"definition-item\"><span class=\"definition-label\">$1：</span><span class=\"definition-content\">$2</span></div>').replace(/^(შენიშვნა|note|note):(.+)$/gm,'<div class=\"note-item\"><span class=\"note-label\">$1：</span><span class=\"note-content\">$2</span></div>');content=formatLists(content);content=formatMathSymbols(content);content=formatParagraphs(content);return content;}\nfunction formatLists(content){var lines=content.split('\\n'),result=[],inOrderedList=false,inUnorderedList=false;for(var i=0;i<lines.length;i++){var line=lines[i],trimmed=line.trim();var orderedMatch=line.match(/^(\\d+)\\.\\s*(.+)$/);var unorderedMatch=line.match(/^[-*+]\\s*(.+)$/);if(orderedMatch){if(!inOrderedList){if(inUnorderedList){result.push('</ul>');inUnorderedList=false;}result.push('<ol class=\"formatted-list ordered\">');inOrderedList=true;}var listContent=orderedMatch[2].replace(/^\\*\\*([^*]+?)\\*\\*：(.+)$/,'<strong class=\"list-title\">$1：</strong><span class=\"list-content\">$2</span>');result.push('<li>'+listContent+'</li>');}else if(unorderedMatch){if(!inUnorderedList){if(inOrderedList){result.push('</ol>');inOrderedList=false;}result.push('<ul class=\"formatted-list unordered\">');inUnorderedList=true;}var listContent=unorderedMatch[1].replace(/^\\*\\*([^*]+?)\\*\\*：(.+)$/,'<strong class=\"list-title\">$1：</strong><span class=\"list-content\">$2</span>');result.push('<li>'+listContent+'</li>');}else{if(trimmed===''){if(i+1<lines.length){var nextLine=lines[i+1].trim();if(!nextLine.match(/^\\d+\\.\\s/)&&!nextLine.match(/^[-*+]\\s/)){if(inOrderedList){result.push('</ol>');inOrderedList=false;}if(inUnorderedList){result.push('</ul>');inUnorderedList=false;}}}result.push(line);}else{if(inOrderedList){result.push('</ol>');inOrderedList=false;}if(inUnorderedList){result.push('</ul>');inUnorderedList=false;}result.push(line);}}}if(inOrderedList)result.push('</ol>');if(inUnorderedList)result.push('</ul>');return result.join('\\n');}\nfunction formatMathSymbols(content){var mathSymbols={'α':'&alpha;','β':'&beta;','γ':'&gamma;','δ':'&delta;','ε':'&epsilon;','ζ':'&zeta;','η':'&eta;','θ':'&theta;','λ':'&lambda;','μ':'&mu;','π':'&pi;','σ':'&sigma;','∑':'&sum;','∏':'&prod;','∫':'&int;','∂':'&part;','∇':'&nabla;','∞':'&infin;','≤':'&le;','≥':'&ge;','≠':'&ne;','≈':'&asymp;','±':'&plusmn;','×':'&times;','÷':'&divide;','→':'&rarr;','←':'&larr;','↑':'&uarr;','↓':'&darr;','⇒':'&rArr;','⇐':'&lArr;'};var subMap={'₀':'0','₁':'1','₂':'2','₃':'3','₄':'4','₅':'5','₆':'6','₇':'7','₈':'8','₉':'9','ₖ':'k','ₜ':'t','ₛ':'s','₊':'+','₋':'-'};var supMap={'⁰':'0','¹':'1','²':'2','³':'3','⁴':'4','⁵':'5','⁶':'6','⁷':'7','⁸':'8','⁹':'9','ᵏ':'k','ᵗ':'t','ˢ':'s','⁺':'+','⁻':'-'};for(var symbol in mathSymbols){content=content.replace(new RegExp(escapeRegExp(symbol),'g'),mathSymbols[symbol]);}content=content.replace(/([a-zA-Z])₍([0-9]+)₎/g,'$1<sub>$2</sub>').replace(/([a-zA-Z])⁽([0-9]+)⁾/g,'$1<sup>$2</sup>').replace(/([a-zA-Z])([₀₁₂₃₄₅₆₇₈₉ₖₜₛ₊₋]+)/g,function(match,base,subscript){var converted=subscript.replace(/[₀₁₂₃₄₅₆₇₈₉ₖₜₛ₊₋]/g,function(char){return subMap[char]||char;});return base+'<sub>'+converted+'</sub>';}).replace(/([a-zA-Z])([⁰¹²³⁴⁵⁶⁷⁸⁹ᵏᵗˢ⁺⁻]+)/g,function(match,base,superscript){var converted=superscript.replace(/[⁰¹²³⁴⁵⁶⁷⁸⁹ᵏᵗˢ⁺⁻]/g,function(char){return supMap[char]||char;});return base+'<sup>'+converted+'</sup>';}).replace(/\\b([A-Za-z])_\\{([^}]+)\\}/g,'$1<sub>$2</sub>').replace(/\\b([A-Za-z])\\^\\{([^}]+)\\}/g,'$1<sup>$2</sup>');return content;}\nfunction formatParagraphs(content){var lines=content.split('\\n'),result=[],inParagraph=false,currentParagraph=[];for(var i=0;i<lines.length;i++){var originalLine=lines[i],line=originalLine.trim();if(line.match(/^<[^>]+>/)||line===''){if(inParagraph&&currentParagraph.length>0){var paragraphContent=currentParagraph.join(' ').trim();if(paragraphContent)result.push('<p class=\"content-paragraph indent-paragraph\">'+paragraphContent+'</p>');currentParagraph=[];inParagraph=false;}result.push(originalLine);continue;}if(line.match(/^#{1,6}\\s/)||line.match(/^\\d+\\.\\s/)||line.match(/^[-*+]\\s/)||line.match(/^>/)){if(inParagraph&&currentParagraph.length>0){var paragraphContent=currentParagraph.join(' ').trim();if(paragraphContent)result.push('<p class=\"content-paragraph indent-paragraph\">'+paragraphContent+'</p>');currentParagraph=[];inParagraph=false;}result.push(originalLine);continue;}if(line){currentParagraph.push(line);inParagraph=true;}}if(inParagraph&&currentParagraph.length>0){var paragraphContent=currentParagraph.join(' ').trim();if(paragraphContent)result.push('<p class=\"content-paragraph indent-paragraph\">'+paragraphContent+'</p>');}return result.join('\\n');}\nfunction escapeRegExp(string){return string.replace(/[.*+?^${}()|[\\]\\\\]/g,'\\\\$&');}\nfunction formatLinks(content){return content.replace(/<a\\s+href=\"(https?:\\/\\/[^\"]+)\"([^>]*)>/gi,function(match,url,attrs){return !attrs.includes('target=')?'<a href=\"'+url+'\"'+attrs+' target=\"_blank\" rel=\"noopener noreferrer\">':match;});}\nfunction escapeHtml(text){var div=document.createElement('div');div.textContent=text;return div.innerHTML;}\nfunction addTocClickEvents(){document.querySelectorAll('#tableOfContents a').forEach(function(link){link.addEventListener('click',function(e){e.preventDefault();var targetElement=document.getElementById(this.getAttribute('data-target'));if(targetElement){targetElement.scrollIntoView({behavior:'smooth',block:'start'});updateActiveTocItem(this);}});});}\nfunction addScrollHighlight(headings){var throttleTimer=null;function updateActiveHeading(){var windowHeight=window.innerHeight,activeHeading=null;for(var i=headings.length-1;i>=0;i--){var rect=headings[i].getBoundingClientRect();if(rect.top<=windowHeight*0.3){activeHeading=headings[i];break;}}if(activeHeading){var activeLink=document.querySelector('#tableOfContents a[data-target=\"'+activeHeading.id+'\"]');if(activeLink)updateActiveTocItem(activeLink);}}window.addEventListener('scroll',function(){if(throttleTimer)clearTimeout(throttleTimer);throttleTimer=setTimeout(updateActiveHeading,100);});updateActiveHeading();}\nfunction updateActiveTocItem(activeLink){document.querySelectorAll('#tableOfContents a').forEach(function(link){link.classList.remove('active');});if(activeLink){activeLink.classList.add('active');scrollTocToActiveItem(activeLink);}}\nfunction scrollTocToActiveItem(activeLink){var tocCard=document.getElementById('tocCard');if(!activeLink||!tocCard)return;var cardRect=tocCard.getBoundingClientRect();var activeRect=activeLink.getBoundingClientRect();var relativeTop=activeRect.top-cardRect.top;var relativeBottom=activeRect.bottom-cardRect.bottom;var cardStyle=window.getComputedStyle(tocCard);var paddingTop=parseFloat(cardStyle.paddingTop)||0;var titleHeight=tocCard.querySelector('h5')?tocCard.querySelector('h5').offsetHeight:0;var contentTop=paddingTop+titleHeight;if(relativeTop<contentTop){tocCard.scrollTop+=(relativeTop-contentTop-10);}else if(relativeBottom>0){tocCard.scrollTop+=(relativeBottom+10);}}\nfunction optimizeImages(){document.querySelectorAll('.article-content img').forEach(function(img){img.loading='lazy';img.onerror=function(){this.style.display='none';};if(!img.alt)img.alt='სტატია სურათებით';});}\nfunction initReadingProgress(){var progressBar=document.getElementById('readingProgress');function updateProgress(){var scrollTop=window.pageYOffset||document.documentElement.scrollTop;var docHeight=document.documentElement.scrollHeight-window.innerHeight;progressBar.style.width=Math.min((scrollTop/docHeight)*100,100)+'%';}window.addEventListener('scroll',updateProgress);updateProgress();}\nfunction initTableOfContents(){var content=document.querySelector('.article-content');if(!content)return;var headings=content.querySelectorAll('h1, h2, h3, h4, h5, h6');if(headings.length<=1)return;var tocCard=document.getElementById('tocCard');var tocList=document.getElementById('tableOfContents');var html='',counters=[0,0,0,0,0,0];headings.forEach(function(heading,index){var id='heading-'+index;heading.id=id;var level=parseInt(heading.tagName.substring(1));var indent=(level-1)*15;counters[level-1]++;for(var i=level;i<counters.length;i++)counters[i]=0;var numberText='';for(var i=0;i<level;i++){if(counters[i]>0)numberText+=(numberText?'.':'')+counters[i];}numberText+='.';html+='<a href=\"#'+id+'\" class=\"toc-item toc-level-'+level+'\" style=\"padding-left: '+indent+'px;\" data-target=\"'+id+'\"><span class=\"toc-number\">'+numberText+'</span> <span class=\"toc-text\">'+heading.textContent+'</span></a>';});tocList.innerHTML=html;tocCard.style.display='block';addTocClickEvents();addScrollHighlight(headings);tocList.addEventListener('click',function(e){if(e.target.tagName==='A'){e.preventDefault();var targetElement=document.getElementById(e.target.getAttribute('data-target'));if(targetElement){targetElement.scrollIntoView({behavior:'smooth',block:'start'});targetElement.style.transition='all 0.3s ease';targetElement.style.backgroundColor='#e3f2fd';targetElement.style.padding='10px';targetElement.style.borderRadius='6px';setTimeout(function(){targetElement.style.backgroundColor='';targetElement.style.padding='';targetElement.style.borderRadius='';},2000);}}});}\nfunction shareToWeibo(){window.open('https://service.weibo.com/share/share.php?url='+encodeURIComponent(window.location.href)+'&title='+encodeURIComponent(document.title),'_blank');}\nfunction copyUrl(){navigator.clipboard.writeText(window.location.href).then(function(){alert('ბმული გადაწერილია ბუფერში');}).catch(function(){var textArea=document.createElement('textarea');textArea.value=window.location.href;textArea.style.cssText='position:fixed;left:-999999px;top:-999999px';document.body.appendChild(textArea);textArea.focus();textArea.select();try{alert(document.execCommand('copy')?'ბმული გადაწერილია ბუფერში':'თუ ასლი ვერ მოხერხდა, გთხოვთ ხელით დააკოპიროთ ბმული');}catch(err){alert('თუ ასლი ვერ მოხერხდა, გთხოვთ ხელით დააკოპიროთ ბმული');}document.body.removeChild(textArea);});}\nfunction printArticle(){var printWindow=window.open('','_blank');var articleContent=document.querySelector('.article-detail').innerHTML;var articleTitle=document.querySelector('.article-title').textContent;printWindow.document.write('<!DOCTYPE html><html lang=\"ka\"><title>'+articleTitle+'</title><style>body{font-family:-apple-system,BlinkMacSystemFont,\"Segoe UI\",Roboto,\"Helvetica Neue\",Arial,sans-serif;line-height:1.6;color:#333;max-width:800px;margin:0 auto;padding:20px}h1,h2,h3,h4,h5,h6{color:#1a1c1f;margin-top:1.5em;margin-bottom:0.5em}h1{font-size:2em;border-bottom:2px solid #e9ecef;padding-bottom:0.3em}h2{font-size:1.5em;border-bottom:1px solid #e9ecef;padding-bottom:0.3em}h3{font-size:1.25em}p{margin-bottom:1em}pre{background:#f8f9fa;border:1px solid #e9ecef;border-radius:4px;padding:1em;overflow-x:auto;font-family:Consolas,Monaco,\"Courier New\",monospace}code{background:#f1f3f4;padding:2px 4px;border-radius:3px;font-family:Consolas,Monaco,\"Courier New\",monospace}blockquote{border-left:4px solid #1764ff;background:#f8f9fa;padding:1em;margin:1em 0;border-radius:0 4px 4px 0}img{max-width:100%;height:auto}table{width:100%;border-collapse:collapse;margin:1em 0}th,td{border:1px solid #ddd;padding:8px;text-align:left}th{background-color:#f2f2f2}.article-meta,.article-footer,.share-buttons{display:none}</style><body>'+articleContent+'</body></html>');printWindow.document.close();printWindow.focus();setTimeout(function(){printWindow.print();printWindow.close();},250);}\nfunction improvedStickyPosition(){var sidebarCards=document.querySelectorAll('.sidebar-card');var originalPositions=[],isFixed=[];sidebarCards.forEach(function(card,index){var rect=card.getBoundingClientRect();originalPositions[index]=rect.top+window.pageYOffset;isFixed[index]=false;var placeholder=document.createElement('div');placeholder.style.cssText='height:'+card.offsetHeight+'px;display:none';placeholder.className='sticky-placeholder';card.parentNode.insertBefore(placeholder,card.nextSibling);});function updateStickyPosition(){var scrollTop=window.pageYOffset||document.documentElement.scrollTop;sidebarCards.forEach(function(card,index){var originalTop=originalPositions[index];var stickyTop=index>0?(document.querySelector('.toc-card').style.position==='fixed'?60+document.querySelector('.toc-card').offsetHeight+8:80):60;var placeholder=card.nextSibling;if(scrollTop>originalTop-stickyTop){if(!isFixed[index]){var colContainer=card.closest('[class*=\"col-\"]');var colRect=colContainer.getBoundingClientRect();var colStyle=window.getComputedStyle(colContainer);var paddingLeft=parseFloat(colStyle.paddingLeft)||0;var paddingRight=parseFloat(colStyle.paddingRight)||0;card.style.cssText='position:fixed;top:'+stickyTop+'px;left:'+(colRect.left+paddingLeft)+'px;width:'+(colRect.width-paddingLeft-paddingRight)+'px;z-index:9999;box-sizing:border-box';card.classList.add('fixed');placeholder.style.display='block';isFixed[index]=true;}}else if(isFixed[index]){card.style.cssText='';card.classList.remove('fixed');placeholder.style.display='none';isFixed[index]=false;}});}window.addEventListener('scroll',updateStickyPosition);window.addEventListener('resize',function(){setTimeout(function(){sidebarCards.forEach(function(card,index){if(isFixed[index]){var colContainer=card.closest('[class*=\"col-\"]');var colRect=colContainer.getBoundingClientRect();var colStyle=window.getComputedStyle(colContainer);var paddingLeft=parseFloat(colStyle.paddingLeft)||0;var paddingRight=parseFloat(colStyle.paddingRight)||0;card.style.left=(colRect.left+paddingLeft)+'px';card.style.width=(colRect.width-paddingLeft-paddingRight)+'px';}else{originalPositions[index]=card.getBoundingClientRect().top+window.pageYOffset;}});},100);});updateStickyPosition();}\ndocument.addEventListener('DOMContentLoaded',function(){setTimeout(improvedStickyPosition,1000);});\n    </script>\n\r\n            </div>\r\n        </form>\r\n\r\n        <div class=\"fixed-icon\">\r\n            <div style=\"width: 120px; position: relative; left: 1rem; top: -12px;\">\r\n            </div>\r\n            <a id=\"webchat\" class=\"wx-con disa\" href=\"http://wpa.qq.com/msgrd?v=3&uin=365833440&site=qq&menu=yes\" target=\"_blank\">\r\n                <img class=\"wechat-ico\" src=\"site/image/icon/qq.svg\" alt=\"OCR ასისტენტი QQ ონლაინ მომხმარებელთა მომსახურება\" />\r\n                <div class=\"wx-text\">QQ მომხმარებელთა მომსახურება (365833440)</div>\r\n                <label class=\"badge badge-danger\" style=\"background: rgb(255, 34, 122); display: inline-block;\">1</label>\r\n            </a>\r\n\r\n            <a class=\"wx-con\" href=\"https://qm.qq.com/cgi-bin/qm/qr?k=bohNy9GMdgD5bsYrB-fcIjy-s6Ot3zOg&jump_from=webapi&qr=1\" target=\"_blank\">\r\n                <img class=\"wechat-ico\" src=\"site/image/icon/qq.svg\" alt=\"OCR ასისტენტი QQ მომხმარებლის საკომუნიკაციო ჯგუფი\" />\r\n                <div class=\"wx-text\">QQ ჯგუფი (100029010)</div>\r\n                <label class=\"badge badge-danger\" style=\"background: rgb(255, 34, 122); display: inline-block;\">2</label>\r\n            </a>\r\n            <a href=\"mailto:<EMAIL>\" class=\"wx-con\">\r\n                <img class=\"wechat-ico\" src=\"site/image/icon/im.svg\" alt=\"OCR ასისტენტი დაუკავშირდით მომხმარებელთა მომსახურებას ელექტრონული ფოსტით\" />\r\n                <div class=\"wx-text\" style=\"width: 240px; text-align: left; left: -15.3rem; padding: 10px 20px;\">\r\n                    <div style=\"font-size: 15px\">ელ-ფოსტა: <EMAIL></div>\r\n                    <hr style=\"background: #c1c1c1; margin: 10px 10px 6px 0;\" />\r\n                    <span style=\"font-size: 13px; color: #c1c1c1\">გმადლობთ კომენტარებისა და შემოთავაზებებისთვის!</span>\r\n                </div>\r\n                <label class=\"badge badge-danger\" style=\"background: rgb(255, 34, 122); display: inline-block;\">3</label>\r\n            </a>\r\n        </div>\r\n\r\n        <style>\r\n            .content {\r\n                width: 100%;\r\n                padding: 0 !important;\r\n                margin: 0 auto\r\n            }\r\n\r\n            h3.h6 {\r\n                font-size: 1rem !important;\r\n                font-weight: bold !important;\r\n                line-height: 1.2 !important;\r\n                margin-bottom: .5rem;\r\n                font-family: inherit\r\n            }\r\n        </style>\r\n        <footer class=\"page-footer\">\r\n            <div class=\"container\">\r\n                <div class=\"row foot-cont pt-4 no-gutters\" style=\"border-top: 1px solid #e1e6ed; max-width: 100%\">\r\n                    <div class=\"col-lg-12 hu-an p-0 mb-3 mt-3 text-l color-gray\" style=\"text-align: center;\">\r\n                        OCR ტექსტის ამოცნობის ასისტენტი&nbsp;©️ 2025 ALL RIGHTS RESERVED. ყველა უფლება დაცულია&nbsp;&nbsp;&nbsp;|&nbsp;&nbsp;&nbsp;\r\n                        <a href=\"PrivacyPolicy.aspx\" style=\"text-decoration: underline;\">კონფიდენციალურობის ხელშეკრულება</a>&nbsp;&nbsp;&nbsp;|&nbsp;&nbsp;&nbsp;\r\n                        <a href=\"UserAgreement.aspx\" style=\"text-decoration: underline;\">სამომხმარებლო შეთანხმება</a>&nbsp;&nbsp;&nbsp;|&nbsp;&nbsp;&nbsp;\r\n                        <a href=\"Status.aspx\" style=\"text-decoration: underline;\">მომსახურების ვადა</a>&nbsp;&nbsp;&nbsp;|&nbsp;&nbsp;&nbsp;\r\n                        <a class=\"mb-2 color-gray\" href=\"https://beian.miit.gov.cn/\" target=\"_blank\" style=\"text-decoration: underline;\">E ICP მომზადება 2021012692</a>\r\n                    </div>\r\n                </div>\r\n            </div>\r\n        </footer>\r\n        <style>\r\n            #translate > .translateSelectLanguage {\r\n                right: 2rem;\r\n                font-size: 1rem;\r\n                width: 150px;\r\n                padding: .3rem;\r\n                margin-right: 20px;\r\n                border: 1px solid #C9C9C9;\r\n                background: #fff;\r\n                color: #555\r\n            }\r\n        </style>\r\n    </div>\r\n    <script>\r\n        document.addEventListener('DOMContentLoaded', function () {\r\n            var setScroll = function (active) {\r\n                forEach(qa('.v4_header_pc,.v4_header_mob'), function (e) {\r\n                    if (active) addClass(e, 'scrollActive'); else removeClass(e, 'scrollActive');\r\n                });\r\n            };\r\n            if (getScrollY() > 60) setScroll(1);\r\n            window.addEventListener('scroll', function () { setScroll(getScrollY() > 60); });\r\n\r\n            forEach(qa('.v4_header_pc'), function (h) {\r\n                h.addEventListener('mouseenter', function () { addClass(h, 'active'); });\r\n                h.addEventListener('mouseleave', function () { removeClass(h, 'active'); });\r\n            });\r\n\r\n            var menuTimer;\r\n            var hideMenu = function (menuId) {\r\n                var m = document.getElementById(menuId);\r\n                if (m) {\r\n                    m.style.cssText = 'visibility:hidden;height:0;display:none';\r\n                    var n = q('.nav-drown-con', m);\r\n                    if (n) { n.style.cssText = 'height:0;opacity:0;transform:translate(0,-100%)'; }\r\n                }\r\n            };\r\n\r\n            forEach(qa('ul.top-nav>li'), function (i) {\r\n                i.addEventListener('mouseenter', function () {\r\n                    clearTimeout(menuTimer);\r\n                    var l = this.querySelector('a'), t = this.querySelector('.triangle'), s = l ? l.getAttribute('data-sub') : null;\r\n                    if (l) addClass(l, 'active'); if (t) addClass(t, 'active');\r\n                    if (s) {\r\n                        var m = document.getElementById(s);\r\n                        if (m) {\r\n                            m.style.cssText = 'display:block;visibility:visible';\r\n                            var siblings = m.parentNode ? toArray(m.parentNode.children) : [];\r\n                            forEach(siblings, function (c) { if (c !== m) c.style.display = 'none'; });\r\n                            var n = q('.nav-drown-con', m);\r\n                            if (n) { n.style.cssText = 'height:100%;opacity:1;transform:translate(0,0)'; }\r\n\r\n                            m.addEventListener('mouseenter', function () { clearTimeout(menuTimer); });\r\n                            m.addEventListener('mouseleave', function () { menuTimer = setTimeout(function () { hideMenu(s); }, 100); });\r\n                        }\r\n                    }\r\n                });\r\n                i.addEventListener('mouseleave', function () {\r\n                    var l = this.querySelector('a'), t = this.querySelector('.triangle'), s = l ? l.getAttribute('data-sub') : null;\r\n                    if (l) removeClass(l, 'active'); if (t) removeClass(t, 'active');\r\n                    if (s) { menuTimer = setTimeout(function () { hideMenu(s); }, 100); }\r\n                });\r\n            });\r\n\r\n            var r = q('.v4_header_mob .right-menu');\r\n            if (r) r.addEventListener('click', function () {\r\n                var arr = [r, q('.v4_header_mob'), q('#nav_products'), q('.mob-nav-content .sidebar-fix')];\r\n                for (var i = 0; i < arr.length; i++) {\r\n                    var e = arr[i];\r\n                    if (e) {\r\n                        var name = (i === 0 ? 'active' : (i === 1 ? 'active' : (i === 2 ? 'active1' : 'fixedActiveBg')));\r\n                        toggleClass(e, name);\r\n                    }\r\n                }\r\n                var f = q('.mob-nav-content .sidebar-fix');\r\n                if (f) {\r\n                    toggleClass(f, 'show');\r\n                    document.body.style.overflow = hasClass(f, 'show') ? 'hidden' : 'scroll';\r\n                }\r\n            });\r\n\r\n            document.addEventListener('click', function (e) {\r\n                e = e || window.event;\r\n                var target = e.target || e.srcElement;\r\n                var m = closest(target, '.sidebar-fix-left .mob-nav-item');\r\n                if (m) {\r\n                    var p = m.parentNode;\r\n                    if (p) forEach(toArray(p.children), function (c) { removeClass(c, 'active'); });\r\n                    addClass(m, 'active');\r\n                    var d = m.getAttribute('data');\r\n                    if (d) forEach(qa('.sidebar-fix-rigth .sub-nav'), function (n, i) { n.style.display = (i == d ? 'block' : 'none'); });\r\n                }\r\n            });\r\n        });\r\n    </script>\r\n</body>\r\n</html>\r\n\r\n\t            <script type=\"text/javascript\">\r\n                    var _hmt = _hmt || [];\r\n                    (function () {\r\n                        var hm = document.createElement('script');hm.async=1;\r\n                        hm.src = 'https://hm.baidu.com/hm.js?5ee048d7caaf0d439cf16309a1687754';\r\n                        var s = document.getElementsByTagName('script')[0];s.parentNode.insertBefore(hm, s);\r\n                    })();\r\n                </script><div id=\"translate\" class=\"ignore\" style=\"display: none\"></div><script src=\"/static/js/translate.js\"></script>"
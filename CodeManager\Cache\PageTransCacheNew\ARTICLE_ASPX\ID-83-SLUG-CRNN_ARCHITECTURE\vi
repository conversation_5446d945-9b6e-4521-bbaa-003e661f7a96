﻿"<!DOCTYPE html>\r\n<html xmlns=\"http://www.w3.org/1999/xhtml\" lang=\"vi\">\r\n<head><link rel=\"canonical\" href=\"https://ocr.oldfish.cn/vi/Article.aspx?id=83&slug=crnn-architecture\" /><link rel=\"alternate\" href=\"https://ocr.oldfish.cn/Article.aspx?id=83&slug=crnn-architecture\" hreflang=\"x-default\" />\r\n<link rel=\"alternate\" href=\"https://ocr.oldfish.cn/Article.aspx?id=83&slug=crnn-architecture\" hreflang=\"zh\" />\r\n<link rel=\"alternate\" href=\"https://ocr.oldfish.cn/Article.aspx?id=83&slug=crnn-architecture\" hreflang=\"zh\" />\r\n<link rel=\"alternate\" href=\"https://ocr.oldfish.cn/zh-Hant/Article.aspx?id=83&slug=crnn-architecture\" hreflang=\"zh-tw\" />\r\n<link rel=\"alternate\" href=\"https://ocr.oldfish.cn/yue/Article.aspx?id=83&slug=crnn-architecture\" hreflang=\"zh-yue\" />\r\n<link rel=\"alternate\" href=\"https://ocr.oldfish.cn/en/Article.aspx?id=83&slug=crnn-architecture\" hreflang=\"en\" />\r\n<link rel=\"alternate\" href=\"https://ocr.oldfish.cn/es/Article.aspx?id=83&slug=crnn-architecture\" hreflang=\"es\" />\r\n<link rel=\"alternate\" href=\"https://ocr.oldfish.cn/hi/Article.aspx?id=83&slug=crnn-architecture\" hreflang=\"hi\" />\r\n<link rel=\"alternate\" href=\"https://ocr.oldfish.cn/ar/Article.aspx?id=83&slug=crnn-architecture\" hreflang=\"ar\" />\r\n<link rel=\"alternate\" href=\"https://ocr.oldfish.cn/pt/Article.aspx?id=83&slug=crnn-architecture\" hreflang=\"pt\" />\r\n<link rel=\"alternate\" href=\"https://ocr.oldfish.cn/bn/Article.aspx?id=83&slug=crnn-architecture\" hreflang=\"bn\" />\r\n<link rel=\"alternate\" href=\"https://ocr.oldfish.cn/ru/Article.aspx?id=83&slug=crnn-architecture\" hreflang=\"ru\" />\r\n<link rel=\"alternate\" href=\"https://ocr.oldfish.cn/ja/Article.aspx?id=83&slug=crnn-architecture\" hreflang=\"ja\" />\r\n<link rel=\"alternate\" href=\"https://ocr.oldfish.cn/pa/Article.aspx?id=83&slug=crnn-architecture\" hreflang=\"pa\" />\r\n<link rel=\"alternate\" href=\"https://ocr.oldfish.cn/de/Article.aspx?id=83&slug=crnn-architecture\" hreflang=\"de\" />\r\n<link rel=\"alternate\" href=\"https://ocr.oldfish.cn/fr/Article.aspx?id=83&slug=crnn-architecture\" hreflang=\"fr\" />\r\n<link rel=\"alternate\" href=\"https://ocr.oldfish.cn/mr/Article.aspx?id=83&slug=crnn-architecture\" hreflang=\"mr\" />\r\n<link rel=\"alternate\" href=\"https://ocr.oldfish.cn/te/Article.aspx?id=83&slug=crnn-architecture\" hreflang=\"te\" />\r\n<link rel=\"alternate\" href=\"https://ocr.oldfish.cn/vi/Article.aspx?id=83&slug=crnn-architecture\" hreflang=\"vi\" />\r\n<link rel=\"alternate\" href=\"https://ocr.oldfish.cn/ko/Article.aspx?id=83&slug=crnn-architecture\" hreflang=\"ko\" />\r\n<link rel=\"alternate\" href=\"https://ocr.oldfish.cn/ta/Article.aspx?id=83&slug=crnn-architecture\" hreflang=\"ta\" />\r\n<link rel=\"alternate\" href=\"https://ocr.oldfish.cn/ur/Article.aspx?id=83&slug=crnn-architecture\" hreflang=\"ur\" />\r\n<link rel=\"alternate\" href=\"https://ocr.oldfish.cn/tr/Article.aspx?id=83&slug=crnn-architecture\" hreflang=\"tr\" />\r\n<link rel=\"alternate\" href=\"https://ocr.oldfish.cn/it/Article.aspx?id=83&slug=crnn-architecture\" hreflang=\"it\" />\r\n<link rel=\"alternate\" href=\"https://ocr.oldfish.cn/gu/Article.aspx?id=83&slug=crnn-architecture\" hreflang=\"gu\" />\r\n<link rel=\"alternate\" href=\"https://ocr.oldfish.cn/pl/Article.aspx?id=83&slug=crnn-architecture\" hreflang=\"pl\" />\r\n<link rel=\"alternate\" href=\"https://ocr.oldfish.cn/uk/Article.aspx?id=83&slug=crnn-architecture\" hreflang=\"uk\" />\r\n<link rel=\"alternate\" href=\"https://ocr.oldfish.cn/ms/Article.aspx?id=83&slug=crnn-architecture\" hreflang=\"ms\" />\r\n<link rel=\"alternate\" href=\"https://ocr.oldfish.cn/id/Article.aspx?id=83&slug=crnn-architecture\" hreflang=\"id\" />\r\n<link rel=\"alternate\" href=\"https://ocr.oldfish.cn/ml/Article.aspx?id=83&slug=crnn-architecture\" hreflang=\"ml\" />\r\n<link rel=\"alternate\" href=\"https://ocr.oldfish.cn/kn/Article.aspx?id=83&slug=crnn-architecture\" hreflang=\"kn\" />\r\n<link rel=\"alternate\" href=\"https://ocr.oldfish.cn/fa/Article.aspx?id=83&slug=crnn-architecture\" hreflang=\"fa\" />\r\n<link rel=\"alternate\" href=\"https://ocr.oldfish.cn/nl/Article.aspx?id=83&slug=crnn-architecture\" hreflang=\"nl\" />\r\n<link rel=\"alternate\" href=\"https://ocr.oldfish.cn/th/Article.aspx?id=83&slug=crnn-architecture\" hreflang=\"th\" />\r\n<link rel=\"alternate\" href=\"https://ocr.oldfish.cn/sw/Article.aspx?id=83&slug=crnn-architecture\" hreflang=\"sw\" />\r\n<link rel=\"alternate\" href=\"https://ocr.oldfish.cn/ro/Article.aspx?id=83&slug=crnn-architecture\" hreflang=\"ro\" />\r\n<link rel=\"alternate\" href=\"https://ocr.oldfish.cn/my/Article.aspx?id=83&slug=crnn-architecture\" hreflang=\"my\" />\r\n<link rel=\"alternate\" href=\"https://ocr.oldfish.cn/or/Article.aspx?id=83&slug=crnn-architecture\" hreflang=\"or\" />\r\n<link rel=\"alternate\" href=\"https://ocr.oldfish.cn/he/Article.aspx?id=83&slug=crnn-architecture\" hreflang=\"he\" />\r\n<link rel=\"alternate\" href=\"https://ocr.oldfish.cn/am/Article.aspx?id=83&slug=crnn-architecture\" hreflang=\"am\" />\r\n<link rel=\"alternate\" href=\"https://ocr.oldfish.cn/fil/Article.aspx?id=83&slug=crnn-architecture\" hreflang=\"fil\" />\r\n<link rel=\"alternate\" href=\"https://ocr.oldfish.cn/sv/Article.aspx?id=83&slug=crnn-architecture\" hreflang=\"sv\" />\r\n<link rel=\"alternate\" href=\"https://ocr.oldfish.cn/el/Article.aspx?id=83&slug=crnn-architecture\" hreflang=\"el\" />\r\n<link rel=\"alternate\" href=\"https://ocr.oldfish.cn/cs/Article.aspx?id=83&slug=crnn-architecture\" hreflang=\"cs\" />\r\n<link rel=\"alternate\" href=\"https://ocr.oldfish.cn/hu/Article.aspx?id=83&slug=crnn-architecture\" hreflang=\"hu\" />\r\n<link rel=\"alternate\" href=\"https://ocr.oldfish.cn/be/Article.aspx?id=83&slug=crnn-architecture\" hreflang=\"be\" />\r\n<link rel=\"alternate\" href=\"https://ocr.oldfish.cn/si/Article.aspx?id=83&slug=crnn-architecture\" hreflang=\"si\" />\r\n<link rel=\"alternate\" href=\"https://ocr.oldfish.cn/ne/Article.aspx?id=83&slug=crnn-architecture\" hreflang=\"ne\" />\r\n<link rel=\"alternate\" href=\"https://ocr.oldfish.cn/km/Article.aspx?id=83&slug=crnn-architecture\" hreflang=\"km\" />\r\n<link rel=\"alternate\" href=\"https://ocr.oldfish.cn/sk/Article.aspx?id=83&slug=crnn-architecture\" hreflang=\"sk\" />\r\n<link rel=\"alternate\" href=\"https://ocr.oldfish.cn/bg/Article.aspx?id=83&slug=crnn-architecture\" hreflang=\"bg\" />\r\n<link rel=\"alternate\" href=\"https://ocr.oldfish.cn/fr-ca/Article.aspx?id=83&slug=crnn-architecture\" hreflang=\"fr-ca\" />\r\n<link rel=\"alternate\" href=\"https://ocr.oldfish.cn/ha/Article.aspx?id=83&slug=crnn-architecture\" hreflang=\"ha\" />\r\n<link rel=\"alternate\" href=\"https://ocr.oldfish.cn/yo/Article.aspx?id=83&slug=crnn-architecture\" hreflang=\"yo\" />\r\n<link rel=\"alternate\" href=\"https://ocr.oldfish.cn/ig/Article.aspx?id=83&slug=crnn-architecture\" hreflang=\"ig\" />\r\n<link rel=\"alternate\" href=\"https://ocr.oldfish.cn/ku/Article.aspx?id=83&slug=crnn-architecture\" hreflang=\"ku\" />\r\n<link rel=\"alternate\" href=\"https://ocr.oldfish.cn/rw/Article.aspx?id=83&slug=crnn-architecture\" hreflang=\"rw\" />\r\n<link rel=\"alternate\" href=\"https://ocr.oldfish.cn/ca/Article.aspx?id=83&slug=crnn-architecture\" hreflang=\"ca\" />\r\n<link rel=\"alternate\" href=\"https://ocr.oldfish.cn/da/Article.aspx?id=83&slug=crnn-architecture\" hreflang=\"da\" />\r\n<link rel=\"alternate\" href=\"https://ocr.oldfish.cn/fi/Article.aspx?id=83&slug=crnn-architecture\" hreflang=\"fi\" />\r\n<link rel=\"alternate\" href=\"https://ocr.oldfish.cn/nb/Article.aspx?id=83&slug=crnn-architecture\" hreflang=\"nb\" />\r\n<link rel=\"alternate\" href=\"https://ocr.oldfish.cn/hr/Article.aspx?id=83&slug=crnn-architecture\" hreflang=\"hr\" />\r\n<link rel=\"alternate\" href=\"https://ocr.oldfish.cn/sr-Cyrl/Article.aspx?id=83&slug=crnn-architecture\" hreflang=\"sr-cyrl\" />\r\n<link rel=\"alternate\" href=\"https://ocr.oldfish.cn/sr-Latn/Article.aspx?id=83&slug=crnn-architecture\" hreflang=\"sr-latn\" />\r\n<link rel=\"alternate\" href=\"https://ocr.oldfish.cn/sq/Article.aspx?id=83&slug=crnn-architecture\" hreflang=\"sq\" />\r\n<link rel=\"alternate\" href=\"https://ocr.oldfish.cn/so/Article.aspx?id=83&slug=crnn-architecture\" hreflang=\"so\" />\r\n<link rel=\"alternate\" href=\"https://ocr.oldfish.cn/zu/Article.aspx?id=83&slug=crnn-architecture\" hreflang=\"zu\" />\r\n<link rel=\"alternate\" href=\"https://ocr.oldfish.cn/ka/Article.aspx?id=83&slug=crnn-architecture\" hreflang=\"ka\" />\r\n<meta http-equiv=\"Content-Type\" content=\"text/html; charset=UTF-8\" /><meta http-equiv=\"X-UA-Compatible\" content=\"IE=Edge,chrome=1\" /><meta name=\"renderer\" content=\"webkit\" /><meta name=\"viewport\" content=\"width=device-width, initial-scale=1.0, maximum-scale=5.0\" /><link rel=\"icon\" href=\"/favicon.ico\" type=\"image/x-icon\" /><meta name=\"robots\" content=\"all\" /><link rel=\"preconnect\" href=\"https://scm-file-new-1301864755.cos.ap-beijing.myqcloud.com\" /><link rel=\"preconnect\" href=\"https://cdn.oldfish.cn\" /><link rel=\"dns-prefetch\" href=\"https://hm.baidu.com\" /><link rel=\"dns-prefetch\" href=\"https://cdn.oldfish.cn\" /><link rel=\"preload\" href=\"/site/css/site.css\" as=\"style\" />\r\n<script>function toArray(l){var a=[];for(var i=0;i<(l?l.length:0);i++)a.push(l[i]);return a;}function forEach(l,f){for(var i=0,n=(l?l.length:0);i<n;i++)f(l[i],i);}function hasClass(e,c){if(!e||!c)return false;return(' '+e.className+' ').indexOf(' '+c+' ')>-1;}function addClass(e,c){if(!e||!c||hasClass(e,c))return;e.className=(e.className?e.className+' ':'')+c;}function removeClass(e,c){if(!e||!c)return;var u=' '+e.className+' ';while(u.indexOf(' '+c+' ')>-1){u=u.replace(' '+c+' ',' ');}e.className=u.replace(/^\\s+|\\s+$/g,'');}function toggleClass(e,c){if(!e||!c)return;if(hasClass(e,c))removeClass(e,c);else addClass(e,c);}function matchesSelector(e,s){if(!e||e.nodeType!==1)return false;if(e.matches)return e.matches(s);var p=e.parentNode||document;var m=toArray(p.querySelectorAll(s));for(var i=0;i<m.length;i++)if(m[i]===e)return true;return false;}function closest(e,s){var n=e;while(n&&n.nodeType===1){if(matchesSelector(n,s))return n;n=n.parentNode;}return null;}function safeRemove(e){if(e&&e.parentNode)e.parentNode.removeChild(e);}function qa(s,r){return toArray((r||document).querySelectorAll(s));}function q(s,r){return(r||document).querySelector(s);}function xhrGet(u,o,e){try{var x=new XMLHttpRequest();x.open('GET',u,true);x.onreadystatechange=function(){if(x.readyState===4){if(x.status>=200&&x.status<300)o&&o(x.responseText);else e&&e(x);}};x.send(null);}catch(r){e&&e(r);}}function getScrollY(){return window.pageYOffset||document.documentElement.scrollTop||document.body.scrollTop||0;}function getOrigin(){return window.location.protocol+'//'+window.location.host;}</script>\r\n<script>var isWebP=(function(){try{return document.createElement('canvas').toDataURL('image/webp').indexOf('data:image/webp')===0;}catch(e){return false;}})();document.addEventListener('DOMContentLoaded',function(){var c=document.getElementById('user-login-container');if(c){var u=c.getAttribute('data-url');if(u){xhrGet(u,function(h){c.innerHTML=h;forEach(qa('a',c),function(l){l.style.display='inline-block';l.style.marginLeft='10px';});});}}});</script>\r\n    \n    \n\n    <meta name=\"description\" content=\"Phân tích chi tiết kiến trúc CRNN, bao gồm trích xuất tính năng CNN, mô hình trình tự RNN và triển khai hoàn chỉnh chức năng mất CTC. Đi sâu vào sự kết hợp hoàn hảo giữa CNN và RNN.\" />\n    <meta name=\"keywords\" content=\"CRNN, CNN, RNN, LSTM, CTC, OCR, học sâu, đầu cuối, mô hình hóa trình tự, nhận dạng văn bản OCR, hình ảnh thành văn bản, công nghệ OCR\" />\n    <meta property=\"og:title\" content=\"【Deep Learning OCR Series·6】Phân tích chuyên sâu về kiến trúc CRNN\" />\n    <meta property=\"og:description\" content=\"Phân tích chi tiết kiến trúc CRNN, bao gồm trích xuất tính năng CNN, mô hình trình tự RNN và triển khai hoàn chỉnh chức năng mất CTC. Đi sâu vào sự kết hợp hoàn hảo giữa CNN và RNN.\" />\n    <meta property=\"og:type\" content=\"article\" />\n    <meta property=\"og:url\" content=\"https://ocr.oldfish.cn/Article.aspx?id=83&slug=crnn-architecture&lang=zh-Hans&_translation_source=true\" />\n    <meta property=\"og:site_name\" content=\"Trợ lý nhận dạng văn bản OCR\" />\n    <meta name=\"twitter:card\" content=\"summary\" />\n    <meta name=\"twitter:title\" content=\"【Deep Learning OCR Series·6】Phân tích chuyên sâu về kiến trúc CRNN\" />\n    <meta name=\"twitter:description\" content=\"Phân tích chi tiết kiến trúc CRNN, bao gồm trích xuất tính năng CNN, mô hình trình tự RNN và triển khai hoàn chỉnh chức năng mất CTC. Đi sâu vào sự kết hợp hoàn hảo giữa CNN và RNN.\" />\n\n    \n    <script type=\"application/ld+json\">\n    {\n        \"@context\": \"https://schema.org\",\n        \"@type\": \"Article\",\n        \"headline\": \"【Deep Learning OCR Series 6] Phân tích chuyên sâu về kiến trúc CRNN\",\n        \"description\": \"Phân tích chi tiết kiến trúc CRNN, bao gồm trích xuất tính năng CNN, mô hình trình tự RNN và triển khai hoàn chỉnh chức năng mất CTC. Đi sâu vào sự kết hợp hoàn hảo giữa CNN và RNN。\",\n        \"publisher\": {\n            \"@type\": \"Organization\",\n            \"name\": \"Nhóm trợ lý nhận dạng văn bản OCR\"\n        },\n        \"datePublished\": \"2025-08-19T06:32:14Z\",\n        \"dateModified\": \"2025-08-19T06:32:14Z\",\n        \"mainEntityOfPage\": {\n            \"@type\": \"WebPage\",\n            \"@id\": \"https://ocr.oldfish.cn/Article.aspx?id=83&slug=crnn-architecture&lang=zh-Hans&_translation_source=true\"\n        }\n    }\n    </script>\n    \n    <style>\n        .reading-progress { position: fixed; top: 0; left: 0; width: 0%; height: 3px; background: linear-gradient(90deg, #1764ff, #0d47a1); z-index: 9999; transition: width 0.2s ease; box-shadow: 0 1px 3px rgba(23, 100, 255, 0.3); }\n        .home-container.root { overflow: visible !important; }\n        .article-detail-container { background: #f5f7fc; min-height: calc(100vh - 60px); padding: 80px 0 60px; position: relative; overflow: visible; }\n        .article-detail-container .row { margin: 0 -12px; align-items: flex-start !important; min-height: 100vh !important; }\n        .article-detail-container .row > [class*=\"col-\"] { padding: 0 12px; box-sizing: border-box; min-width: 0; }\n        .article-detail-container .row > .col-lg-8, .article-detail-container .row > .col-xl-9 { overflow: hidden; }\n        .article-detail-container .row > .col-lg-4, .article-detail-container .row > .col-xl-3 { overflow: visible !important; height: 100% !important; min-height: 100% !important; position: static !important; display: block !important; transform: none !important; will-change: auto !important; contain: none !important; perspective: none !important; filter: none !important; }\n        .article-detail { background: #fff; border-radius: 8px; padding: 1.5rem; margin-bottom: 2rem; box-shadow: 0 2px 10px rgba(0,0,0,0.1); width: 100%; box-sizing: border-box; }\n        .article-title { font-size: 1.7rem; color: #1a1c1f; line-height: 1.3; margin-bottom: 1.5rem; font-weight: 600; }\n        .article-meta { display: flex; align-items: center; flex-wrap: nowrap; gap: 1.2rem; color: #6c757d; font-size: 0.88rem; margin-bottom: 2rem; padding: 1.2rem 1.4rem; background: linear-gradient(135deg, #f8f9fa 0%, #ffffff 100%); border-radius: 12px; border-left: 4px solid #1764ff; box-shadow: 0 2px 8px rgba(0,0,0,0.06); overflow-x: hidden; transition: all 0.3s ease; }\n        .article-meta:hover { box-shadow: 0 4px 16px rgba(0,0,0,0.1); transform: translateY(-1px); }\n        .article-meta .meta-item { display: flex; align-items: center; gap: 0.25rem; font-weight: 500; color: #495057; white-space: nowrap; flex-shrink: 1; transition: color 0.2s ease; cursor: default; }\n        .article-meta .meta-item:hover { color: #1764ff; }\n        .article-meta .meta-item:hover .meta-icon { transform: scale(1.1); filter: drop-shadow(0 2px 6px rgba(13, 71, 161, 0.25)); }\n        .meta-icon { display: inline-block; font-size: 16px; flex-shrink: 0; vertical-align: middle; transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1); margin-right: 4px; opacity: 0.9; }\n        .meta-item:hover .meta-icon { opacity: 1; }\n        .meta-item:hover { color: #1764ff; }\n        .meta-item { position: relative; }\n        .meta-item::after { content: ''; position: absolute; bottom: -2px; left: 0; width: 0; height: 2px; background: linear-gradient(90deg, #1764ff, #00d4ff); transition: width 0.3s ease; }\n        .meta-item:hover::after { width: 100%; }\n        .article-category, .read-time { display: inline-block; padding: 6px 12px; border-radius: 16px; font-size: 0.8rem; font-weight: 600; }\n        .article-category { background: linear-gradient(135deg, #1764ff, #0d47a1); color: white; text-decoration: none; text-transform: uppercase; letter-spacing: 0.5px; transition: all 0.3s ease; }\n        .article-category:hover { background: linear-gradient(135deg, #0d47a1, #1764ff); color: white; text-decoration: none; transform: translateY(-1px); box-shadow: 0 4px 12px rgba(23, 100, 255, 0.3); }\n        .read-time { background: #e3f2fd; color: #1565c0; border: 1px solid #90caf9; }\n        .article-tags { margin: 20px 0 1.5rem; }\n        .article-tags .tag { display: inline-block; background: #1764ff; color: #fff; padding: 4px 12px; border-radius: 12px; font-size: 0.8rem; margin: 0 8px 4px 0; }\n        .article-summary { background: #e3f2fd; border-left: 4px solid #1764ff; padding: 1rem 1.5rem; margin-bottom: 2rem; border-radius: 0 4px 4px 0; }\n        .article-content { line-height: 1.8; font-size: 16px; color: #333; word-wrap: break-word; }\n        .article-content h1, .article-content h2, .article-content h3, .article-content h4, .article-content h5, .article-content h6 { margin: 2rem 0 1rem; font-weight: 600; line-height: 1.25; color: #1a1c1f; }\n        .article-content h1 { font-size: 2rem; border-bottom: 2px solid #e9ecef; padding-bottom: 0.5rem; }\n        .article-content h2 { font-size: 1.5rem; border-bottom: 1px solid #e9ecef; padding-bottom: 0.3rem; }\n        .article-content h3 { font-size: 1.25rem; }\n        .article-content h4 { font-size: 1.1rem; }\n        .article-content h5, .article-content h6 { font-size: 1rem; }\n        .article-content ul, .article-content ol { margin: 1rem 0; padding-left: 2rem; }\n        .article-content ul ul, .article-content ol ol, .article-content ul ol, .article-content ol ul { margin: 0.5rem 0; }\n        .article-content blockquote { margin: 1rem 0; padding: 0.5rem 1rem; border-left: 4px solid #1764ff; background-color: #f8f9fa; color: #6c757d; font-style: italic; }\n        .article-content pre { background-color: #f8f9fa; border: 1px solid #e9ecef; border-radius: 6px; padding: 1rem; margin: 1rem 0; overflow-x: auto; font-family: 'Courier New', Courier, monospace; font-size: 0.9em; line-height: 1.4; }\n        .article-content pre code { background: none; border: none; padding: 0; color: inherit; }\n        .article-content p { margin: 1rem 0; }\n        .article-content p:first-child { margin-top: 0; }\n        .article-content p:last-child { margin-bottom: 0; }\n        .article-footer { margin-top: 4rem; padding: 2.5rem; background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%); border-radius: 12px; border: 1px solid #e9ecef; position: relative; overflow: hidden; }\n        .article-footer::before { content: ''; position: absolute; top: 0; left: 0; right: 0; height: 3px; background: linear-gradient(90deg, #1764ff, #17a2b8, #28a745); }\n        .article-share h6 { color: #495057; font-weight: 600; margin-bottom: 1.5rem; font-size: 16px; }\n        .share-buttons { display: flex; gap: 12px; flex-wrap: wrap; }\n        .share-buttons .btn { padding: 10px 20px; border-radius: 25px; font-weight: 500; font-size: 14px; border: none; transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1); box-shadow: 0 2px 8px rgba(0,0,0,0.1); display: inline-flex; align-items: center; gap: 8px; text-decoration: none; }\n        .btn-weibo { background: linear-gradient(135deg, #e6162d, #c41230); color: white; }\n        .btn-copy { background: #1764ff; color: white; }\n        .btn-print { background: linear-gradient(135deg, #17a2b8, #138496); color: white; }\n        .btn-weibo:hover { background: linear-gradient(135deg, #c41230, #a00e26); box-shadow: 0 4px 16px rgba(230, 22, 45, 0.3); }\n        .btn-copy:hover { background: #0d47a1; box-shadow: 0 4px 16px rgba(23, 100, 255, 0.3); }\n        .btn-print:hover { background: linear-gradient(135deg, #138496, #117a8b); box-shadow: 0 4px 16px rgba(23, 162, 184, 0.3); }\n        .btn-weibo:hover, .btn-copy:hover, .btn-print:hover { transform: translateY(-2px); color: white; }\n        .article-actions { display: flex; justify-content: flex-end; align-items: center; }\n        .related-articles { margin-top: 3rem; }\n        .related-article-item { background: #fff; border-radius: 8px; padding: 1.5rem; margin-bottom: 1rem; box-shadow: 0 2px 8px rgba(0,0,0,0.1); transition: transform 0.3s ease; }\n        .related-article-item:hover { transform: translateY(-2px); }\n        .sidebar-card { background: linear-gradient(135deg, rgba(255,255,255,0.9) 0%, rgba(255,255,255,0.8) 100%); backdrop-filter: blur(20px); border: 1px solid rgba(255,255,255,0.3); border-radius: 16px; padding: 1rem; margin-bottom: 0.5rem; box-shadow: 0 4px 20px rgba(0,0,0,0.08), inset 0 1px 0 rgba(255,255,255,0.2); overflow: visible; box-sizing: border-box; width: 100%; z-index: 10; }\n        .sidebar-card.fixed { box-shadow: 0 8px 32px rgba(0,0,0,0.12); }\n        .sidebar-card::before { content: ''; position: absolute; top: 0; left: 0; right: 0; height: 3px; background: linear-gradient(90deg, #00d4ff, #1764ff, #9c27b0); border-radius: 20px 20px 0 0; }\n        .sidebar-card h5 { color: #1a202c; padding-bottom: 0.8rem; border-bottom: 2px solid rgba(23, 100, 255, 0.2); font-weight: 700; font-size: 1.1rem; }\n        .sidebar { min-height: 100vh; position: relative !important; display: block !important; flex-direction: column !important; gap: 1rem; }\n        .toc-card { flex: 0 1 auto; max-height: 60vh; overflow-y: auto; }\n        .sidebar-card:not(.toc-card) { flex: 1 1 auto; overflow: visible; }\n        .recommended-item { padding: 0.75rem 0; border-bottom: 1px solid rgba(0,0,0,0.05); }\n        .recommended-item:last-child { border-bottom: none; }\n        .recommended-item h6 { margin: 0 0 0.5rem 0; font-size: 0.9rem; line-height: 1.4; }\n        .recommended-item h6 a { color: #1a1c1f; text-decoration: none; transition: color 0.3s ease; }\n        .recommended-item h6 a:hover { color: #1764ff; }\n        .recommended-item .text-muted { font-size: 0.75rem; margin: 0; }\n        .toc-list { list-style: none; padding: 0; margin: 0; max-height: none; overflow-y: visible; overflow-x: hidden; }\n        .toc-list::-webkit-scrollbar { width: 4px; }\n        .toc-list::-webkit-scrollbar-track { background: rgba(0,0,0,0.1); border-radius: 2px; }\n        .toc-list::-webkit-scrollbar-thumb { background: linear-gradient(135deg, #00d4ff, #1764ff); border-radius: 2px; }\n        .toc-list::-webkit-scrollbar-thumb:hover { background: linear-gradient(135deg, #1764ff, #0d47a1); }\n        .toc-list a { color: #4a5568; text-decoration: none; font-size: 13px; font-weight: 500; display: block; padding: 8px 12px; border-radius: 8px; transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1); position: relative; margin-bottom: 4px; border: 1px solid transparent; }\n        .toc-list a::before { content: ''; position: absolute; left: 0; top: 0; bottom: 0; width: 3px; background: linear-gradient(135deg, #00d4ff, #1764ff); border-radius: 0 12px 12px 0; transform: scaleY(0); transition: transform 0.3s ease; }\n        .toc-list a:hover { color: #1764ff; background: linear-gradient(135deg, rgba(0, 212, 255, 0.1), rgba(23, 100, 255, 0.1)); border-color: rgba(23, 100, 255, 0.2); transform: translateX(8px); box-shadow: 0 4px 12px rgba(0, 212, 255, 0.2); }\n        .toc-list a:hover::before, .toc-list a.active::before { transform: scaleY(1); }\n        .toc-list a.active { color: #1764ff; background: linear-gradient(135deg, rgba(0, 212, 255, 0.15), rgba(23, 100, 255, 0.15)); font-weight: 700; border-color: rgba(23, 100, 255, 0.3); transform: translateX(4px); box-shadow: 0 6px 16px rgba(0, 212, 255, 0.25); }\n        .toc-number { color: #1764ff; font-weight: 600; margin-right: 0.5rem; font-size: 0.9em; }\n        .toc-text { flex: 1; }\n        .toc-level-1 { font-weight: 700; font-size: 15px; }\n        .toc-level-2 { font-size: 14px; }\n        .toc-level-3 { font-size: 13px; opacity: 0.9; }\n        .toc-level-4, .toc-level-5, .toc-level-6 { font-size: 12px; opacity: 0.8; }\n        .article-content .formatted-list { margin: 1.5rem 0; padding-left: 0; list-style: none; }\n        .article-content .formatted-list.ordered { counter-reset: list-counter; }\n        .article-content .formatted-list.ordered li { counter-increment: list-counter; position: relative; margin: 1rem 0; padding: 1rem 1rem 1rem 3rem; background: linear-gradient(135deg, rgba(23, 100, 255, 0.03), rgba(0, 212, 255, 0.03)); border-left: 4px solid #1764ff; border-radius: 0 8px 8px 0; transition: all 0.3s ease; }\n        .article-content .formatted-list.ordered li::before { content: counter(list-counter); position: absolute; left: 0.8rem; top: 1rem; background: linear-gradient(135deg, #1764ff, #00d4ff); color: white; width: 1.5rem; height: 1.5rem; border-radius: 50%; display: flex; align-items: center; justify-content: center; font-weight: 600; font-size: 0.9rem; }\n        .article-content .formatted-list.ordered li:hover { background: linear-gradient(135deg, rgba(23, 100, 255, 0.06), rgba(0, 212, 255, 0.06)); transform: translateX(4px); box-shadow: 0 2px 8px rgba(23, 100, 255, 0.15); }\n        .article-content .formatted-list.unordered li { position: relative; margin: 0.8rem 0; padding: 0.8rem 1rem 0.8rem 2.5rem; background: linear-gradient(135deg, rgba(40, 167, 69, 0.03), rgba(25, 135, 84, 0.03)); border-left: 4px solid #28a745; border-radius: 0 6px 6px 0; transition: all 0.3s ease; }\n        .article-content .formatted-list.unordered li::before { content: \"•\"; position: absolute; left: 1rem; top: 0.8rem; color: #28a745; font-size: 1.2rem; font-weight: bold; }\n        .article-content .formatted-list.unordered li:hover { background: linear-gradient(135deg, rgba(40, 167, 69, 0.06), rgba(25, 135, 84, 0.06)); transform: translateX(4px); box-shadow: 0 2px 8px rgba(40, 167, 69, 0.15); }\n        .article-content .list-title { color: #1764ff; font-weight: 600; display: inline-block; margin-bottom: 0.3rem; }\n        .article-content .list-content { color: #2c3e50; line-height: 1.6; display: block; }\n        .article-content .inline-code { background: #f8f9fa; border: 1px solid #e9ecef; border-radius: 4px; padding: 2px 6px; font-family: 'Consolas', 'Monaco', 'Courier New', monospace; font-size: 0.9em; color: #e83e8c; }\n        .article-content .label-item { margin: 0.8rem 0; }\n        .article-content .label { color: #1764ff; font-weight: 600; }\n        .article-content .section-label { color: #2c3e50; font-weight: 600; margin: 1rem 0 0.5rem 0; font-size: 1.1em; border-left: 4px solid #1764ff; padding-left: 0.8rem; }\n        .article-content p, .article-content .content-paragraph { margin: 1.2rem 0; line-height: 1.8; text-align: justify; color: #2c3e50; font-size: 1rem; }\n        .article-content .content-paragraph { text-indent: 0; margin: 1rem 0; }\n        .article-content .indent-paragraph { text-indent: 2em; margin: 1.2rem 0; }\n        .article-content .text-indent-1 { text-indent: 2em; display: block; }\n        .article-content .text-indent-2 { text-indent: 4em; display: block; }\n        .article-content .text-indent-3 { text-indent: 6em; display: block; }\n        .article-content .network-config { background: linear-gradient(135deg, #f8f9fa, #e9ecef); border: 1px solid #dee2e6; border-radius: 6px; padding: 0.8rem 1rem; margin: 0.5rem 0; font-family: 'Consolas', 'Monaco', 'Courier New', monospace; font-size: 0.9rem; box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05); }\n        .article-content .config-label { color: #1764ff; font-weight: 600; }\n        .article-content .config-desc { color: #495057; }\n        .article-content .algorithm-step { background: linear-gradient(135deg, rgba(23, 100, 255, 0.05), rgba(0, 212, 255, 0.05)); border-left: 4px solid #1764ff; padding: 0.8rem 1rem; margin: 0.5rem 0; border-radius: 0 6px 6px 0; transition: all 0.3s ease; }\n        .article-content .step-number { color: #1764ff; font-weight: 700; margin-right: 0.5rem; }\n        .article-content .step-desc { color: #2c3e50; line-height: 1.5; }\n        .article-content table { width: 100%; border-collapse: collapse; margin: 1.5rem 0; background: white; border-radius: 8px; overflow: hidden; box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1); }\n        .article-content table th { background: linear-gradient(135deg, #1764ff, #00d4ff); color: white; padding: 1rem; text-align: left; font-weight: 600; }\n        .article-content table td { padding: 0.8rem 1rem; border-bottom: 1px solid #e9ecef; }\n        .article-content table tr:hover { background: rgba(23, 100, 255, 0.05); }\n        .article-content strong { color: #1764ff; font-weight: 600; }\n        .article-content em { color: #6c757d; font-style: italic; }\n        .article-content a { color: #1764ff; text-decoration: none; border-bottom: 1px solid transparent; transition: all 0.3s ease; }\n        .article-content a:hover { border-bottom-color: #1764ff; color: #0056b3; }\n        .article-content .definition-item { background: linear-gradient(135deg, rgba(40, 167, 69, 0.05), rgba(25, 135, 84, 0.05)); border-left: 4px solid #28a745; padding: 1rem; margin: 1rem 0; border-radius: 0 8px 8px 0; }\n        .article-content .definition-label { color: #28a745; font-weight: 600; display: block; margin-bottom: 0.5rem; }\n        .article-content .definition-content { color: #2c3e50; line-height: 1.6; }\n        .article-content .note-item { background: linear-gradient(135deg, rgba(255, 193, 7, 0.1), rgba(255, 193, 7, 0.05)); border: 1px solid rgba(255, 193, 7, 0.3); border-radius: 8px; padding: 1rem; margin: 1rem 0; position: relative; }\n        .article-content .note-item::before { content: \"💡\"; position: absolute; left: 1rem; top: 1rem; font-size: 1.2em; }\n        .article-content .note-label { color: #856404; font-weight: 600; margin-left: 2rem; }\n        .article-content .note-content { color: #2c3e50; line-height: 1.6; margin-left: 2rem; display: block; margin-top: 0.3rem; }\n        .article-content .math-formula { background: #f8f9fa; border: 1px solid #e9ecef; border-radius: 6px; padding: 1rem; margin: 1rem 0; text-align: center; font-family: 'Times New Roman', serif; font-size: 1.1em; color: #2c3e50; }\n        @media (min-width: 1400px) { .article-content { font-size: 17px; line-height: 1.8; } .article-detail { padding: 2.5rem; } }\n        @media (max-width: 1199px) and (min-width: 992px) { .article-detail { padding: 1.5rem; } .sidebar-card { padding: 1rem; } .toc-list a { font-size: 12px; padding: 6px 8px; } }\n        @media (max-width: 768px) {\n            .article-detail-container { padding: 60px 15px; }\n            .article-content .network-config, .article-content .algorithm-step, .article-content .definition-item, .article-content .note-item { margin: 0.5rem -1rem; border-radius: 0; }\n            .article-content .note-item::before { left: 0.5rem; }\n            .article-content .note-label, .article-content .note-content { margin-left: 1.5rem; }\n            .article-detail { padding: 1.5rem; }\n            .article-title { font-size: 1.6rem; line-height: 1.3; }\n            .article-meta { flex-wrap: wrap; gap: 1rem; padding: 1rem; font-size: 0.8rem; }\n            .article-meta .meta-item { flex-shrink: 1; }\n            .meta-icon { font-size: 14px; margin-right: 3px; }\n            .article-content { font-size: 15px; }\n            .article-content h1 { font-size: 1.6rem; }\n            .article-content h2 { font-size: 1.4rem; }\n            .article-content h3 { font-size: 1.2rem; }\n            .article-content h4 { font-size: 1.1rem; }\n            .article-content pre { padding: 0.75rem; font-size: 0.85em; overflow-x: auto; }\n            .article-content code { font-size: 0.85em; padding: 1px 4px; }\n            .sidebar { margin-top: 2rem; }\n            .sidebar-card { position: static; }\n            .toc-card { max-height: none; overflow-y: visible; }\n            .share-buttons { flex-direction: column; gap: 0.5rem; }\n            .share-buttons .btn { width: 100%; justify-content: center; }\n        }\n        @media print { body * { visibility: hidden; } .article-detail, .article-detail * { visibility: visible; } .article-detail { position: absolute; left: 0; top: 0; width: 100%; background: white; box-shadow: none; border: none; margin: 0; padding: 20px; } .article-meta { border: none; background: none; padding: 0; margin-bottom: 20px; } .article-footer, .reading-progress, .back-to-top { display: none !important; } .article-content { font-size: 12pt; line-height: 1.6; } .article-content h1, .article-content h2, .article-content h3, .article-content h4, .article-content h5, .article-content h6 { page-break-after: avoid; margin-top: 20pt; margin-bottom: 10pt; } .article-content pre, .article-content blockquote { page-break-inside: avoid; } }\n    </style>\n\n<script type=\"application/ld+json\">{\"@context\":\"https://schema.org\",\"@type\":\"BreadcrumbList\",\"itemListElement\":[{\"@type\":\"ListItem\",\"position\":1,\"name\":\"Nhà\",\"item\":\"https://ocr.oldfish.cn/default.aspx\"},{\"@type\":\"ListItem\",\"position\":2,\"name\":\"Bài viết kỹ thuật\",\"item\":\"https://ocr.oldfish.cn/Articles.aspx\"},{\"@type\":\"ListItem\",\"position\":3,\"name\":\"Chi tiết bài viết\",\"item\":\"https://ocr.oldfish.cn/Article.aspx?id=83&slug=crnn-architecture&lang=zh-Hans&_translation_source=true\"}]}</script>\n<link rel=\"stylesheet\" href=\"/site/css/site.css\" type=\"text/css\" /><title>【Deep Learning OCR Series·6】Phân tích chuyên sâu về kiến trúc CRNN</title><meta http-equiv=\"Content-Language\" content=\"vi\" />\n</head>\r\n<body>\r\n    <div class=\"home-container root\">\r\n        <header class=\"v4_header_pc\">\r\n            <div class=\"header_pc_left\">\r\n                <a href=\"Default.aspx\" title=\"Trang chủ | Nhận dạng văn bản thông minh AI\" class=\"pc-logo-wrap ml-4 mr-5\">\r\n                    <picture>\r\n                        <source srcset=\"site/image/logo-s.png.webp\" type=\"image/webp\" />\r\n                        <img src=\"site/image/logo-s.png\" class=\"pc-logo\" style=\"height: 100%\" alt=\"Logo trang web chính thức của Trợ lý nhận dạng văn bản OCR - Nền tảng nhận dạng văn bản thông minh AI\" />\r\n                    </picture>\r\n                    <span style=\"font-size: 18px; font-weight: bold; color: #333;\">Trợ lý nhận dạng văn bản OCR</span>\r\n                </a>\r\n                <ul class=\"top-nav\" role=\"navigation\" aria-label=\"Điều hướng chính\">\r\n                    <li><a class=\"color-default\" href=\"Default.aspx\" title=\"Trang chủ Trợ lý nhận dạng văn bản OCR\">Nhà</a></li>\r\n                    <li><a class=\"color-default\" data-sub=\"product-sub-nav\" href=\"Detail.aspx\" title=\"Giới thiệu chức năng sản phẩm OCR\">Tính năng sản phẩm:</a><div class=\"triangle\"></div>\r\n                    </li>\r\n                    <li><a class=\"color-default\" data-sub=\"experience-sub-nav\" href=\"Ocr.aspx\" title=\"Trải nghiệm các tính năng OCR trực tuyến\">Trải nghiệm trực tuyến</a><div class=\"triangle\"></div>\r\n                    </li>\r\n                    <li><a class=\"color-default\" data-sub=\"vip-sub-nav\" href=\"Version.aspx\" title=\"Dịch vụ nâng cấp thành viên OCR\">Nâng cấp tư cách thành viên</a><div class=\"triangle\"></div>\r\n                    </li>\r\n                    <li><a class=\"color-default\" href=\"Download.aspx\" title=\"Tải xuống Trợ lý nhận dạng văn bản OCR miễn phí\">Tải xuống miễn phí</a></li>\r\n                    <li><a class=\"color-default\" data-sub=\"articles-sub-nav\" href=\"Articles.aspx\" title=\"Các bài viết kỹ thuật OCR và chia sẻ kiến thức\">Chia sẻ công nghệ</a><div class=\"triangle\"></div>\r\n                    </li>\r\n                    <li><a class=\"color-default\" data-sub=\"support-sub-nav\" href=\"FAQ.aspx\" title=\"Trợ giúp sử dụng OCR và hỗ trợ kỹ thuật\">Trung tâm trợ giúp</a><div class=\"triangle\"></div>\r\n                    </li>\r\n                </ul>\r\n                <div class=\"product-con\">\r\n                    <section id=\"product-sub-nav\" class=\"pls-nav-dropdown\" style=\"visibility: hidden; height: 0px; display: none;\">\r\n                        <div class=\"d-flex nav-drown-con\" style=\"height: 0px; transition: transform 0.2s ease-in-out 0s,opacity 0.2s ease-in-out 0s; opacity: 0; transform: translate(0px, -100%);\">\r\n                            <div class=\"prdocu-sub-left\">\r\n                                <div class=\"mr-auto\">\r\n                                    <picture>\r\n                                        <source class=\"p-icon\" srcset=\"site/image/icon/p_1.png.webp\" type=\"image/webp\" alt=\"\" aria-hidden=\"true\" />\r\n                                        <img class=\"p-icon\" src=\"site/image/icon/p_1.png\" alt=\"Biểu tượng chức năng sản phẩm OCR\" aria-hidden=\"true\" loading=\"lazy\" />\r\n                                    </picture>\r\n                                    <div class=\"mt-4\">\r\n                                        <h3 class=\"h6\">Trợ lý nhận dạng văn bản OCR</h3>\r\n                                        <p class=\"color-gray fn14 mt-3 sub-left-desc\">Nâng cao hiệu quả, giảm chi phí và tạo ra giá trị</p>\r\n                                        <p class=\"color-gray fn14 mt-3 sub-left-desc\">&nbsp;</p>\r\n                                        <p class=\"color-gray fn14 mt-3 sub-left-desc\">Nhận dạng thông minh, xử lý tốc độ cao và đầu ra chính xác</p>\r\n                                        <p class=\"color-gray fn14 mt-3 sub-left-desc\">Từ văn bản đến bảng, từ công thức đến bản dịch</p>\r\n                                        <p class=\"color-gray fn14 mt-3 sub-left-desc\">Làm cho mọi xử lý văn bản trở nên dễ dàng</p>\r\n                                        <a class=\"link block disa fn14 contact-business shake\" href=\"Detail.aspx\">Tìm hiểu về các tính năng<span class=\"iconfont4 icon-xiangzuo gt\"></span></a>\r\n                                    </div>\r\n                                </div>\r\n                            </div>\r\n                            <div class=\"bg-white prdocu-sub-right\">\r\n                                <div>\r\n                                    <h2 class=\"text-base-color h6\">Tính năng sản phẩm:</h2>\r\n                                    <div class=\"mt-4 d-flex flex-wrap\">\r\n                                        <a href=\"Detail.aspx\" class=\"pro-item color-default\" title=\"Kiểm tra chi tiết các chức năng cốt lõi của Trợ lý OCR\">\r\n                                            <div class=\"letter-wrap\">\r\n                                                <h3 class=\"h6\">Các tính năng cốt lõi:</h3>\r\n                                                <span class=\"color-gray fn14\">Tìm hiểu thêm về các tính năng cốt lõi và lợi ích kỹ thuật của Trợ lý OCR, với tỷ lệ nhận dạng 98%+</span>\r\n                                            </div>\r\n                                        </a>\r\n                                        <a href=\"Version.aspx\" class=\"pro-item color-default\" title=\"So sánh sự khác biệt giữa các phiên bản OCR Assistant\">\r\n                                            <div class=\"letter-wrap\">\r\n                                                <h3 class=\"h6\">So sánh phiên bản</h3>\r\n                                                <span class=\"color-gray fn14\">So sánh chi tiết sự khác biệt về chức năng của phiên bản miễn phí, phiên bản cá nhân, phiên bản chuyên nghiệp và phiên bản cuối cùng</span>\r\n                                            </div>\r\n                                        </a>\r\n                                        <a href=\"FAQ.aspx\" class=\"pro-item color-default\" title=\"Kiểm tra Câu hỏi thường gặp về Trợ lý OCR\">\r\n                                            <div class=\"letter-wrap\">\r\n                                                <h3 class=\"h6\">Hỏi & Đáp sản phẩm</h3>\r\n                                                <span class=\"color-gray fn14\">Nhanh chóng tìm hiểu về các tính năng của sản phẩm, phương pháp sử dụng và câu trả lời chi tiết cho các câu hỏi thường gặp</span>\r\n                                            </div>\r\n                                        </a>\r\n                                        <a href=\"Download.aspx\" class=\"pro-item color-default\" title=\"Tải xuống Trợ lý nhận dạng văn bản OCR miễn phí\">\r\n                                            <div class=\"letter-wrap\">\r\n                                                <h3 class=\"h6\">Dùng thử miễn phí</h3>\r\n                                                <span class=\"color-gray fn14\">Tải xuống và cài đặt OCR Assistant ngay bây giờ để trải nghiệm chức năng nhận dạng văn bản mạnh mẽ miễn phí</span>\r\n                                            </div>\r\n                                        </a>\r\n                                        <div style=\"width: 100%; margin: 15px 0;\">\r\n                                            <h4 class=\"h6\" style=\"color: #007cfa; margin-bottom: 10px;\">Nhận dạng OCR trực tuyến</h4>\r\n                                        </div>\r\n                                        <a href=\"Ocr.aspx?type=text_recognize\" class=\"pro-item color-default\" title=\"Trải nghiệm nhận dạng văn bản phổ quát trực tuyến\">\r\n                                            <div class=\"letter-wrap\">\r\n                                                <h3 class=\"h6\">Nhận dạng ký tự phổ quát</h3>\r\n                                                <span class=\"color-gray fn14\">Trích xuất thông minh văn bản đa ngôn ngữ có độ chính xác cao, hỗ trợ nhận dạng hình ảnh phức tạp và in nhiều cảnh</span>\r\n                                            </div>\r\n                                        </a>\r\n                                        <a href=\"Ocr.aspx?type=table\" class=\"pro-item color-default\">\r\n                                            <div class=\"letter-wrap\">\r\n                                                <h3 class=\"h6\">Nhận dạng bảng phổ quát</h3>\r\n                                                <span class=\"color-gray fn14\">Chuyển đổi thông minh hình ảnh bảng sang tệp Excel, tự động xử lý cấu trúc bảng phức tạp và ô hợp nhất</span>\r\n                                            </div>\r\n                                        </a>\r\n                                        <a href=\"Ocr.aspx?type=handwritten_ocr\" class=\"pro-item color-default\">\r\n                                            <div class=\"letter-wrap\">\r\n                                                <h3 class=\"h6\">Nhận dạng chữ viết tay</h3>\r\n                                                <span class=\"color-gray fn14\">Nhận dạng thông minh nội dung viết tay tiếng Trung và tiếng Anh, hỗ trợ ghi chú lớp học, hồ sơ y tế và các tình huống khác</span>\r\n                                            </div>\r\n                                        </a>\r\n                                        <a href=\"Ocr.aspx?type=pdf2word\" class=\"pro-item color-default\">\r\n                                            <div class=\"letter-wrap\">\r\n                                                <h3 class=\"h6\">PDF sang Word</h3>\r\n                                                <span class=\"color-gray fn14\">Tài liệu PDF được chuyển đổi nhanh chóng sang định dạng Word, giữ nguyên hoàn hảo bố cục ban đầu và bố cục đồ họa</span>\r\n                                            </div>\r\n                                        </a>\r\n                                    </div>\r\n                                </div>\r\n                            </div>\r\n                        </div>\r\n                    </section>\r\n                    <section id=\"experience-sub-nav\" class=\"pls-nav-dropdown solution-part\" style=\"visibility: hidden; height: 0px; display: none;\">\r\n                        <div class=\"d-flex nav-drown-con\" style=\"height: 0px; transition: transform 0.2s ease-in-out 0s,opacity 0.2s ease-in-out 0s; opacity: 0; transform: translate(0px, -100%);\">\r\n                            <div class=\"prdocu-sub-left\">\r\n                                <div class=\"mr-auto\">\r\n                                    <picture>\r\n                                        <source srcset=\"site/image/icon/p_4.png.webp\" type=\"image/webp\" />\r\n                                        <img class=\"p-icon\" src=\"site/image/icon/p_4.png\" alt=\"Biểu tượng Trung tâm trải nghiệm OCR trực tuyến\" aria-hidden=\"true\" loading=\"lazy\" />\r\n                                    </picture>\r\n                                    <div class=\"mt-4\">\r\n                                        <h3 class=\"h6\">Trợ lý nhận dạng văn bản OCR</h3>\r\n                                        <p class=\"color-gray fn14 mt-3 sub-left-desc\">Văn bản, bảng, công thức, tài liệu, bản dịch</p>\r\n                                        <p class=\"color-gray fn14 mt-3 sub-left-desc\">&nbsp;</p>\r\n                                        <p class=\"color-gray fn14 mt-3 sub-left-desc\">Hoàn thành tất cả các nhu cầu xử lý văn bản của bạn trong ba bước</p>\r\n                                        <p class=\"color-gray fn14 mt-3 sub-left-desc\">Ảnh chụp màn hình → Xác định ứng dụng →</p>\r\n                                        <p class=\"color-gray fn14 mt-3 sub-left-desc\">Tăng hiệu quả công việc lên 300%</p>\r\n                                        <a class=\"link block disa fn14 contact-business shake\" href=\"Ocr.aspx\">Dùng thử ngay<span class=\"iconfont4 icon-xiangzuo gt\"></span></a>\r\n                                    </div>\r\n                                </div>\r\n                            </div>\r\n                            <div class=\"bg-white prdocu-sub-right\">\r\n                                <div>\r\n                                    <div class=\"mt-4 d-flex flex-wrap\">\r\n                                        <div style=\"width: 100%; margin-bottom: 15px;\">\r\n                                            <h4 class=\"h6\" style=\"color: #007cfa; margin-bottom: 10px;\">Trải nghiệm chức năng OCR</h4>\r\n                                        </div>\r\n                                        <a href=\"Ocr.aspx?type=index\" class=\"pro-item color-default\">\r\n                                            <div class=\"letter-wrap\">\r\n                                                <h3 class=\"h6\">Đầy đủ chức năng</h3>\r\n                                                <span class=\"color-gray fn14\">Trải nghiệm tất cả các tính năng thông minh OCR ở một nơi để nhanh chóng tìm ra giải pháp tốt nhất cho nhu cầu của bạn</span>\r\n                                            </div>\r\n                                        </a>\r\n                                        <a href=\"Ocr.aspx?type=text_recognize\" class=\"pro-item color-default\">\r\n                                            <div class=\"letter-wrap\">\r\n                                                <h3 class=\"h6\">Nhận dạng ký tự phổ quát</h3>\r\n                                                <span class=\"color-gray fn14\">Trích xuất thông minh văn bản đa ngôn ngữ có độ chính xác cao, hỗ trợ nhận dạng hình ảnh phức tạp và in nhiều cảnh</span>\r\n                                            </div>\r\n                                        </a>\r\n                                        <a href=\"Ocr.aspx?type=table\" class=\"pro-item color-default\">\r\n                                            <div class=\"letter-wrap\">\r\n                                                <h3 class=\"h6\">Nhận dạng bảng phổ quát</h3>\r\n                                                <span class=\"color-gray fn14\">Chuyển đổi thông minh hình ảnh bảng sang tệp Excel, tự động xử lý cấu trúc bảng phức tạp và ô hợp nhất</span>\r\n                                            </div>\r\n                                        </a>\r\n                                        <a href=\"Ocr.aspx?type=handwritten_ocr\" class=\"pro-item color-default\">\r\n                                            <div class=\"letter-wrap\">\r\n                                                <h3 class=\"h6\">Nhận dạng chữ viết tay</h3>\r\n                                                <span class=\"color-gray fn14\">Nhận dạng thông minh nội dung viết tay tiếng Trung và tiếng Anh, hỗ trợ ghi chú lớp học, hồ sơ y tế và các tình huống khác</span>\r\n                                            </div>\r\n                                        </a>\r\n                                        <a href=\"Ocr.aspx?type=pdf2word\" class=\"pro-item color-default\">\r\n                                            <div class=\"letter-wrap\">\r\n                                                <h3 class=\"h6\">PDF sang Word</h3>\r\n                                                <span class=\"color-gray fn14\">Tài liệu PDF được chuyển đổi nhanh chóng sang định dạng Word, giữ nguyên hoàn hảo bố cục ban đầu và bố cục đồ họa</span>\r\n                                            </div>\r\n                                        </a>\r\n                                        <a href=\"Ocr.aspx?type=pdf2markdown\" class=\"pro-item color-default\">\r\n                                            <div class=\"letter-wrap\">\r\n                                                <h3 class=\"h6\">PDF thành Markdown</h3>\r\n                                                <span class=\"color-gray fn14\">Tài liệu PDF được chuyển đổi thông minh sang định dạng MD, các khối mã và cấu trúc văn bản được tự động tối ưu hóa để xử lý</span>\r\n                                            </div>\r\n                                        </a>\r\n                                        <div style=\"width: 100%; margin: 15px 0;\">\r\n                                            <h4 class=\"h6\" style=\"color: #007cfa; margin-bottom: 10px;\">Công cụ xử lý tài liệu</h4>\r\n                                        </div>\r\n                                        <a href=\"Ocr.aspx?type=word2pdf\" class=\"pro-item color-default\">\r\n                                            <div class=\"letter-wrap\">\r\n                                                <h3 class=\"h6\">Word sang PDF</h3>\r\n                                                <span class=\"color-gray fn14\">Tài liệu Word được chuyển đổi sang PDF chỉ bằng một cú nhấp chuột, giữ nguyên hoàn hảo định dạng gốc, phù hợp để lưu trữ và chia sẻ tài liệu chính thức</span>\r\n                                            </div>\r\n                                        </a>\r\n                                        <a href=\"Ocr.aspx?type=word2jpg\" class=\"pro-item color-default\">\r\n                                            <div class=\"letter-wrap\">\r\n                                                <h3 class=\"h6\">Từ thành hình ảnh</h3>\r\n                                                <span class=\"color-gray fn14\">Chuyển đổi thông minh tài liệu Word sang hình ảnh JPG, hỗ trợ xử lý nhiều trang, dễ dàng chia sẻ trên mạng xã hội</span>\r\n                                            </div>\r\n                                        </a>\r\n                                        <a href=\"Ocr.aspx?type=pdf2jpg\" class=\"pro-item color-default\">\r\n                                            <div class=\"letter-wrap\">\r\n                                                <h3 class=\"h6\">PDF thành hình ảnh</h3>\r\n                                                <span class=\"color-gray fn14\">Chuyển đổi tài liệu PDF sang hình ảnh JPG ở độ nét cao, hỗ trợ xử lý hàng loạt và độ phân giải tùy chỉnh</span>\r\n                                            </div>\r\n                                        </a>\r\n                                        <a href=\"Ocr.aspx?type=image2pdf\" class=\"pro-item color-default\">\r\n                                            <div class=\"letter-wrap\">\r\n                                                <h3 class=\"h6\">Hình ảnh sang PDF</h3>\r\n                                                <span class=\"color-gray fn14\">Hợp nhất nhiều hình ảnh thành tài liệu PDF, hỗ trợ sắp xếp và thiết lập trang</span>\r\n                                            </div>\r\n                                        </a>\r\n                                        <div style=\"width: 100%; margin: 15px 0;\">\r\n                                            <h4 class=\"h6\" style=\"color: #007cfa; margin-bottom: 10px;\">Công cụ dành cho nhà phát triển</h4>\r\n                                        </div>\r\n                                        <a href=\"Tool.aspx?type=Json\" class=\"pro-item color-default\">\r\n                                            <div class=\"letter-wrap\">\r\n                                                <h3 class=\"h6\">Định dạng JSON</h3>\r\n                                                <span class=\"color-gray fn14\">Làm đẹp cấu trúc mã JSON một cách thông minh, hỗ trợ nén và mở rộng, đồng thời tạo điều kiện phát triển và gỡ lỗi</span>\r\n                                            </div>\r\n                                        </a>\r\n                                        <a href=\"Tool.aspx?type=Regex\" class=\"pro-item color-default\">\r\n                                            <div class=\"letter-wrap\">\r\n                                                <h3 class=\"h6\">Biểu thức chính quy</h3>\r\n                                                <span class=\"color-gray fn14\">Xác minh hiệu ứng khớp biểu thức chính quy trong thời gian thực, với thư viện tích hợp các mẫu phổ biến</span>\r\n                                            </div>\r\n                                        </a>\r\n                                        <a href=\"Tool.aspx?type=Encode\" class=\"pro-item color-default\">\r\n                                            <div class=\"letter-wrap\">\r\n                                                <h3 class=\"h6\">Chuyển đổi mã hóa văn bản</h3>\r\n                                                <span class=\"color-gray fn14\">Nó hỗ trợ chuyển đổi nhiều định dạng mã hóa như Base64, URL và Unicode</span>\r\n                                            </div>\r\n                                        </a>\r\n                                        <a href=\"Tool.aspx?type=Diff\" class=\"pro-item color-default\">\r\n                                            <div class=\"letter-wrap\">\r\n                                                <h3 class=\"h6\">Khớp và hợp nhất văn bản</h3>\r\n                                                <span class=\"color-gray fn14\">Làm nổi bật sự khác biệt của văn bản và hỗ trợ so sánh từng dòng và hợp nhất thông minh</span>\r\n                                            </div>\r\n                                        </a>\r\n                                        <a href=\"Tool.aspx?type=Color\" class=\"pro-item color-default\">\r\n                                            <div class=\"letter-wrap\">\r\n                                                <h3 class=\"h6\">Công cụ màu</h3>\r\n                                                <span class=\"color-gray fn14\">Chuyển đổi màu RGB / HEX, bộ chọn màu trực tuyến, một công cụ phải có để phát triển giao diện người dùng</span>\r\n                                            </div>\r\n                                        </a>\r\n                                        <a href=\"Tool.aspx?type=Count\" class=\"pro-item color-default\">\r\n                                            <div class=\"letter-wrap\">\r\n                                                <h3 class=\"h6\">Số từ</h3>\r\n                                                <span class=\"color-gray fn14\">Đếm ký tự, từ vựng và đoạn văn thông minh, đồng thời tự động tối ưu hóa bố cục văn bản</span>\r\n                                            </div>\r\n                                        </a>\r\n                                        <a href=\"Tool.aspx?type=Timespan\" class=\"pro-item color-default\">\r\n                                            <div class=\"letter-wrap\">\r\n                                                <h3 class=\"h6\">Chuyển đổi dấu thời gian</h3>\r\n                                                <span class=\"color-gray fn14\">Thời gian được chuyển đổi sang và từ dấu thời gian Unix, đồng thời hỗ trợ nhiều định dạng và cài đặt múi giờ</span>\r\n                                            </div>\r\n                                        </a>\r\n                                        <a href=\"Tool.aspx?type=Calc\" class=\"pro-item color-default\">\r\n                                            <div class=\"letter-wrap\">\r\n                                                <h3 class=\"h6\">Công cụ máy tính</h3>\r\n                                                <span class=\"color-gray fn14\">Máy tính khoa học trực tuyến hỗ trợ các phép toán cơ bản và tính toán hàm toán học nâng cao</span>\r\n                                            </div>\r\n                                        </a>\r\n                                    </div>\r\n                                </div>\r\n                            </div>\r\n                        </div>\r\n                    </section>\r\n                    <section id=\"articles-sub-nav\" class=\"pls-nav-dropdown solution-part\" style=\"visibility: hidden; height: 0px; display: none;\">\r\n                        <div class=\"d-flex nav-drown-con\" style=\"height: 0px; transition: transform 0.2s ease-in-out 0s,opacity 0.2s ease-in-out 0s; opacity: 0; transform: translate(0px, -100%);\">\r\n                            <div class=\"prdocu-sub-left\">\r\n                                <div class=\"mr-auto\">\r\n                                    <picture>\r\n                                        <source srcset=\"site/image/icon/p_4.png.webp\" type=\"image/webp\" />\r\n                                        <img class=\"p-icon\" src=\"site/image/icon/p_4.png\" alt=\"Biểu tượng Trung tâm Chia sẻ Công nghệ\" aria-hidden=\"true\" loading=\"lazy\" />\r\n                                    </picture>\r\n                                    <div class=\"mt-4\">\r\n                                        <h3 class=\"h6\">Chia sẻ công nghệ OCR</h3>\r\n                                        <p class=\"color-gray fn14 mt-3 sub-left-desc\">Hướng dẫn kỹ thuật, trường hợp ứng dụng, đề xuất công cụ</p>\r\n                                        <p class=\"color-gray fn14 mt-3 sub-left-desc\">&nbsp;</p>\r\n                                        <p class=\"color-gray fn14 mt-3 sub-left-desc\">Một lộ trình học tập hoàn chỉnh từ người mới bắt đầu đến thành thạo</p>\r\n                                        <p class=\"color-gray fn14 mt-3 sub-left-desc\">Các trường hợp thực tế → phân tích kỹ thuật → ứng dụng công cụ</p>\r\n                                        <p class=\"color-gray fn14 mt-3 sub-left-desc\">Trao quyền cho con đường cải tiến công nghệ OCR của bạn</p>\r\n                                        <a class=\"link block disa fn14 contact-business shake\" href=\"Articles.aspx\">Duyệt các bài viết<span class=\"iconfont4 icon-xiangzuo gt\"></span></a>\r\n                                    </div>\r\n                                </div>\r\n                            </div>\r\n                            <div class=\"bg-white prdocu-sub-right\">\r\n                                <div>\r\n                                    <h2 class=\"text-base-color h6\">Chia sẻ công nghệ</h2>\r\n                                    <div class=\"mt-4 d-flex flex-wrap\">\r\n                                        <a href=\"Articles.aspx\" class=\"pro-item color-default\" title=\"Xem tất cả các bài viết kỹ thuật OCR\">\r\n                                            <div class=\"letter-wrap\">\r\n                                                <h3 class=\"h6\">Tất cả các bài viết</h3>\r\n                                                <span class=\"color-gray fn14\">Duyệt qua tất cả các bài viết kỹ thuật OCR bao gồm toàn bộ kiến thức từ cơ bản đến nâng cao</span>\r\n                                            </div>\r\n                                        </a>\r\n                                        \r\n                                        <a href=\"Articles.aspx?category=2\" class=\"pro-item color-default\" title=\"Hướng dẫn kỹ thuật OCR và hướng dẫn bắt đầu\">\r\n                                            <div class=\"letter-wrap\">\r\n                                                <h3 class=\"h6\">Hướng dẫn nâng cao</h3>\r\n                                                <span class=\"color-gray fn14\">Từ hướng dẫn kỹ thuật OCR giới thiệu đến thành thạo, hướng dẫn chi tiết và hướng dẫn thực tế</span>\r\n                                            </div>\r\n                                        </a>\r\n                                        \r\n                                        <a href=\"Articles.aspx?category=3\" class=\"pro-item color-default\" title=\"Nguyên tắc, thuật toán và ứng dụng công nghệ OCR\">\r\n                                            <div class=\"letter-wrap\">\r\n                                                <h3 class=\"h6\">Khám phá công nghệ</h3>\r\n                                                <span class=\"color-gray fn14\">Khám phá ranh giới của công nghệ OCR, từ nguyên tắc đến ứng dụng và phân tích sâu các thuật toán cốt lõi</span>\r\n                                            </div>\r\n                                        </a>\r\n                                        \r\n                                        <a href=\"Articles.aspx?category=4\" class=\"pro-item color-default\" title=\"Những phát triển và xu hướng phát triển mới nhất trong ngành OCR\">\r\n                                            <div class=\"letter-wrap\">\r\n                                                <h3 class=\"h6\">Xu hướng ngành</h3>\r\n                                                <span class=\"color-gray fn14\">Thông tin chi tiết chuyên sâu về xu hướng phát triển công nghệ OCR, phân tích thị trường, động lực ngành và triển vọng tương lai</span>\r\n                                            </div>\r\n                                        </a>\r\n                                        \r\n                                        <a href=\"Articles.aspx?category=5\" class=\"pro-item color-default\" title=\"Các trường hợp ứng dụng công nghệ OCR trong các ngành công nghiệp khác nhau\">\r\n                                            <div class=\"letter-wrap\">\r\n                                                <h3 class=\"h6\">Các trường hợp sử dụng:</h3>\r\n                                                <span class=\"color-gray fn14\">Các trường hợp ứng dụng, giải pháp và thực tiễn tốt nhất trong thế giới thực của công nghệ OCR trong các ngành khác nhau được chia sẻ</span>\r\n                                            </div>\r\n                                        </a>\r\n                                        \r\n                                        <a href=\"Articles.aspx?category=6\" class=\"pro-item color-default\" title=\"Đánh giá chuyên nghiệp, phân tích so sánh và hướng dẫn được đề xuất để sử dụng các công cụ phần mềm OCR\">\r\n                                            <div class=\"letter-wrap\">\r\n                                                <h3 class=\"h6\">Đánh giá công cụ</h3>\r\n                                                <span class=\"color-gray fn14\">Đánh giá các phần mềm và công cụ nhận dạng văn bản OCR khác nhau, đồng thời cung cấp các đề xuất lựa chọn và so sánh chức năng chi tiết</span>\r\n                                            </div>\r\n                                        </a>\r\n                                        \r\n                                    </div>\r\n                                </div>\r\n                            </div>\r\n                        </div>\r\n                    </section>\r\n                    <section id=\"vip-sub-nav\" class=\"pls-nav-dropdown solution-part\" style=\"visibility: hidden; height: 0px; display: none;\">\r\n                        <div class=\"d-flex nav-drown-con\" style=\"height: 0px; transition: transform 0.2s ease-in-out 0s,opacity 0.2s ease-in-out 0s; opacity: 0; transform: translate(0px, -100%);\">\r\n                            <div class=\"prdocu-sub-left\">\r\n                                <div class=\"mr-auto\">\r\n                                    <picture>\r\n                                        <source class=\"p-icon\" srcset=\"site/image/icon/p_1.png.webp\" type=\"image/webp\" alt=\"\" aria-hidden=\"true\" />\r\n                                        <img class=\"p-icon\" src=\"site/image/icon/p_1.png\" alt=\"Biểu tượng dịch vụ nâng cấp thành viên\" aria-hidden=\"true\" loading=\"lazy\" />\r\n                                    </picture>\r\n                                    <div class=\"mt-4\">\r\n                                        <h3 class=\"h6\">Dịch vụ nâng cấp thành viên</h3>\r\n                                        <p class=\"color-gray fn14 mt-3 sub-left-desc\">Mở khóa tất cả các tính năng cao cấp và tận hưởng các dịch vụ độc quyền</p>\r\n                                        <p class=\"color-gray fn14 mt-3 sub-left-desc\">&nbsp;</p>\r\n                                        <p class=\"color-gray fn14 mt-3 sub-left-desc\">Nhận dạng ngoại tuyến, xử lý hàng loạt, sử dụng không giới hạn</p>\r\n                                        <p class=\"color-gray fn14 mt-3 sub-left-desc\">Pro → Ultimate → Enterprise</p>\r\n                                        <p class=\"color-gray fn14 mt-3 sub-left-desc\">Có một cái gì đó phù hợp với nhu cầu của bạn</p>\r\n                                        <a class=\"link block disa fn14 contact-business shake\" href=\"Version.aspx\">Xem chi tiết<span class=\"iconfont4 icon-xiangzuo gt\"></span></a>\r\n                                    </div>\r\n                                </div>\r\n                            </div>\r\n                            <div class=\"bg-white prdocu-sub-right\">\r\n                                <div>\r\n                                    <h2 class=\"text-base-color h6\" style=\"margin: inherit;\">Nâng cấp tư cách thành viên</h2>\r\n                                    <div class=\"mt-4 d-flex flex-wrap\">\r\n                                        <a href=\"Version.aspx\" class=\"pro-item color-default\">\r\n                                            <div class=\"letter-wrap\">\r\n                                                <h3 class=\"h6\">Đặc quyền thành viên</h3>\r\n                                                <span class=\"color-gray fn14\">Tìm hiểu thêm về sự khác biệt giữa các phiên bản và chọn hạng thành viên phù hợp nhất với bạn</span>\r\n                                            </div>\r\n                                        </a>\r\n                                        <a href=\"Upgrade.aspx\" class=\"pro-item color-default\">\r\n                                            <div class=\"letter-wrap\">\r\n                                                <h3 class=\"h6\">Nâng cấp ngay</h3>\r\n                                                <span class=\"color-gray fn14\">Nhanh chóng nâng cấp tư cách thành viên VIP của bạn để mở khóa nhiều tính năng cao cấp và dịch vụ độc quyền hơn</span>\r\n                                            </div>\r\n                                        </a>\r\n                                        <a href=\"User.aspx\" class=\"pro-item color-default\">\r\n                                            <div class=\"letter-wrap\">\r\n                                                <h3 class=\"h6\">Tài khoản của tôi</h3>\r\n                                                <span class=\"color-gray fn14\">Quản lý thông tin tài khoản, trạng thái đăng ký và lịch sử sử dụng để cá nhân hóa cài đặt</span>\r\n                                            </div>\r\n                                        </a>\r\n                                    </div>\r\n                                </div>\r\n                            </div>\r\n                        </div>\r\n                    </section>\r\n                    <section id=\"support-sub-nav\" class=\"pls-nav-dropdown solution-part\" style=\"visibility: hidden; height: 0px; display: none;\">\r\n                        <div class=\"d-flex nav-drown-con\" style=\"height: 0px; transition: transform 0.2s ease-in-out 0s,opacity 0.2s ease-in-out 0s; opacity: 0; transform: translate(0px, -100%);\">\r\n                            <div class=\"prdocu-sub-left\">\r\n                                <div class=\"mr-auto\">\r\n                                    <picture>\r\n                                        <source class=\"p-icon\" srcset=\"site/image/icon/p_4.png.webp\" type=\"image/webp\" alt=\"\" aria-hidden=\"true\" />\r\n                                        <img class=\"p-icon\" src=\"site/image/icon/p_4.png\" alt=\"Biểu tượng hỗ trợ Trung tâm trợ giúp\" aria-hidden=\"true\" loading=\"lazy\" />\r\n                                    </picture>\r\n                                    <div class=\"mt-4\">\r\n                                        <h3 class=\"h6\">Trung tâm trợ giúp</h3>\r\n                                        <p class=\"color-gray fn14 mt-3 sub-left-desc\">Dịch vụ khách hàng chuyên nghiệp, tài liệu chi tiết và phản hồi nhanh chóng</p>\r\n                                        <p class=\"color-gray fn14 mt-3 sub-left-desc\">&nbsp;</p>\r\n                                        <p class=\"color-gray fn14 mt-3 sub-left-desc\">Đừng hoảng sợ khi gặp vấn đề</p>\r\n                                        <p class=\"color-gray fn14 mt-3 sub-left-desc\">Vấn đề → tìm → giải quyết</p>\r\n                                        <p class=\"color-gray fn14 mt-3 sub-left-desc\">Làm cho trải nghiệm của bạn mượt mà hơn</p>\r\n                                        <a class=\"link block disa fn14 contact-business shake\" href=\"FAQ.aspx\">Nhận trợ giúp<span class=\"iconfont4 icon-xiangzuo gt\"></span></a>\r\n                                    </div>\r\n                                </div>\r\n                            </div>\r\n                            <div class=\"bg-white prdocu-sub-right\">\r\n                                <div>\r\n                                    <h2 class=\"text-base-color h6\" style=\"margin: inherit;\">Trung tâm trợ giúp</h2>\r\n                                    <div class=\"mt-4 d-flex flex-wrap\">\r\n                                        <a href=\"FAQ.aspx\" class=\"pro-item color-default\">\r\n                                            <div class=\"letter-wrap\">\r\n                                                <h3 class=\"h6\">Những câu hỏi thường gặp</h3>\r\n                                                <span class=\"color-gray fn14\">Nhanh chóng trả lời các câu hỏi phổ biến của người dùng và cung cấp hướng dẫn sử dụng chi tiết và hỗ trợ kỹ thuật</span>\r\n                                            </div>\r\n                                        </a>\r\n                                        <a href=\"AboutUs.aspx\" class=\"pro-item color-default\">\r\n                                            <div class=\"letter-wrap\">\r\n                                                <h3 class=\"h6\">Về chúng tôi</h3>\r\n                                                <span class=\"color-gray fn14\">Tìm hiểu về lịch sử phát triển, chức năng cốt lõi và khái niệm dịch vụ của trợ lý nhận dạng văn bản OCR</span>\r\n                                            </div>\r\n                                        </a>\r\n                                        <a href=\"UserAgreement.aspx\" class=\"pro-item color-default\">\r\n                                            <div class=\"letter-wrap\">\r\n                                                <h3 class=\"h6\">Thỏa thuận người dùng</h3>\r\n                                                <span class=\"color-gray fn14\">Điều khoản dịch vụ chi tiết và quyền và nghĩa vụ của người dùng</span>\r\n                                            </div>\r\n                                        </a>\r\n                                        <a href=\"PrivacyPolicy.aspx\" class=\"pro-item color-default\">\r\n                                            <div class=\"letter-wrap\">\r\n                                                <h3 class=\"h6\">Thỏa thuận bảo mật</h3>\r\n                                                <span class=\"color-gray fn14\">Chính sách bảo vệ thông tin cá nhân và các biện pháp bảo mật dữ liệu</span>\r\n                                            </div>\r\n                                        </a>\r\n                                        <a href=\"Status.aspx\" class=\"pro-item color-default\">\r\n                                            <div class=\"letter-wrap\">\r\n                                                <h3 class=\"h6\">Trạng thái hệ thống</h3>\r\n                                                <span class=\"color-gray fn14\">Giám sát trạng thái hoạt động của các nút nhận dạng toàn cầu trong thời gian thực và xem dữ liệu hiệu suất hệ thống</span>\r\n                                            </div>\r\n                                        </a>\r\n                                        <a href=\"javascript:void(0);\" class=\"pro-item color-default\" onclick=\"if(window.openCustomerService) window.openCustomerService(); else alert('Vui lòng nhấp vào biểu tượng cửa sổ nổi bên phải để liên hệ với bộ phận chăm sóc khách hàng');\">\r\n                                            <div class=\"letter-wrap\">\r\n                                                <h3 class=\"h6\">Liên hệ với bộ phận chăm sóc khách hàng</h3>\r\n                                                <span class=\"color-gray fn14\">Hỗ trợ dịch vụ khách hàng trực tuyến để trả lời nhanh chóng các câu hỏi và nhu cầu của bạn</span>\r\n                                            </div>\r\n                                        </a>\r\n                                    </div>\r\n                                </div>\r\n                            </div>\r\n                        </div>\r\n                    </section>\r\n                </div>\r\n            </div>\r\n            <div class=\"header_pc_right\" style=\"display: flex; align-items: center; justify-content: flex-end;\">\r\n                <div id=\"translate\"></div>\r\n                <div id=\"user-login-container\" data-url=\"User.ashx?op=userinfo\" style=\"white-space: nowrap; padding-right: 20px;\"></div>\r\n            </div>\r\n        </header>\r\n        <header class=\"v4_header_mob\">\r\n            <div class=\"container\">\r\n                <div class=\"row no-gutters justify-content-between align-items-center\">\r\n                    <a href=\"Default.aspx\" title=\"Trang chủ | Nhận dạng văn bản thông minh AI\" class=\"mob-logo-wrap\">\r\n                        <picture>\r\n                            <source srcset=\"site/image/logo-s.png.webp\" type=\"image/webp\" />\r\n                            <img src=\"site/image/logo-s.png\" class=\"pc-logo\" style=\"height: 32px; width: 32px\" alt=\"Logo di động trợ lý nhận dạng văn bản OCR\" />\r\n                        </picture>\r\n                        <span style=\"font-size: 18px; font-weight: bold; color: #333;\">Trợ lý nhận dạng văn bản OCR</span>\r\n                    </a>\r\n                    <div class=\"right-menu\" role=\"button\" tabindex=\"0\" aria-label=\"Mở menu điều hướng\" data-toggle=\"collapse\" data-target=\"#mob-header-collapse\" aria-expanded=\"false\" aria-controls=\"mob-header-collapse\">\r\n                        <nav-products id=\"nav_products\">\r\n                            <nav-button class=\"submail-mdi-nav-btn\" style=\"width: 60px\">\r\n                                <label id=\"mdi-submail-nav-btn\">\r\n                                    <span class=\"mdi-nav-products-1\">\r\n                                        <span class=\"nav-products-icon\"></span>\r\n                                    </span>\r\n                                    <span class=\"mdi-nav-products-2\">\r\n                                        <span class=\"nav-products-icon\"></span>\r\n                                    </span>\r\n                                    <span class=\"mdi-nav-products-3\">\r\n                                        <span class=\"nav-products-icon\"></span>\r\n                                    </span>\r\n                                </label>\r\n                            </nav-button>\r\n                        </nav-products>\r\n                    </div>\r\n                </div>\r\n            </div>\r\n            <div class=\"mob-nav-content\">\r\n                <div class=\"sidebar-fix\">\r\n                    <div class=\"container\">\r\n                        <div class=\"row\">\r\n                            <div class=\"col-4 bg-white sidebar-fix-left\" style=\"border-right: 1px solid #e1e6ed\">\r\n                                <div class=\"mob-nav-item active\" data=\"0\">\r\n                                    <div class=\"nav-header\"><a href=\"Default.aspx\" class=\"nav-header-letter px-3\"><span>Nhà</span></a></div>\r\n                                </div>\r\n                                <div class=\"mob-nav-item\" data=\"1\">\r\n                                    <div class=\"nav-header\"><a href=\"Detail.aspx\" class=\"nav-header-letter px-3\"><span>chức năng</span></a></div>\r\n                                </div>\r\n                                <div class=\"mob-nav-item\" data=\"2\">\r\n                                    <div class=\"nav-header\"><a href=\"Ocr.aspx\" class=\"nav-header-letter px-3\"><span>kinh nghiệm</span></a></div>\r\n                                </div>\r\n                                <div class=\"mob-nav-item\" data=\"3\">\r\n                                    <div class=\"nav-header\"><a href=\"Version.aspx\" class=\"nav-header-letter px-3\"><span>thành viên</span></a></div>\r\n                                </div>\r\n                                <div class=\"mob-nav-item\" data=\"4\">\r\n                                    <div class=\"nav-header\"><a href=\"Download.aspx\" class=\"nav-header-letter px-3\"><span>Tải xuống</span></a></div>\r\n                                </div>\r\n                                <div class=\"mob-nav-item\" data=\"5\">\r\n                                    <div class=\"nav-header\"><a href=\"Articles.aspx\" class=\"nav-header-letter px-3\"><span>chia sẻ</span></a></div>\r\n                                </div>\r\n                                <div class=\"mob-nav-item\" data=\"6\">\r\n                                    <div class=\"nav-header\"><a href=\"FAQ.aspx\" class=\"nav-header-letter px-3\"><span>Trợ giúp</span></a></div>\r\n                                </div>\r\n                            </div>\r\n                            <div class=\"col-8 bg-white sidebar-fix-rigth\">\r\n                                <div class=\"sub-nav\">\r\n                                    <div class=\"sub-nav-item py-2 pb-3 px-4\">\r\n                                        <h2 class=\"h5\" style=\"margin: inherit;\">Công cụ năng suất hiệu quả</h2>\r\n                                        <p class=\"color-gray fn14 mt-3 sub-left-desc\">Nhận dạng thông minh, xử lý tốc độ cao và đầu ra chính xác</p>\r\n                                        <p class=\"color-gray fn14 mt-3 sub-left-desc\">&nbsp;</p>\r\n                                        <p class=\"color-gray fn14 mt-3 sub-left-desc\">Nhận dạng toàn bộ trang tài liệu trong 3 giây</p>\r\n                                        <p class=\"color-gray fn14 mt-3 sub-left-desc\">Độ chính xác nhận dạng 98%+</p>\r\n                                        <p class=\"color-gray fn14 mt-3 sub-left-desc\">Xử lý thời gian thực đa ngôn ngữ không bị chậm trễ</p>\r\n                                        <a class=\"link block disa fn14 contact-business shake\" href=\"Download.aspx\">Tải xuống trải nghiệm ngay bây giờ<span class=\"iconfont4 icon-xiangzuo gt\"></span></a>\r\n                                    </div>\r\n                                </div>\r\n                                <div class=\"sub-nav\">\r\n                                    <div class=\"sub-nav-item py-3 px-4\">\r\n                                        <h2 class=\"h5\" style=\"margin: inherit;\">Tính năng sản phẩm:</h2>\r\n                                        <p class=\"color-gray fn14 mt-3 sub-left-desc\">Nhận dạng thông minh AI, giải pháp một cửa</p>\r\n                                    </div>\r\n                                    <div class=\"sub-nav-item py-3 px-4\"><a href=\"Detail.aspx\">Giới thiệu chức năng</a></div>\r\n                                    <div class=\"sub-nav-item py-3 px-4\"><a href=\"Download.aspx\">Tải xuống phần mềm</a></div>\r\n                                    <div class=\"sub-nav-item py-3 px-4\"><a href=\"Version.aspx\">So sánh phiên bản</a></div>\r\n                                    <div class=\"sub-nav-item py-3 px-4\"><a href=\"Ocr.aspx\">Trải nghiệm trực tuyến</a></div>\r\n                                    <div class=\"sub-nav-item py-3 px-4\"><a href=\"Status.aspx\">Trạng thái hệ thống</a></div>\r\n                                </div>\r\n                                <div class=\"sub-nav\">\r\n                                    <div class=\"sub-nav-item py-3 px-4\">\r\n                                        <h2 class=\"h5\" style=\"margin: inherit;\">Trải nghiệm trực tuyến</h2>\r\n                                        <p class=\"color-gray fn14 mt-3 sub-left-desc\">Trải nghiệm chức năng OCR trực tuyến miễn phí</p>\r\n                                    </div>\r\n                                    <div class=\"sub-nav-item py-3 px-4\"><a href=\"Ocr.aspx\">Đầy đủ chức năng</a></div>\r\n                                    <div class=\"sub-nav-item py-3 px-4\"><a href=\"Ocr.aspx?type=text_recognize\">Nhận dạng từ</a></div>\r\n                                    <div class=\"sub-nav-item py-3 px-4\"><a href=\"Ocr.aspx?type=table\">Nhận dạng bảng</a></div>\r\n                                    <div class=\"sub-nav-item py-3 px-4\"><a href=\"Ocr.aspx?type=pdf2word\">PDF sang Word</a></div>\r\n                                </div>\r\n                                <div class=\"sub-nav\">\r\n                                    <div class=\"sub-nav-item py-3 px-4\">\r\n                                        <h2 class=\"h5\" style=\"margin: inherit;\">Nâng cấp tư cách thành viên</h2>\r\n                                        <p class=\"color-gray fn14 mt-3 sub-left-desc\">Mở khóa tất cả các tính năng và tận hưởng các dịch vụ độc quyền</p>\r\n                                    </div>\r\n                                    <div class=\"sub-nav-item py-3 px-4\"><a href=\"Version.aspx\">Quyền lợi thành viên</a></div>\r\n                                    <div class=\"sub-nav-item py-3 px-4\"><a href=\"Upgrade.aspx\">Kích hoạt ngay lập tức</a></div>\r\n                                </div>\r\n                                <div class=\"sub-nav\">\r\n                                    <div class=\"sub-nav-item py-3 px-4\">\r\n                                        <h2 class=\"h5\" style=\"margin: inherit;\">Tải xuống phần mềm</h2>\r\n                                        <p class=\"color-gray fn14 mt-3 sub-left-desc\">Tải xuống phần mềm OCR chuyên nghiệp miễn phí</p>\r\n                                    </div>\r\n                                    <div class=\"sub-nav-item py-3 px-4\"><a href=\"Download.aspx\">Tải ngay</a></div>\r\n                                    <div class=\"sub-nav-item py-3 px-4\"><a href=\"Version.aspx\">So sánh phiên bản</a></div>\r\n                                </div>\r\n                                <div class=\"sub-nav\">\r\n                                    <div class=\"sub-nav-item py-3 px-4\">\r\n                                        <h2 class=\"h5\" style=\"margin: inherit;\">Chia sẻ công nghệ</h2>\r\n                                        <p class=\"color-gray fn14 mt-3 sub-left-desc\">Các bài viết kỹ thuật OCR và chia sẻ kiến thức</p>\r\n                                    </div>\r\n                                    <div class=\"sub-nav-item py-3 px-4\"><a href=\"Articles.aspx\">Tất cả các bài viết</a></div>\r\n                                    \r\n                                    <div class=\"sub-nav-item py-3 px-4\"><a href=\"Articles.aspx?category=2\">Hướng dẫn nâng cao</a></div>\r\n                                    \r\n                                    <div class=\"sub-nav-item py-3 px-4\"><a href=\"Articles.aspx?category=3\">Khám phá công nghệ</a></div>\r\n                                    \r\n                                    <div class=\"sub-nav-item py-3 px-4\"><a href=\"Articles.aspx?category=4\">Xu hướng ngành</a></div>\r\n                                    \r\n                                    <div class=\"sub-nav-item py-3 px-4\"><a href=\"Articles.aspx?category=5\">Các trường hợp sử dụng:</a></div>\r\n                                    \r\n                                    <div class=\"sub-nav-item py-3 px-4\"><a href=\"Articles.aspx?category=6\">Đánh giá công cụ</a></div>\r\n                                    \r\n                                </div>\r\n                                <div class=\"sub-nav\">\r\n                                    <div class=\"sub-nav-item py-3 px-4\">\r\n                                        <h2 class=\"h5\" style=\"margin: inherit;\">Trung tâm trợ giúp</h2>\r\n                                        <p class=\"color-gray fn14 mt-3 sub-left-desc\">Dịch vụ khách hàng chuyên nghiệp, dịch vụ thân mật</p>\r\n                                    </div>\r\n                                    <div class=\"sub-nav-item py-3 px-4\"><a href=\"FAQ.aspx\">Sử dụng trợ giúp</a></div>\r\n                                    <div class=\"sub-nav-item py-3 px-4\"><a href=\"AboutUs.aspx\">Về chúng tôi</a></div>\r\n                                    <div class=\"sub-nav-item py-3 px-4\"><a href=\"mailto:<EMAIL>\">Liên hệ với bộ phận chăm sóc khách hàng</a></div>\r\n                                    <div class=\"sub-nav-item py-3 px-4\"><a href=\"UserAgreement.aspx\">Điều khoản dịch vụ</a></div>\r\n                                </div>\r\n                            </div>\r\n                        </div>\r\n                    </div>\r\n                </div>\r\n            </div>\r\n        </header>\r\n        <form method=\"post\" action=\"./Article.aspx?id=83&amp;slug=crnn-architecture&amp;lang=zh-Hans&amp;_translation_source=true\" id=\"aspnetForm\">\r\n<div class=\"aspNetHidden\">\r\n<input type=\"hidden\" name=\"__VIEWSTATE\" id=\"__VIEWSTATE\" value=\"nOmaUAZZy6ARGTxQKYXhPLNw3GGifeyiGxNnqAm8ZUi1I3tSxztRbcisXPXbbPeg8n/fKXZa4gV7K6PEAZ+qMXRA2vn/4exegZLnNWi3Ofw=\" />\r\n</div>\r\n\r\n<div class=\"aspNetHidden\">\r\n\r\n\t<input type=\"hidden\" name=\"__VIEWSTATEGENERATOR\" id=\"__VIEWSTATEGENERATOR\" value=\"2173C2F0\" />\r\n</div>\r\n            <div>\r\n                \n\n<div class=\"reading-progress\" id=\"readingProgress\"></div>\n<div class=\"article-detail-container\">\n<div class=\"container\">\n<div class=\"row\">\n<div class=\"col-lg-8 col-xl-9 col-md-12\">\n                    \n                    <article class=\"article-detail\" id=\"articleDetail\" data-article-id=\"83\">\n                        <header class=\"article-header\">\n                            <h1 class=\"article-title\">【Deep Learning OCR Series·6】Phân tích chuyên sâu về kiến trúc CRNN</h1>\n                            <div class=\"article-meta\">\n                                <div class=\"meta-item\">\n                                    <span class=\"meta-icon\">📅</span>\n                                    <span>Thời gian đăng: 2025-08-19</span>\n                                </div>\n                                <div class=\"meta-item\">\n                                    <span class=\"meta-icon\">👁️</span>\n                                    <span>Sự đọc:<span class=\"view-count\">1420</span></span>\n                                </div>\n                                <div class=\"meta-item\">\n                                    <span class=\"meta-icon\">⏱️</span>\n                                    <span>Xấp xỉ 22 phút (4248 từ)</span>\n                                </div>\n                                \n                                <div class=\"meta-item\">\n                                    <span class=\"meta-icon\">📁</span>\n                                    <span>Thể loại: Hướng dẫn nâng cao</span>\n                                </div>\n                                \n                            </div>\n                            <div class=\"article-summary\"><p>Phân tích chi tiết kiến trúc CRNN, bao gồm trích xuất tính năng CNN, mô hình trình tự RNN và triển khai hoàn chỉnh chức năng mất CTC. Đi sâu vào sự kết hợp hoàn hảo giữa CNN và RNN.</p></div>\n                        </header>\n                        <div class=\"article-content\" id=\"articleContent\">## Giới thiệu\r\n\r\nCRNN (Convolutional Recurrent Neural Network) là một trong những kiến trúc quan trọng nhất trong lĩnh vực OCR học sâu, được đề xuất bởi Bai Xiang et al. vào năm 2015. CRNN khéo léo kết hợp khả năng trích xuất tính năng của mạng nơ-ron tích chập (CNN) với khả năng mô hình hóa trình tự của mạng nơ-ron tuần hoàn (RNN) để đạt được nhận dạng văn bản từ đầu đến cuối. Bài viết này sẽ cung cấp phân tích chuyên sâu về thiết kế kiến trúc, nguyên lý làm việc, phương pháp đào tạo và các ứng dụng cụ thể trong OCR của CRNN, cung cấp cho người đọc sự hiểu biết kỹ thuật toàn diện.\r\n\r\n## Tổng quan về Kiến trúc CRNN\r\n\r\n### Động lực thiết kế\r\n\r\nTrước CRNN, các hệ thống OCR thường áp dụng cách tiếp cận từng bước: phát hiện và phân đoạn ký tự được thực hiện trước, sau đó mỗi ký tự được nhận dạng. Cách tiếp cận này có các vấn đề sau:\r\n\r\n**Hạn chế của phương pháp truyền thống**:\r\n- Lan truyền lỗi: Lỗi trong phân đoạn ký tự có thể ảnh hưởng trực tiếp đến kết quả nhận dạng\r\n- Độ phức tạp: Yêu cầu thiết kế các thuật toán phân đoạn ký tự phức tạp\r\n- Độ bền kém: Nhạy cảm với khoảng cách ký tự và thay đổi phông chữ\r\n- Không có khả năng xử lý các nét liên tục: Hiện tượng các nét liên tục trong văn bản viết tay khó tách biệt\r\n\r\n** Ý tưởng sáng tạo của CRNN **:\r\n- Học từ đầu đến cuối: Lập bản đồ trực tiếp từ hình ảnh sang chuỗi văn bản\r\n- Không phân đoạn: Tránh sự phức tạp của phân đoạn nhân vật\r\n- Mô hình hóa trình tự: Sử dụng RNN để mô hình hóa sự phụ thuộc giữa các ký tự\r\n- Căn chỉnh CTC: Giải quyết độ dài trình tự đầu vào-đầu ra không khớp\r\n\r\n### Kiến trúc tổng thể\r\n\r\nKiến trúc CRNN bao gồm ba thành phần chính:\r\n\r\n**1. Lớp tích chập**:\r\n- Chức năng: Trích xuất chuỗi tính năng từ hình ảnh đầu vào\r\n- Đầu vào: Hình ảnh dòng văn bản (chiều cao cố định, chiều rộng thay đổi)\r\n- Đầu ra: Trình tự bản đồ tính năng\r\n\r\n**2. Lớp lặp lại**:\r\n- Chức năng: Mô hình phụ thuộc ngữ cảnh trong chuỗi tính năng\r\n- Đầu vào: Trình tự tính năng được trích xuất bởi CNN\r\n- Đầu ra: Một chuỗi tính năng với thông tin ngữ cảnh\r\n\r\n**3. Lớp phiên âm**:\r\n- Chức năng: Chuyển đổi chuỗi tính năng thành chuỗi văn bản\r\n- Phương pháp: Sử dụng CTC (Phân loại thời gian kết nối)\r\n- Đầu ra: Kết quả nhận dạng văn bản cuối cùng\r\n\r\n## Giải thích chi tiết về các lớp tích chập\r\n\r\n### Chiến lược trích xuất tính năng\r\n\r\nLớp tích chập của CRNN được thiết kế đặc biệt để nhận dạng văn bản:\r\n\r\n** Tính năng cấu trúc mạng **:\r\n- Độ sâu nông: 7 lớp tích chập thường được sử dụng\r\n- Nhân tích chập nhỏ: 3×3 nhân tích chập được sử dụng chủ yếu\r\n- Chiến lược gộp: Sử dụng gộp một cách tiết kiệm theo hướng chiều rộng\r\n\r\n** Cấu hình mạng cụ thể **:\r\nĐầu vào: 32×W×1 (Chiều cao 32, Chiều rộng W, Kênh đơn)\r\nConv1: 64 3×3 nhân tích chập, bước 1, điền 1\r\nMaxPool1: 2×2 hồ bơi, chiều dài bước 2\r\nConv2: 128 3×3 nhân tích chập, bước 1, điền 1  \r\nMaxPool2: 2×2 gộp, kích thước bước 2\r\nConv3: 256 3×3 nhân tích chập, bước 1, điền 1\r\nConv4: 256 lõi tích chập 3×3, bước 1, điền 1\r\nMaxPool3: 2×1 gộp, kích thước bước (2,1)\r\nConv5: 512 lõi tích chập 3×3, bước 1, điền 1\r\nĐịnh mức lô + ReLU\r\nConv6: 512 3×3 nhân tích chập, bước 1, điền 1\r\nĐịnh mức lô + ReLU\r\nMaxPool4: 2×1 gộp, kích thước bước (2,1)\r\nConv7: 512 2×2 nhân tích chập, bước 1, điền 0\r\nĐầu ra: 512 × 1 × W / 4\r\n\r\n### Cân nhắc thiết kế chính\r\n\r\n**Chiến lược nén cao**:\r\n- Mục tiêu: Nén hình ảnh cao 1 pixel\r\n- Phương pháp: Nén dần chiều cao bằng cách sử dụng nhiều lớp gộp\r\n- Lý do: Chiều cao của dòng văn bản tương đối không quan trọng\r\n\r\n**Chiến lược giữ chiều rộng**:\r\n- Mục tiêu: Duy trì thông tin chiều rộng của hình ảnh càng nhiều càng tốt\r\n- Phương pháp: Giảm các hoạt động gộp theo hướng chiều rộng\r\n- Lý do: Thông tin trình tự của văn bản chủ yếu được phản ánh theo hướng chiều rộng\r\n\r\n** Chuyển đổi bản đồ tính năng **:\r\nĐầu ra của lớp tích chập cần được chuyển đổi sang định dạng đầu vào của RNN:\r\n- Đầu ra thô: C×H×W (chiều cao × kênh× chiều rộng)\r\n- Chuyển đổi: W×C (Độ dài trình tự× Kích thước tính năng)\r\n- Phương pháp: Lấy vectơ tính năng cho từng vị trí chiều rộng làm bước thời gian\r\n\r\n## Giải thích chi tiết về lớp tròn\r\n\r\n### Lựa chọn RNN\r\n\r\nCRNN thường sử dụng LSTM hai chiều làm lớp vòng lặp:\r\n\r\n** Ưu điểm của LSTM hai chiều **:\r\n- Thông tin theo ngữ cảnh: Sử dụng cả ngữ cảnh tiến và lùi\r\n- Phụ thuộc đường dài: LSTM có khả năng xử lý các phụ thuộc đường dài\r\n- Ổn định gradient: Tránh vấn đề biến mất gradient\r\n\r\n** Cấu hình mạng **:\r\nĐầu vào: W×512 (chiều dài trình tự × kích thước tính năng)\r\nBiLSTM1: 256 ô ẩn (128 tiến + 128 lùi)\r\nBiLSTM2: 256 ô ẩn (128 tiến + 128 lùi)\r\nĐầu ra: W×256 (chiều dài trình tự× kích thước ẩn)\r\n\r\n### Cơ chế mô hình hóa trình tự\r\n\r\n** Mô hình hóa phụ thuộc thời gian **:\r\nLớp RNN nắm bắt sự phụ thuộc thời gian giữa các ký tự:\r\n- Thông tin của nhân vật trước giúp nhận dạng nhân vật hiện tại\r\n- Thông tin cho các nhân vật tiếp theo cũng có thể cung cấp ngữ cảnh hữu ích\r\n- Thông tin của toàn bộ từ hoặc cụm từ giúp phân định\r\n\r\n** Cải tiến tính năng **:\r\nCác tính năng do RNN xử lý có các đặc điểm sau:\r\n- Nhạy cảm với ngữ cảnh: Các tính năng của mỗi vị trí chứa thông tin theo ngữ cảnh\r\n- Tính nhất quán về thời gian: Các tính năng ở các vị trí liền kề có tính liên tục nhất định\r\n- Sự phong phú về ngữ nghĩa: Kết hợp các tính năng trực quan và trình tự\r\n\r\n## Giải thích chi tiết về lớp phiên âm\r\n\r\n### Cơ chế CTC\r\n\r\nCTC (Phân loại thời gian kết nối) là một thành phần quan trọng của CRNN:\r\n\r\n**Vai trò của CTC**:\r\n- Giải quyết các vấn đề về căn chỉnh: Độ dài trình tự đầu vào không khớp với độ dài trình tự đầu ra\r\n- Đào tạo từ đầu đến cuối: Không cần chú thích căn chỉnh cấp ký tự\r\n- Xử lý trùng lặp: Xử lý đúng các trường hợp ký tự trùng lặp\r\n\r\n**Cách thức hoạt động của CTC**:\r\n1. Mở rộng bộ nhãn: Thêm nhãn trống lên trên bộ ký tự ban đầu\r\n2. Liệt kê đường dẫn: Liệt kê tất cả các đường dẫn căn chỉnh có thể có\r\n3. Xác suất đường dẫn: Tính xác suất của từng đường dẫn\r\n4. Thiệt thòi: tổng xác suất của tất cả các đường dẫn để có được xác suất trình tự\r\n\r\n### Chức năng mất CTC\r\n\r\n**Biểu diễn toán học**:\r\nVới trình tự đầu vào X và dãy mục tiêu Y, tổn thất CTC được định nghĩa là:\r\n\r\nL_CTC = -log P(Y| X)\r\n\r\ntrong đó P(Y| X) thu được bằng cách tổng xác suất của tất cả các đường dẫn thẳng hàng có thể:\r\n\r\nP (Y | X) = Σ_π∈B^(-1)(Y) P(π| X)\r\n\r\nỞ đây B^(-1)(Y) đại diện cho tất cả các tập hợp các đường dẫn có thể được ánh xạ đến dãy mục tiêu Y.\r\n\r\n** Thuật toán tiến-lùi **:\r\nĐể tính toán tổn thất CTC một cách hiệu quả, một thuật toán tiến-lùi để lập trình động được sử dụng:\r\n- Thuật toán chuyển tiếp: Tính toán xác suất đạt được từng trạng thái\r\n- Thuật toán ngược: Tính toán xác suất từ mỗi trạng thái đến cuối\r\n- Tính toán gradient: Tính toán gradient kết hợp với xác suất tiến-lùi\r\n\r\n## Chiến lược đào tạo CRNN\r\n\r\n### Tiền xử lý dữ liệu\r\n\r\n**Tiền xử lý hình ảnh**:\r\n- Chuẩn hóa kích thước: Thống nhất chiều cao hình ảnh thành 32 pixel\r\n- Duy trì tỷ lệ khung hình: Duy trì tỷ lệ khung hình của ảnh gốc\r\n- Chuyển đổi thang độ xám: Chuyển đổi thành hình ảnh thang độ xám một kênh\r\n- Chuẩn hóa số: các giá trị pixel được chuẩn hóa thành [0,1] hoặc [-1,1]\r\n\r\n**Nâng cao dữ liệu**:\r\n- Biến đổi hình học: xoay, nghiêng, biến đổi phối cảnh\r\n- Thay đổi ánh sáng: điều chỉnh độ sáng, độ tương phản\r\n- Thêm tiếng ồn: Tiếng ồn Gaussian, tiếng muối và tiếng tiêu\r\n- Blur: Làm mờ chuyển động, làm mờ Gaussian\r\n\r\n### Kỹ thuật đào tạo\r\n\r\n**Lập lịch tỷ lệ học tập**:\r\n- Tỷ lệ học tập ban đầu: Thường được đặt thành 0,001\r\n- Chiến lược phân rã: Phân rã theo cấp số nhân hoặc phân rã bước\r\n- Chiến lược khởi động: Một vài kỷ nguyên đầu tiên sử dụng tốc độ học tập nhỏ\r\n\r\n**Kỹ thuật chính quy hóa**:\r\n- Dropout: Thêm một dropout sau layer RNN\r\n- Suy giảm trọng lượng: Chính quy hóa L2 ngăn ngừa quá khớp\r\n- Chuẩn hóa hàng loạt: Sử dụng chuẩn hóa hàng loạt trong lớp CNN\r\n\r\n**Lựa chọn trình tối ưu hóa**:\r\n- Adam: Tốc độ học tập thích ứng, hội tụ nhanh\r\n- RMSprop: Thích hợp cho đào tạo RNN\r\n- SGD + Momentum: Tùy chọn truyền thống nhưng ổn định\r\n\r\n## Tối ưu hóa và cải tiến CRNN\r\n\r\n### Tối ưu hóa kiến trúc\r\n\r\n**Cải tiến một phần của CNN**:\r\n- Kết nối ResNet: Thêm các kết nối còn lại để cải thiện độ ổn định của quá trình tập luyện\r\n- Vải DenseNet: Kết nối dày đặc cải thiện ghép kênh tính năng\r\n- Cơ chế chú ý: Giới thiệu sự chú ý không gian trong CNN\r\n\r\n**Cải tiến một phần RNN**:\r\n- Thay thế GRU: Sử dụng GRU để giảm số lượng thông số\r\n- Máy biến áp: Thay thế RNN bằng cách sử dụng cơ chế tự chú ý\r\n- Tính năng đa quy mô: Kết hợp các tính năng từ các thang đo khác nhau\r\n\r\n### Tối ưu hóa hiệu suất\r\n\r\n**Tăng tốc suy luận**:\r\n- Lượng tử hóa mô hình: Lượng tử hóa INT8 làm giảm nỗ lực tính toán\r\n- Cắt tỉa mô hình: Loại bỏ các kết nối không quan trọng\r\n- Chưng cất kiến thức: Học kiến thức về các mô hình lớn với mô hình nhỏ\r\n\r\n**Tối ưu hóa bộ nhớ**:\r\n- Điểm kiểm tra gradient: Giảm lượng bộ nhớ trong quá trình đào tạo\r\n- Độ chính xác hỗn hợp: Đào tạo với FP16\r\n- Tối ưu hóa biểu đồ động: Tối ưu hóa cấu trúc của biểu đồ được tính toán\r\n\r\n## Các trường hợp ứng dụng trong thế giới thực\r\n\r\n### Nhận dạng văn bản viết tay\r\n\r\n** Kịch bản ứng dụng **:\r\n- Số hóa ghi chú viết tay\r\n- Tự động điền biểu mẫu\r\n- Nhận dạng tài liệu lịch sử\r\n\r\n** Tính năng kỹ thuật **:\r\n- Biến thể ký tự lớn: Yêu cầu khả năng trích xuất tính năng mạnh mẽ\r\n- Xử lý đột quỵ liên tục: Ưu điểm của cơ chế CTC là rõ ràng\r\n- Vấn đề ngữ cảnh: Khả năng mô hình hóa trình tự của RNN là rất quan trọng\r\n\r\n### Nhận dạng văn bản in\r\n\r\n** Kịch bản ứng dụng **:\r\n- Số hóa tài liệu\r\n- Nhận dạng vé\r\n- Nhận dạng biển báo\r\n\r\n** Tính năng kỹ thuật **:\r\n- Font Regularity: Trích xuất tính năng CNN tương đối đơn giản\r\n- Quy tắc kiểu chữ: Thông tin bố cục có thể được sử dụng\r\n- Yêu cầu độ chính xác cao: Yêu cầu tinh chỉnh mô hình\r\n\r\n### Nhận dạng văn bản cảnh\r\n\r\n** Kịch bản ứng dụng **:\r\n- Nhận dạng văn bản Chế độ xem phố\r\n- Nhận dạng nhãn sản phẩm\r\n- Nhận dạng biển báo giao thông\r\n\r\n** Tính năng kỹ thuật **:\r\n- Nền phức tạp: Yêu cầu trích xuất tính năng mạnh mẽ\r\n- Biến dạng nghiêm trọng: Yêu cầu thiết kế kiến trúc mạnh mẽ\r\n- Yêu cầu thời gian thực: Yêu cầu suy luận hiệu quả\r\n\r\n## Tóm tắt\r\n\r\nLà một kiến trúc cổ điển của deep learning OCR, CRNN giải quyết thành công nhiều vấn đề của các phương pháp OCR truyền thống. Phương pháp đào tạo từ đầu đến cuối, khái niệm thiết kế không phân đoạn ký tự và sự ra đời của cơ chế CTC đều cung cấp nguồn cảm hứng quan trọng cho sự phát triển tiếp theo của công nghệ OCR.\r\n\r\n**Đóng góp chính**:\r\n- Học tập từ đầu đến cuối: Đơn giản hóa việc thiết kế hệ thống OCR\r\n- Mô hình hóa trình tự: Sử dụng hiệu quả các thuộc tính trình tự của văn bản\r\n- CTC Alignment: Giải quyết độ dài trình tự không khớp\r\n- Kiến trúc đơn giản: Dễ hiểu và dễ triển khai\r\n\r\n**Hướng phát triển**:\r\n- Cơ chế chú ý: Giới thiệu sự chú ý để cải thiện hiệu suất\r\n- Transformer: Thay thế RNN bằng sự chú ý của bản thân\r\n- Hợp nhất đa phương thức: Kết hợp các thông tin khác như mô hình ngôn ngữ\r\n- Thiết kế nhẹ: nén mô hình cho thiết bị di động\r\n\r\nThành công của CRNN là minh chứng cho tiềm năng to lớn của deep learning trong lĩnh vực OCR và cung cấp kinh nghiệm quý báu để hiểu cách thiết kế hệ thống học tập end-to-end hiệu quả. Trong bài viết tiếp theo, chúng ta sẽ đi sâu vào các chi tiết toán học và triển khai của hàm mất CTC.</div>\n                        \n                        <div class=\"article-tags\">\n                            <h6>Nhãn:</h6>\n                            <div class=\"tags-list\">\n                                \n                                <span class=\"tag\">CRNN</span>\n                                \n                                <span class=\"tag\">CNN</span>\n                                \n                                <span class=\"tag\">RNN</span>\n                                \n                                <span class=\"tag\">LSTM</span>\n                                \n                                <span class=\"tag\">CTC</span>\n                                \n                                <span class=\"tag\">OCR</span>\n                                \n                                <span class=\"tag\">Học sâu</span>\n                                \n                                <span class=\"tag\">Từ đầu đến cuối</span>\n                                \n                                <span class=\"tag\">Mô hình hóa trình tự</span>\n                                \n                            </div>\n                        </div>\n                        \n                    </article>\n                    \n                    <footer class=\"article-footer\" id=\"articleFooter\">\n                        <div class=\"article-share\">\n                            <h6 style=\"color: #1a1c1f; margin-bottom: 1rem;\">Chia sẻ và vận hành:</h6>\n                            <div class=\"share-buttons\">\n                                <a href=\"javascript:void(0)\" onclick=\"shareToWeibo()\" class=\"btn btn-weibo\">📱 Weibo đã chia sẻ</a>\n                                <a href=\"javascript:void(0)\" onclick=\"copyUrl()\" class=\"btn btn-copy\">🔗 Sao chép liên kết</a>\n                                <button type=\"button\" class=\"btn btn-print\" onclick=\"printArticle()\">🖨️ In bài viết</button>\n                            </div>\n                        </div>\n                    </footer>\n                    \n                </div>\n                <div class=\"col-lg-4 col-xl-3 col-md-12\">\n                    <div class=\"sidebar\">\n                        <div class=\"sidebar-card toc-card\" id=\"tocCard\" style=\"display: none;\"><h5>Mục lục</h5><div id=\"tableOfContents\" class=\"toc-list\"></div></div>\n                        <div class=\"sidebar-card\">\n                            <h5>Đề xuất đọc</h5>\n                            <div id=\"recommendedArticles\">\n                                \n                                <div class=\"recommended-item\"><h6><a href=\"Article.aspx?slug=document-intelligence-future-outlook\">【Loạt xử lý thông minh tài liệu·20】Triển vọng phát triển của công nghệ xử lý tài liệu thông minh</a></h6><p class=\"text-muted small\">2025-08-19 • 1203 Bài đọc tiếp theo</p></div>\n                                \n                                <div class=\"recommended-item\"><h6><a href=\"Article.aspx?slug=document-intelligence-quality-assurance\">【Dòng xử lý thông minh tài liệu·19】Hệ thống đảm bảo chất lượng xử lý thông minh tài liệu</a></h6><p class=\"text-muted small\">2025-08-19 • 1200 Bài đọc tiếp theo</p></div>\n                                \n                                <div class=\"recommended-item\"><h6><a href=\"Article.aspx?slug=large-scale-document-processing-optimization\">【Dòng xử lý thông minh tài liệu·18】Tối ưu hóa hiệu suất xử lý tài liệu quy mô lớn</a></h6><p class=\"text-muted small\">2025-08-19 • 1201 Bài đọc tiếp theo</p></div>\n                                \n                            </div>\n                        </div>\n                    </div>\n</div>\n</div>\n</div>\n</div>\n<script>\nvar articleId,viewCountKey,now=new Date().getTime();\ndocument.addEventListener('DOMContentLoaded',function(){var articleDetail=document.getElementById('articleDetail');if(articleDetail&&articleDetail.querySelector('.article-content')){formatContent();initReadingProgress();optimizeImages();updateViewCount();}});\nfunction updateViewCount(){articleId=getArticleId();if(!articleId)return;viewCountKey='article_view_'+articleId;var lastViewTime=localStorage.getItem(viewCountKey);if(lastViewTime&&(now-parseInt(lastViewTime))<3600000)return;var xhr=new XMLHttpRequest();xhr.open('POST','/Article.ashx',true);xhr.setRequestHeader('Content-Type','application/x-www-form-urlencoded');xhr.onreadystatechange=function(){if(xhr.readyState===4&&xhr.status===200){try{var response=JSON.parse(xhr.responseText);if(response.success){updateViewCountDisplay(response.viewCount);localStorage.setItem(viewCountKey,now.toString());}}catch(e){}}};xhr.send('action=updateviewcount&articleId='+encodeURIComponent(articleId));}\nfunction getArticleId(){var articleDetail=document.getElementById('articleDetail');if(articleDetail){var id=articleDetail.getAttribute('data-article-id');if(id)return id;}var urlParams=new URLSearchParams(window.location.search);return urlParams.get('id');}\nfunction updateViewCountDisplay(newCount){document.querySelectorAll('.view-count').forEach(function(element){element.textContent=newCount;});}\nfunction decodeHtmlEntities(text){var textArea=document.createElement('textarea');textArea.innerHTML=text;return textArea.value;}\nfunction formatContent(){var contentDiv=document.getElementById('articleContent');if(!contentDiv)return;var content=contentDiv.textContent||contentDiv.innerText;if(!content)return;content=decodeHtmlEntities(content);content=formatMarkdownContent(content);content=formatIndentation(content);content=formatLists(content);content=formatParagraphs(content);content=formatLinks(content);contentDiv.innerHTML=content;initTableOfContents();}\nfunction formatIndentation(content){var lines=content.split('\\n'),result=[];for(var i=0;i<lines.length;i++){var line=lines[i];if(line.trim().match(/^<[^>]+>/)){result.push(line);continue;}line=line.replace(/^\\t+/g,function(match){return '    '.repeat(match.length);});if(line.match(/^  +[^\\s]/)){var indentMatch=line.match(/^( +)(.*)$/);if(indentMatch){var indentLevel=Math.floor(indentMatch[1].length/2);line='<span class=\"text-indent-'+indentLevel+'\">'+indentMatch[2]+'</span>';}}result.push(line);}return result.join('\\n');}\nfunction formatMarkdownContent(content){content=content.replace(/^\\s*##### (.+)$/gm,'<h5>$1</h5>').replace(/^\\s*#### (.+)$/gm,'<h4>$1</h4>').replace(/^\\s*### (.+)$/gm,'<h3>$1</h3>').replace(/^\\s*## (.+)$/gm,'<h2>$1</h2>').replace(/^\\s*# (.+)$/gm,'<h1>$1</h1>').replace(/^> (.+)$/gm,'<blockquote><p>$1</p></blockquote>').replace(/\\*\\*([^*]+?)\\*\\*/g,'<strong>$1</strong>').replace(/\\*([^*\\n]+?)\\*/g,'<em>$1</em>').replace(/`([^`]+?)`/g,'<code class=\"inline-code\">$1</code>').replace(/^(\\*\\*[^*]+?\\*\\*)：/gm,'<div class=\"label-item\"><span class=\"label\">$1</span></div>').replace(/^([^：\\n]+?)：$/gm,'<div class=\"section-label\">$1：</div>').replace(/^(\\*\\*[^*]+?\\*\\*)：(.+)$/gm,'<div class=\"definition-item\"><span class=\"definition-label\">$1：</span><span class=\"definition-content\">$2</span></div>').replace(/^(note|note|note):(.+)$/gm,'<div class=\"note-item\"><span class=\"note-label\">$1：</span><span class=\"note-content\">$2</span></div>');content=formatLists(content);content=formatMathSymbols(content);content=formatParagraphs(content);return content;}\nfunction formatLists(content){var lines=content.split('\\n'),result=[],inOrderedList=false,inUnorderedList=false;for(var i=0;i<lines.length;i++){var line=lines[i],trimmed=line.trim();var orderedMatch=line.match(/^(\\d+)\\.\\s*(.+)$/);var unorderedMatch=line.match(/^[-*+]\\s*(.+)$/);if(orderedMatch){if(!inOrderedList){if(inUnorderedList){result.push('</ul>');inUnorderedList=false;}result.push('<ol class=\"formatted-list ordered\">');inOrderedList=true;}var listContent=orderedMatch[2].replace(/^\\*\\*([^*]+?)\\*\\*：(.+)$/,'<strong class=\"list-title\">$1：</strong><span class=\"list-content\">$2</span>');result.push('<li>'+listContent+'</li>');}else if(unorderedMatch){if(!inUnorderedList){if(inOrderedList){result.push('</ol>');inOrderedList=false;}result.push('<ul class=\"formatted-list unordered\">');inUnorderedList=true;}var listContent=unorderedMatch[1].replace(/^\\*\\*([^*]+?)\\*\\*：(.+)$/,'<strong class=\"list-title\">$1：</strong><span class=\"list-content\">$2</span>');result.push('<li>'+listContent+'</li>');}else{if(trimmed===''){if(i+1<lines.length){var nextLine=lines[i+1].trim();if(!nextLine.match(/^\\d+\\.\\s/)&&!nextLine.match(/^[-*+]\\s/)){if(inOrderedList){result.push('</ol>');inOrderedList=false;}if(inUnorderedList){result.push('</ul>');inUnorderedList=false;}}}result.push(line);}else{if(inOrderedList){result.push('</ol>');inOrderedList=false;}if(inUnorderedList){result.push('</ul>');inUnorderedList=false;}result.push(line);}}}if(inOrderedList)result.push('</ol>');if(inUnorderedList)result.push('</ul>');return result.join('\\n');}\nfunction formatMathSymbols(content){var mathSymbols={'α':'&alpha;','β':'&beta;','γ':'&gamma;','δ':'&delta;','ε':'&epsilon;','ζ':'&zeta;','η':'&eta;','θ':'&theta;','λ':'&lambda;','μ':'&mu;','π':'&pi;','σ':'&sigma;','∑':'&sum;','∏':'&prod;','∫':'&int;','∂':'&part;','∇':'&nabla;','∞':'&infin;','≤':'&le;','≥':'&ge;','≠':'&ne;','≈':'&asymp;','±':'&plusmn;','×':'&times;','÷':'&divide;','→':'&rarr;','←':'&larr;','↑':'&uarr;','↓':'&darr;','⇒':'&rArr;','⇐':'&lArr;'};var subMap={'₀':'0','₁':'1','₂':'2','₃':'3','₄':'4','₅':'5','₆':'6','₇':'7','₈':'8','₉':'9','ₖ':'k','ₜ':'t','ₛ':'s','₊':'+','₋':'-'};var supMap={'⁰':'0','¹':'1','²':'2','³':'3','⁴':'4','⁵':'5','⁶':'6','⁷':'7','⁸':'8','⁹':'9','ᵏ':'k','ᵗ':'t','ˢ':'s','⁺':'+','⁻':'-'};for(var symbol in mathSymbols){content=content.replace(new RegExp(escapeRegExp(symbol),'g'),mathSymbols[symbol]);}content=content.replace(/([a-zA-Z])₍([0-9]+)₎/g,'$1<sub>$2</sub>').replace(/([a-zA-Z])⁽([0-9]+)⁾/g,'$1<sup>$2</sup>').replace(/([a-zA-Z])([₀₁₂₃₄₅₆₇₈₉ₖₜₛ₊₋]+)/g,function(match,base,subscript){var converted=subscript.replace(/[₀₁₂₃₄₅₆₇₈₉ₖₜₛ₊₋]/g,function(char){return subMap[char]||char;});return base+'<sub>'+converted+'</sub>';}).replace(/([a-zA-Z])([⁰¹²³⁴⁵⁶⁷⁸⁹ᵏᵗˢ⁺⁻]+)/g,function(match,base,superscript){var converted=superscript.replace(/[⁰¹²³⁴⁵⁶⁷⁸⁹ᵏᵗˢ⁺⁻]/g,function(char){return supMap[char]||char;});return base+'<sup>'+converted+'</sup>';}).replace(/\\b([A-Za-z])_\\{([^}]+)\\}/g,'$1<sub>$2</sub>').replace(/\\b([A-Za-z])\\^\\{([^}]+)\\}/g,'$1<sup>$2</sup>');return content;}\nfunction formatParagraphs(content){var lines=content.split('\\n'),result=[],inParagraph=false,currentParagraph=[];for(var i=0;i<lines.length;i++){var originalLine=lines[i],line=originalLine.trim();if(line.match(/^<[^>]+>/)||line===''){if(inParagraph&&currentParagraph.length>0){var paragraphContent=currentParagraph.join(' ').trim();if(paragraphContent)result.push('<p class=\"content-paragraph indent-paragraph\">'+paragraphContent+'</p>');currentParagraph=[];inParagraph=false;}result.push(originalLine);continue;}if(line.match(/^#{1,6}\\s/)||line.match(/^\\d+\\.\\s/)||line.match(/^[-*+]\\s/)||line.match(/^>/)){if(inParagraph&&currentParagraph.length>0){var paragraphContent=currentParagraph.join(' ').trim();if(paragraphContent)result.push('<p class=\"content-paragraph indent-paragraph\">'+paragraphContent+'</p>');currentParagraph=[];inParagraph=false;}result.push(originalLine);continue;}if(line){currentParagraph.push(line);inParagraph=true;}}if(inParagraph&&currentParagraph.length>0){var paragraphContent=currentParagraph.join(' ').trim();if(paragraphContent)result.push('<p class=\"content-paragraph indent-paragraph\">'+paragraphContent+'</p>');}return result.join('\\n');}\nfunction escapeRegExp(string){return string.replace(/[.*+?^${}()|[\\]\\\\]/g,'\\\\$&');}\nfunction formatLinks(content){return content.replace(/<a\\s+href=\"(https?:\\/\\/[^\"]+)\"([^>]*)>/gi,function(match,url,attrs){return !attrs.includes('target=')?'<a href=\"'+url+'\"'+attrs+' target=\"_blank\" rel=\"noopener noreferrer\">':match;});}\nfunction escapeHtml(text){var div=document.createElement('div');div.textContent=text;return div.innerHTML;}\nfunction addTocClickEvents(){document.querySelectorAll('#tableOfContents a').forEach(function(link){link.addEventListener('click',function(e){e.preventDefault();var targetElement=document.getElementById(this.getAttribute('data-target'));if(targetElement){targetElement.scrollIntoView({behavior:'smooth',block:'start'});updateActiveTocItem(this);}});});}\nfunction addScrollHighlight(headings){var throttleTimer=null;function updateActiveHeading(){var windowHeight=window.innerHeight,activeHeading=null;for(var i=headings.length-1;i>=0;i--){var rect=headings[i].getBoundingClientRect();if(rect.top<=windowHeight*0.3){activeHeading=headings[i];break;}}if(activeHeading){var activeLink=document.querySelector('#tableOfContents a[data-target=\"'+activeHeading.id+'\"]');if(activeLink)updateActiveTocItem(activeLink);}}window.addEventListener('scroll',function(){if(throttleTimer)clearTimeout(throttleTimer);throttleTimer=setTimeout(updateActiveHeading,100);});updateActiveHeading();}\nfunction updateActiveTocItem(activeLink){document.querySelectorAll('#tableOfContents a').forEach(function(link){link.classList.remove('active');});if(activeLink){activeLink.classList.add('active');scrollTocToActiveItem(activeLink);}}\nfunction scrollTocToActiveItem(activeLink){var tocCard=document.getElementById('tocCard');if(!activeLink||!tocCard)return;var cardRect=tocCard.getBoundingClientRect();var activeRect=activeLink.getBoundingClientRect();var relativeTop=activeRect.top-cardRect.top;var relativeBottom=activeRect.bottom-cardRect.bottom;var cardStyle=window.getComputedStyle(tocCard);var paddingTop=parseFloat(cardStyle.paddingTop)||0;var titleHeight=tocCard.querySelector('h5')?tocCard.querySelector('h5').offsetHeight:0;var contentTop=paddingTop+titleHeight;if(relativeTop<contentTop){tocCard.scrollTop+=(relativeTop-contentTop-10);}else if(relativeBottom>0){tocCard.scrollTop+=(relativeBottom+10);}}\nfunction optimizeImages(){document.querySelectorAll('.article-content img').forEach(function(img){img.loading='lazy';img.onerror=function(){this.style.display='none';};if(!img.alt)img.alt='Bài viết có hình ảnh';});}\nfunction initReadingProgress(){var progressBar=document.getElementById('readingProgress');function updateProgress(){var scrollTop=window.pageYOffset||document.documentElement.scrollTop;var docHeight=document.documentElement.scrollHeight-window.innerHeight;progressBar.style.width=Math.min((scrollTop/docHeight)*100,100)+'%';}window.addEventListener('scroll',updateProgress);updateProgress();}\nfunction initTableOfContents(){var content=document.querySelector('.article-content');if(!content)return;var headings=content.querySelectorAll('h1, h2, h3, h4, h5, h6');if(headings.length<=1)return;var tocCard=document.getElementById('tocCard');var tocList=document.getElementById('tableOfContents');var html='',counters=[0,0,0,0,0,0];headings.forEach(function(heading,index){var id='heading-'+index;heading.id=id;var level=parseInt(heading.tagName.substring(1));var indent=(level-1)*15;counters[level-1]++;for(var i=level;i<counters.length;i++)counters[i]=0;var numberText='';for(var i=0;i<level;i++){if(counters[i]>0)numberText+=(numberText?'.':'')+counters[i];}numberText+='.';html+='<a href=\"#'+id+'\" class=\"toc-item toc-level-'+level+'\" style=\"padding-left: '+indent+'px;\" data-target=\"'+id+'\"><span class=\"toc-number\">'+numberText+'</span> <span class=\"toc-text\">'+heading.textContent+'</span></a>';});tocList.innerHTML=html;tocCard.style.display='block';addTocClickEvents();addScrollHighlight(headings);tocList.addEventListener('click',function(e){if(e.target.tagName==='A'){e.preventDefault();var targetElement=document.getElementById(e.target.getAttribute('data-target'));if(targetElement){targetElement.scrollIntoView({behavior:'smooth',block:'start'});targetElement.style.transition='all 0.3s ease';targetElement.style.backgroundColor='#e3f2fd';targetElement.style.padding='10px';targetElement.style.borderRadius='6px';setTimeout(function(){targetElement.style.backgroundColor='';targetElement.style.padding='';targetElement.style.borderRadius='';},2000);}}});}\nfunction shareToWeibo(){window.open('https://service.weibo.com/share/share.php?url='+encodeURIComponent(window.location.href)+'&title='+encodeURIComponent(document.title),'_blank');}\nfunction copyUrl(){navigator.clipboard.writeText(window.location.href).then(function(){alert('Liên kết đã được sao chép vào khay nhớ tạm');}).catch(function(){var textArea=document.createElement('textarea');textArea.value=window.location.href;textArea.style.cssText='position:fixed;left:-999999px;top:-999999px';document.body.appendChild(textArea);textArea.focus();textArea.select();try{alert(document.execCommand('copy')?'Liên kết đã được sao chép vào khay nhớ tạm':'Nếu sao chép không thành công, vui lòng sao chép liên kết theo cách thủ công');}catch(err){alert('Nếu sao chép không thành công, vui lòng sao chép liên kết theo cách thủ công');}document.body.removeChild(textArea);});}\nfunction printArticle(){var printWindow=window.open('','_blank');var articleContent=document.querySelector('.article-detail').innerHTML;var articleTitle=document.querySelector('.article-title').textContent;printWindow.document.write('<!DOCTYPE html><html lang=\"vi\"><title>'+articleTitle+'</title><style>body{font-family:-apple-system,BlinkMacSystemFont,\"Segoe UI\",Roboto,\"Helvetica Neue\",Arial,sans-serif;line-height:1.6;color:#333;max-width:800px;margin:0 auto;padding:20px}h1,h2,h3,h4,h5,h6{color:#1a1c1f;margin-top:1.5em;margin-bottom:0.5em}h1{font-size:2em;border-bottom:2px solid #e9ecef;padding-bottom:0.3em}h2{font-size:1.5em;border-bottom:1px solid #e9ecef;padding-bottom:0.3em}h3{font-size:1.25em}p{margin-bottom:1em}pre{background:#f8f9fa;border:1px solid #e9ecef;border-radius:4px;padding:1em;overflow-x:auto;font-family:Consolas,Monaco,\"Courier New\",monospace}code{background:#f1f3f4;padding:2px 4px;border-radius:3px;font-family:Consolas,Monaco,\"Courier New\",monospace}blockquote{border-left:4px solid #1764ff;background:#f8f9fa;padding:1em;margin:1em 0;border-radius:0 4px 4px 0}img{max-width:100%;height:auto}table{width:100%;border-collapse:collapse;margin:1em 0}th,td{border:1px solid #ddd;padding:8px;text-align:left}th{background-color:#f2f2f2}.article-meta,.article-footer,.share-buttons{display:none}</style><body>'+articleContent+'</body></html>');printWindow.document.close();printWindow.focus();setTimeout(function(){printWindow.print();printWindow.close();},250);}\nfunction improvedStickyPosition(){var sidebarCards=document.querySelectorAll('.sidebar-card');var originalPositions=[],isFixed=[];sidebarCards.forEach(function(card,index){var rect=card.getBoundingClientRect();originalPositions[index]=rect.top+window.pageYOffset;isFixed[index]=false;var placeholder=document.createElement('div');placeholder.style.cssText='height:'+card.offsetHeight+'px;display:none';placeholder.className='sticky-placeholder';card.parentNode.insertBefore(placeholder,card.nextSibling);});function updateStickyPosition(){var scrollTop=window.pageYOffset||document.documentElement.scrollTop;sidebarCards.forEach(function(card,index){var originalTop=originalPositions[index];var stickyTop=index>0?(document.querySelector('.toc-card').style.position==='fixed'?60+document.querySelector('.toc-card').offsetHeight+8:80):60;var placeholder=card.nextSibling;if(scrollTop>originalTop-stickyTop){if(!isFixed[index]){var colContainer=card.closest('[class*=\"col-\"]');var colRect=colContainer.getBoundingClientRect();var colStyle=window.getComputedStyle(colContainer);var paddingLeft=parseFloat(colStyle.paddingLeft)||0;var paddingRight=parseFloat(colStyle.paddingRight)||0;card.style.cssText='position:fixed;top:'+stickyTop+'px;left:'+(colRect.left+paddingLeft)+'px;width:'+(colRect.width-paddingLeft-paddingRight)+'px;z-index:9999;box-sizing:border-box';card.classList.add('fixed');placeholder.style.display='block';isFixed[index]=true;}}else if(isFixed[index]){card.style.cssText='';card.classList.remove('fixed');placeholder.style.display='none';isFixed[index]=false;}});}window.addEventListener('scroll',updateStickyPosition);window.addEventListener('resize',function(){setTimeout(function(){sidebarCards.forEach(function(card,index){if(isFixed[index]){var colContainer=card.closest('[class*=\"col-\"]');var colRect=colContainer.getBoundingClientRect();var colStyle=window.getComputedStyle(colContainer);var paddingLeft=parseFloat(colStyle.paddingLeft)||0;var paddingRight=parseFloat(colStyle.paddingRight)||0;card.style.left=(colRect.left+paddingLeft)+'px';card.style.width=(colRect.width-paddingLeft-paddingRight)+'px';}else{originalPositions[index]=card.getBoundingClientRect().top+window.pageYOffset;}});},100);});updateStickyPosition();}\ndocument.addEventListener('DOMContentLoaded',function(){setTimeout(improvedStickyPosition,1000);});\n    </script>\n\r\n            </div>\r\n        </form>\r\n\r\n        <div class=\"fixed-icon\">\r\n            <div style=\"width: 120px; position: relative; left: 1rem; top: -12px;\">\r\n            </div>\r\n            <a id=\"webchat\" class=\"wx-con disa\" href=\"http://wpa.qq.com/msgrd?v=3&uin=365833440&site=qq&menu=yes\" target=\"_blank\">\r\n                <img class=\"wechat-ico\" src=\"site/image/icon/qq.svg\" alt=\"Trợ lý OCR QQ dịch vụ khách hàng trực tuyến\" />\r\n                <div class=\"wx-text\">Dịch vụ khách hàng QQ (365833440)</div>\r\n                <label class=\"badge badge-danger\" style=\"background: rgb(255, 34, 122); display: inline-block;\">1</label>\r\n            </a>\r\n\r\n            <a class=\"wx-con\" href=\"https://qm.qq.com/cgi-bin/qm/qr?k=bohNy9GMdgD5bsYrB-fcIjy-s6Ot3zOg&jump_from=webapi&qr=1\" target=\"_blank\">\r\n                <img class=\"wechat-ico\" src=\"site/image/icon/qq.svg\" alt=\"Trợ lý OCR Nhóm giao tiếp người dùng QQ\" />\r\n                <div class=\"wx-text\">Tập đoàn QQ (100029010)</div>\r\n                <label class=\"badge badge-danger\" style=\"background: rgb(255, 34, 122); display: inline-block;\">2</label>\r\n            </a>\r\n            <a href=\"mailto:<EMAIL>\" class=\"wx-con\">\r\n                <img class=\"wechat-ico\" src=\"site/image/icon/im.svg\" alt=\"Trợ lý OCR liên hệ với dịch vụ khách hàng qua email\" />\r\n                <div class=\"wx-text\" style=\"width: 240px; text-align: left; left: -15.3rem; padding: 10px 20px;\">\r\n                    <div style=\"font-size: 15px\">Email: <EMAIL></div>\r\n                    <hr style=\"background: #c1c1c1; margin: 10px 10px 6px 0;\" />\r\n                    <span style=\"font-size: 13px; color: #c1c1c1\">Cảm ơn bạn đã nhận xét và đề xuất của bạn!</span>\r\n                </div>\r\n                <label class=\"badge badge-danger\" style=\"background: rgb(255, 34, 122); display: inline-block;\">3</label>\r\n            </a>\r\n        </div>\r\n\r\n        <style>\r\n            .content {\r\n                width: 100%;\r\n                padding: 0 !important;\r\n                margin: 0 auto\r\n            }\r\n\r\n            h3.h6 {\r\n                font-size: 1rem !important;\r\n                font-weight: bold !important;\r\n                line-height: 1.2 !important;\r\n                margin-bottom: .5rem;\r\n                font-family: inherit\r\n            }\r\n        </style>\r\n        <footer class=\"page-footer\">\r\n            <div class=\"container\">\r\n                <div class=\"row foot-cont pt-4 no-gutters\" style=\"border-top: 1px solid #e1e6ed; max-width: 100%\">\r\n                    <div class=\"col-lg-12 hu-an p-0 mb-3 mt-3 text-l color-gray\" style=\"text-align: center;\">\r\n                        Trợ lý nhận dạng văn bản OCR&nbsp;©️ 2025 ALL RIGHTS RESERVED. Đã đăng ký Bản quyền&nbsp;&nbsp;&nbsp;|&nbsp;&nbsp;&nbsp;\r\n                        <a href=\"PrivacyPolicy.aspx\" style=\"text-decoration: underline;\">Thỏa thuận bảo mật</a>&nbsp;&nbsp;&nbsp;|&nbsp;&nbsp;&nbsp;\r\n                        <a href=\"UserAgreement.aspx\" style=\"text-decoration: underline;\">Thỏa thuận người dùng</a>&nbsp;&nbsp;&nbsp;|&nbsp;&nbsp;&nbsp;\r\n                        <a href=\"Status.aspx\" style=\"text-decoration: underline;\">Tình trạng dịch vụ</a>&nbsp;&nbsp;&nbsp;|&nbsp;&nbsp;&nbsp;\r\n                        <a class=\"mb-2 color-gray\" href=\"https://beian.miit.gov.cn/\" target=\"_blank\" style=\"text-decoration: underline;\">E ICP Chuẩn bị số 2021012692</a>\r\n                    </div>\r\n                </div>\r\n            </div>\r\n        </footer>\r\n        <style>\r\n            #translate > .translateSelectLanguage {\r\n                right: 2rem;\r\n                font-size: 1rem;\r\n                width: 150px;\r\n                padding: .3rem;\r\n                margin-right: 20px;\r\n                border: 1px solid #C9C9C9;\r\n                background: #fff;\r\n                color: #555\r\n            }\r\n        </style>\r\n    </div>\r\n    <script>\r\n        document.addEventListener('DOMContentLoaded', function () {\r\n            var setScroll = function (active) {\r\n                forEach(qa('.v4_header_pc,.v4_header_mob'), function (e) {\r\n                    if (active) addClass(e, 'scrollActive'); else removeClass(e, 'scrollActive');\r\n                });\r\n            };\r\n            if (getScrollY() > 60) setScroll(1);\r\n            window.addEventListener('scroll', function () { setScroll(getScrollY() > 60); });\r\n\r\n            forEach(qa('.v4_header_pc'), function (h) {\r\n                h.addEventListener('mouseenter', function () { addClass(h, 'active'); });\r\n                h.addEventListener('mouseleave', function () { removeClass(h, 'active'); });\r\n            });\r\n\r\n            var menuTimer;\r\n            var hideMenu = function (menuId) {\r\n                var m = document.getElementById(menuId);\r\n                if (m) {\r\n                    m.style.cssText = 'visibility:hidden;height:0;display:none';\r\n                    var n = q('.nav-drown-con', m);\r\n                    if (n) { n.style.cssText = 'height:0;opacity:0;transform:translate(0,-100%)'; }\r\n                }\r\n            };\r\n\r\n            forEach(qa('ul.top-nav>li'), function (i) {\r\n                i.addEventListener('mouseenter', function () {\r\n                    clearTimeout(menuTimer);\r\n                    var l = this.querySelector('a'), t = this.querySelector('.triangle'), s = l ? l.getAttribute('data-sub') : null;\r\n                    if (l) addClass(l, 'active'); if (t) addClass(t, 'active');\r\n                    if (s) {\r\n                        var m = document.getElementById(s);\r\n                        if (m) {\r\n                            m.style.cssText = 'display:block;visibility:visible';\r\n                            var siblings = m.parentNode ? toArray(m.parentNode.children) : [];\r\n                            forEach(siblings, function (c) { if (c !== m) c.style.display = 'none'; });\r\n                            var n = q('.nav-drown-con', m);\r\n                            if (n) { n.style.cssText = 'height:100%;opacity:1;transform:translate(0,0)'; }\r\n\r\n                            m.addEventListener('mouseenter', function () { clearTimeout(menuTimer); });\r\n                            m.addEventListener('mouseleave', function () { menuTimer = setTimeout(function () { hideMenu(s); }, 100); });\r\n                        }\r\n                    }\r\n                });\r\n                i.addEventListener('mouseleave', function () {\r\n                    var l = this.querySelector('a'), t = this.querySelector('.triangle'), s = l ? l.getAttribute('data-sub') : null;\r\n                    if (l) removeClass(l, 'active'); if (t) removeClass(t, 'active');\r\n                    if (s) { menuTimer = setTimeout(function () { hideMenu(s); }, 100); }\r\n                });\r\n            });\r\n\r\n            var r = q('.v4_header_mob .right-menu');\r\n            if (r) r.addEventListener('click', function () {\r\n                var arr = [r, q('.v4_header_mob'), q('#nav_products'), q('.mob-nav-content .sidebar-fix')];\r\n                for (var i = 0; i < arr.length; i++) {\r\n                    var e = arr[i];\r\n                    if (e) {\r\n                        var name = (i === 0 ? 'active' : (i === 1 ? 'active' : (i === 2 ? 'active1' : 'fixedActiveBg')));\r\n                        toggleClass(e, name);\r\n                    }\r\n                }\r\n                var f = q('.mob-nav-content .sidebar-fix');\r\n                if (f) {\r\n                    toggleClass(f, 'show');\r\n                    document.body.style.overflow = hasClass(f, 'show') ? 'hidden' : 'scroll';\r\n                }\r\n            });\r\n\r\n            document.addEventListener('click', function (e) {\r\n                e = e || window.event;\r\n                var target = e.target || e.srcElement;\r\n                var m = closest(target, '.sidebar-fix-left .mob-nav-item');\r\n                if (m) {\r\n                    var p = m.parentNode;\r\n                    if (p) forEach(toArray(p.children), function (c) { removeClass(c, 'active'); });\r\n                    addClass(m, 'active');\r\n                    var d = m.getAttribute('data');\r\n                    if (d) forEach(qa('.sidebar-fix-rigth .sub-nav'), function (n, i) { n.style.display = (i == d ? 'block' : 'none'); });\r\n                }\r\n            });\r\n        });\r\n    </script>\r\n</body>\r\n</html>\r\n\r\n\t            <script type=\"text/javascript\">\r\n                    var _hmt = _hmt || [];\r\n                    (function () {\r\n                        var hm = document.createElement('script');hm.async=1;\r\n                        hm.src = 'https://hm.baidu.com/hm.js?5ee048d7caaf0d439cf16309a1687754';\r\n                        var s = document.getElementsByTagName('script')[0];s.parentNode.insertBefore(hm, s);\r\n                    })();\r\n                </script><div id=\"translate\" class=\"ignore\" style=\"display: none\"></div><script src=\"/static/js/translate.js\"></script>"
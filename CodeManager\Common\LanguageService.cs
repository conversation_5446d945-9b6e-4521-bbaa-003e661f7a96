using Account.Web.UrlBuilding;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Web;

namespace Account.Web
{
    /// <summary>
    /// 语言服务 - 负责多语言处理的核心功能
    /// 包括语言定义、获取、翻译和缓存管理
    /// </summary>
    public static class LanguageService
    {
        #region 性能优化缓存

        /// <summary>
        /// 语言代码反向查找缓存 - 从别名到标准代码的映射
        /// </summary>
        private static readonly Dictionary<string, string> _languageCodeCache =
            new Dictionary<string, string>(StringComparer.OrdinalIgnoreCase);

        /// <summary>
        /// 标准语言代码集合 - 用于快速检查是否为标准代码
        /// </summary>
        private static readonly HashSet<string> _standardLanguageCodes =
            new HashSet<string>(StringComparer.OrdinalIgnoreCase);

        /// <summary>
        /// 缓存锁对象
        /// </summary>
        private static readonly object _cacheLock = new object();

        /// <summary>
        /// 缓存是否已初始化
        /// </summary>
        private static volatile bool _cacheInitialized = false;

        /// <summary>
        /// 静态构造函数 - 初始化性能优化缓存
        /// </summary>
        static LanguageService()
        {
            InitializeLanguageCodeCache();
        }

        /// <summary>
        /// 初始化语言代码查找缓存
        /// </summary>
        private static void InitializeLanguageCodeCache()
        {
            if (_cacheInitialized)
                return;

            lock (_cacheLock)
            {
                if (_cacheInitialized)
                    return;

                try
                {
                    // 构建反向查找缓存
                    foreach (var entry in CommonTranslate.DicJsLanguage)
                    {
                        var standardCode = entry.Key;

                        // 添加标准代码到集合
                        _standardLanguageCodes.Add(standardCode);

                        // 标准代码映射到自己
                        _languageCodeCache[standardCode] = standardCode;

                        // 所有别名映射到标准代码
                        foreach (var alias in entry.Value)
                        {
                            if (!string.IsNullOrEmpty(alias))
                            {
                                _languageCodeCache[alias] = standardCode;
                            }
                        }
                    }

                    _cacheInitialized = true;
                }
                catch (Exception ex)
                {
                    // 记录错误但不抛出异常，确保系统能继续运行
                    System.Diagnostics.Debug.WriteLine($"LanguageService cache initialization failed: {ex.Message}");
                }
            }
        }

        #endregion

        /// <summary>
        /// 尝试将语言代码转换为标准格式 - 优化版本使用缓存
        /// </summary>
        /// <param name="langCode">输入的语言代码</param>
        /// <param name="standardCode">输出的标准语言代码</param>
        /// <param name="debugInfo">可选的调试信息收集器</param>
        /// <returns>如果找到匹配返回true，否则返回false</returns>
        public static bool TryGetStandardLanguageCode(string langCode, out string standardCode, LanguageDebugInfo debugInfo = null)
        {
            debugInfo?.AddWarning($"TryGetStandardLanguageCode 尝试匹配输入值: '{langCode}'");

            // 输入为空，无法转换
            if (string.IsNullOrEmpty(langCode))
            {
                standardCode = LanguageConfiguration.DefaultLanguage;
                debugInfo?.AddWarning("结果: 失败，输入为空");
                return false;
            }

            // 使用缓存进行快速查找
            if (_languageCodeCache.TryGetValue(langCode, out standardCode))
            {
                debugInfo?.AddWarning($"结果: 成功 (缓存命中), 输出: '{standardCode}'");
                return true;
            }

            // 缓存未命中，回退到原始逻辑（这种情况应该很少发生）
            debugInfo?.AddWarning("缓存未命中，使用原始查找逻辑");

            // 因为字典是 OrdinalIgnoreCase 的，所以 ContainsKey 本身就是不区分大小写的
            if (CommonTranslate.DicJsLanguage.ContainsKey(langCode))
            {
                // 为了获取字典中key的原始大小写（例如 "zh-Hans" 而不是 "zh-hans"），我们还需要一次查找
                standardCode = CommonTranslate.DicJsLanguage.Keys.First(k => k.Equals(langCode, StringComparison.OrdinalIgnoreCase));
                debugInfo?.AddWarning($"结果: 成功 (作为字典Key匹配), 输出: '{standardCode}'");

                // 将结果添加到缓存中
                lock (_cacheLock)
                {
                    if (!_languageCodeCache.ContainsKey(langCode))
                    {
                        _languageCodeCache[langCode] = standardCode;
                    }
                }

                return true;
            }

            // 如果语言代码是value中的一个值
            foreach (var entry in CommonTranslate.DicJsLanguage)
            {
                if (entry.Value.Any(v => v.Equals(langCode, StringComparison.OrdinalIgnoreCase)))
                {
                    standardCode = entry.Key;
                    debugInfo?.AddWarning($"结果: 成功 (作为字典Value匹配), 输出: '{standardCode}'");

                    // 将结果添加到缓存中
                    lock (_cacheLock)
                    {
                        if (!_languageCodeCache.ContainsKey(langCode))
                        {
                            _languageCodeCache[langCode] = standardCode;
                        }
                    }

                    return true;
                }
            }

            // 如果没有匹配
            standardCode = LanguageConfiguration.DefaultLanguage;
            debugInfo?.AddWarning("结果: 失败，无匹配项");
            return false;
        }

        private static readonly Dictionary<string, string> LanguageCodeMap = new Dictionary<string, string>(StringComparer.OrdinalIgnoreCase)
        {
            { "zh-Hans", "zh" },
            { "zh-Hant", "zh-tw" },
            { "yue", "zh-yue" },
        };

        #region 智能SEO标签生成

        // 将服务ID转换为标准语言代码
        private static string ConvertToStandardLanguageCode(string serviceId)
        {
            if (LanguageCodeMap.TryGetValue(serviceId, out var standardCode))
                return standardCode;
            return serviceId.ToLower();
        }

        /// <summary>
        /// 生成多语言链接标签
        /// </summary>
        public static string GenerateSiteMapLanguageLinks(string path)
        {
            StringBuilder sb = new StringBuilder();

            // 当前页面的URL
            string currentUrl = LanguageConfiguration.HostUrl + path.TrimStart('/');

            sb.AppendLine(string.Format("<xhtml:link rel=\"alternate\" hreflang=\"x-default\" href=\"{0}\" />", currentUrl));

            foreach (var lang in CommonTranslate.DicJsLanguage.Keys)
            {
                // 获取标准化的语言代码
                string hreflangValue = ConvertToStandardLanguageCode(lang);

                // 路径格式URL
                sb.AppendLine(string.Format("<xhtml:link rel=\"alternate\" hreflang=\"{3}\" href=\"{0}{1}/{2}\" />",
                    LanguageConfiguration.HostUrl, lang, path, hreflangValue));
            }

            return sb.ToString();
        }

        /// <summary>
        /// 生成智能Canonical URL - 使用统一URL构建器
        /// </summary>
        /// <param name="path">页面路径</param>
        /// <param name="language">当前语言</param>
        /// <param name="queryString">查询参数（可选）</param>
        /// <returns>Canonical URL</returns>
        public static string GenerateSmartCanonicalUrl(string path)
        {
            return GenerateSmartCanonicalUrl(path, HttpContext.Current.Items["CurrentLanguage"]?.ToString() ?? LanguageConfiguration.DefaultLanguage);
        }

        /// <summary>
        /// 生成智能Canonical URL - 使用统一URL构建器
        /// </summary>
        /// <param name="path">页面路径</param>
        /// <param name="language">当前语言</param>
        /// <param name="queryString">查询参数（可选）</param>
        /// <returns>Canonical URL</returns>
        public static string GenerateSmartCanonicalUrl(string path, string language, string queryString = null)
        {
            // 过滤掉lang参数，因为语言信息已经在URL路径中体现
            var filteredQuery = string.IsNullOrEmpty(queryString) ? null : FilterQueryString(queryString, "lang");

            // 使用统一URL构建器生成Canonical URL
            return UnifiedUrlBuilder.BuildCanonicalUrl(path, language, filteredQuery);
        }

        /// <summary>
        /// 生成智能Hreflang标签 - 使用统一URL构建器
        /// </summary>
        /// <param name="path">页面路径</param>
        /// <param name="currentLanguage">当前语言</param>
        /// <param name="queryString">查询参数（可选）</param>
        /// <returns>完整的hreflang标签HTML</returns>
        public static string GenerateSmartHreflangTags(string path, string currentLanguage, string queryString = null)
        {
            StringBuilder sb = new StringBuilder();

            // 过滤查询参数
            string filteredQuery = string.IsNullOrEmpty(queryString) ? null : FilterQueryString(queryString, "lang");

            // x-default 指向默认语言的简洁URL
            string xDefaultUrl = UnifiedUrlBuilder.BuildHreflangUrl(path, LanguageConfiguration.DefaultLanguage, filteredQuery);
            sb.AppendLine($"<link rel=\"alternate\" href=\"{xDefaultUrl}\" hreflang=\"x-default\" />");

            // 为所有支持的语言生成hreflang标签
            foreach (var language in LanguageConfiguration.SupportedLanguages)
            {
                string langUrl = UnifiedUrlBuilder.BuildHreflangUrl(path, language, filteredQuery);
                string hreflangCode = ConvertToHreflangCode(language);
                sb.AppendLine($"<link rel=\"alternate\" href=\"{langUrl}\" hreflang=\"{hreflangCode}\" />");

                // 如果是中文简体，也添加简化的zh标签
                if (language == "zh-Hans")
                {
                    sb.AppendLine($"<link rel=\"alternate\" href=\"{langUrl}\" hreflang=\"zh\" />");
                }
            }

            return sb.ToString();
        }

        /// <summary>
        /// 检查是否为默认语言
        /// </summary>
        /// <param name="languageCode">语言代码</param>
        /// <returns>如果是默认语言返回true</returns>
        public static bool IsDefaultLanguage(string languageCode)
        {
            return string.Equals(languageCode, LanguageConfiguration.DefaultLanguage, StringComparison.OrdinalIgnoreCase);
        }

        /// <summary>
        /// 将语言代码转换为hreflang标准代码
        /// </summary>
        /// <param name="languageCode">内部语言代码</param>
        /// <returns>hreflang标准代码</returns>
        private static string ConvertToHreflangCode(string languageCode)
        {
            // 使用现有的映射逻辑
            if (LanguageCodeMap.TryGetValue(languageCode, out var hreflangCode))
                return hreflangCode;
            return languageCode.ToLower();
        }

        /// <summary>
        /// 过滤查询字符串，移除指定参数
        /// </summary>
        /// <param name="queryString">原始查询字符串</param>
        /// <param name="excludeParams">要排除的参数名</param>
        /// <returns>过滤后的查询字符串</returns>
        private static string FilterQueryString(string queryString, params string[] excludeParams)
        {
            if (string.IsNullOrEmpty(queryString))
                return string.Empty;

            var queryParams = HttpUtility.ParseQueryString(queryString);

            // 移除指定参数
            foreach (var param in excludeParams)
            {
                queryParams.Remove(param);
            }

            // 重新构建查询字符串
            var filteredPairs = new List<string>();
            foreach (string key in queryParams.Keys)
            {
                if (key != null)
                {
                    foreach (string value in queryParams.GetValues(key) ?? new string[0])
                    {
                        filteredPairs.Add($"{HttpUtility.UrlEncode(key)}={HttpUtility.UrlEncode(value)}");
                    }
                }
            }

            return string.Join("&", filteredPairs);
        }

        #endregion
    }
}

﻿"<!DOCTYPE html>\r\n<html xmlns=\"http://www.w3.org/1999/xhtml\" lang=\"hu\">\r\n<head><link rel=\"canonical\" href=\"https://ocr.oldfish.cn/hu/Article.aspx?id=82&slug=attention-mechanism-principles\" /><link rel=\"alternate\" href=\"https://ocr.oldfish.cn/Article.aspx?id=82&slug=attention-mechanism-principles\" hreflang=\"x-default\" />\r\n<link rel=\"alternate\" href=\"https://ocr.oldfish.cn/Article.aspx?id=82&slug=attention-mechanism-principles\" hreflang=\"zh\" />\r\n<link rel=\"alternate\" href=\"https://ocr.oldfish.cn/Article.aspx?id=82&slug=attention-mechanism-principles\" hreflang=\"zh\" />\r\n<link rel=\"alternate\" href=\"https://ocr.oldfish.cn/zh-Hant/Article.aspx?id=82&slug=attention-mechanism-principles\" hreflang=\"zh-tw\" />\r\n<link rel=\"alternate\" href=\"https://ocr.oldfish.cn/yue/Article.aspx?id=82&slug=attention-mechanism-principles\" hreflang=\"zh-yue\" />\r\n<link rel=\"alternate\" href=\"https://ocr.oldfish.cn/en/Article.aspx?id=82&slug=attention-mechanism-principles\" hreflang=\"en\" />\r\n<link rel=\"alternate\" href=\"https://ocr.oldfish.cn/es/Article.aspx?id=82&slug=attention-mechanism-principles\" hreflang=\"es\" />\r\n<link rel=\"alternate\" href=\"https://ocr.oldfish.cn/hi/Article.aspx?id=82&slug=attention-mechanism-principles\" hreflang=\"hi\" />\r\n<link rel=\"alternate\" href=\"https://ocr.oldfish.cn/ar/Article.aspx?id=82&slug=attention-mechanism-principles\" hreflang=\"ar\" />\r\n<link rel=\"alternate\" href=\"https://ocr.oldfish.cn/pt/Article.aspx?id=82&slug=attention-mechanism-principles\" hreflang=\"pt\" />\r\n<link rel=\"alternate\" href=\"https://ocr.oldfish.cn/bn/Article.aspx?id=82&slug=attention-mechanism-principles\" hreflang=\"bn\" />\r\n<link rel=\"alternate\" href=\"https://ocr.oldfish.cn/ru/Article.aspx?id=82&slug=attention-mechanism-principles\" hreflang=\"ru\" />\r\n<link rel=\"alternate\" href=\"https://ocr.oldfish.cn/ja/Article.aspx?id=82&slug=attention-mechanism-principles\" hreflang=\"ja\" />\r\n<link rel=\"alternate\" href=\"https://ocr.oldfish.cn/pa/Article.aspx?id=82&slug=attention-mechanism-principles\" hreflang=\"pa\" />\r\n<link rel=\"alternate\" href=\"https://ocr.oldfish.cn/de/Article.aspx?id=82&slug=attention-mechanism-principles\" hreflang=\"de\" />\r\n<link rel=\"alternate\" href=\"https://ocr.oldfish.cn/fr/Article.aspx?id=82&slug=attention-mechanism-principles\" hreflang=\"fr\" />\r\n<link rel=\"alternate\" href=\"https://ocr.oldfish.cn/mr/Article.aspx?id=82&slug=attention-mechanism-principles\" hreflang=\"mr\" />\r\n<link rel=\"alternate\" href=\"https://ocr.oldfish.cn/te/Article.aspx?id=82&slug=attention-mechanism-principles\" hreflang=\"te\" />\r\n<link rel=\"alternate\" href=\"https://ocr.oldfish.cn/vi/Article.aspx?id=82&slug=attention-mechanism-principles\" hreflang=\"vi\" />\r\n<link rel=\"alternate\" href=\"https://ocr.oldfish.cn/ko/Article.aspx?id=82&slug=attention-mechanism-principles\" hreflang=\"ko\" />\r\n<link rel=\"alternate\" href=\"https://ocr.oldfish.cn/ta/Article.aspx?id=82&slug=attention-mechanism-principles\" hreflang=\"ta\" />\r\n<link rel=\"alternate\" href=\"https://ocr.oldfish.cn/ur/Article.aspx?id=82&slug=attention-mechanism-principles\" hreflang=\"ur\" />\r\n<link rel=\"alternate\" href=\"https://ocr.oldfish.cn/tr/Article.aspx?id=82&slug=attention-mechanism-principles\" hreflang=\"tr\" />\r\n<link rel=\"alternate\" href=\"https://ocr.oldfish.cn/it/Article.aspx?id=82&slug=attention-mechanism-principles\" hreflang=\"it\" />\r\n<link rel=\"alternate\" href=\"https://ocr.oldfish.cn/gu/Article.aspx?id=82&slug=attention-mechanism-principles\" hreflang=\"gu\" />\r\n<link rel=\"alternate\" href=\"https://ocr.oldfish.cn/pl/Article.aspx?id=82&slug=attention-mechanism-principles\" hreflang=\"pl\" />\r\n<link rel=\"alternate\" href=\"https://ocr.oldfish.cn/uk/Article.aspx?id=82&slug=attention-mechanism-principles\" hreflang=\"uk\" />\r\n<link rel=\"alternate\" href=\"https://ocr.oldfish.cn/ms/Article.aspx?id=82&slug=attention-mechanism-principles\" hreflang=\"ms\" />\r\n<link rel=\"alternate\" href=\"https://ocr.oldfish.cn/id/Article.aspx?id=82&slug=attention-mechanism-principles\" hreflang=\"id\" />\r\n<link rel=\"alternate\" href=\"https://ocr.oldfish.cn/ml/Article.aspx?id=82&slug=attention-mechanism-principles\" hreflang=\"ml\" />\r\n<link rel=\"alternate\" href=\"https://ocr.oldfish.cn/kn/Article.aspx?id=82&slug=attention-mechanism-principles\" hreflang=\"kn\" />\r\n<link rel=\"alternate\" href=\"https://ocr.oldfish.cn/fa/Article.aspx?id=82&slug=attention-mechanism-principles\" hreflang=\"fa\" />\r\n<link rel=\"alternate\" href=\"https://ocr.oldfish.cn/nl/Article.aspx?id=82&slug=attention-mechanism-principles\" hreflang=\"nl\" />\r\n<link rel=\"alternate\" href=\"https://ocr.oldfish.cn/th/Article.aspx?id=82&slug=attention-mechanism-principles\" hreflang=\"th\" />\r\n<link rel=\"alternate\" href=\"https://ocr.oldfish.cn/sw/Article.aspx?id=82&slug=attention-mechanism-principles\" hreflang=\"sw\" />\r\n<link rel=\"alternate\" href=\"https://ocr.oldfish.cn/ro/Article.aspx?id=82&slug=attention-mechanism-principles\" hreflang=\"ro\" />\r\n<link rel=\"alternate\" href=\"https://ocr.oldfish.cn/my/Article.aspx?id=82&slug=attention-mechanism-principles\" hreflang=\"my\" />\r\n<link rel=\"alternate\" href=\"https://ocr.oldfish.cn/or/Article.aspx?id=82&slug=attention-mechanism-principles\" hreflang=\"or\" />\r\n<link rel=\"alternate\" href=\"https://ocr.oldfish.cn/he/Article.aspx?id=82&slug=attention-mechanism-principles\" hreflang=\"he\" />\r\n<link rel=\"alternate\" href=\"https://ocr.oldfish.cn/am/Article.aspx?id=82&slug=attention-mechanism-principles\" hreflang=\"am\" />\r\n<link rel=\"alternate\" href=\"https://ocr.oldfish.cn/fil/Article.aspx?id=82&slug=attention-mechanism-principles\" hreflang=\"fil\" />\r\n<link rel=\"alternate\" href=\"https://ocr.oldfish.cn/sv/Article.aspx?id=82&slug=attention-mechanism-principles\" hreflang=\"sv\" />\r\n<link rel=\"alternate\" href=\"https://ocr.oldfish.cn/el/Article.aspx?id=82&slug=attention-mechanism-principles\" hreflang=\"el\" />\r\n<link rel=\"alternate\" href=\"https://ocr.oldfish.cn/cs/Article.aspx?id=82&slug=attention-mechanism-principles\" hreflang=\"cs\" />\r\n<link rel=\"alternate\" href=\"https://ocr.oldfish.cn/hu/Article.aspx?id=82&slug=attention-mechanism-principles\" hreflang=\"hu\" />\r\n<link rel=\"alternate\" href=\"https://ocr.oldfish.cn/be/Article.aspx?id=82&slug=attention-mechanism-principles\" hreflang=\"be\" />\r\n<link rel=\"alternate\" href=\"https://ocr.oldfish.cn/si/Article.aspx?id=82&slug=attention-mechanism-principles\" hreflang=\"si\" />\r\n<link rel=\"alternate\" href=\"https://ocr.oldfish.cn/ne/Article.aspx?id=82&slug=attention-mechanism-principles\" hreflang=\"ne\" />\r\n<link rel=\"alternate\" href=\"https://ocr.oldfish.cn/km/Article.aspx?id=82&slug=attention-mechanism-principles\" hreflang=\"km\" />\r\n<link rel=\"alternate\" href=\"https://ocr.oldfish.cn/sk/Article.aspx?id=82&slug=attention-mechanism-principles\" hreflang=\"sk\" />\r\n<link rel=\"alternate\" href=\"https://ocr.oldfish.cn/bg/Article.aspx?id=82&slug=attention-mechanism-principles\" hreflang=\"bg\" />\r\n<link rel=\"alternate\" href=\"https://ocr.oldfish.cn/fr-ca/Article.aspx?id=82&slug=attention-mechanism-principles\" hreflang=\"fr-ca\" />\r\n<link rel=\"alternate\" href=\"https://ocr.oldfish.cn/ha/Article.aspx?id=82&slug=attention-mechanism-principles\" hreflang=\"ha\" />\r\n<link rel=\"alternate\" href=\"https://ocr.oldfish.cn/yo/Article.aspx?id=82&slug=attention-mechanism-principles\" hreflang=\"yo\" />\r\n<link rel=\"alternate\" href=\"https://ocr.oldfish.cn/ig/Article.aspx?id=82&slug=attention-mechanism-principles\" hreflang=\"ig\" />\r\n<link rel=\"alternate\" href=\"https://ocr.oldfish.cn/ku/Article.aspx?id=82&slug=attention-mechanism-principles\" hreflang=\"ku\" />\r\n<link rel=\"alternate\" href=\"https://ocr.oldfish.cn/rw/Article.aspx?id=82&slug=attention-mechanism-principles\" hreflang=\"rw\" />\r\n<link rel=\"alternate\" href=\"https://ocr.oldfish.cn/ca/Article.aspx?id=82&slug=attention-mechanism-principles\" hreflang=\"ca\" />\r\n<link rel=\"alternate\" href=\"https://ocr.oldfish.cn/da/Article.aspx?id=82&slug=attention-mechanism-principles\" hreflang=\"da\" />\r\n<link rel=\"alternate\" href=\"https://ocr.oldfish.cn/fi/Article.aspx?id=82&slug=attention-mechanism-principles\" hreflang=\"fi\" />\r\n<link rel=\"alternate\" href=\"https://ocr.oldfish.cn/nb/Article.aspx?id=82&slug=attention-mechanism-principles\" hreflang=\"nb\" />\r\n<link rel=\"alternate\" href=\"https://ocr.oldfish.cn/hr/Article.aspx?id=82&slug=attention-mechanism-principles\" hreflang=\"hr\" />\r\n<link rel=\"alternate\" href=\"https://ocr.oldfish.cn/sr-Cyrl/Article.aspx?id=82&slug=attention-mechanism-principles\" hreflang=\"sr-cyrl\" />\r\n<link rel=\"alternate\" href=\"https://ocr.oldfish.cn/sr-Latn/Article.aspx?id=82&slug=attention-mechanism-principles\" hreflang=\"sr-latn\" />\r\n<link rel=\"alternate\" href=\"https://ocr.oldfish.cn/sq/Article.aspx?id=82&slug=attention-mechanism-principles\" hreflang=\"sq\" />\r\n<link rel=\"alternate\" href=\"https://ocr.oldfish.cn/so/Article.aspx?id=82&slug=attention-mechanism-principles\" hreflang=\"so\" />\r\n<link rel=\"alternate\" href=\"https://ocr.oldfish.cn/zu/Article.aspx?id=82&slug=attention-mechanism-principles\" hreflang=\"zu\" />\r\n<link rel=\"alternate\" href=\"https://ocr.oldfish.cn/ka/Article.aspx?id=82&slug=attention-mechanism-principles\" hreflang=\"ka\" />\r\n<meta http-equiv=\"Content-Type\" content=\"text/html; charset=UTF-8\" /><meta http-equiv=\"X-UA-Compatible\" content=\"IE=Edge,chrome=1\" /><meta name=\"renderer\" content=\"webkit\" /><meta name=\"viewport\" content=\"width=device-width, initial-scale=1.0, maximum-scale=5.0\" /><link rel=\"icon\" href=\"/favicon.ico\" type=\"image/x-icon\" /><meta name=\"robots\" content=\"all\" /><link rel=\"preconnect\" href=\"https://scm-file-new-1301864755.cos.ap-beijing.myqcloud.com\" /><link rel=\"preconnect\" href=\"https://cdn.oldfish.cn\" /><link rel=\"dns-prefetch\" href=\"https://hm.baidu.com\" /><link rel=\"dns-prefetch\" href=\"https://cdn.oldfish.cn\" /><link rel=\"preload\" href=\"/site/css/site.css\" as=\"style\" />\r\n<script>function toArray(l){var a=[];for(var i=0;i<(l?l.length:0);i++)a.push(l[i]);return a;}function forEach(l,f){for(var i=0,n=(l?l.length:0);i<n;i++)f(l[i],i);}function hasClass(e,c){if(!e||!c)return false;return(' '+e.className+' ').indexOf(' '+c+' ')>-1;}function addClass(e,c){if(!e||!c||hasClass(e,c))return;e.className=(e.className?e.className+' ':'')+c;}function removeClass(e,c){if(!e||!c)return;var u=' '+e.className+' ';while(u.indexOf(' '+c+' ')>-1){u=u.replace(' '+c+' ',' ');}e.className=u.replace(/^\\s+|\\s+$/g,'');}function toggleClass(e,c){if(!e||!c)return;if(hasClass(e,c))removeClass(e,c);else addClass(e,c);}function matchesSelector(e,s){if(!e||e.nodeType!==1)return false;if(e.matches)return e.matches(s);var p=e.parentNode||document;var m=toArray(p.querySelectorAll(s));for(var i=0;i<m.length;i++)if(m[i]===e)return true;return false;}function closest(e,s){var n=e;while(n&&n.nodeType===1){if(matchesSelector(n,s))return n;n=n.parentNode;}return null;}function safeRemove(e){if(e&&e.parentNode)e.parentNode.removeChild(e);}function qa(s,r){return toArray((r||document).querySelectorAll(s));}function q(s,r){return(r||document).querySelector(s);}function xhrGet(u,o,e){try{var x=new XMLHttpRequest();x.open('GET',u,true);x.onreadystatechange=function(){if(x.readyState===4){if(x.status>=200&&x.status<300)o&&o(x.responseText);else e&&e(x);}};x.send(null);}catch(r){e&&e(r);}}function getScrollY(){return window.pageYOffset||document.documentElement.scrollTop||document.body.scrollTop||0;}function getOrigin(){return window.location.protocol+'//'+window.location.host;}</script>\r\n<script>var isWebP=(function(){try{return document.createElement('canvas').toDataURL('image/webp').indexOf('data:image/webp')===0;}catch(e){return false;}})();document.addEventListener('DOMContentLoaded',function(){var c=document.getElementById('user-login-container');if(c){var u=c.getAttribute('data-url');if(u){xhrGet(u,function(h){c.innerHTML=h;forEach(qa('a',c),function(l){l.style.display='inline-block';l.style.marginLeft='10px';});});}}});</script>\r\n    \n    \n\n    <meta name=\"description\" content=\"Merüljön el a figyelemmechanizmusok, a többfejű figyelem, az önfigyelő mechanizmusok és az OCR konkrét alkalmazásainak matematikai alapelveiben. A figyelemsúly számítások, a pozíciókódolás és a teljesítményoptimalizálási stratégiák részletes elemzése.\" />\n    <meta name=\"keywords\" content=\"Figyelemmechanizmus, többfejű figyelem, önfigyelem, pozíciókódolás, keresztfigyelem, ritka figyelem, OCR, transzformátor, OCR szövegfelismerés, kép-szöveg, OCR technológia\" />\n    <meta property=\"og:title\" content=\"【Mély tanulás OCR sorozat·5】 A figyelemmechanizmus elve és megvalósítása\" />\n    <meta property=\"og:description\" content=\"Merüljön el a figyelemmechanizmusok, a többfejű figyelem, az önfigyelő mechanizmusok és az OCR konkrét alkalmazásainak matematikai alapelveiben. A figyelemsúly számítások, a pozíciókódolás és a teljesítményoptimalizálási stratégiák részletes elemzése.\" />\n    <meta property=\"og:type\" content=\"article\" />\n    <meta property=\"og:url\" content=\"https://ocr.oldfish.cn/Article.aspx?id=82&slug=attention-mechanism-principles&lang=zh-Hans&_translation_source=true\" />\n    <meta property=\"og:site_name\" content=\"OCR szövegfelismerő asszisztens\" />\n    <meta name=\"twitter:card\" content=\"summary\" />\n    <meta name=\"twitter:title\" content=\"【Mély tanulás OCR sorozat·5】 A figyelemmechanizmus elve és megvalósítása\" />\n    <meta name=\"twitter:description\" content=\"Merüljön el a figyelemmechanizmusok, a többfejű figyelem, az önfigyelő mechanizmusok és az OCR konkrét alkalmazásainak matematikai alapelveiben. A figyelemsúly számítások, a pozíciókódolás és a teljesítményoptimalizálási stratégiák részletes elemzése.\" />\n\n    \n    <script type=\"application/ld+json\">\n    {\n        \"@context\": \"https://schema.org\",\n        \"@type\": \"Article\",\n        \"headline\": \"【Deep Learning OCR sorozat 5] A figyelemmechanizmus elve és megvalósítása\",\n        \"description\": \"Merüljön el a figyelemmechanizmusok, a többfejű figyelem, az önfigyelő mechanizmusok és az OCR konkrét alkalmazásainak matematikai alapelveiben. A figyelemsúly számítások, a pozíciókódolás és a teljesítményoptimalizálási stratégiák részletes elemzése。\",\n        \"publisher\": {\n            \"@type\": \"Organization\",\n            \"name\": \"OCR szövegfelismerő asszisztens csapat\"\n        },\n        \"datePublished\": \"2025-08-19T06:32:10Z\",\n        \"dateModified\": \"2025-08-19T06:32:10Z\",\n        \"mainEntityOfPage\": {\n            \"@type\": \"WebPage\",\n            \"@id\": \"https://ocr.oldfish.cn/Article.aspx?id=82&slug=attention-mechanism-principles&lang=zh-Hans&_translation_source=true\"\n        }\n    }\n    </script>\n    \n    <style>\n        .reading-progress { position: fixed; top: 0; left: 0; width: 0%; height: 3px; background: linear-gradient(90deg, #1764ff, #0d47a1); z-index: 9999; transition: width 0.2s ease; box-shadow: 0 1px 3px rgba(23, 100, 255, 0.3); }\n        .home-container.root { overflow: visible !important; }\n        .article-detail-container { background: #f5f7fc; min-height: calc(100vh - 60px); padding: 80px 0 60px; position: relative; overflow: visible; }\n        .article-detail-container .row { margin: 0 -12px; align-items: flex-start !important; min-height: 100vh !important; }\n        .article-detail-container .row > [class*=\"col-\"] { padding: 0 12px; box-sizing: border-box; min-width: 0; }\n        .article-detail-container .row > .col-lg-8, .article-detail-container .row > .col-xl-9 { overflow: hidden; }\n        .article-detail-container .row > .col-lg-4, .article-detail-container .row > .col-xl-3 { overflow: visible !important; height: 100% !important; min-height: 100% !important; position: static !important; display: block !important; transform: none !important; will-change: auto !important; contain: none !important; perspective: none !important; filter: none !important; }\n        .article-detail { background: #fff; border-radius: 8px; padding: 1.5rem; margin-bottom: 2rem; box-shadow: 0 2px 10px rgba(0,0,0,0.1); width: 100%; box-sizing: border-box; }\n        .article-title { font-size: 1.7rem; color: #1a1c1f; line-height: 1.3; margin-bottom: 1.5rem; font-weight: 600; }\n        .article-meta { display: flex; align-items: center; flex-wrap: nowrap; gap: 1.2rem; color: #6c757d; font-size: 0.88rem; margin-bottom: 2rem; padding: 1.2rem 1.4rem; background: linear-gradient(135deg, #f8f9fa 0%, #ffffff 100%); border-radius: 12px; border-left: 4px solid #1764ff; box-shadow: 0 2px 8px rgba(0,0,0,0.06); overflow-x: hidden; transition: all 0.3s ease; }\n        .article-meta:hover { box-shadow: 0 4px 16px rgba(0,0,0,0.1); transform: translateY(-1px); }\n        .article-meta .meta-item { display: flex; align-items: center; gap: 0.25rem; font-weight: 500; color: #495057; white-space: nowrap; flex-shrink: 1; transition: color 0.2s ease; cursor: default; }\n        .article-meta .meta-item:hover { color: #1764ff; }\n        .article-meta .meta-item:hover .meta-icon { transform: scale(1.1); filter: drop-shadow(0 2px 6px rgba(13, 71, 161, 0.25)); }\n        .meta-icon { display: inline-block; font-size: 16px; flex-shrink: 0; vertical-align: middle; transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1); margin-right: 4px; opacity: 0.9; }\n        .meta-item:hover .meta-icon { opacity: 1; }\n        .meta-item:hover { color: #1764ff; }\n        .meta-item { position: relative; }\n        .meta-item::after { content: ''; position: absolute; bottom: -2px; left: 0; width: 0; height: 2px; background: linear-gradient(90deg, #1764ff, #00d4ff); transition: width 0.3s ease; }\n        .meta-item:hover::after { width: 100%; }\n        .article-category, .read-time { display: inline-block; padding: 6px 12px; border-radius: 16px; font-size: 0.8rem; font-weight: 600; }\n        .article-category { background: linear-gradient(135deg, #1764ff, #0d47a1); color: white; text-decoration: none; text-transform: uppercase; letter-spacing: 0.5px; transition: all 0.3s ease; }\n        .article-category:hover { background: linear-gradient(135deg, #0d47a1, #1764ff); color: white; text-decoration: none; transform: translateY(-1px); box-shadow: 0 4px 12px rgba(23, 100, 255, 0.3); }\n        .read-time { background: #e3f2fd; color: #1565c0; border: 1px solid #90caf9; }\n        .article-tags { margin: 20px 0 1.5rem; }\n        .article-tags .tag { display: inline-block; background: #1764ff; color: #fff; padding: 4px 12px; border-radius: 12px; font-size: 0.8rem; margin: 0 8px 4px 0; }\n        .article-summary { background: #e3f2fd; border-left: 4px solid #1764ff; padding: 1rem 1.5rem; margin-bottom: 2rem; border-radius: 0 4px 4px 0; }\n        .article-content { line-height: 1.8; font-size: 16px; color: #333; word-wrap: break-word; }\n        .article-content h1, .article-content h2, .article-content h3, .article-content h4, .article-content h5, .article-content h6 { margin: 2rem 0 1rem; font-weight: 600; line-height: 1.25; color: #1a1c1f; }\n        .article-content h1 { font-size: 2rem; border-bottom: 2px solid #e9ecef; padding-bottom: 0.5rem; }\n        .article-content h2 { font-size: 1.5rem; border-bottom: 1px solid #e9ecef; padding-bottom: 0.3rem; }\n        .article-content h3 { font-size: 1.25rem; }\n        .article-content h4 { font-size: 1.1rem; }\n        .article-content h5, .article-content h6 { font-size: 1rem; }\n        .article-content ul, .article-content ol { margin: 1rem 0; padding-left: 2rem; }\n        .article-content ul ul, .article-content ol ol, .article-content ul ol, .article-content ol ul { margin: 0.5rem 0; }\n        .article-content blockquote { margin: 1rem 0; padding: 0.5rem 1rem; border-left: 4px solid #1764ff; background-color: #f8f9fa; color: #6c757d; font-style: italic; }\n        .article-content pre { background-color: #f8f9fa; border: 1px solid #e9ecef; border-radius: 6px; padding: 1rem; margin: 1rem 0; overflow-x: auto; font-family: 'Courier New', Courier, monospace; font-size: 0.9em; line-height: 1.4; }\n        .article-content pre code { background: none; border: none; padding: 0; color: inherit; }\n        .article-content p { margin: 1rem 0; }\n        .article-content p:first-child { margin-top: 0; }\n        .article-content p:last-child { margin-bottom: 0; }\n        .article-footer { margin-top: 4rem; padding: 2.5rem; background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%); border-radius: 12px; border: 1px solid #e9ecef; position: relative; overflow: hidden; }\n        .article-footer::before { content: ''; position: absolute; top: 0; left: 0; right: 0; height: 3px; background: linear-gradient(90deg, #1764ff, #17a2b8, #28a745); }\n        .article-share h6 { color: #495057; font-weight: 600; margin-bottom: 1.5rem; font-size: 16px; }\n        .share-buttons { display: flex; gap: 12px; flex-wrap: wrap; }\n        .share-buttons .btn { padding: 10px 20px; border-radius: 25px; font-weight: 500; font-size: 14px; border: none; transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1); box-shadow: 0 2px 8px rgba(0,0,0,0.1); display: inline-flex; align-items: center; gap: 8px; text-decoration: none; }\n        .btn-weibo { background: linear-gradient(135deg, #e6162d, #c41230); color: white; }\n        .btn-copy { background: #1764ff; color: white; }\n        .btn-print { background: linear-gradient(135deg, #17a2b8, #138496); color: white; }\n        .btn-weibo:hover { background: linear-gradient(135deg, #c41230, #a00e26); box-shadow: 0 4px 16px rgba(230, 22, 45, 0.3); }\n        .btn-copy:hover { background: #0d47a1; box-shadow: 0 4px 16px rgba(23, 100, 255, 0.3); }\n        .btn-print:hover { background: linear-gradient(135deg, #138496, #117a8b); box-shadow: 0 4px 16px rgba(23, 162, 184, 0.3); }\n        .btn-weibo:hover, .btn-copy:hover, .btn-print:hover { transform: translateY(-2px); color: white; }\n        .article-actions { display: flex; justify-content: flex-end; align-items: center; }\n        .related-articles { margin-top: 3rem; }\n        .related-article-item { background: #fff; border-radius: 8px; padding: 1.5rem; margin-bottom: 1rem; box-shadow: 0 2px 8px rgba(0,0,0,0.1); transition: transform 0.3s ease; }\n        .related-article-item:hover { transform: translateY(-2px); }\n        .sidebar-card { background: linear-gradient(135deg, rgba(255,255,255,0.9) 0%, rgba(255,255,255,0.8) 100%); backdrop-filter: blur(20px); border: 1px solid rgba(255,255,255,0.3); border-radius: 16px; padding: 1rem; margin-bottom: 0.5rem; box-shadow: 0 4px 20px rgba(0,0,0,0.08), inset 0 1px 0 rgba(255,255,255,0.2); overflow: visible; box-sizing: border-box; width: 100%; z-index: 10; }\n        .sidebar-card.fixed { box-shadow: 0 8px 32px rgba(0,0,0,0.12); }\n        .sidebar-card::before { content: ''; position: absolute; top: 0; left: 0; right: 0; height: 3px; background: linear-gradient(90deg, #00d4ff, #1764ff, #9c27b0); border-radius: 20px 20px 0 0; }\n        .sidebar-card h5 { color: #1a202c; padding-bottom: 0.8rem; border-bottom: 2px solid rgba(23, 100, 255, 0.2); font-weight: 700; font-size: 1.1rem; }\n        .sidebar { min-height: 100vh; position: relative !important; display: block !important; flex-direction: column !important; gap: 1rem; }\n        .toc-card { flex: 0 1 auto; max-height: 60vh; overflow-y: auto; }\n        .sidebar-card:not(.toc-card) { flex: 1 1 auto; overflow: visible; }\n        .recommended-item { padding: 0.75rem 0; border-bottom: 1px solid rgba(0,0,0,0.05); }\n        .recommended-item:last-child { border-bottom: none; }\n        .recommended-item h6 { margin: 0 0 0.5rem 0; font-size: 0.9rem; line-height: 1.4; }\n        .recommended-item h6 a { color: #1a1c1f; text-decoration: none; transition: color 0.3s ease; }\n        .recommended-item h6 a:hover { color: #1764ff; }\n        .recommended-item .text-muted { font-size: 0.75rem; margin: 0; }\n        .toc-list { list-style: none; padding: 0; margin: 0; max-height: none; overflow-y: visible; overflow-x: hidden; }\n        .toc-list::-webkit-scrollbar { width: 4px; }\n        .toc-list::-webkit-scrollbar-track { background: rgba(0,0,0,0.1); border-radius: 2px; }\n        .toc-list::-webkit-scrollbar-thumb { background: linear-gradient(135deg, #00d4ff, #1764ff); border-radius: 2px; }\n        .toc-list::-webkit-scrollbar-thumb:hover { background: linear-gradient(135deg, #1764ff, #0d47a1); }\n        .toc-list a { color: #4a5568; text-decoration: none; font-size: 13px; font-weight: 500; display: block; padding: 8px 12px; border-radius: 8px; transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1); position: relative; margin-bottom: 4px; border: 1px solid transparent; }\n        .toc-list a::before { content: ''; position: absolute; left: 0; top: 0; bottom: 0; width: 3px; background: linear-gradient(135deg, #00d4ff, #1764ff); border-radius: 0 12px 12px 0; transform: scaleY(0); transition: transform 0.3s ease; }\n        .toc-list a:hover { color: #1764ff; background: linear-gradient(135deg, rgba(0, 212, 255, 0.1), rgba(23, 100, 255, 0.1)); border-color: rgba(23, 100, 255, 0.2); transform: translateX(8px); box-shadow: 0 4px 12px rgba(0, 212, 255, 0.2); }\n        .toc-list a:hover::before, .toc-list a.active::before { transform: scaleY(1); }\n        .toc-list a.active { color: #1764ff; background: linear-gradient(135deg, rgba(0, 212, 255, 0.15), rgba(23, 100, 255, 0.15)); font-weight: 700; border-color: rgba(23, 100, 255, 0.3); transform: translateX(4px); box-shadow: 0 6px 16px rgba(0, 212, 255, 0.25); }\n        .toc-number { color: #1764ff; font-weight: 600; margin-right: 0.5rem; font-size: 0.9em; }\n        .toc-text { flex: 1; }\n        .toc-level-1 { font-weight: 700; font-size: 15px; }\n        .toc-level-2 { font-size: 14px; }\n        .toc-level-3 { font-size: 13px; opacity: 0.9; }\n        .toc-level-4, .toc-level-5, .toc-level-6 { font-size: 12px; opacity: 0.8; }\n        .article-content .formatted-list { margin: 1.5rem 0; padding-left: 0; list-style: none; }\n        .article-content .formatted-list.ordered { counter-reset: list-counter; }\n        .article-content .formatted-list.ordered li { counter-increment: list-counter; position: relative; margin: 1rem 0; padding: 1rem 1rem 1rem 3rem; background: linear-gradient(135deg, rgba(23, 100, 255, 0.03), rgba(0, 212, 255, 0.03)); border-left: 4px solid #1764ff; border-radius: 0 8px 8px 0; transition: all 0.3s ease; }\n        .article-content .formatted-list.ordered li::before { content: counter(list-counter); position: absolute; left: 0.8rem; top: 1rem; background: linear-gradient(135deg, #1764ff, #00d4ff); color: white; width: 1.5rem; height: 1.5rem; border-radius: 50%; display: flex; align-items: center; justify-content: center; font-weight: 600; font-size: 0.9rem; }\n        .article-content .formatted-list.ordered li:hover { background: linear-gradient(135deg, rgba(23, 100, 255, 0.06), rgba(0, 212, 255, 0.06)); transform: translateX(4px); box-shadow: 0 2px 8px rgba(23, 100, 255, 0.15); }\n        .article-content .formatted-list.unordered li { position: relative; margin: 0.8rem 0; padding: 0.8rem 1rem 0.8rem 2.5rem; background: linear-gradient(135deg, rgba(40, 167, 69, 0.03), rgba(25, 135, 84, 0.03)); border-left: 4px solid #28a745; border-radius: 0 6px 6px 0; transition: all 0.3s ease; }\n        .article-content .formatted-list.unordered li::before { content: \"•\"; position: absolute; left: 1rem; top: 0.8rem; color: #28a745; font-size: 1.2rem; font-weight: bold; }\n        .article-content .formatted-list.unordered li:hover { background: linear-gradient(135deg, rgba(40, 167, 69, 0.06), rgba(25, 135, 84, 0.06)); transform: translateX(4px); box-shadow: 0 2px 8px rgba(40, 167, 69, 0.15); }\n        .article-content .list-title { color: #1764ff; font-weight: 600; display: inline-block; margin-bottom: 0.3rem; }\n        .article-content .list-content { color: #2c3e50; line-height: 1.6; display: block; }\n        .article-content .inline-code { background: #f8f9fa; border: 1px solid #e9ecef; border-radius: 4px; padding: 2px 6px; font-family: 'Consolas', 'Monaco', 'Courier New', monospace; font-size: 0.9em; color: #e83e8c; }\n        .article-content .label-item { margin: 0.8rem 0; }\n        .article-content .label { color: #1764ff; font-weight: 600; }\n        .article-content .section-label { color: #2c3e50; font-weight: 600; margin: 1rem 0 0.5rem 0; font-size: 1.1em; border-left: 4px solid #1764ff; padding-left: 0.8rem; }\n        .article-content p, .article-content .content-paragraph { margin: 1.2rem 0; line-height: 1.8; text-align: justify; color: #2c3e50; font-size: 1rem; }\n        .article-content .content-paragraph { text-indent: 0; margin: 1rem 0; }\n        .article-content .indent-paragraph { text-indent: 2em; margin: 1.2rem 0; }\n        .article-content .text-indent-1 { text-indent: 2em; display: block; }\n        .article-content .text-indent-2 { text-indent: 4em; display: block; }\n        .article-content .text-indent-3 { text-indent: 6em; display: block; }\n        .article-content .network-config { background: linear-gradient(135deg, #f8f9fa, #e9ecef); border: 1px solid #dee2e6; border-radius: 6px; padding: 0.8rem 1rem; margin: 0.5rem 0; font-family: 'Consolas', 'Monaco', 'Courier New', monospace; font-size: 0.9rem; box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05); }\n        .article-content .config-label { color: #1764ff; font-weight: 600; }\n        .article-content .config-desc { color: #495057; }\n        .article-content .algorithm-step { background: linear-gradient(135deg, rgba(23, 100, 255, 0.05), rgba(0, 212, 255, 0.05)); border-left: 4px solid #1764ff; padding: 0.8rem 1rem; margin: 0.5rem 0; border-radius: 0 6px 6px 0; transition: all 0.3s ease; }\n        .article-content .step-number { color: #1764ff; font-weight: 700; margin-right: 0.5rem; }\n        .article-content .step-desc { color: #2c3e50; line-height: 1.5; }\n        .article-content table { width: 100%; border-collapse: collapse; margin: 1.5rem 0; background: white; border-radius: 8px; overflow: hidden; box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1); }\n        .article-content table th { background: linear-gradient(135deg, #1764ff, #00d4ff); color: white; padding: 1rem; text-align: left; font-weight: 600; }\n        .article-content table td { padding: 0.8rem 1rem; border-bottom: 1px solid #e9ecef; }\n        .article-content table tr:hover { background: rgba(23, 100, 255, 0.05); }\n        .article-content strong { color: #1764ff; font-weight: 600; }\n        .article-content em { color: #6c757d; font-style: italic; }\n        .article-content a { color: #1764ff; text-decoration: none; border-bottom: 1px solid transparent; transition: all 0.3s ease; }\n        .article-content a:hover { border-bottom-color: #1764ff; color: #0056b3; }\n        .article-content .definition-item { background: linear-gradient(135deg, rgba(40, 167, 69, 0.05), rgba(25, 135, 84, 0.05)); border-left: 4px solid #28a745; padding: 1rem; margin: 1rem 0; border-radius: 0 8px 8px 0; }\n        .article-content .definition-label { color: #28a745; font-weight: 600; display: block; margin-bottom: 0.5rem; }\n        .article-content .definition-content { color: #2c3e50; line-height: 1.6; }\n        .article-content .note-item { background: linear-gradient(135deg, rgba(255, 193, 7, 0.1), rgba(255, 193, 7, 0.05)); border: 1px solid rgba(255, 193, 7, 0.3); border-radius: 8px; padding: 1rem; margin: 1rem 0; position: relative; }\n        .article-content .note-item::before { content: \"💡\"; position: absolute; left: 1rem; top: 1rem; font-size: 1.2em; }\n        .article-content .note-label { color: #856404; font-weight: 600; margin-left: 2rem; }\n        .article-content .note-content { color: #2c3e50; line-height: 1.6; margin-left: 2rem; display: block; margin-top: 0.3rem; }\n        .article-content .math-formula { background: #f8f9fa; border: 1px solid #e9ecef; border-radius: 6px; padding: 1rem; margin: 1rem 0; text-align: center; font-family: 'Times New Roman', serif; font-size: 1.1em; color: #2c3e50; }\n        @media (min-width: 1400px) { .article-content { font-size: 17px; line-height: 1.8; } .article-detail { padding: 2.5rem; } }\n        @media (max-width: 1199px) and (min-width: 992px) { .article-detail { padding: 1.5rem; } .sidebar-card { padding: 1rem; } .toc-list a { font-size: 12px; padding: 6px 8px; } }\n        @media (max-width: 768px) {\n            .article-detail-container { padding: 60px 15px; }\n            .article-content .network-config, .article-content .algorithm-step, .article-content .definition-item, .article-content .note-item { margin: 0.5rem -1rem; border-radius: 0; }\n            .article-content .note-item::before { left: 0.5rem; }\n            .article-content .note-label, .article-content .note-content { margin-left: 1.5rem; }\n            .article-detail { padding: 1.5rem; }\n            .article-title { font-size: 1.6rem; line-height: 1.3; }\n            .article-meta { flex-wrap: wrap; gap: 1rem; padding: 1rem; font-size: 0.8rem; }\n            .article-meta .meta-item { flex-shrink: 1; }\n            .meta-icon { font-size: 14px; margin-right: 3px; }\n            .article-content { font-size: 15px; }\n            .article-content h1 { font-size: 1.6rem; }\n            .article-content h2 { font-size: 1.4rem; }\n            .article-content h3 { font-size: 1.2rem; }\n            .article-content h4 { font-size: 1.1rem; }\n            .article-content pre { padding: 0.75rem; font-size: 0.85em; overflow-x: auto; }\n            .article-content code { font-size: 0.85em; padding: 1px 4px; }\n            .sidebar { margin-top: 2rem; }\n            .sidebar-card { position: static; }\n            .toc-card { max-height: none; overflow-y: visible; }\n            .share-buttons { flex-direction: column; gap: 0.5rem; }\n            .share-buttons .btn { width: 100%; justify-content: center; }\n        }\n        @media print { body * { visibility: hidden; } .article-detail, .article-detail * { visibility: visible; } .article-detail { position: absolute; left: 0; top: 0; width: 100%; background: white; box-shadow: none; border: none; margin: 0; padding: 20px; } .article-meta { border: none; background: none; padding: 0; margin-bottom: 20px; } .article-footer, .reading-progress, .back-to-top { display: none !important; } .article-content { font-size: 12pt; line-height: 1.6; } .article-content h1, .article-content h2, .article-content h3, .article-content h4, .article-content h5, .article-content h6 { page-break-after: avoid; margin-top: 20pt; margin-bottom: 10pt; } .article-content pre, .article-content blockquote { page-break-inside: avoid; } }\n    </style>\n\n<script type=\"application/ld+json\">{\"@context\":\"https://schema.org\",\"@type\":\"BreadcrumbList\",\"itemListElement\":[{\"@type\":\"ListItem\",\"position\":1,\"name\":\"Otthon\",\"item\":\"https://ocr.oldfish.cn/default.aspx\"},{\"@type\":\"ListItem\",\"position\":2,\"name\":\"Műszaki cikkek\",\"item\":\"https://ocr.oldfish.cn/Articles.aspx\"},{\"@type\":\"ListItem\",\"position\":3,\"name\":\"Cikk részletei\",\"item\":\"https://ocr.oldfish.cn/Article.aspx?id=82&slug=attention-mechanism-principles&lang=zh-Hans&_translation_source=true\"}]}</script>\n<link rel=\"stylesheet\" href=\"/site/css/site.css\" type=\"text/css\" /><title>【Mély tanulás OCR sorozat·5】 A figyelemmechanizmus elve és megvalósítása</title><meta http-equiv=\"Content-Language\" content=\"hu\" />\n</head>\r\n<body>\r\n    <div class=\"home-container root\">\r\n        <header class=\"v4_header_pc\">\r\n            <div class=\"header_pc_left\">\r\n                <a href=\"Default.aspx\" title=\"Kezdőlap | AI intelligens szövegfelismerés\" class=\"pc-logo-wrap ml-4 mr-5\">\r\n                    <picture>\r\n                        <source srcset=\"site/image/logo-s.png.webp\" type=\"image/webp\" />\r\n                        <img src=\"site/image/logo-s.png\" class=\"pc-logo\" style=\"height: 100%\" alt=\"OCR Text Recognition Assistant hivatalos webhely logója – AI intelligens szövegfelismerő platform\" />\r\n                    </picture>\r\n                    <span style=\"font-size: 18px; font-weight: bold; color: #333;\">OCR szövegfelismerő asszisztens</span>\r\n                </a>\r\n                <ul class=\"top-nav\" role=\"navigation\" aria-label=\"Fő navigáció\">\r\n                    <li><a class=\"color-default\" href=\"Default.aspx\" title=\"OCR szövegfelismerő asszisztens honlapja\">Otthon</a></li>\r\n                    <li><a class=\"color-default\" data-sub=\"product-sub-nav\" href=\"Detail.aspx\" title=\"OCR termékfunkció bemutatása\">A termék jellemzői:</a><div class=\"triangle\"></div>\r\n                    </li>\r\n                    <li><a class=\"color-default\" data-sub=\"experience-sub-nav\" href=\"Ocr.aspx\" title=\"Tapasztalja meg az OCR funkciókat online\">Online élmény</a><div class=\"triangle\"></div>\r\n                    </li>\r\n                    <li><a class=\"color-default\" data-sub=\"vip-sub-nav\" href=\"Version.aspx\" title=\"OCR-tagság frissítési szolgáltatás\">Tagság felminősítése</a><div class=\"triangle\"></div>\r\n                    </li>\r\n                    <li><a class=\"color-default\" href=\"Download.aspx\" title=\"Töltse le ingyen az OCR szövegfelismerő asszisztenst\">Ingyenes letöltés</a></li>\r\n                    <li><a class=\"color-default\" data-sub=\"articles-sub-nav\" href=\"Articles.aspx\" title=\"OCR műszaki cikkek és tudásmegosztás\">Technológia megosztása</a><div class=\"triangle\"></div>\r\n                    </li>\r\n                    <li><a class=\"color-default\" data-sub=\"support-sub-nav\" href=\"FAQ.aspx\" title=\"OCR használati segítség és technikai támogatás\">Súgó</a><div class=\"triangle\"></div>\r\n                    </li>\r\n                </ul>\r\n                <div class=\"product-con\">\r\n                    <section id=\"product-sub-nav\" class=\"pls-nav-dropdown\" style=\"visibility: hidden; height: 0px; display: none;\">\r\n                        <div class=\"d-flex nav-drown-con\" style=\"height: 0px; transition: transform 0.2s ease-in-out 0s,opacity 0.2s ease-in-out 0s; opacity: 0; transform: translate(0px, -100%);\">\r\n                            <div class=\"prdocu-sub-left\">\r\n                                <div class=\"mr-auto\">\r\n                                    <picture>\r\n                                        <source class=\"p-icon\" srcset=\"site/image/icon/p_1.png.webp\" type=\"image/webp\" alt=\"\" aria-hidden=\"true\" />\r\n                                        <img class=\"p-icon\" src=\"site/image/icon/p_1.png\" alt=\"OCR termék funkció ikon\" aria-hidden=\"true\" loading=\"lazy\" />\r\n                                    </picture>\r\n                                    <div class=\"mt-4\">\r\n                                        <h3 class=\"h6\">OCR szövegfelismerő asszisztens</h3>\r\n                                        <p class=\"color-gray fn14 mt-3 sub-left-desc\">Javítsa a hatékonyságot, csökkentse a költségeket és teremtsen értéket</p>\r\n                                        <p class=\"color-gray fn14 mt-3 sub-left-desc\">&nbsp;</p>\r\n                                        <p class=\"color-gray fn14 mt-3 sub-left-desc\">Intelligens felismerés, nagy sebességű feldolgozás és pontos kimenet</p>\r\n                                        <p class=\"color-gray fn14 mt-3 sub-left-desc\">A szövegtől a táblázatokig, a képletektől a fordításokig</p>\r\n                                        <p class=\"color-gray fn14 mt-3 sub-left-desc\">Tegyen minden szövegszerkesztést olyan egyszerűvé</p>\r\n                                        <a class=\"link block disa fn14 contact-business shake\" href=\"Detail.aspx\">Tudjon meg többet a funkciókról<span class=\"iconfont4 icon-xiangzuo gt\"></span></a>\r\n                                    </div>\r\n                                </div>\r\n                            </div>\r\n                            <div class=\"bg-white prdocu-sub-right\">\r\n                                <div>\r\n                                    <h2 class=\"text-base-color h6\">A termék jellemzői:</h2>\r\n                                    <div class=\"mt-4 d-flex flex-wrap\">\r\n                                        <a href=\"Detail.aspx\" class=\"pro-item color-default\" title=\"Tekintse meg az OCR Assistant alapvető funkcióinak részleteit\">\r\n                                            <div class=\"letter-wrap\">\r\n                                                <h3 class=\"h6\">Alapvető jellemzők:</h3>\r\n                                                <span class=\"color-gray fn14\">Tudjon meg többet az OCR Assistant alapvető funkcióiról és technikai előnyeiről, 98%+ felismerési aránnyal</span>\r\n                                            </div>\r\n                                        </a>\r\n                                        <a href=\"Version.aspx\" class=\"pro-item color-default\" title=\"Az OCR Assistant verziói közötti különbségek összehasonlítása\">\r\n                                            <div class=\"letter-wrap\">\r\n                                                <h3 class=\"h6\">Verzió összehasonlítás</h3>\r\n                                                <span class=\"color-gray fn14\">Hasonlítsa össze részletesen az ingyenes verzió, a személyes verzió, a professzionális verzió és a végső verzió funkcionális különbségeit</span>\r\n                                            </div>\r\n                                        </a>\r\n                                        <a href=\"FAQ.aspx\" class=\"pro-item color-default\" title=\"Tekintse meg az OCR Assistant GYIK-et\">\r\n                                            <div class=\"letter-wrap\">\r\n                                                <h3 class=\"h6\">Termékkérdések és válaszok</h3>\r\n                                                <span class=\"color-gray fn14\">Gyorsan megismerheti a termék jellemzőit, használati módjait és részletes válaszokat a gyakran ismételt kérdésekre</span>\r\n                                            </div>\r\n                                        </a>\r\n                                        <a href=\"Download.aspx\" class=\"pro-item color-default\" title=\"Töltse le ingyen az OCR szövegfelismerő asszisztenst\">\r\n                                            <div class=\"letter-wrap\">\r\n                                                <h3 class=\"h6\">Próbálja ki ingyen</h3>\r\n                                                <span class=\"color-gray fn14\">Töltse le és telepítse az OCR Assistant alkalmazást most, hogy ingyenesen megtapasztalhassa a hatékony szövegfelismerő funkciót</span>\r\n                                            </div>\r\n                                        </a>\r\n                                        <div style=\"width: 100%; margin: 15px 0;\">\r\n                                            <h4 class=\"h6\" style=\"color: #007cfa; margin-bottom: 10px;\">Online OCR-felismerés</h4>\r\n                                        </div>\r\n                                        <a href=\"Ocr.aspx?type=text_recognize\" class=\"pro-item color-default\" title=\"Tapasztalja meg az univerzális szövegfelismerést online\">\r\n                                            <div class=\"letter-wrap\">\r\n                                                <h3 class=\"h6\">Univerzális karakterfelismerés</h3>\r\n                                                <span class=\"color-gray fn14\">Többnyelvű, nagy pontosságú szöveg intelligens kinyerése, amely támogatja a nyomtatott és több jelenetből álló összetett képfelismerést</span>\r\n                                            </div>\r\n                                        </a>\r\n                                        <a href=\"Ocr.aspx?type=table\" class=\"pro-item color-default\">\r\n                                            <div class=\"letter-wrap\">\r\n                                                <h3 class=\"h6\">Univerzális asztalazonosítás</h3>\r\n                                                <span class=\"color-gray fn14\">Táblázatképek intelligens konvertálása Excel fájlokká, összetett táblázatszerkezetek és egyesített cellák automatikus feldolgozása</span>\r\n                                            </div>\r\n                                        </a>\r\n                                        <a href=\"Ocr.aspx?type=handwritten_ocr\" class=\"pro-item color-default\">\r\n                                            <div class=\"letter-wrap\">\r\n                                                <h3 class=\"h6\">Kézírás-felismerés</h3>\r\n                                                <span class=\"color-gray fn14\">A kínai és angol kézzel írt tartalmak intelligens felismerése, az osztálytermi jegyzetek, az orvosi feljegyzések és egyéb forgatókönyvek támogatása</span>\r\n                                            </div>\r\n                                        </a>\r\n                                        <a href=\"Ocr.aspx?type=pdf2word\" class=\"pro-item color-default\">\r\n                                            <div class=\"letter-wrap\">\r\n                                                <h3 class=\"h6\">PDF-ből Word-be</h3>\r\n                                                <span class=\"color-gray fn14\">A PDF-dokumentumok gyorsan Word formátumba konvertálhatók, tökéletesen megőrizve az eredeti elrendezést és a grafikus elrendezést</span>\r\n                                            </div>\r\n                                        </a>\r\n                                    </div>\r\n                                </div>\r\n                            </div>\r\n                        </div>\r\n                    </section>\r\n                    <section id=\"experience-sub-nav\" class=\"pls-nav-dropdown solution-part\" style=\"visibility: hidden; height: 0px; display: none;\">\r\n                        <div class=\"d-flex nav-drown-con\" style=\"height: 0px; transition: transform 0.2s ease-in-out 0s,opacity 0.2s ease-in-out 0s; opacity: 0; transform: translate(0px, -100%);\">\r\n                            <div class=\"prdocu-sub-left\">\r\n                                <div class=\"mr-auto\">\r\n                                    <picture>\r\n                                        <source srcset=\"site/image/icon/p_4.png.webp\" type=\"image/webp\" />\r\n                                        <img class=\"p-icon\" src=\"site/image/icon/p_4.png\" alt=\"Online OCR Experience Center ikon\" aria-hidden=\"true\" loading=\"lazy\" />\r\n                                    </picture>\r\n                                    <div class=\"mt-4\">\r\n                                        <h3 class=\"h6\">OCR szövegfelismerő asszisztens</h3>\r\n                                        <p class=\"color-gray fn14 mt-3 sub-left-desc\">Szövegek, táblázatok, képletek, dokumentumok, fordítások</p>\r\n                                        <p class=\"color-gray fn14 mt-3 sub-left-desc\">&nbsp;</p>\r\n                                        <p class=\"color-gray fn14 mt-3 sub-left-desc\">Teljesítse az összes szövegszerkesztési igényét három lépésben</p>\r\n                                        <p class=\"color-gray fn14 mt-3 sub-left-desc\">Képernyőkép → Azonosítsa → alkalmazást</p>\r\n                                        <p class=\"color-gray fn14 mt-3 sub-left-desc\">Növelje a munka hatékonyságát 300%-kal</p>\r\n                                        <a class=\"link block disa fn14 contact-business shake\" href=\"Ocr.aspx\">Próbálja ki most<span class=\"iconfont4 icon-xiangzuo gt\"></span></a>\r\n                                    </div>\r\n                                </div>\r\n                            </div>\r\n                            <div class=\"bg-white prdocu-sub-right\">\r\n                                <div>\r\n                                    <div class=\"mt-4 d-flex flex-wrap\">\r\n                                        <div style=\"width: 100%; margin-bottom: 15px;\">\r\n                                            <h4 class=\"h6\" style=\"color: #007cfa; margin-bottom: 10px;\">OCR funkció tapasztalat</h4>\r\n                                        </div>\r\n                                        <a href=\"Ocr.aspx?type=index\" class=\"pro-item color-default\">\r\n                                            <div class=\"letter-wrap\">\r\n                                                <h3 class=\"h6\">Teljes funkcionalitás</h3>\r\n                                                <span class=\"color-gray fn14\">Tapasztalja meg az összes OCR intelligens funkciót egy helyen, hogy gyorsan megtalálja az igényeinek leginkább megfelelő megoldást</span>\r\n                                            </div>\r\n                                        </a>\r\n                                        <a href=\"Ocr.aspx?type=text_recognize\" class=\"pro-item color-default\">\r\n                                            <div class=\"letter-wrap\">\r\n                                                <h3 class=\"h6\">Univerzális karakterfelismerés</h3>\r\n                                                <span class=\"color-gray fn14\">Többnyelvű, nagy pontosságú szöveg intelligens kinyerése, amely támogatja a nyomtatott és több jelenetből álló összetett képfelismerést</span>\r\n                                            </div>\r\n                                        </a>\r\n                                        <a href=\"Ocr.aspx?type=table\" class=\"pro-item color-default\">\r\n                                            <div class=\"letter-wrap\">\r\n                                                <h3 class=\"h6\">Univerzális asztalazonosítás</h3>\r\n                                                <span class=\"color-gray fn14\">Táblázatképek intelligens konvertálása Excel fájlokká, összetett táblázatszerkezetek és egyesített cellák automatikus feldolgozása</span>\r\n                                            </div>\r\n                                        </a>\r\n                                        <a href=\"Ocr.aspx?type=handwritten_ocr\" class=\"pro-item color-default\">\r\n                                            <div class=\"letter-wrap\">\r\n                                                <h3 class=\"h6\">Kézírás-felismerés</h3>\r\n                                                <span class=\"color-gray fn14\">A kínai és angol kézzel írt tartalmak intelligens felismerése, az osztálytermi jegyzetek, az orvosi feljegyzések és egyéb forgatókönyvek támogatása</span>\r\n                                            </div>\r\n                                        </a>\r\n                                        <a href=\"Ocr.aspx?type=pdf2word\" class=\"pro-item color-default\">\r\n                                            <div class=\"letter-wrap\">\r\n                                                <h3 class=\"h6\">PDF-ből Word-be</h3>\r\n                                                <span class=\"color-gray fn14\">A PDF-dokumentumok gyorsan Word formátumba konvertálhatók, tökéletesen megőrizve az eredeti elrendezést és a grafikus elrendezést</span>\r\n                                            </div>\r\n                                        </a>\r\n                                        <a href=\"Ocr.aspx?type=pdf2markdown\" class=\"pro-item color-default\">\r\n                                            <div class=\"letter-wrap\">\r\n                                                <h3 class=\"h6\">PDF-ből Markdownba</h3>\r\n                                                <span class=\"color-gray fn14\">A PDF-dokumentumok intelligensen MD formátumba konvertálódnak, a kódblokkok és a szövegszerkezetek pedig automatikusan optimalizálódnak a feldolgozáshoz</span>\r\n                                            </div>\r\n                                        </a>\r\n                                        <div style=\"width: 100%; margin: 15px 0;\">\r\n                                            <h4 class=\"h6\" style=\"color: #007cfa; margin-bottom: 10px;\">Dokumentumfeldolgozó eszközök</h4>\r\n                                        </div>\r\n                                        <a href=\"Ocr.aspx?type=word2pdf\" class=\"pro-item color-default\">\r\n                                            <div class=\"letter-wrap\">\r\n                                                <h3 class=\"h6\">Word-ből PDF-be</h3>\r\n                                                <span class=\"color-gray fn14\">A Word-dokumentumok egyetlen kattintással PDF-be konvertálódnak, tökéletesen megtartva az eredeti formátumot, amely alkalmas archiválásra és hivatalos dokumentummegosztásra</span>\r\n                                            </div>\r\n                                        </a>\r\n                                        <a href=\"Ocr.aspx?type=word2jpg\" class=\"pro-item color-default\">\r\n                                            <div class=\"letter-wrap\">\r\n                                                <h3 class=\"h6\">Szóból képpé</h3>\r\n                                                <span class=\"color-gray fn14\">Word dokumentum intelligens konvertálása JPG képpé, támogatja a többoldalas feldolgozást, könnyen megosztható a közösségi médiában</span>\r\n                                            </div>\r\n                                        </a>\r\n                                        <a href=\"Ocr.aspx?type=pdf2jpg\" class=\"pro-item color-default\">\r\n                                            <div class=\"letter-wrap\">\r\n                                                <h3 class=\"h6\">PDF-ből képbe</h3>\r\n                                                <span class=\"color-gray fn14\">Konvertálja a PDF dokumentumokat JPG képekké nagy felbontásban, támogatja a kötegelt feldolgozást és az egyedi felbontást</span>\r\n                                            </div>\r\n                                        </a>\r\n                                        <a href=\"Ocr.aspx?type=image2pdf\" class=\"pro-item color-default\">\r\n                                            <div class=\"letter-wrap\">\r\n                                                <h3 class=\"h6\">Kép PDF-be</h3>\r\n                                                <span class=\"color-gray fn14\">Több kép egyesítése PDF-dokumentumokba, rendezés és oldalbeállítás támogatása</span>\r\n                                            </div>\r\n                                        </a>\r\n                                        <div style=\"width: 100%; margin: 15px 0;\">\r\n                                            <h4 class=\"h6\" style=\"color: #007cfa; margin-bottom: 10px;\">Fejlesztői eszközök</h4>\r\n                                        </div>\r\n                                        <a href=\"Tool.aspx?type=Json\" class=\"pro-item color-default\">\r\n                                            <div class=\"letter-wrap\">\r\n                                                <h3 class=\"h6\">JSON formázás</h3>\r\n                                                <span class=\"color-gray fn14\">Intelligensen szépíti a JSON-kód szerkezetét, támogatja a tömörítést és a bővítést, valamint megkönnyíti a fejlesztést és a hibakeresést</span>\r\n                                            </div>\r\n                                        </a>\r\n                                        <a href=\"Tool.aspx?type=Regex\" class=\"pro-item color-default\">\r\n                                            <div class=\"letter-wrap\">\r\n                                                <h3 class=\"h6\">reguláris kifejezés</h3>\r\n                                                <span class=\"color-gray fn14\">Ellenőrizze a reguláris kifejezések egyezési effektusait valós időben a gyakori minták beépített könyvtárával</span>\r\n                                            </div>\r\n                                        </a>\r\n                                        <a href=\"Tool.aspx?type=Encode\" class=\"pro-item color-default\">\r\n                                            <div class=\"letter-wrap\">\r\n                                                <h3 class=\"h6\">Szövegkódolás átalakítása</h3>\r\n                                                <span class=\"color-gray fn14\">Támogatja több kódolási formátum, például a Base64, az URL és a Unicode konvertálását</span>\r\n                                            </div>\r\n                                        </a>\r\n                                        <a href=\"Tool.aspx?type=Diff\" class=\"pro-item color-default\">\r\n                                            <div class=\"letter-wrap\">\r\n                                                <h3 class=\"h6\">Szövegegyeztetés és egyesítés</h3>\r\n                                                <span class=\"color-gray fn14\">Szövegkülönbségek kiemelése, soronkénti összehasonlítás és intelligens egyesítés támogatása</span>\r\n                                            </div>\r\n                                        </a>\r\n                                        <a href=\"Tool.aspx?type=Color\" class=\"pro-item color-default\">\r\n                                            <div class=\"letter-wrap\">\r\n                                                <h3 class=\"h6\">Szín eszköz</h3>\r\n                                                <span class=\"color-gray fn14\">RGB/HEX színkonverzió, online színválasztó, a front-end fejlesztés elengedhetetlen eszköze</span>\r\n                                            </div>\r\n                                        </a>\r\n                                        <a href=\"Tool.aspx?type=Count\" class=\"pro-item color-default\">\r\n                                            <div class=\"letter-wrap\">\r\n                                                <h3 class=\"h6\">Szavak száma</h3>\r\n                                                <span class=\"color-gray fn14\">A karakterek, a szókincs és a bekezdések intelligens számlálása, valamint a szövegelrendezés automatikus optimalizálása</span>\r\n                                            </div>\r\n                                        </a>\r\n                                        <a href=\"Tool.aspx?type=Timespan\" class=\"pro-item color-default\">\r\n                                            <div class=\"letter-wrap\">\r\n                                                <h3 class=\"h6\">Időbélyeg konverzió</h3>\r\n                                                <span class=\"color-gray fn14\">Az idő Unix-időbélyegzővé és -ből konvertálódik, és többféle formátum és időzóna-beállítás támogatott</span>\r\n                                            </div>\r\n                                        </a>\r\n                                        <a href=\"Tool.aspx?type=Calc\" class=\"pro-item color-default\">\r\n                                            <div class=\"letter-wrap\">\r\n                                                <h3 class=\"h6\">Számológép eszköz</h3>\r\n                                                <span class=\"color-gray fn14\">Online tudományos számológép az alapvető műveletek és a fejlett matematikai függvényszámítások támogatásával</span>\r\n                                            </div>\r\n                                        </a>\r\n                                    </div>\r\n                                </div>\r\n                            </div>\r\n                        </div>\r\n                    </section>\r\n                    <section id=\"articles-sub-nav\" class=\"pls-nav-dropdown solution-part\" style=\"visibility: hidden; height: 0px; display: none;\">\r\n                        <div class=\"d-flex nav-drown-con\" style=\"height: 0px; transition: transform 0.2s ease-in-out 0s,opacity 0.2s ease-in-out 0s; opacity: 0; transform: translate(0px, -100%);\">\r\n                            <div class=\"prdocu-sub-left\">\r\n                                <div class=\"mr-auto\">\r\n                                    <picture>\r\n                                        <source srcset=\"site/image/icon/p_4.png.webp\" type=\"image/webp\" />\r\n                                        <img class=\"p-icon\" src=\"site/image/icon/p_4.png\" alt=\"A Tech Sharing Center ikonja\" aria-hidden=\"true\" loading=\"lazy\" />\r\n                                    </picture>\r\n                                    <div class=\"mt-4\">\r\n                                        <h3 class=\"h6\">OCR technológia megosztása</h3>\r\n                                        <p class=\"color-gray fn14 mt-3 sub-left-desc\">Műszaki oktatóanyagok, alkalmazási esetek, eszközajánlások</p>\r\n                                        <p class=\"color-gray fn14 mt-3 sub-left-desc\">&nbsp;</p>\r\n                                        <p class=\"color-gray fn14 mt-3 sub-left-desc\">Teljes tanulási út a kezdőtől a mesterig</p>\r\n                                        <p class=\"color-gray fn14 mt-3 sub-left-desc\">Gyakorlati esetek → technikai elemzés → szerszámalkalmazások</p>\r\n                                        <p class=\"color-gray fn14 mt-3 sub-left-desc\">Tegye lehetővé az OCR-technológia fejlesztéséhez vezető utat</p>\r\n                                        <a class=\"link block disa fn14 contact-business shake\" href=\"Articles.aspx\">Böngészhet a cikkek között<span class=\"iconfont4 icon-xiangzuo gt\"></span></a>\r\n                                    </div>\r\n                                </div>\r\n                            </div>\r\n                            <div class=\"bg-white prdocu-sub-right\">\r\n                                <div>\r\n                                    <h2 class=\"text-base-color h6\">Technológia megosztása</h2>\r\n                                    <div class=\"mt-4 d-flex flex-wrap\">\r\n                                        <a href=\"Articles.aspx\" class=\"pro-item color-default\" title=\"Az összes OCR műszaki cikk megtekintése\">\r\n                                            <div class=\"letter-wrap\">\r\n                                                <h3 class=\"h6\">Összes cikk</h3>\r\n                                                <span class=\"color-gray fn14\">Böngésszen az összes OCR műszaki cikk között, amelyek az alapoktól a haladókig teljes tudást lefednek</span>\r\n                                            </div>\r\n                                        </a>\r\n                                        \r\n                                        <a href=\"Articles.aspx?category=2\" class=\"pro-item color-default\" title=\"OCR technikai oktatóanyagok és első lépések útmutatói\">\r\n                                            <div class=\"letter-wrap\">\r\n                                                <h3 class=\"h6\">Haladó útmutató</h3>\r\n                                                <span class=\"color-gray fn14\">A bevezetőtől a gyakorlott OCR technikai oktatóanyagokig, részletes útmutatókig és gyakorlati útmutatókig</span>\r\n                                            </div>\r\n                                        </a>\r\n                                        \r\n                                        <a href=\"Articles.aspx?category=3\" class=\"pro-item color-default\" title=\"OCR technológiai elvek, algoritmusok és alkalmazások\">\r\n                                            <div class=\"letter-wrap\">\r\n                                                <h3 class=\"h6\">Technológiai feltárás</h3>\r\n                                                <span class=\"color-gray fn14\">Fedezze fel az OCR-technológia határait az alapelvektől az alkalmazásokig, és elemezze mélyrehatóan az alapvető algoritmusokat</span>\r\n                                            </div>\r\n                                        </a>\r\n                                        \r\n                                        <a href=\"Articles.aspx?category=4\" class=\"pro-item color-default\" title=\"Az OCR iparág legújabb fejleményei és fejlődési trendjei\">\r\n                                            <div class=\"letter-wrap\">\r\n                                                <h3 class=\"h6\">Iparági trendek</h3>\r\n                                                <span class=\"color-gray fn14\">Mélyreható betekintés az OCR technológia fejlesztési trendjeibe, a piacelemzésbe, az iparági dinamikába és a jövőbeli kilátásokba</span>\r\n                                            </div>\r\n                                        </a>\r\n                                        \r\n                                        <a href=\"Articles.aspx?category=5\" class=\"pro-item color-default\" title=\"Az OCR technológia alkalmazási esetei különböző iparágakban\">\r\n                                            <div class=\"letter-wrap\">\r\n                                                <h3 class=\"h6\">Felhasználási esetek:</h3>\r\n                                                <span class=\"color-gray fn14\">Megosztják az OCR-technológia valós alkalmazási eseteit, megoldásait és bevált gyakorlatait a különböző iparágakban</span>\r\n                                            </div>\r\n                                        </a>\r\n                                        \r\n                                        <a href=\"Articles.aspx?category=6\" class=\"pro-item color-default\" title=\"Szakmai áttekintések, összehasonlító elemzések és ajánlott irányelvek az OCR szoftvereszközök használatához\">\r\n                                            <div class=\"letter-wrap\">\r\n                                                <h3 class=\"h6\">Eszköz áttekintése</h3>\r\n                                                <span class=\"color-gray fn14\">Értékelje ki a különféle OCR szövegfelismerő szoftvereket és eszközöket, és adjon részletes funkció-összehasonlítási és kiválasztási javaslatokat</span>\r\n                                            </div>\r\n                                        </a>\r\n                                        \r\n                                    </div>\r\n                                </div>\r\n                            </div>\r\n                        </div>\r\n                    </section>\r\n                    <section id=\"vip-sub-nav\" class=\"pls-nav-dropdown solution-part\" style=\"visibility: hidden; height: 0px; display: none;\">\r\n                        <div class=\"d-flex nav-drown-con\" style=\"height: 0px; transition: transform 0.2s ease-in-out 0s,opacity 0.2s ease-in-out 0s; opacity: 0; transform: translate(0px, -100%);\">\r\n                            <div class=\"prdocu-sub-left\">\r\n                                <div class=\"mr-auto\">\r\n                                    <picture>\r\n                                        <source class=\"p-icon\" srcset=\"site/image/icon/p_1.png.webp\" type=\"image/webp\" alt=\"\" aria-hidden=\"true\" />\r\n                                        <img class=\"p-icon\" src=\"site/image/icon/p_1.png\" alt=\"A tagság bővítési szolgáltatásának ikonja\" aria-hidden=\"true\" loading=\"lazy\" />\r\n                                    </picture>\r\n                                    <div class=\"mt-4\">\r\n                                        <h3 class=\"h6\">Tagság bővítési szolgáltatás</h3>\r\n                                        <p class=\"color-gray fn14 mt-3 sub-left-desc\">Oldja fel az összes prémium funkciót, és élvezze az exkluzív szolgáltatásokat</p>\r\n                                        <p class=\"color-gray fn14 mt-3 sub-left-desc\">&nbsp;</p>\r\n                                        <p class=\"color-gray fn14 mt-3 sub-left-desc\">Offline felismerés, kötegelt feldolgozás, korlátlan használat</p>\r\n                                        <p class=\"color-gray fn14 mt-3 sub-left-desc\">Pro → Ultimate → Enterprise</p>\r\n                                        <p class=\"color-gray fn14 mt-3 sub-left-desc\">Van valami, ami megfelel az igényeidnek</p>\r\n                                        <a class=\"link block disa fn14 contact-business shake\" href=\"Version.aspx\">Részletek megtekintése<span class=\"iconfont4 icon-xiangzuo gt\"></span></a>\r\n                                    </div>\r\n                                </div>\r\n                            </div>\r\n                            <div class=\"bg-white prdocu-sub-right\">\r\n                                <div>\r\n                                    <h2 class=\"text-base-color h6\" style=\"margin: inherit;\">Tagság felminősítése</h2>\r\n                                    <div class=\"mt-4 d-flex flex-wrap\">\r\n                                        <a href=\"Version.aspx\" class=\"pro-item color-default\">\r\n                                            <div class=\"letter-wrap\">\r\n                                                <h3 class=\"h6\">Tagsági jogosultságok</h3>\r\n                                                <span class=\"color-gray fn14\">Tudjon meg többet a kiadások közötti különbségekről, és válassza ki az Önnek legmegfelelőbb tagsági szintet</span>\r\n                                            </div>\r\n                                        </a>\r\n                                        <a href=\"Upgrade.aspx\" class=\"pro-item color-default\">\r\n                                            <div class=\"letter-wrap\">\r\n                                                <h3 class=\"h6\">Frissítsen most</h3>\r\n                                                <span class=\"color-gray fn14\">Gyorsan frissítse VIP tagságát, hogy további prémium funkciókat és exkluzív szolgáltatásokat érjen el</span>\r\n                                            </div>\r\n                                        </a>\r\n                                        <a href=\"User.aspx\" class=\"pro-item color-default\">\r\n                                            <div class=\"letter-wrap\">\r\n                                                <h3 class=\"h6\">Az én fiókom</h3>\r\n                                                <span class=\"color-gray fn14\">Fiókadatok, előfizetési állapot és használati előzmények kezelése a beállítások személyre szabásához</span>\r\n                                            </div>\r\n                                        </a>\r\n                                    </div>\r\n                                </div>\r\n                            </div>\r\n                        </div>\r\n                    </section>\r\n                    <section id=\"support-sub-nav\" class=\"pls-nav-dropdown solution-part\" style=\"visibility: hidden; height: 0px; display: none;\">\r\n                        <div class=\"d-flex nav-drown-con\" style=\"height: 0px; transition: transform 0.2s ease-in-out 0s,opacity 0.2s ease-in-out 0s; opacity: 0; transform: translate(0px, -100%);\">\r\n                            <div class=\"prdocu-sub-left\">\r\n                                <div class=\"mr-auto\">\r\n                                    <picture>\r\n                                        <source class=\"p-icon\" srcset=\"site/image/icon/p_4.png.webp\" type=\"image/webp\" alt=\"\" aria-hidden=\"true\" />\r\n                                        <img class=\"p-icon\" src=\"site/image/icon/p_4.png\" alt=\"A Súgóközpont támogatási ikonja\" aria-hidden=\"true\" loading=\"lazy\" />\r\n                                    </picture>\r\n                                    <div class=\"mt-4\">\r\n                                        <h3 class=\"h6\">Súgó</h3>\r\n                                        <p class=\"color-gray fn14 mt-3 sub-left-desc\">Professzionális ügyfélszolgálat, részletes dokumentáció és gyors reagálás</p>\r\n                                        <p class=\"color-gray fn14 mt-3 sub-left-desc\">&nbsp;</p>\r\n                                        <p class=\"color-gray fn14 mt-3 sub-left-desc\">Ne essen pánikba, ha problémákkal találkozik</p>\r\n                                        <p class=\"color-gray fn14 mt-3 sub-left-desc\">Probléma → Találja meg → megoldva</p>\r\n                                        <p class=\"color-gray fn14 mt-3 sub-left-desc\">Tegye gördülékenyebbé az élményt</p>\r\n                                        <a class=\"link block disa fn14 contact-business shake\" href=\"FAQ.aspx\">Segítség kérése<span class=\"iconfont4 icon-xiangzuo gt\"></span></a>\r\n                                    </div>\r\n                                </div>\r\n                            </div>\r\n                            <div class=\"bg-white prdocu-sub-right\">\r\n                                <div>\r\n                                    <h2 class=\"text-base-color h6\" style=\"margin: inherit;\">Súgó</h2>\r\n                                    <div class=\"mt-4 d-flex flex-wrap\">\r\n                                        <a href=\"FAQ.aspx\" class=\"pro-item color-default\">\r\n                                            <div class=\"letter-wrap\">\r\n                                                <h3 class=\"h6\">Gyakran ismételt kérdések</h3>\r\n                                                <span class=\"color-gray fn14\">Gyorsan válaszolhat a gyakori felhasználói kérdésekre, és részletes használati útmutatókat és technikai támogatást nyújthat</span>\r\n                                            </div>\r\n                                        </a>\r\n                                        <a href=\"AboutUs.aspx\" class=\"pro-item color-default\">\r\n                                            <div class=\"letter-wrap\">\r\n                                                <h3 class=\"h6\">Rólunk</h3>\r\n                                                <span class=\"color-gray fn14\">Ismerje meg az OCR szövegfelismerő asszisztens fejlesztési előzményeit, alapvető funkcióit és szolgáltatási koncepcióit</span>\r\n                                            </div>\r\n                                        </a>\r\n                                        <a href=\"UserAgreement.aspx\" class=\"pro-item color-default\">\r\n                                            <div class=\"letter-wrap\">\r\n                                                <h3 class=\"h6\">Felhasználói megállapodás</h3>\r\n                                                <span class=\"color-gray fn14\">Részletes szolgáltatási feltételek, valamint felhasználói jogok és kötelezettségek</span>\r\n                                            </div>\r\n                                        </a>\r\n                                        <a href=\"PrivacyPolicy.aspx\" class=\"pro-item color-default\">\r\n                                            <div class=\"letter-wrap\">\r\n                                                <h3 class=\"h6\">Adatvédelmi megállapodás</h3>\r\n                                                <span class=\"color-gray fn14\">A személyes adatok védelmére vonatkozó szabályzat és az adatbiztonsági intézkedések</span>\r\n                                            </div>\r\n                                        </a>\r\n                                        <a href=\"Status.aspx\" class=\"pro-item color-default\">\r\n                                            <div class=\"letter-wrap\">\r\n                                                <h3 class=\"h6\">A rendszer állapota</h3>\r\n                                                <span class=\"color-gray fn14\">Valós időben figyelje a globális azonosító csomópontok működési állapotát, és tekintse meg a rendszer teljesítményadatait</span>\r\n                                            </div>\r\n                                        </a>\r\n                                        <a href=\"javascript:void(0);\" class=\"pro-item color-default\" onclick=\"if(window.openCustomerService) window.openCustomerService(); else alert('Kérjük, kattintson a jobb oldalon található lebegő ablak ikonra, hogy kapcsolatba lépjen az ügyfélszolgálattal');\">\r\n                                            <div class=\"letter-wrap\">\r\n                                                <h3 class=\"h6\">Kapcsolatfelvétel az ügyfélszolgálattal</h3>\r\n                                                <span class=\"color-gray fn14\">Online ügyfélszolgálati támogatás, hogy gyorsan válaszolhasson kérdéseire és igényeire</span>\r\n                                            </div>\r\n                                        </a>\r\n                                    </div>\r\n                                </div>\r\n                            </div>\r\n                        </div>\r\n                    </section>\r\n                </div>\r\n            </div>\r\n            <div class=\"header_pc_right\" style=\"display: flex; align-items: center; justify-content: flex-end;\">\r\n                <div id=\"translate\"></div>\r\n                <div id=\"user-login-container\" data-url=\"User.ashx?op=userinfo\" style=\"white-space: nowrap; padding-right: 20px;\"></div>\r\n            </div>\r\n        </header>\r\n        <header class=\"v4_header_mob\">\r\n            <div class=\"container\">\r\n                <div class=\"row no-gutters justify-content-between align-items-center\">\r\n                    <a href=\"Default.aspx\" title=\"Kezdőlap | AI intelligens szövegfelismerés\" class=\"mob-logo-wrap\">\r\n                        <picture>\r\n                            <source srcset=\"site/image/logo-s.png.webp\" type=\"image/webp\" />\r\n                            <img src=\"site/image/logo-s.png\" class=\"pc-logo\" style=\"height: 32px; width: 32px\" alt=\"OCR szövegfelismerő asszisztens mobil logó\" />\r\n                        </picture>\r\n                        <span style=\"font-size: 18px; font-weight: bold; color: #333;\">OCR szövegfelismerő asszisztens</span>\r\n                    </a>\r\n                    <div class=\"right-menu\" role=\"button\" tabindex=\"0\" aria-label=\"A navigációs menü megnyitása\" data-toggle=\"collapse\" data-target=\"#mob-header-collapse\" aria-expanded=\"false\" aria-controls=\"mob-header-collapse\">\r\n                        <nav-products id=\"nav_products\">\r\n                            <nav-button class=\"submail-mdi-nav-btn\" style=\"width: 60px\">\r\n                                <label id=\"mdi-submail-nav-btn\">\r\n                                    <span class=\"mdi-nav-products-1\">\r\n                                        <span class=\"nav-products-icon\"></span>\r\n                                    </span>\r\n                                    <span class=\"mdi-nav-products-2\">\r\n                                        <span class=\"nav-products-icon\"></span>\r\n                                    </span>\r\n                                    <span class=\"mdi-nav-products-3\">\r\n                                        <span class=\"nav-products-icon\"></span>\r\n                                    </span>\r\n                                </label>\r\n                            </nav-button>\r\n                        </nav-products>\r\n                    </div>\r\n                </div>\r\n            </div>\r\n            <div class=\"mob-nav-content\">\r\n                <div class=\"sidebar-fix\">\r\n                    <div class=\"container\">\r\n                        <div class=\"row\">\r\n                            <div class=\"col-4 bg-white sidebar-fix-left\" style=\"border-right: 1px solid #e1e6ed\">\r\n                                <div class=\"mob-nav-item active\" data=\"0\">\r\n                                    <div class=\"nav-header\"><a href=\"Default.aspx\" class=\"nav-header-letter px-3\"><span>Otthon</span></a></div>\r\n                                </div>\r\n                                <div class=\"mob-nav-item\" data=\"1\">\r\n                                    <div class=\"nav-header\"><a href=\"Detail.aspx\" class=\"nav-header-letter px-3\"><span>funkció</span></a></div>\r\n                                </div>\r\n                                <div class=\"mob-nav-item\" data=\"2\">\r\n                                    <div class=\"nav-header\"><a href=\"Ocr.aspx\" class=\"nav-header-letter px-3\"><span>tapasztalat</span></a></div>\r\n                                </div>\r\n                                <div class=\"mob-nav-item\" data=\"3\">\r\n                                    <div class=\"nav-header\"><a href=\"Version.aspx\" class=\"nav-header-letter px-3\"><span>tag</span></a></div>\r\n                                </div>\r\n                                <div class=\"mob-nav-item\" data=\"4\">\r\n                                    <div class=\"nav-header\"><a href=\"Download.aspx\" class=\"nav-header-letter px-3\"><span>Letöltés</span></a></div>\r\n                                </div>\r\n                                <div class=\"mob-nav-item\" data=\"5\">\r\n                                    <div class=\"nav-header\"><a href=\"Articles.aspx\" class=\"nav-header-letter px-3\"><span>részvény</span></a></div>\r\n                                </div>\r\n                                <div class=\"mob-nav-item\" data=\"6\">\r\n                                    <div class=\"nav-header\"><a href=\"FAQ.aspx\" class=\"nav-header-letter px-3\"><span>Segítség</span></a></div>\r\n                                </div>\r\n                            </div>\r\n                            <div class=\"col-8 bg-white sidebar-fix-rigth\">\r\n                                <div class=\"sub-nav\">\r\n                                    <div class=\"sub-nav-item py-2 pb-3 px-4\">\r\n                                        <h2 class=\"h5\" style=\"margin: inherit;\">Hatékony termelékenységi eszközök</h2>\r\n                                        <p class=\"color-gray fn14 mt-3 sub-left-desc\">Intelligens felismerés, nagy sebességű feldolgozás és pontos kimenet</p>\r\n                                        <p class=\"color-gray fn14 mt-3 sub-left-desc\">&nbsp;</p>\r\n                                        <p class=\"color-gray fn14 mt-3 sub-left-desc\">Teljes oldalnyi dokumentum felismerése 3 másodperc alatt</p>\r\n                                        <p class=\"color-gray fn14 mt-3 sub-left-desc\">98%+ felismerési pontosság</p>\r\n                                        <p class=\"color-gray fn14 mt-3 sub-left-desc\">Többnyelvű valós idejű feldolgozás késedelem nélkül</p>\r\n                                        <a class=\"link block disa fn14 contact-business shake\" href=\"Download.aspx\">Töltse le az élményt most<span class=\"iconfont4 icon-xiangzuo gt\"></span></a>\r\n                                    </div>\r\n                                </div>\r\n                                <div class=\"sub-nav\">\r\n                                    <div class=\"sub-nav-item py-3 px-4\">\r\n                                        <h2 class=\"h5\" style=\"margin: inherit;\">A termék jellemzői:</h2>\r\n                                        <p class=\"color-gray fn14 mt-3 sub-left-desc\">AI intelligens azonosítás, egyablakos megoldás</p>\r\n                                    </div>\r\n                                    <div class=\"sub-nav-item py-3 px-4\"><a href=\"Detail.aspx\">Funkció bemutatása</a></div>\r\n                                    <div class=\"sub-nav-item py-3 px-4\"><a href=\"Download.aspx\">Szoftver letöltése</a></div>\r\n                                    <div class=\"sub-nav-item py-3 px-4\"><a href=\"Version.aspx\">Verzió összehasonlítás</a></div>\r\n                                    <div class=\"sub-nav-item py-3 px-4\"><a href=\"Ocr.aspx\">Online élmény</a></div>\r\n                                    <div class=\"sub-nav-item py-3 px-4\"><a href=\"Status.aspx\">A rendszer állapota</a></div>\r\n                                </div>\r\n                                <div class=\"sub-nav\">\r\n                                    <div class=\"sub-nav-item py-3 px-4\">\r\n                                        <h2 class=\"h5\" style=\"margin: inherit;\">Online élmény</h2>\r\n                                        <p class=\"color-gray fn14 mt-3 sub-left-desc\">Ingyenes online OCR funkcióélmény</p>\r\n                                    </div>\r\n                                    <div class=\"sub-nav-item py-3 px-4\"><a href=\"Ocr.aspx\">Teljes funkcionalitás</a></div>\r\n                                    <div class=\"sub-nav-item py-3 px-4\"><a href=\"Ocr.aspx?type=text_recognize\">Szófelismerés</a></div>\r\n                                    <div class=\"sub-nav-item py-3 px-4\"><a href=\"Ocr.aspx?type=table\">Tábla azonosítása</a></div>\r\n                                    <div class=\"sub-nav-item py-3 px-4\"><a href=\"Ocr.aspx?type=pdf2word\">PDF-ből Word-be</a></div>\r\n                                </div>\r\n                                <div class=\"sub-nav\">\r\n                                    <div class=\"sub-nav-item py-3 px-4\">\r\n                                        <h2 class=\"h5\" style=\"margin: inherit;\">Tagság felminősítése</h2>\r\n                                        <p class=\"color-gray fn14 mt-3 sub-left-desc\">Oldja fel az összes funkciót, és élvezze az exkluzív szolgáltatásokat</p>\r\n                                    </div>\r\n                                    <div class=\"sub-nav-item py-3 px-4\"><a href=\"Version.aspx\">Tagsági előnyök</a></div>\r\n                                    <div class=\"sub-nav-item py-3 px-4\"><a href=\"Upgrade.aspx\">Azonnali aktiválás</a></div>\r\n                                </div>\r\n                                <div class=\"sub-nav\">\r\n                                    <div class=\"sub-nav-item py-3 px-4\">\r\n                                        <h2 class=\"h5\" style=\"margin: inherit;\">Szoftver letöltése</h2>\r\n                                        <p class=\"color-gray fn14 mt-3 sub-left-desc\">Töltse le ingyen a professzionális OCR szoftvert</p>\r\n                                    </div>\r\n                                    <div class=\"sub-nav-item py-3 px-4\"><a href=\"Download.aspx\">Töltse le most</a></div>\r\n                                    <div class=\"sub-nav-item py-3 px-4\"><a href=\"Version.aspx\">Verzió összehasonlítás</a></div>\r\n                                </div>\r\n                                <div class=\"sub-nav\">\r\n                                    <div class=\"sub-nav-item py-3 px-4\">\r\n                                        <h2 class=\"h5\" style=\"margin: inherit;\">Technológia megosztása</h2>\r\n                                        <p class=\"color-gray fn14 mt-3 sub-left-desc\">OCR műszaki cikkek és tudásmegosztás</p>\r\n                                    </div>\r\n                                    <div class=\"sub-nav-item py-3 px-4\"><a href=\"Articles.aspx\">Összes cikk</a></div>\r\n                                    \r\n                                    <div class=\"sub-nav-item py-3 px-4\"><a href=\"Articles.aspx?category=2\">Haladó útmutató</a></div>\r\n                                    \r\n                                    <div class=\"sub-nav-item py-3 px-4\"><a href=\"Articles.aspx?category=3\">Technológiai feltárás</a></div>\r\n                                    \r\n                                    <div class=\"sub-nav-item py-3 px-4\"><a href=\"Articles.aspx?category=4\">Iparági trendek</a></div>\r\n                                    \r\n                                    <div class=\"sub-nav-item py-3 px-4\"><a href=\"Articles.aspx?category=5\">Felhasználási esetek:</a></div>\r\n                                    \r\n                                    <div class=\"sub-nav-item py-3 px-4\"><a href=\"Articles.aspx?category=6\">Eszköz áttekintése</a></div>\r\n                                    \r\n                                </div>\r\n                                <div class=\"sub-nav\">\r\n                                    <div class=\"sub-nav-item py-3 px-4\">\r\n                                        <h2 class=\"h5\" style=\"margin: inherit;\">Súgó</h2>\r\n                                        <p class=\"color-gray fn14 mt-3 sub-left-desc\">Professzionális ügyfélszolgálat, intim kiszolgálás</p>\r\n                                    </div>\r\n                                    <div class=\"sub-nav-item py-3 px-4\"><a href=\"FAQ.aspx\">Súgó használata</a></div>\r\n                                    <div class=\"sub-nav-item py-3 px-4\"><a href=\"AboutUs.aspx\">Rólunk</a></div>\r\n                                    <div class=\"sub-nav-item py-3 px-4\"><a href=\"mailto:<EMAIL>\">Kapcsolatfelvétel az ügyfélszolgálattal</a></div>\r\n                                    <div class=\"sub-nav-item py-3 px-4\"><a href=\"UserAgreement.aspx\">Általános Szerződési Feltételek</a></div>\r\n                                </div>\r\n                            </div>\r\n                        </div>\r\n                    </div>\r\n                </div>\r\n            </div>\r\n        </header>\r\n        <form method=\"post\" action=\"./Article.aspx?id=82&amp;slug=attention-mechanism-principles&amp;lang=zh-Hans&amp;_translation_source=true\" id=\"aspnetForm\">\r\n<div class=\"aspNetHidden\">\r\n<input type=\"hidden\" name=\"__VIEWSTATE\" id=\"__VIEWSTATE\" value=\"BqrhS+P8MW5VC3DOBfGwj6vOm3+QcH2E0p5DXnNjlyGuctPQR14YJZeSsWbjw86X57yDpdIFIGGGE04n8DnmgHdYirvyFNNsxJ81r0FsIZo=\" />\r\n</div>\r\n\r\n<div class=\"aspNetHidden\">\r\n\r\n\t<input type=\"hidden\" name=\"__VIEWSTATEGENERATOR\" id=\"__VIEWSTATEGENERATOR\" value=\"2173C2F0\" />\r\n</div>\r\n            <div>\r\n                \n\n<div class=\"reading-progress\" id=\"readingProgress\"></div>\n<div class=\"article-detail-container\">\n<div class=\"container\">\n<div class=\"row\">\n<div class=\"col-lg-8 col-xl-9 col-md-12\">\n                    \n                    <article class=\"article-detail\" id=\"articleDetail\" data-article-id=\"82\">\n                        <header class=\"article-header\">\n                            <h1 class=\"article-title\">【Mély tanulás OCR sorozat·5】 A figyelemmechanizmus elve és megvalósítása</h1>\n                            <div class=\"article-meta\">\n                                <div class=\"meta-item\">\n                                    <span class=\"meta-icon\">📅</span>\n                                    <span>Feladás időpontja: 2025-08-19</span>\n                                </div>\n                                <div class=\"meta-item\">\n                                    <span class=\"meta-icon\">👁️</span>\n                                    <span>Olvasás:<span class=\"view-count\">1250</span></span>\n                                </div>\n                                <div class=\"meta-item\">\n                                    <span class=\"meta-icon\">⏱️</span>\n                                    <span>Kb. 58 perc (11464 szó)</span>\n                                </div>\n                                \n                                <div class=\"meta-item\">\n                                    <span class=\"meta-icon\">📁</span>\n                                    <span>Kategória: Haladó útmutatók</span>\n                                </div>\n                                \n                            </div>\n                            <div class=\"article-summary\"><p>Merüljön el a figyelemmechanizmusok, a többfejű figyelem, az önfigyelő mechanizmusok és az OCR konkrét alkalmazásainak matematikai alapelveiben. A figyelemsúly számítások, a pozíciókódolás és a teljesítményoptimalizálási stratégiák részletes elemzése.</p></div>\n                        </header>\n                        <div class=\"article-content\" id=\"articleContent\">## Bevezetés\r\n\r\nA figyelemmechanizmus fontos újítás a mély tanulás területén, amely szimulálja a szelektív figyelmet az emberi kognitív folyamatokban. Az OCR-feladatokban a figyelemmechanizmus segíthet a modellnek dinamikusan fókuszálni a kép fontos területeire, jelentősen javítva a szövegfelismerés pontosságát és hatékonyságát. Ez a cikk elmélyül az OCR figyelemmechanizmusainak elméleti alapjaival, matematikai alapelveivel, megvalósítási módszereivel és konkrét alkalmazásaival, átfogó technikai megértést és gyakorlati útmutatást nyújtva az olvasóknak.\r\n\r\n## A figyelemmechanizmusok biológiai következményei\r\n\r\n### Emberi vizuális figyelmi rendszer\r\n\r\nAz emberi vizuális rendszer erős képességgel rendelkezik a szelektív figyelem figyelésére, ami lehetővé teszi számunkra, hogy hatékonyan nyerjünk ki hasznos információkat összetett vizuális környezetben. Amikor elolvasunk egy szöveget, a szem automatikusan az éppen felismert karakterre fókuszál, a környező információk mérsékelt elnyomásával.\r\n\r\n**Az emberi figyelem jellemzői**:\r\n- Szelektivitás: Képes kiválasztani a fontos szakaszokat nagy mennyiségű információból\r\n- Dinamikus: A figyelem dinamikusan igazodik a feladat igényei alapján\r\n- Hierarchikusság: A figyelem az absztrakció különböző szintjein osztható el\r\n- Párhuzamosság: Egyszerre több kapcsolódó régióra lehet összpontosítani\r\n- Kontextusérzékenység: A figyelem elosztását a kontextuális információk befolyásolják\r\n\r\n**A vizuális figyelem idegi mechanizmusai**:\r\nAz idegtudományi kutatásokban a vizuális figyelem több agyi régió összehangolt munkáját foglalja magában:\r\n- Parietális kéreg: felelős a térbeli figyelem szabályozásáért\r\n- Prefrontális kéreg: felelős a célorientált figyelemszabályozásért\r\n- Vizuális kéreg: Felelős a jellemzők észleléséért és ábrázolásáért\r\n- Thalamus: közvetítőállomásként szolgál a figyelem információihoz\r\n\r\n### Számítási modell követelményei\r\n\r\nA hagyományos neurális hálózatok általában az összes bemeneti információt rögzített hosszúságú vektorba tömörítik a szekvenciaadatok feldolgozásakor. Ennek a megközelítésnek nyilvánvaló információs szűk keresztmetszetei vannak, különösen hosszú sorozatok esetén, ahol a korai információkat könnyen felülírják a későbbi információk.\r\n\r\n**A hagyományos módszerek korlátai**:\r\n- Információs szűk keresztmetszetek: A rögzített hosszúságú kódolt vektorok nehezen tudnak minden fontos információt tárolni\r\n- Távolsági függőségek: A bemeneti sorrendben egymástól távol eső elemek közötti kapcsolatok modellezésének nehézsége\r\n- Számítási hatékonyság: A teljes szekvenciát fel kell dolgozni a végeredmény eléréséhez\r\n- Megmagyarázhatóság: Nehézségek a modell döntéshozatali folyamatának megértésében\r\n- Rugalmasság: Nem tudja dinamikusan beállítani az információfeldolgozási stratégiákat a feladatigények alapján\r\n\r\n**Megoldások a figyelemmechanizmusokra**:\r\nA figyelemmechanizmus lehetővé teszi a modell számára, hogy szelektíven összpontosítson a bemenet különböző részeire, miközben az egyes kimeneteket feldolgozza, dinamikus súlyelosztási mechanizmus bevezetésével:\r\n- Dinamikus kiválasztás: Dinamikusan válassza ki a releváns információkat az aktuális feladatkövetelmények alapján\r\n- Globális hozzáférés: Közvetlen hozzáférés a bemeneti sorozat bármely helyéhez\r\n- Párhuzamos számítástechnika: Támogatja a párhuzamos feldolgozást a számítási hatékonyság javítása érdekében\r\n- Magyarázhatóság: A figyelemsúlyok vizuálisan magyarázzák a modell döntéseit\r\n\r\n## A figyelemmechanizmusok matematikai alapelvei\r\n\r\n### Alapvető figyelem modell\r\n\r\nA figyelemmechanizmus alapötlete az, hogy a bemeneti sorozat minden eleméhez súlyt rendeljen, amely tükrözi, hogy az adott elem mennyire fontos az adott feladat szempontjából.\r\n\r\n**Matematikai ábrázolás**:\r\nAz X = {x₁, x₂, ..., xn} bemeneti sorozat és a q lekérdezési vektor alapján a figyelemmechanizmus kiszámítja az egyes bemeneti elemek figyelemsúlyát:\r\n\r\nα_i = f(q, x_i) # Figyelem pontszám függvény\r\nα̃_i = softmax(α_i) = exp(α_i) / Σj exp(αj) # Normalizált súly\r\n\r\nA végső kontextusvektort súlyozott összegzéssel kapjuk meg:\r\nc = Σi α̃_i · x_i\r\n\r\n**A figyelemmechanizmusok összetevői**:\r\n1. Lekérdezés: Jelzi azokat az információkat, amelyekre jelenleg figyelni kell\r\n2. Kulcs: A figyelem súlyának kiszámításához használt referenciainformációk\r\n3. Érték: A súlyozott összegben ténylegesen részt vevő információ\r\n4. **Figyelem funkció**: Olyan függvény, amely kiszámítja a lekérdezések és a billentyűk közötti hasonlóságot\r\n\r\n### A figyelempontszám funkció részletes magyarázata\r\n\r\nA figyelempontszám függvény határozza meg, hogyan számítja ki a rendszer a lekérdezés és a bemenet közötti korrelációt. A különböző pontozási funkciók különböző alkalmazási forgatókönyvekhez alkalmasak.\r\n\r\n**1. Pont-termék figyelem**:\r\nα_i = q^T · x_i\r\n\r\nEz a legegyszerűbb figyelemmechanizmus, és számítási szempontból hatékony, de a lekérdezéseknek és a bemeneteknek azonos méretűek kell.\r\n\r\n**Érdem**:\r\n- Egyszerű számítások és nagy hatékonyság\r\n- Kis számú paraméter és nincs szükség további tanulható paraméterekre\r\n- Hatékonyan különbözteti meg a hasonló és eltérő vektorokat a nagy dimenziós térben\r\n\r\n**Hiány**:\r\n- A lekérdezések és kulcsok azonos dimenzióinak megkövetelése\r\n- Numerikus instabilitás fordulhat elő a nagy dimenziós térben\r\n- A tanulási képesség hiánya a komplex hasonlósági kapcsolatokhoz való alkalmazkodáshoz\r\n\r\n**2. Méretezett pont-termék figyelem**:\r\nα_i = (q^T · x_i) / √d\r\n\r\nahol d a vektor mérete. A méretezési tényező megakadályozza a nagy pontszorzat által okozott gradiens eltűnési problémát a nagy dimenziós térben.\r\n\r\n**A méretezés szükségessége**:\r\nHa a d méret nagy, a pontszorzat varianciája megnő, aminek következtében a softmax függvény belép a telítettségi tartományba, és a színátmenet kicsi lesz. A √d-vel való osztással a pontszorzat varianciája stabilan tartható.\r\n\r\n**Matematikai levezetés**:\r\nFeltételezve, hogy a q és k elemek független valószínűségi változók, 0 átlaggal és 1 varianciával, akkor:\r\n- q^T · A k varianciája d\r\n- A (q^T · k) / √d varianciája 1\r\n\r\n**3. Additív figyelem**:\r\nα_i = v^T · tanh(W_q · q + W_x · x_i)\r\n\r\nA lekérdezések és a bemenetek ugyanarra a térre vannak leképezve egy megtanulható paramétermátrixon keresztül W_q és W_x, majd kiszámítja a hasonlóságot.\r\n\r\n**Előnyelemzés**:\r\n- Rugalmasság: Különböző dimenziókban képes kezelni a lekérdezéseket és a kulcsokat\r\n- Tanulási képességek: Alkalmazkodás a tanulható paraméterekkel rendelkező összetett hasonlósági kapcsolatokhoz\r\n- Kifejezési képességek: A nemlineáris átalakítások továbbfejlesztett kifejezési képességeket biztosítanak\r\n\r\n**Paraméterelemzés**:\r\n- W_q ∈ R^{d_h×d_q}: A vetítési mátrix lekérdezése\r\n- W_x ∈ R^{d_h×d_x}: Kulcsvetítési mátrix\r\n- v ∈ R^{d_h}: Figyelem súlyvektor\r\n- d_h: Rejtett rétegméretek\r\n\r\n**4. MLP figyelem**:\r\nα_i = MLP([q; x_i])\r\n\r\nA többrétegű perceptronok használatával közvetlenül megtanulhatja a lekérdezések és a bemenetek közötti korrelációs függvényeket.\r\n\r\n**Hálózati struktúra**:\r\nAz MLP-k általában 2-3 teljesen összekapcsolt réteget tartalmaznak:\r\n- Bemeneti réteg: lekérdezések és kulcsvektorok illesztése\r\n- Rejtett réteg: Funkciók aktiválása a ReLU vagy a tanh segítségével\r\n- Kimeneti réteg: Skaláris figyelempontszámokat ad ki\r\n\r\n**Előnyök és hátrányok elemzése**:\r\nÉrdem:\r\n- A legerősebb kifejezőkészség\r\n- Komplex nemlineáris kapcsolatok tanulhatók meg\r\n- Nincs korlátozás a bemeneti méretekre vonatkozóan\r\n\r\nHiány:\r\n- Nagyszámú paraméter és könnyű túlillesztés\r\n- Magas számítási összetettség\r\n- Hosszú edzésidő\r\n\r\n### Több fej figyelem mechanizmusa\r\n\r\nA Multi-Head Attention a transzformátor architektúra egyik központi eleme, amely lehetővé teszi a modellek számára, hogy párhuzamosan figyeljenek a különböző típusú információkra a különböző reprezentációs alterekben.\r\n\r\n**Matematikai meghatározás**:\r\nMultiHead(Q, K, V) = Concat(fej₁, fej₂, ..., fej) · W^O\r\n\r\nahol az egyes figyelemfejek meghatározása a következő:\r\nheadi = Figyelem(Q· W_i^Q, K· W_i^K, V·W_i^V)\r\n\r\n**Paraméter mátrix**:\r\n- W_i^Q ∈ R^{d_model×d_k}: Az i-edik fejléc lekérdezési vetítési mátrixa\r\n- W_i^K ∈ R^{d_model×d_k}: az i-edik fejléc kulcsvetítési mátrixa\r\n- W_i^V ∈ R^{d_model×d_v}: Értékvetületi mátrix az i-edik fejhez\r\n- W^O ∈ R^{h·d_v×d_model}: Kimeneti vetítési mátrix\r\n\r\n**A Bull Attention előnyei**:\r\n1. **Sokszínűség**: A különböző fejek különböző típusú tulajdonságokra összpontosíthatnak\r\n2. **Párhuzamosság**: Több fej is kiszámítható párhuzamosan, javítva a hatékonyságot\r\n3. **Kifejezési képesség**: Javítottuk a modell reprezentációs tanulási képességét\r\n4. **Stabilitás**: Több fej integrációs hatása stabilabb\r\n5. **Specializáció**: Minden vezető szakosodhat bizonyos típusú kapcsolatokra\r\n\r\n**Megfontolások a fej kiválasztásához**:\r\n- Túl kevés fej: Lehet, hogy nem ragad meg elegendő információs sokszínűséget\r\n- Túlzott létszám: Növeli a számítási összetettséget, ami túlillesztéshez vezethet\r\n- Általános opciók: 8 vagy 16 fej, a modell méretének és a feladat összetettségének megfelelően beállítva\r\n\r\n**Dimenzióelosztási stratégia**:\r\nÁltalában d_k = d_v = d_model / h beállítással kell biztosítani, hogy a paraméterek teljes mennyisége ésszerű legyen:\r\n- A teljes számítási mennyiség viszonylag stabil tartása\r\n- Minden fej elegendő reprezentációs kapacitással rendelkezik\r\n- Kerülje el a túl kis méretek okozta információvesztést\r\n\r\n## Önfigyelmi mechanizmus\r\n\r\n### Az önfigyelem fogalma\r\n\r\nAz önfigyelem a figyelemmechanizmus egy speciális formája, amelyben a lekérdezések, kulcsok és értékek mind ugyanabból a bemeneti sorrendből származnak. Ez a mechanizmus lehetővé teszi, hogy a sorozat minden eleme a sorozat összes többi elemére összpontosítson.\r\n\r\n**Matematikai ábrázolás**:\r\nAz X = {x₁, x₂, ..., xn} bemeneti sorozathoz:\r\n- Lekérdezési mátrix: Q = X · W^Q\r\n- Kulcsmátrix: K = X · W^K  \r\n- Értékmátrix: V = X · W^V\r\n\r\nFigyelem kimenete:\r\nFigyelem(Q, K, V) = softmax(QK^T / √d_k) · V\r\n\r\n**Az önfigyelés számítási folyamata**:\r\n1. **Lineáris transzformáció**: A bemeneti szekvenciát három különböző lineáris transzformációval kapjuk meg a Q, K és V előállításához\r\n2. **Hasonlósági számítás**: Számítsa ki a hasonlósági mátrixot az összes pozíciópár között\r\n3. **Súly normalizálása**: Használja a softmax funkciót a figyelemsúlyok normalizálásához\r\n4. **Súlyozott összegzés**: Értékvektorok súlyozott összegzése figyelemsúlyok alapján\r\n\r\n### Az önfigyelés előnyei\r\n\r\n**1. Távolsági függőségi modellezés**:\r\nAz önfigyelem közvetlenül modellezheti a sorrend bármely két pozíciója közötti kapcsolatot, távolságtól függetlenül. Ez különösen fontos az OCR-feladatoknál, ahol a karakterfelismerés gyakran megköveteli a kontextuális információk távolról történő figyelembevételét.\r\n\r\n**Időkomplexitás elemzése**:\r\n- RNN: O(n) szekvencia számítás, nehéz párhuzamosítani\r\n- CNN: O(log n) a teljes sorozat lefedésére\r\n- Önfigyelem: Az O(1) úthossza közvetlenül kapcsolódik bármely helyhez\r\n\r\n**2. Párhuzamos számítás**:\r\nAz RNN-ekkel ellentétben az önfigyelem kiszámítása teljesen párhuzamosítható, ami nagymértékben javítja az edzés hatékonyságát.\r\n\r\n**Párhuzamosítási előnyök**:\r\n- Az összes pozíció figyelemsúlya egyszerre számítható ki\r\n- A mátrixműveletek teljes mértékben kihasználhatják a GPU-k párhuzamos számítási teljesítményét\r\n- A betanítási idő jelentősen lerövidül az RNN-hez képest\r\n\r\n**3. Értelmezhetőség**:\r\nA figyelemsúly-mátrix vizuális magyarázatot ad a modell döntéseire, így könnyen érthető, hogyan működik a modell.\r\n\r\n**Vizuális elemzés**:\r\n- Figyelem hőtérkép: Megmutatja, hogy az egyes helyek mekkora figyelmet fordítanak a többire\r\n- Figyelemminták: Elemezze a különböző fejek figyelemmintáit\r\n- Hierarchikus elemzés: Figyelje meg a figyelemminták változásait különböző szinteken\r\n\r\n**4. Hajlékonyság**:\r\nKönnyen kiterjeszthető különböző hosszúságú sorozatokra a modell architektúrájának módosítása nélkül.\r\n\r\n### Pozíció kódolás\r\n\r\nMivel maga az önfigyelő mechanizmus nem tartalmaz pozícióinformációt, a modellt pozíciókódolás útján kell ellátni a sorozat elemeinek helyzetinformációival.\r\n\r\n**A pozíciókódolás szükségessége**:\r\nAz önfigyelő mechanizmus megváltoztathatatlan, azaz a bemeneti sorrend megváltoztatása nem befolyásolja a kimenetet. De az OCR-feladatokban a karakterek helyinformációi kulcsfontosságúak.\r\n\r\n**Szinusz pozíció kódolás**:\r\nPE(pos, 2i) = sin(pos / 10000^(2i/d_model))\r\nPE(pos, 2i+1) = cos(pos / 10000^(2i/d_model))\r\n\r\nEbbe bele:\r\n- pos: Helyindex\r\n- i: Dimenzióindex\r\n- d_model: Modell mérete\r\n\r\n**A szinuszos pozíciókódolás előnyei**:\r\n- Determinisztikus: Nincs szükség tanulásra, csökkentve a paraméterek mennyiségét\r\n- Extrapoláció: Hosszabb szekvenciákat képes kezelni, mint a betanítás során\r\n- Periodicitás: Jó periodikus jellegű, ami kényelmes a modell számára a relatív pozíciókapcsolatok megtanulásához\r\n\r\n**Megtanulható pozíciókódolás**:\r\nA pozíciókódolást megtanulható paraméterként használják, és az optimális pozícióábrázolást automatikusan megtanulják a betanítási folyamat során.\r\n\r\n**Megvalósítási módszer**:\r\n- Rendeljen hozzá egy tanulható vektort minden pozícióhoz\r\n- Adja össze a bemeneti beágyazásokkal a végső bemenet eléréséhez\r\n- A pozíciókód frissítése visszaterjedéssel\r\n\r\n**A megtanulható pozíciókódolás előnyei és hátrányai**:\r\nÉrdem:\r\n- Adaptálható a feladatspecifikus helyzetábrázolások megtanulásához\r\n- A teljesítmény általában valamivel jobb, mint a rögzített pozíciós kódolás\r\n\r\nHiány:\r\n- Növelje a paraméterek mennyiségét\r\n- Képtelenség a betanítás hosszán túli szekvenciák feldolgozására\r\n- Több képzési adatra van szükség\r\n\r\n**Relatív pozíciókódolás**:\r\nNem kódolja közvetlenül az abszolút pozíciót, hanem a relatív pozíciókapcsolatokat.\r\n\r\n**Végrehajtási elv**:\r\n- Relatív pozíciótorzítás hozzáadása a figyelemszámításokhoz\r\n- Csak az elemek közötti relatív távolságra összpontosítson, ne az abszolút helyzetükre\r\n- Jobb általánosítási képesség\r\n\r\n## Figyelem alkalmazások az OCR-ben\r\n\r\n### Figyelem szekvenciáról szekvenciára\r\n\r\nAz OCR-feladatok leggyakoribb alkalmazása a figyelemmechanizmusok használata a szekvencia-szekvencia modellekben. A kódoló a bemeneti képet jellemzők sorozatába kódolja, a dekóder pedig a kódoló megfelelő részére fókuszál egy figyelemmechanizmuson keresztül, miközben minden karaktert generál.\r\n\r\n**Kódoló-dekóder architektúra**:\r\n1. **Kódoló**: A CNN kinyeri a képjellemzőket, az RNN szekvenciaábrázolásként kódol\r\n2. **Figyelem modul**: Számítsa ki a dekóder állapotának és a kódoló kimenetének figyelemsúlyát\r\n3. **Dekóder**: Karaktersorozatok generálása figyelemre súlyozott kontextusvektorok alapján\r\n\r\n**Figyelemszámítási folyamat**:\r\nA t dekódolási pillanatban a dekódoló állapota s_t, a kódoló kimenete pedig H = {h₁, h₂, ..., hn}:\r\n\r\ne_ti = a(s_t, h_i) # Figyelem pontszám\r\nα_ti = softmax(e_ti) # Figyelem súly\r\nc_t = Σi α_ti · h_i # Kontextus vektor\r\n\r\n**Figyelemfunkciók kiválasztása**:\r\nAz általánosan használt figyelemfunkciók a következők:\r\n- Felhalmozott figyelem: e_ti = s_t^T · h_i\r\n- Additív figyelem: e_ti = v^T · tanh(W_s · s_t + W_h · h_i)\r\n- Bilineáris figyelem: e_ti = s_t^T · W · h_i\r\n\r\n### Vizuális figyelem modul\r\n\r\nA vizuális figyelem közvetlenül a képjellemző térképre alkalmazza a figyelemmechanizmusokat, lehetővé téve a modell számára, hogy a kép fontos területeire összpontosítson.\r\n\r\n**Térbeli figyelem**:\r\nSzámítsa ki a figyelemsúlyokat a jellemzőtérkép egyes térbeli pozícióihoz:\r\nA(i,j) = σ(W_a · [F(i,j); g])\r\n\r\nEbbe bele:\r\n- F(i,j): a pozíció sajátvektora (i,j).\r\n- g: Globális kontextusinformációk\r\n- W_a: Tanulható súlymátrix\r\n- σ: szigmoid aktiválási funkció\r\n\r\n**A térbeli figyelem elérésének lépései**:\r\n1. **Jellemző kinyerése**: A CNN segítségével kinyerheti a képjellemzők térképeit\r\n2. **Globális információösszesítés**: Szerezzen globális funkciókat globális átlagos összevonás vagy globális maximális készletezés révén\r\n3. **Figyelemszámítás**: Számítsa ki a figyelemsúlyokat a helyi és globális jellemzők alapján\r\n4. **Funkciójavítás**: Javítsa az eredeti funkciót figyelemsúlyokkal\r\n\r\n**Csatorna figyelem**:\r\nA figyelemsúlyok kiszámítása a jellemzőgráf minden csatornájára vonatkozik:\r\nA_c = σ(W_c · GAP(F_c))\r\n\r\nEbbe bele:\r\n- GAP: Globális átlagos pooling\r\n- F_c: A c csatorna jellemző térképe\r\n- W_c: A csatorna figyelmének súlymátrixa\r\n\r\n**A csatorna figyelmének alapelvei**:\r\n- A különböző csatornák különböző típusú funkciókat rögzítenek\r\n- Fontos funkciócsatornák kiválasztása figyelemmechanizmusokon keresztül\r\n- Elnyomja az irreleváns funkciókat és javítja a hasznosakat\r\n\r\n**Vegyes figyelem**:\r\nKombinálja a térbeli figyelmet és a figyelmet:\r\nF_output = F ⊙ A_spatial ⊙ A_channel\r\n\r\nahol ⊙ elemszintű szorzást jelent.\r\n\r\n**A vegyes figyelem előnyei**:\r\n- Vegye figyelembe mind a tér, mind az átjáró dimenzióinak fontosságát\r\n- Kifinomultabb funkcióválasztási képességek\r\n- Jobb teljesítmény\r\n\r\n### Többléptékű figyelem\r\n\r\nAz OCR-feladat szövege különböző léptékű, és a többléptékű figyelemmechanizmus különböző felbontásban képes figyelni a releváns információkra.\r\n\r\n**Jellegzetes piramis figyelem**:\r\nA figyelemmechanizmust a különböző léptékű jellemzőtérképekre alkalmazzuk, majd több lépték figyelemeredményeit összeolvasztjuk.\r\n\r\n**Megvalósítási architektúra**:\r\n1. **Többléptékű jellemzők kinyerése**: Funkciópiramis-hálózatok használata különböző léptékű jellemzők kinyeréséhez\r\n2. **Skála-specifikus figyelem**: Számítsa ki a figyelemsúlyokat egymástól függetlenül az egyes skálákon\r\n3. **Skálákon átívelő fúzió**: Integrálja a különböző skálák figyelmi eredményeit\r\n4. **Végső előrejelzés**: Készítsen végső előrejelzést az összevont jellemzők alapján\r\n\r\n**Adaptív skálaválasztás**:\r\nAz aktuális felismerési feladat igényeinek megfelelően dinamikusan kerül kiválasztásra a legmegfelelőbb jellemző skála.\r\n\r\n**Kiválasztási stratégia**:\r\n- Tartalomalapú kiválasztás: Automatikusan kiválasztja a megfelelő méretarányt a kép tartalma alapján\r\n- Feladat alapú kiválasztás: Válassza ki a skálát az azonosított feladat jellemzői alapján\r\n- Dinamikus súlyelosztás: Dinamikus súlyok hozzárendelése a különböző mérlegekhez\r\n\r\n## A figyelemmechanizmusok variációi\r\n\r\n### Ritka figyelem\r\n\r\nA standard önfigyelmi mechanizmus számítási összetettsége O(n²), ami hosszú szekvenciák esetén számítási szempontból költséges. A ritka figyelem csökkenti a számítási összetettséget azáltal, hogy korlátozza a figyelem tartományát.\r\n\r\n**Helyi figyelem**:\r\nMinden hely csak a körülötte lévő rögzített ablakon belüli helyre összpontosít.\r\n\r\n**Matematikai ábrázolás**:\r\nAz i pozíció esetében csak az [i-w, i+w] pozíciótartományon belüli figyelemsúlyt számítja ki, ahol w az ablak mérete.\r\n\r\n**Előnyök és hátrányok elemzése**:\r\nÉrdem:\r\n- A számítási komplexitás O(n·w)-ra csökkentve\r\n- A helyi kontextusinformációk karbantartása\r\n- Alkalmas hosszú sorozatok kezelésére\r\n\r\nHiány:\r\n- Nem lehet nagy távolságú függőségeket rögzíteni\r\n- Az ablak méretét gondosan be kell állítani\r\n- Fontos globális információk elvesztése\r\n\r\n**Darabolási figyelem**:\r\nOssza fel a sorozatot darabokra, amelyek mindegyike csak az ugyanazon blokkon belüli többire összpontosít.\r\n\r\n**Megvalósítási módszer**:\r\n1. Ossza fel az n hosszúságú sorozatot n/b blokkokra, amelyek mindegyike b méretű\r\n2. Számítsa ki a teljes figyelmet az egyes blokkokon belül\r\n3. Nincs figyelemszámítás a blokkok között\r\n\r\nSzámítási komplexitás: O(n·b), ahol b << n\r\n\r\n**Véletlenszerű figyelem**:\r\nMinden pozíció véletlenszerűen kiválasztja a hely egy részét a figyelemszámításhoz.\r\n\r\n**Véletlenszerű kiválasztási stratégia**:\r\n- Fix véletlenszerű: Előre meghatározott véletlenszerű kapcsolati minták\r\n- Dinamikus véletlenszerű: Dinamikus kapcsolatok kiválasztása a betanítás során\r\n- Strukturált véletlenszerű: Kombinálja a helyi és véletlenszerű kapcsolatokat\r\n\r\n### Lineáris figyelem\r\n\r\nA lineáris figyelem matematikai transzformációk révén csökkenti a figyelemszámítások összetettségét O(n²)-ről O(n)-ra.\r\n\r\n**Nukleáris figyelem**:\r\nSoftmax műveletek közelítése kernelfüggvények használatával:\r\nFigyelem(Q, K, V) ≈ φ(Q) · (φ(K)^T · V)\r\n\r\nEzek közül φ jellemzőleképezési funkciók.\r\n\r\n**Általános kernelfunkciók**:\r\n- ReLU mag: φ(x) = ReLU(x)\r\n- ELU Kernel: φ(x) = ELU(x) + 1\r\n- Véletlenszerű funkciómagok: Véletlenszerű Fourier-jellemzők használata\r\n\r\n**A lineáris figyelem előnyei**:\r\n- A számítási komplexitás lineárisan növekszik\r\n- A memóriaigény jelentősen csökken\r\n- Nagyon hosszú szekvenciák kezelésére alkalmas\r\n\r\n**Teljesítménybeli kompromisszumok**:\r\n- Pontosság: Jellemzően valamivel a szokásos figyelem alatt van\r\n- Hatékonyság: Jelentősen javítja a számítási hatékonyságot\r\n- Alkalmazhatóság: Erőforrás-korlátozott forgatókönyvekhez alkalmas\r\n\r\n### Kereszt figyelem\r\n\r\nA multimodális feladatokban a keresztfigyelem lehetővé teszi az információk kölcsönhatását a különböző modalitások között.\r\n\r\n**Kép-szöveg keresztfigyelem**:\r\nA szöveges funkciókat lekérdezésként, a képfunkciókat pedig kulcsként és értékként használják a szöveg képekre való figyelmének megvalósításához.\r\n\r\n**Matematikai ábrázolás**:\r\nCrossAttention(Q_text, K_image, V_image) = softmax(Q_text · K_image^T / √d) · V_image\r\n\r\n**Alkalmazási forgatókönyvek**:\r\n- Képleírás generálása\r\n- Vizuális kérdések és válaszok\r\n- Multimodális dokumentumok megértése\r\n\r\n**Kétirányú keresztfigyelem**:\r\nSzámítsa ki a képből szöveggé és a szövegből képre való figyelmet.\r\n\r\n**Megvalósítási módszer**:\r\n1. Kép szöveggé: Figyelem (Q_image, K_text, V_text)\r\n2. Szöveg képpé: Figyelem (Q_text, K_image, V_image)\r\n3. Funkciófúzió: A figyelem egyesítése mindkét irányban eredményeket mutat\r\n\r\n## Képzési stratégiák és optimalizálás\r\n\r\n### Figyelem felügyelete\r\n\r\nIrányítsa a modellt a helyes figyelemminták megtanulásához felügyelt figyelemjelek megadásával.\r\n\r\n**Figyelemigazítás elvesztése**:\r\nL_align = || A - A_gt|| ²\r\n\r\nEbbe bele:\r\n- A: Előrejelzett figyelemsúly mátrix\r\n- A_gt: Hiteles figyelemcímkék\r\n\r\n**Felügyelt jelvétel**:\r\n- Kézi megjegyzés: A szakértők fontos területeket jelölnek meg\r\n- Heurisztika: Figyelemcímkék generálása szabályok alapján\r\n- Gyenge felügyelet: Használjon durva szemcsés felügyeleti jeleket\r\n\r\n**Figyelem szabályozása**:\r\nÖsztönözze a figyelemsúlyok ritkaságát vagy simaságát:\r\nL_reg = λ₁ · || A|| ₁ + λ₂ · || ∇A|| ²\r\n\r\nEbbe bele:\r\n- || A|| ₁: L1 regularizálás a ritkaság ösztönzésére\r\n- || ∇A|| ²: Simaság szabályozása, hasonló figyelemsúlyok ösztönzése a szomszédos pozíciókban\r\n\r\n**Multitasking tanulás**:\r\nA figyelem-előrejelzés másodlagos feladatként használatos, és a fő feladattal együtt van betanítva.\r\n\r\n**Veszteségfüggvény kialakítása**:\r\nL_total = L_main + α · L_attention + β · L_reg\r\n\r\nahol α és β azok a hiperparaméterek, amelyek kiegyensúlyozzák a különböző veszteségi kifejezéseket.\r\n\r\n### Figyelem vizualizáció\r\n\r\nA figyelemsúlyok vizualizációja segít megérteni a modell működését és hibakeresést a modellproblémákban.\r\n\r\n**Hőtérkép megjelenítése**:\r\nTérképezze le a figyelemsúlyokat hőtérképként, és fedje le őket az eredeti képen, hogy megmutassa a modell érdeklődési területét.\r\n\r\n**Megvalósítási lépések**:\r\n1. Vonja ki a figyelem súlymátrixát\r\n2. A súlyértékek leképezése a színtérre\r\n3. Állítsa be a hőtérkép méretét, hogy megfeleljen az eredeti képnek\r\n4. Átfedés vagy egymás mellett\r\n\r\n**Figyelem pályája**:\r\nMegjeleníti a figyelem fókuszának mozgási pályáját a dekódolás során, segítve a modell felismerési folyamatának megértését.\r\n\r\n**Pályaelemzés**:\r\n- A figyelem mozgásának sorrendje\r\n- Figyelemképes lakás\r\n- A figyelemugrások mintája\r\n- Rendellenes figyelemfelkeltő viselkedés azonosítása\r\n\r\n**Többfejű figyelem megjelenítése**:\r\nA különböző figyelemfejek súlyeloszlását külön-külön vizualizáljuk, és elemezzük az egyes fejek specializációjának mértékét.\r\n\r\n**Analitikai méretek**:\r\n- Fej-fej melletti különbségek: A különböző fejek számára aggodalomra okot adó regionális különbségek\r\n- Fej specializáció: Egyes vezetők bizonyos típusú funkciókra specializálódtak\r\n- A fejek fontossága: A különböző fejek hozzájárulása a végeredményhez\r\n\r\n### Számítási optimalizálás\r\n\r\n**Memória optimalizálás**:\r\n- Gradiens ellenőrzőpontok: Használjon gradiens ellenőrzőpontokat a hosszú sorozatú betanításban a memória lábnyomának csökkentése érdekében\r\n- Vegyes pontosság: Csökkenti a memóriaigényt az FP16 edzéssel\r\n- Figyelem-gyorsítótárazás: Gyorsítótárazza a számított figyelemsúlyokat\r\n\r\n**Számítási gyorsulás**:\r\n- Mátrix darabolás: Számítsa ki a nagy mátrixokat adattömbökben a memóriacsúcsok csökkentése érdekében\r\n- Ritka számítások: Gyorsítsa fel a számításokat a figyelemsúlyok ritkájával\r\n- Hardveroptimalizálás: Optimalizálja a figyelemszámításokat adott hardverhez\r\n\r\n**Párhuzamosítási stratégia**:\r\n- Adatpárhuzamosság: Különböző minták párhuzamos feldolgozása több GPU-n\r\n- Modell párhuzamossága: Ossza el a figyelemszámításokat több eszköz között\r\n- Folyamat párhuzamosítása: Folyamat különböző számítási rétegei\r\n\r\n## Teljesítményértékelés és -elemzés\r\n\r\n### Figyelem minőségértékelése\r\n\r\n**Figyelem pontossága**:\r\nMérje meg a figyelemsúlyok igazítását kézi megjegyzésekkel.\r\n\r\nSzámítási képlet:\r\nPontosság = (Helyesen fókuszált pozíciók száma) / (Összes pozíció)\r\n\r\n**Koncentráció**:\r\nA figyelemeloszlás koncentrációját entrópiával vagy Gini-együtthatóval mérjük.\r\n\r\nEntrópia kiszámítása:\r\nH(A) = -Σi αi · log(αi)\r\n\r\nahol αi az i-edik pozíció figyelemsúlya.\r\n\r\n**Figyelem stabilitása**:\r\nÉrtékelje a figyelemminták konzisztenciáját hasonló bemenetek alatt.\r\n\r\nStabilitási mutatók:\r\nStabilitás = 1 - || A₁ - A₂|| ₂ / 2\r\n\r\nahol A₁ és A₂ a hasonló bemenetek figyelemsúly-mátrixai.\r\n\r\n### Számítási hatékonyság elemzése\r\n\r\n**Időbeli összetettség**:\r\nElemezze a különböző figyelemmechanizmusok számítási összetettségét és tényleges futási idejét.\r\n\r\nÖsszetettség összehasonlítása:\r\n- Normál figyelem: O(n²d)\r\n- Ritka figyelem: O(n·k·d), k<< n\r\n- Lineáris figyelem: O(n·d²)\r\n\r\n**Memóriahasználat**:\r\nÉrtékelje ki a GPU-memória igényét a figyelemmechanizmusokhoz.\r\n\r\nMemória elemzés:\r\n- Figyelem súlymátrix: O(n²)\r\n- Közbenső számítási eredmény: O(n·d)\r\n- Színátmenet tárolása: O(n²d)\r\n\r\n**Energiafogyasztás elemzése**:\r\nÉrtékelje a mobil eszközök figyelemmechanizmusainak energiafogyasztási hatását.\r\n\r\nEnergiafogyasztási tényezők:\r\n- Számítási erősség: Lebegőpontos műveletek száma\r\n- Memória-hozzáférés: Adatátviteli többletterhelés\r\n- Hardver kihasználtság: A számítási erőforrások hatékony felhasználása\r\n\r\n## Valós alkalmazási esetek\r\n\r\n### Kézzel írt szövegfelismerés\r\n\r\nA kézzel írt szövegfelismerésben a figyelemmechanizmus segít a modellnek az aktuálisan felismert karakterre összpontosítani, figyelmen kívül hagyva az egyéb zavaró információkat.\r\n\r\n**Alkalmazási effektusok**:\r\n- A felismerési pontosság 15-20%-kal nőtt\r\n- Fokozott robusztusság összetett hátterekhez\r\n- Javított képesség a szabálytalanul elrendezett szöveg kezelésére\r\n\r\n**Műszaki megvalósítás**:\r\n1. **Térbeli figyelem**: Ügyeljen arra a térbeli területre, ahol a karakter található\r\n2. **Időbeli figyelem**: Használja ki a karakterek közötti időbeli kapcsolatot\r\n3. **Többléptékű figyelem**: Különböző méretű karakterek kezelése\r\n\r\n**Esettanulmány**:\r\nA kézzel írt angol szófelismerő feladatokban a figyelemmechanizmusok:\r\n- Pontosan megtalálja az egyes karakterek helyzetét\r\n- Kezelje a karakterek közötti folyamatos vonások jelenségét\r\n- Használja ki a nyelvi modell ismereteit a szó szintjén\r\n\r\n### Jelenet szövegfelismerése\r\n\r\nA természeti jelenetekben a szöveg gyakran összetett hátterekbe van ágyazva, és a figyelemmechanizmusok hatékonyan elválasztják a szöveget és a hátteret.\r\n\r\n**Műszaki jellemzők**:\r\n- Többléptékű figyelem a különböző méretű szövegekkel való munkához\r\n- Térbeli figyelem a szövegterületek megtalálásához\r\n- Csatorna figyelem kiválasztása a hasznos funkciókhoz\r\n\r\n**Kihívások és megoldások**:\r\n1. **Háttér figyelemelterelés**: Szűrje ki a háttérzajt térbeli figyelemmel\r\n2. **Világítási változások**: Alkalmazkodjon a különböző fényviszonyokhoz a csatorna figyelmével\r\n3. **Geometriai deformáció**: Geometriai korrekciós és figyelemmechanizmusokat tartalmaz\r\n\r\n**Teljesítménybeli fejlesztések**:\r\n- 10-15%-os pontosság javulás az ICDAR adatkészleteken\r\n- Jelentősen javított alkalmazkodóképesség az összetett forgatókönyvekhez\r\n- Az érvelési sebesség elfogadható határokon belül marad\r\n\r\n### Dokumentumelemzés\r\n\r\nA dokumentumelemzési feladatokban a figyelemmechanizmusok segítenek a modelleknek megérteni a dokumentumok szerkezetét és hierarchikus kapcsolatait.\r\n\r\n**Alkalmazási forgatókönyvek**:\r\n- Táblázat azonosítása: Összpontosítson a táblázat oszlopszerkezetére\r\n- Elrendezéselemzés: Azonosítsa az elemeket, például a címsorokat, a törzset, a képeket és egyebeket\r\n- Információkinyerés: a kulcsfontosságú információk helyének megkeresése\r\n\r\n**Technológiai innováció**:\r\n1. **Hierarchikus figyelem**: Fordítsa a figyelmet különböző szinteken\r\n2. **Strukturált figyelem**: Vegye figyelembe a dokumentum strukturált információit\r\n3. **Multimodális figyelem**: Szöveg és vizuális információk keverése\r\n\r\n**Gyakorlati eredmények**:\r\n- Több mint 20%-kal növelheti a táblázatfelismerés pontosságát\r\n- Jelentősen megnövelt feldolgozási teljesítmény összetett elrendezésekhez\r\n- Az információkinyerés pontossága jelentősen javult\r\n\r\n## Jövőbeli fejlesztési trendek\r\n\r\n### Hatékony figyelemmechanizmus\r\n\r\nA szekvencia hosszának növekedésével a figyelemmechanizmus számítási költsége szűk keresztmetszetté válik. A jövőbeni kutatási irányok a következők:\r\n\r\n**Algoritmus optimalizálás**:\r\n- Hatékonyabb ritka figyelem mód\r\n- Fejlesztések a hozzávetőleges számítási módszerekben\r\n- Hardverbarát figyelemfelkeltő kialakítás\r\n\r\n**Építészeti innováció**:\r\n- Hierarchikus figyelemmechanizmus\r\n- Dinamikus figyelemirányítás\r\n- Adaptív számítási diagramok\r\n\r\n**Elméleti áttörés**:\r\n- A figyelem mechanizmusának elméleti elemzése\r\n- Az optimális figyelemminták matematikai bizonyítása\r\n- A figyelem és más mechanizmusok egységes elmélete\r\n\r\n### Multimodális figyelem\r\n\r\nA jövőbeni OCR-rendszerek több információt integrálnak több modalitásból:\r\n\r\n**Vizuális nyelv fúzió**:\r\n- Képek és szövegek közös figyelme\r\n- Információátvitel a modalitások között\r\n- Egységes multimodális reprezentáció\r\n\r\n**Időbeli információfúzió**:\r\n- A figyelem időzítése a videó OCR-ben\r\n- Szövegkövetés dinamikus jelenetekhez\r\n- Téridő közös modellezése\r\n\r\n**Többszenzoros fúzió**:\r\n- 3D figyelem mélységi információkkal kombinálva\r\n- Figyelemmechanizmusok multispektrális képekhez\r\n- Szenzoradatok közös modellezése\r\n\r\n### Értelmezhetőség javítása\r\n\r\nA figyelemmechanizmusok értelmezhetőségének javítása fontos kutatási irány:\r\n\r\n**Figyelem magyarázata**:\r\n- Intuitívabb vizualizációs módszerek\r\n- A figyelemminták szemantikai magyarázata\r\n- Hibaelemző és hibakereső eszközök\r\n\r\n**Ok-okozati érvelés**:\r\n- A figyelem ok-okozati elemzése\r\n- Kontrafaktuális érvelési módszerek\r\n- Robusztusság-ellenőrző technológia\r\n\r\n**Interaktív**:\r\n- Interaktív figyelembeállítás\r\n- Felhasználói visszajelzések beépítése\r\n- Személyre szabott figyelem mód\r\n\r\n## Összefoglalás\r\n\r\nA mély tanulás fontos részeként a figyelemmechanizmus egyre fontosabb szerepet játszik az OCR területén. Az alapszekvenciától a szekvenciafigyelméig a komplex többfejű önfigyelméig, a térbeli figyelemtől a többléptékű figyelemig, ezeknek a technológiáknak a fejlődése nagymértékben javította az OCR rendszerek teljesítményét.\r\n\r\n**Főbb elvezetések**:\r\n- A figyelemmechanizmus szimulálja az emberi szelektív figyelem képességét, és megoldja az információs szűk keresztmetszetek problémáját\r\n- A matematikai alapelvek súlyozott összegzésen alapulnak, lehetővé téve az információválasztást a figyelemsúlyok elsajátításával\r\n- A többfejű figyelem és az önfigyelem a modern figyelemmechanizmusok alapvető technikái\r\n- Az OCR alkalmazásai közé tartozik a szekvenciamodellezés, a vizuális figyelem, a többléptékű feldolgozás és még sok más\r\n- A jövőbeli fejlesztési irányok közé tartozik a hatékonyság optimalizálása, a multimodális fúzió, az értelmezhetőség javítása stb\r\n\r\n**Gyakorlati tanácsok**:\r\n- Válassza ki az adott feladathoz megfelelő figyelemmechanizmust\r\n- Figyeljen a számítási hatékonyság és a teljesítmény közötti egyensúlyra\r\n- Teljes mértékben kihasználja a figyelem értelmezhetőségét a modell hibakereséséhez\r\n- Tartsa szemmel a legújabb kutatási eredményeket és technológiai fejlesztéseket\r\n\r\nA technológia fejlődésével a figyelemmechanizmusok tovább fejlődnek, és még hatékonyabb eszközöket biztosítanak az OCR-hez és más AI-alkalmazásokhoz. A figyelemmechanizmusok elveinek és alkalmazásainak megértése és elsajátítása kulcsfontosságú az OCR kutatással és fejlesztéssel foglalkozó technikusok számára.</div>\n                        \n                        <div class=\"article-tags\">\n                            <h6>Címke:</h6>\n                            <div class=\"tags-list\">\n                                \n                                <span class=\"tag\">Figyelem mechanizmus</span>\n                                \n                                <span class=\"tag\">Bika figyelem</span>\n                                \n                                <span class=\"tag\">Önfigyelem</span>\n                                \n                                <span class=\"tag\">Pozíció kódolás</span>\n                                \n                                <span class=\"tag\">Keresztfigyelem</span>\n                                \n                                <span class=\"tag\">Kevés figyelm</span>\n                                \n                                <span class=\"tag\">OCR</span>\n                                \n                                <span class=\"tag\">Transformer</span>\n                                \n                            </div>\n                        </div>\n                        \n                    </article>\n                    \n                    <footer class=\"article-footer\" id=\"articleFooter\">\n                        <div class=\"article-share\">\n                            <h6 style=\"color: #1a1c1f; margin-bottom: 1rem;\">Megosztás és üzemeltetés:</h6>\n                            <div class=\"share-buttons\">\n                                <a href=\"javascript:void(0)\" onclick=\"shareToWeibo()\" class=\"btn btn-weibo\">📱 Weibo megosztva</a>\n                                <a href=\"javascript:void(0)\" onclick=\"copyUrl()\" class=\"btn btn-copy\">🔗 Link másolása</a>\n                                <button type=\"button\" class=\"btn btn-print\" onclick=\"printArticle()\">🖨️ Nyomtassa ki a cikket</button>\n                            </div>\n                        </div>\n                    </footer>\n                    \n                </div>\n                <div class=\"col-lg-4 col-xl-3 col-md-12\">\n                    <div class=\"sidebar\">\n                        <div class=\"sidebar-card toc-card\" id=\"tocCard\" style=\"display: none;\"><h5>Tartalomjegyzék</h5><div id=\"tableOfContents\" class=\"toc-list\"></div></div>\n                        <div class=\"sidebar-card\">\n                            <h5>Ajánlott olvasmány</h5>\n                            <div id=\"recommendedArticles\">\n                                \n                                <div class=\"recommended-item\"><h6><a href=\"Article.aspx?slug=document-intelligence-future-outlook\">【Dokumentum intelligens feldolgozási sorozat·20】 A dokumentum intelligens feldolgozási technológiájának fejlesztési kilátásai</a></h6><p class=\"text-muted small\">2025-08-19 • 1203 Következő olvasmány</p></div>\n                                \n                                <div class=\"recommended-item\"><h6><a href=\"Article.aspx?slug=document-intelligence-quality-assurance\">【Dokumentum intelligens feldolgozási sorozat·19】 Dokumentum intelligens feldolgozási minőségbiztosítási rendszer</a></h6><p class=\"text-muted small\">2025-08-19 • 1200 Következő olvasmány</p></div>\n                                \n                                <div class=\"recommended-item\"><h6><a href=\"Article.aspx?slug=large-scale-document-processing-optimization\">【Dokumentum intelligens feldolgozási sorozat·18】 Nagyszabású dokumentumfeldolgozási teljesítmény optimalizálása</a></h6><p class=\"text-muted small\">2025-08-19 • 1201 Következő olvasmány</p></div>\n                                \n                            </div>\n                        </div>\n                    </div>\n</div>\n</div>\n</div>\n</div>\n<script>\nvar articleId,viewCountKey,now=new Date().getTime();\ndocument.addEventListener('DOMContentLoaded',function(){var articleDetail=document.getElementById('articleDetail');if(articleDetail&&articleDetail.querySelector('.article-content')){formatContent();initReadingProgress();optimizeImages();updateViewCount();}});\nfunction updateViewCount(){articleId=getArticleId();if(!articleId)return;viewCountKey='article_view_'+articleId;var lastViewTime=localStorage.getItem(viewCountKey);if(lastViewTime&&(now-parseInt(lastViewTime))<3600000)return;var xhr=new XMLHttpRequest();xhr.open('POST','/Article.ashx',true);xhr.setRequestHeader('Content-Type','application/x-www-form-urlencoded');xhr.onreadystatechange=function(){if(xhr.readyState===4&&xhr.status===200){try{var response=JSON.parse(xhr.responseText);if(response.success){updateViewCountDisplay(response.viewCount);localStorage.setItem(viewCountKey,now.toString());}}catch(e){}}};xhr.send('action=updateviewcount&articleId='+encodeURIComponent(articleId));}\nfunction getArticleId(){var articleDetail=document.getElementById('articleDetail');if(articleDetail){var id=articleDetail.getAttribute('data-article-id');if(id)return id;}var urlParams=new URLSearchParams(window.location.search);return urlParams.get('id');}\nfunction updateViewCountDisplay(newCount){document.querySelectorAll('.view-count').forEach(function(element){element.textContent=newCount;});}\nfunction decodeHtmlEntities(text){var textArea=document.createElement('textarea');textArea.innerHTML=text;return textArea.value;}\nfunction formatContent(){var contentDiv=document.getElementById('articleContent');if(!contentDiv)return;var content=contentDiv.textContent||contentDiv.innerText;if(!content)return;content=decodeHtmlEntities(content);content=formatMarkdownContent(content);content=formatIndentation(content);content=formatLists(content);content=formatParagraphs(content);content=formatLinks(content);contentDiv.innerHTML=content;initTableOfContents();}\nfunction formatIndentation(content){var lines=content.split('\\n'),result=[];for(var i=0;i<lines.length;i++){var line=lines[i];if(line.trim().match(/^<[^>]+>/)){result.push(line);continue;}line=line.replace(/^\\t+/g,function(match){return '    '.repeat(match.length);});if(line.match(/^  +[^\\s]/)){var indentMatch=line.match(/^( +)(.*)$/);if(indentMatch){var indentLevel=Math.floor(indentMatch[1].length/2);line='<span class=\"text-indent-'+indentLevel+'\">'+indentMatch[2]+'</span>';}}result.push(line);}return result.join('\\n');}\nfunction formatMarkdownContent(content){content=content.replace(/^\\s*##### (.+)$/gm,'<h5>$1</h5>').replace(/^\\s*#### (.+)$/gm,'<h4>$1</h4>').replace(/^\\s*### (.+)$/gm,'<h3>$1</h3>').replace(/^\\s*## (.+)$/gm,'<h2>$1</h2>').replace(/^\\s*# (.+)$/gm,'<h1>$1</h1>').replace(/^> (.+)$/gm,'<blockquote><p>$1</p></blockquote>').replace(/\\*\\*([^*]+?)\\*\\*/g,'<strong>$1</strong>').replace(/\\*([^*\\n]+?)\\*/g,'<em>$1</em>').replace(/`([^`]+?)`/g,'<code class=\"inline-code\">$1</code>').replace(/^(\\*\\*[^*]+?\\*\\*)：/gm,'<div class=\"label-item\"><span class=\"label\">$1</span></div>').replace(/^([^：\\n]+?)：$/gm,'<div class=\"section-label\">$1：</div>').replace(/^(\\*\\*[^*]+?\\*\\*)：(.+)$/gm,'<div class=\"definition-item\"><span class=\"definition-label\">$1：</span><span class=\"definition-content\">$2</span></div>').replace(/^(jegyzet|megjegyzés|megjegyzés):(.+)$/gm,'<div class=\"note-item\"><span class=\"note-label\">$1：</span><span class=\"note-content\">$2</span></div>');content=formatLists(content);content=formatMathSymbols(content);content=formatParagraphs(content);return content;}\nfunction formatLists(content){var lines=content.split('\\n'),result=[],inOrderedList=false,inUnorderedList=false;for(var i=0;i<lines.length;i++){var line=lines[i],trimmed=line.trim();var orderedMatch=line.match(/^(\\d+)\\.\\s*(.+)$/);var unorderedMatch=line.match(/^[-*+]\\s*(.+)$/);if(orderedMatch){if(!inOrderedList){if(inUnorderedList){result.push('</ul>');inUnorderedList=false;}result.push('<ol class=\"formatted-list ordered\">');inOrderedList=true;}var listContent=orderedMatch[2].replace(/^\\*\\*([^*]+?)\\*\\*：(.+)$/,'<strong class=\"list-title\">$1：</strong><span class=\"list-content\">$2</span>');result.push('<li>'+listContent+'</li>');}else if(unorderedMatch){if(!inUnorderedList){if(inOrderedList){result.push('</ol>');inOrderedList=false;}result.push('<ul class=\"formatted-list unordered\">');inUnorderedList=true;}var listContent=unorderedMatch[1].replace(/^\\*\\*([^*]+?)\\*\\*：(.+)$/,'<strong class=\"list-title\">$1：</strong><span class=\"list-content\">$2</span>');result.push('<li>'+listContent+'</li>');}else{if(trimmed===''){if(i+1<lines.length){var nextLine=lines[i+1].trim();if(!nextLine.match(/^\\d+\\.\\s/)&&!nextLine.match(/^[-*+]\\s/)){if(inOrderedList){result.push('</ol>');inOrderedList=false;}if(inUnorderedList){result.push('</ul>');inUnorderedList=false;}}}result.push(line);}else{if(inOrderedList){result.push('</ol>');inOrderedList=false;}if(inUnorderedList){result.push('</ul>');inUnorderedList=false;}result.push(line);}}}if(inOrderedList)result.push('</ol>');if(inUnorderedList)result.push('</ul>');return result.join('\\n');}\nfunction formatMathSymbols(content){var mathSymbols={'α':'&alpha;','β':'&beta;','γ':'&gamma;','δ':'&delta;','ε':'&epsilon;','ζ':'&zeta;','η':'&eta;','θ':'&theta;','λ':'&lambda;','μ':'&mu;','π':'&pi;','σ':'&sigma;','∑':'&sum;','∏':'&prod;','∫':'&int;','∂':'&part;','∇':'&nabla;','∞':'&infin;','≤':'&le;','≥':'&ge;','≠':'&ne;','≈':'&asymp;','±':'&plusmn;','×':'&times;','÷':'&divide;','→':'&rarr;','←':'&larr;','↑':'&uarr;','↓':'&darr;','⇒':'&rArr;','⇐':'&lArr;'};var subMap={'₀':'0','₁':'1','₂':'2','₃':'3','₄':'4','₅':'5','₆':'6','₇':'7','₈':'8','₉':'9','ₖ':'k','ₜ':'t','ₛ':'s','₊':'+','₋':'-'};var supMap={'⁰':'0','¹':'1','²':'2','³':'3','⁴':'4','⁵':'5','⁶':'6','⁷':'7','⁸':'8','⁹':'9','ᵏ':'k','ᵗ':'t','ˢ':'s','⁺':'+','⁻':'-'};for(var symbol in mathSymbols){content=content.replace(new RegExp(escapeRegExp(symbol),'g'),mathSymbols[symbol]);}content=content.replace(/([a-zA-Z])₍([0-9]+)₎/g,'$1<sub>$2</sub>').replace(/([a-zA-Z])⁽([0-9]+)⁾/g,'$1<sup>$2</sup>').replace(/([a-zA-Z])([₀₁₂₃₄₅₆₇₈₉ₖₜₛ₊₋]+)/g,function(match,base,subscript){var converted=subscript.replace(/[₀₁₂₃₄₅₆₇₈₉ₖₜₛ₊₋]/g,function(char){return subMap[char]||char;});return base+'<sub>'+converted+'</sub>';}).replace(/([a-zA-Z])([⁰¹²³⁴⁵⁶⁷⁸⁹ᵏᵗˢ⁺⁻]+)/g,function(match,base,superscript){var converted=superscript.replace(/[⁰¹²³⁴⁵⁶⁷⁸⁹ᵏᵗˢ⁺⁻]/g,function(char){return supMap[char]||char;});return base+'<sup>'+converted+'</sup>';}).replace(/\\b([A-Za-z])_\\{([^}]+)\\}/g,'$1<sub>$2</sub>').replace(/\\b([A-Za-z])\\^\\{([^}]+)\\}/g,'$1<sup>$2</sup>');return content;}\nfunction formatParagraphs(content){var lines=content.split('\\n'),result=[],inParagraph=false,currentParagraph=[];for(var i=0;i<lines.length;i++){var originalLine=lines[i],line=originalLine.trim();if(line.match(/^<[^>]+>/)||line===''){if(inParagraph&&currentParagraph.length>0){var paragraphContent=currentParagraph.join(' ').trim();if(paragraphContent)result.push('<p class=\"content-paragraph indent-paragraph\">'+paragraphContent+'</p>');currentParagraph=[];inParagraph=false;}result.push(originalLine);continue;}if(line.match(/^#{1,6}\\s/)||line.match(/^\\d+\\.\\s/)||line.match(/^[-*+]\\s/)||line.match(/^>/)){if(inParagraph&&currentParagraph.length>0){var paragraphContent=currentParagraph.join(' ').trim();if(paragraphContent)result.push('<p class=\"content-paragraph indent-paragraph\">'+paragraphContent+'</p>');currentParagraph=[];inParagraph=false;}result.push(originalLine);continue;}if(line){currentParagraph.push(line);inParagraph=true;}}if(inParagraph&&currentParagraph.length>0){var paragraphContent=currentParagraph.join(' ').trim();if(paragraphContent)result.push('<p class=\"content-paragraph indent-paragraph\">'+paragraphContent+'</p>');}return result.join('\\n');}\nfunction escapeRegExp(string){return string.replace(/[.*+?^${}()|[\\]\\\\]/g,'\\\\$&');}\nfunction formatLinks(content){return content.replace(/<a\\s+href=\"(https?:\\/\\/[^\"]+)\"([^>]*)>/gi,function(match,url,attrs){return !attrs.includes('target=')?'<a href=\"'+url+'\"'+attrs+' target=\"_blank\" rel=\"noopener noreferrer\">':match;});}\nfunction escapeHtml(text){var div=document.createElement('div');div.textContent=text;return div.innerHTML;}\nfunction addTocClickEvents(){document.querySelectorAll('#tableOfContents a').forEach(function(link){link.addEventListener('click',function(e){e.preventDefault();var targetElement=document.getElementById(this.getAttribute('data-target'));if(targetElement){targetElement.scrollIntoView({behavior:'smooth',block:'start'});updateActiveTocItem(this);}});});}\nfunction addScrollHighlight(headings){var throttleTimer=null;function updateActiveHeading(){var windowHeight=window.innerHeight,activeHeading=null;for(var i=headings.length-1;i>=0;i--){var rect=headings[i].getBoundingClientRect();if(rect.top<=windowHeight*0.3){activeHeading=headings[i];break;}}if(activeHeading){var activeLink=document.querySelector('#tableOfContents a[data-target=\"'+activeHeading.id+'\"]');if(activeLink)updateActiveTocItem(activeLink);}}window.addEventListener('scroll',function(){if(throttleTimer)clearTimeout(throttleTimer);throttleTimer=setTimeout(updateActiveHeading,100);});updateActiveHeading();}\nfunction updateActiveTocItem(activeLink){document.querySelectorAll('#tableOfContents a').forEach(function(link){link.classList.remove('active');});if(activeLink){activeLink.classList.add('active');scrollTocToActiveItem(activeLink);}}\nfunction scrollTocToActiveItem(activeLink){var tocCard=document.getElementById('tocCard');if(!activeLink||!tocCard)return;var cardRect=tocCard.getBoundingClientRect();var activeRect=activeLink.getBoundingClientRect();var relativeTop=activeRect.top-cardRect.top;var relativeBottom=activeRect.bottom-cardRect.bottom;var cardStyle=window.getComputedStyle(tocCard);var paddingTop=parseFloat(cardStyle.paddingTop)||0;var titleHeight=tocCard.querySelector('h5')?tocCard.querySelector('h5').offsetHeight:0;var contentTop=paddingTop+titleHeight;if(relativeTop<contentTop){tocCard.scrollTop+=(relativeTop-contentTop-10);}else if(relativeBottom>0){tocCard.scrollTop+=(relativeBottom+10);}}\nfunction optimizeImages(){document.querySelectorAll('.article-content img').forEach(function(img){img.loading='lazy';img.onerror=function(){this.style.display='none';};if(!img.alt)img.alt='Cikk képekkel';});}\nfunction initReadingProgress(){var progressBar=document.getElementById('readingProgress');function updateProgress(){var scrollTop=window.pageYOffset||document.documentElement.scrollTop;var docHeight=document.documentElement.scrollHeight-window.innerHeight;progressBar.style.width=Math.min((scrollTop/docHeight)*100,100)+'%';}window.addEventListener('scroll',updateProgress);updateProgress();}\nfunction initTableOfContents(){var content=document.querySelector('.article-content');if(!content)return;var headings=content.querySelectorAll('h1, h2, h3, h4, h5, h6');if(headings.length<=1)return;var tocCard=document.getElementById('tocCard');var tocList=document.getElementById('tableOfContents');var html='',counters=[0,0,0,0,0,0];headings.forEach(function(heading,index){var id='heading-'+index;heading.id=id;var level=parseInt(heading.tagName.substring(1));var indent=(level-1)*15;counters[level-1]++;for(var i=level;i<counters.length;i++)counters[i]=0;var numberText='';for(var i=0;i<level;i++){if(counters[i]>0)numberText+=(numberText?'.':'')+counters[i];}numberText+='.';html+='<a href=\"#'+id+'\" class=\"toc-item toc-level-'+level+'\" style=\"padding-left: '+indent+'px;\" data-target=\"'+id+'\"><span class=\"toc-number\">'+numberText+'</span> <span class=\"toc-text\">'+heading.textContent+'</span></a>';});tocList.innerHTML=html;tocCard.style.display='block';addTocClickEvents();addScrollHighlight(headings);tocList.addEventListener('click',function(e){if(e.target.tagName==='A'){e.preventDefault();var targetElement=document.getElementById(e.target.getAttribute('data-target'));if(targetElement){targetElement.scrollIntoView({behavior:'smooth',block:'start'});targetElement.style.transition='all 0.3s ease';targetElement.style.backgroundColor='#e3f2fd';targetElement.style.padding='10px';targetElement.style.borderRadius='6px';setTimeout(function(){targetElement.style.backgroundColor='';targetElement.style.padding='';targetElement.style.borderRadius='';},2000);}}});}\nfunction shareToWeibo(){window.open('https://service.weibo.com/share/share.php?url='+encodeURIComponent(window.location.href)+'&title='+encodeURIComponent(document.title),'_blank');}\nfunction copyUrl(){navigator.clipboard.writeText(window.location.href).then(function(){alert('A hivatkozás a vágólapra lett másolva');}).catch(function(){var textArea=document.createElement('textarea');textArea.value=window.location.href;textArea.style.cssText='position:fixed;left:-999999px;top:-999999px';document.body.appendChild(textArea);textArea.focus();textArea.select();try{alert(document.execCommand('copy')?'A hivatkozás a vágólapra lett másolva':'Ha a másolás sikertelen, kérjük, másolja ki manuálisan a linket');}catch(err){alert('Ha a másolás sikertelen, kérjük, másolja ki manuálisan a linket');}document.body.removeChild(textArea);});}\nfunction printArticle(){var printWindow=window.open('','_blank');var articleContent=document.querySelector('.article-detail').innerHTML;var articleTitle=document.querySelector('.article-title').textContent;printWindow.document.write('<!DOCTYPE html><html lang=\"hu\"><title>'+articleTitle+'</title><style>body{font-family:-apple-system,BlinkMacSystemFont,\"Segoe UI\",Roboto,\"Helvetica Neue\",Arial,sans-serif;line-height:1.6;color:#333;max-width:800px;margin:0 auto;padding:20px}h1,h2,h3,h4,h5,h6{color:#1a1c1f;margin-top:1.5em;margin-bottom:0.5em}h1{font-size:2em;border-bottom:2px solid #e9ecef;padding-bottom:0.3em}h2{font-size:1.5em;border-bottom:1px solid #e9ecef;padding-bottom:0.3em}h3{font-size:1.25em}p{margin-bottom:1em}pre{background:#f8f9fa;border:1px solid #e9ecef;border-radius:4px;padding:1em;overflow-x:auto;font-family:Consolas,Monaco,\"Courier New\",monospace}code{background:#f1f3f4;padding:2px 4px;border-radius:3px;font-family:Consolas,Monaco,\"Courier New\",monospace}blockquote{border-left:4px solid #1764ff;background:#f8f9fa;padding:1em;margin:1em 0;border-radius:0 4px 4px 0}img{max-width:100%;height:auto}table{width:100%;border-collapse:collapse;margin:1em 0}th,td{border:1px solid #ddd;padding:8px;text-align:left}th{background-color:#f2f2f2}.article-meta,.article-footer,.share-buttons{display:none}</style><body>'+articleContent+'</body></html>');printWindow.document.close();printWindow.focus();setTimeout(function(){printWindow.print();printWindow.close();},250);}\nfunction improvedStickyPosition(){var sidebarCards=document.querySelectorAll('.sidebar-card');var originalPositions=[],isFixed=[];sidebarCards.forEach(function(card,index){var rect=card.getBoundingClientRect();originalPositions[index]=rect.top+window.pageYOffset;isFixed[index]=false;var placeholder=document.createElement('div');placeholder.style.cssText='height:'+card.offsetHeight+'px;display:none';placeholder.className='sticky-placeholder';card.parentNode.insertBefore(placeholder,card.nextSibling);});function updateStickyPosition(){var scrollTop=window.pageYOffset||document.documentElement.scrollTop;sidebarCards.forEach(function(card,index){var originalTop=originalPositions[index];var stickyTop=index>0?(document.querySelector('.toc-card').style.position==='fixed'?60+document.querySelector('.toc-card').offsetHeight+8:80):60;var placeholder=card.nextSibling;if(scrollTop>originalTop-stickyTop){if(!isFixed[index]){var colContainer=card.closest('[class*=\"col-\"]');var colRect=colContainer.getBoundingClientRect();var colStyle=window.getComputedStyle(colContainer);var paddingLeft=parseFloat(colStyle.paddingLeft)||0;var paddingRight=parseFloat(colStyle.paddingRight)||0;card.style.cssText='position:fixed;top:'+stickyTop+'px;left:'+(colRect.left+paddingLeft)+'px;width:'+(colRect.width-paddingLeft-paddingRight)+'px;z-index:9999;box-sizing:border-box';card.classList.add('fixed');placeholder.style.display='block';isFixed[index]=true;}}else if(isFixed[index]){card.style.cssText='';card.classList.remove('fixed');placeholder.style.display='none';isFixed[index]=false;}});}window.addEventListener('scroll',updateStickyPosition);window.addEventListener('resize',function(){setTimeout(function(){sidebarCards.forEach(function(card,index){if(isFixed[index]){var colContainer=card.closest('[class*=\"col-\"]');var colRect=colContainer.getBoundingClientRect();var colStyle=window.getComputedStyle(colContainer);var paddingLeft=parseFloat(colStyle.paddingLeft)||0;var paddingRight=parseFloat(colStyle.paddingRight)||0;card.style.left=(colRect.left+paddingLeft)+'px';card.style.width=(colRect.width-paddingLeft-paddingRight)+'px';}else{originalPositions[index]=card.getBoundingClientRect().top+window.pageYOffset;}});},100);});updateStickyPosition();}\ndocument.addEventListener('DOMContentLoaded',function(){setTimeout(improvedStickyPosition,1000);});\n    </script>\n\r\n            </div>\r\n        </form>\r\n\r\n        <div class=\"fixed-icon\">\r\n            <div style=\"width: 120px; position: relative; left: 1rem; top: -12px;\">\r\n            </div>\r\n            <a id=\"webchat\" class=\"wx-con disa\" href=\"http://wpa.qq.com/msgrd?v=3&uin=365833440&site=qq&menu=yes\" target=\"_blank\">\r\n                <img class=\"wechat-ico\" src=\"site/image/icon/qq.svg\" alt=\"OCR asszisztens QQ online ügyfélszolgálat\" />\r\n                <div class=\"wx-text\">QQ ügyfélszolgálat (365833440)</div>\r\n                <label class=\"badge badge-danger\" style=\"background: rgb(255, 34, 122); display: inline-block;\">1</label>\r\n            </a>\r\n\r\n            <a class=\"wx-con\" href=\"https://qm.qq.com/cgi-bin/qm/qr?k=bohNy9GMdgD5bsYrB-fcIjy-s6Ot3zOg&jump_from=webapi&qr=1\" target=\"_blank\">\r\n                <img class=\"wechat-ico\" src=\"site/image/icon/qq.svg\" alt=\"OCR asszisztens QQ felhasználói kommunikációs csoport\" />\r\n                <div class=\"wx-text\">QQ csoport (100029010)</div>\r\n                <label class=\"badge badge-danger\" style=\"background: rgb(255, 34, 122); display: inline-block;\">2</label>\r\n            </a>\r\n            <a href=\"mailto:<EMAIL>\" class=\"wx-con\">\r\n                <img class=\"wechat-ico\" src=\"site/image/icon/im.svg\" alt=\"OCR asszisztens vegye fel a kapcsolatot az ügyfélszolgálattal e-mailben\" />\r\n                <div class=\"wx-text\" style=\"width: 240px; text-align: left; left: -15.3rem; padding: 10px 20px;\">\r\n                    <div style=\"font-size: 15px\">E-mail: <EMAIL></div>\r\n                    <hr style=\"background: #c1c1c1; margin: 10px 10px 6px 0;\" />\r\n                    <span style=\"font-size: 13px; color: #c1c1c1\">Köszönöm észrevételeiteket és javaslataitokat!</span>\r\n                </div>\r\n                <label class=\"badge badge-danger\" style=\"background: rgb(255, 34, 122); display: inline-block;\">3</label>\r\n            </a>\r\n        </div>\r\n\r\n        <style>\r\n            .content {\r\n                width: 100%;\r\n                padding: 0 !important;\r\n                margin: 0 auto\r\n            }\r\n\r\n            h3.h6 {\r\n                font-size: 1rem !important;\r\n                font-weight: bold !important;\r\n                line-height: 1.2 !important;\r\n                margin-bottom: .5rem;\r\n                font-family: inherit\r\n            }\r\n        </style>\r\n        <footer class=\"page-footer\">\r\n            <div class=\"container\">\r\n                <div class=\"row foot-cont pt-4 no-gutters\" style=\"border-top: 1px solid #e1e6ed; max-width: 100%\">\r\n                    <div class=\"col-lg-12 hu-an p-0 mb-3 mt-3 text-l color-gray\" style=\"text-align: center;\">\r\n                        OCR szövegfelismerő asszisztens&nbsp;©️ 2025 ALL RIGHTS RESERVED. Minden jog fenntartva&nbsp;&nbsp;&nbsp;|&nbsp;&nbsp;&nbsp;\r\n                        <a href=\"PrivacyPolicy.aspx\" style=\"text-decoration: underline;\">Adatvédelmi megállapodás</a>&nbsp;&nbsp;&nbsp;|&nbsp;&nbsp;&nbsp;\r\n                        <a href=\"UserAgreement.aspx\" style=\"text-decoration: underline;\">Felhasználói megállapodás</a>&nbsp;&nbsp;&nbsp;|&nbsp;&nbsp;&nbsp;\r\n                        <a href=\"Status.aspx\" style=\"text-decoration: underline;\">Szolgáltatás állapota</a>&nbsp;&nbsp;&nbsp;|&nbsp;&nbsp;&nbsp;\r\n                        <a class=\"mb-2 color-gray\" href=\"https://beian.miit.gov.cn/\" target=\"_blank\" style=\"text-decoration: underline;\">E ICP előkészítés 2021012692</a>\r\n                    </div>\r\n                </div>\r\n            </div>\r\n        </footer>\r\n        <style>\r\n            #translate > .translateSelectLanguage {\r\n                right: 2rem;\r\n                font-size: 1rem;\r\n                width: 150px;\r\n                padding: .3rem;\r\n                margin-right: 20px;\r\n                border: 1px solid #C9C9C9;\r\n                background: #fff;\r\n                color: #555\r\n            }\r\n        </style>\r\n    </div>\r\n    <script>\r\n        document.addEventListener('DOMContentLoaded', function () {\r\n            var setScroll = function (active) {\r\n                forEach(qa('.v4_header_pc,.v4_header_mob'), function (e) {\r\n                    if (active) addClass(e, 'scrollActive'); else removeClass(e, 'scrollActive');\r\n                });\r\n            };\r\n            if (getScrollY() > 60) setScroll(1);\r\n            window.addEventListener('scroll', function () { setScroll(getScrollY() > 60); });\r\n\r\n            forEach(qa('.v4_header_pc'), function (h) {\r\n                h.addEventListener('mouseenter', function () { addClass(h, 'active'); });\r\n                h.addEventListener('mouseleave', function () { removeClass(h, 'active'); });\r\n            });\r\n\r\n            var menuTimer;\r\n            var hideMenu = function (menuId) {\r\n                var m = document.getElementById(menuId);\r\n                if (m) {\r\n                    m.style.cssText = 'visibility:hidden;height:0;display:none';\r\n                    var n = q('.nav-drown-con', m);\r\n                    if (n) { n.style.cssText = 'height:0;opacity:0;transform:translate(0,-100%)'; }\r\n                }\r\n            };\r\n\r\n            forEach(qa('ul.top-nav>li'), function (i) {\r\n                i.addEventListener('mouseenter', function () {\r\n                    clearTimeout(menuTimer);\r\n                    var l = this.querySelector('a'), t = this.querySelector('.triangle'), s = l ? l.getAttribute('data-sub') : null;\r\n                    if (l) addClass(l, 'active'); if (t) addClass(t, 'active');\r\n                    if (s) {\r\n                        var m = document.getElementById(s);\r\n                        if (m) {\r\n                            m.style.cssText = 'display:block;visibility:visible';\r\n                            var siblings = m.parentNode ? toArray(m.parentNode.children) : [];\r\n                            forEach(siblings, function (c) { if (c !== m) c.style.display = 'none'; });\r\n                            var n = q('.nav-drown-con', m);\r\n                            if (n) { n.style.cssText = 'height:100%;opacity:1;transform:translate(0,0)'; }\r\n\r\n                            m.addEventListener('mouseenter', function () { clearTimeout(menuTimer); });\r\n                            m.addEventListener('mouseleave', function () { menuTimer = setTimeout(function () { hideMenu(s); }, 100); });\r\n                        }\r\n                    }\r\n                });\r\n                i.addEventListener('mouseleave', function () {\r\n                    var l = this.querySelector('a'), t = this.querySelector('.triangle'), s = l ? l.getAttribute('data-sub') : null;\r\n                    if (l) removeClass(l, 'active'); if (t) removeClass(t, 'active');\r\n                    if (s) { menuTimer = setTimeout(function () { hideMenu(s); }, 100); }\r\n                });\r\n            });\r\n\r\n            var r = q('.v4_header_mob .right-menu');\r\n            if (r) r.addEventListener('click', function () {\r\n                var arr = [r, q('.v4_header_mob'), q('#nav_products'), q('.mob-nav-content .sidebar-fix')];\r\n                for (var i = 0; i < arr.length; i++) {\r\n                    var e = arr[i];\r\n                    if (e) {\r\n                        var name = (i === 0 ? 'active' : (i === 1 ? 'active' : (i === 2 ? 'active1' : 'fixedActiveBg')));\r\n                        toggleClass(e, name);\r\n                    }\r\n                }\r\n                var f = q('.mob-nav-content .sidebar-fix');\r\n                if (f) {\r\n                    toggleClass(f, 'show');\r\n                    document.body.style.overflow = hasClass(f, 'show') ? 'hidden' : 'scroll';\r\n                }\r\n            });\r\n\r\n            document.addEventListener('click', function (e) {\r\n                e = e || window.event;\r\n                var target = e.target || e.srcElement;\r\n                var m = closest(target, '.sidebar-fix-left .mob-nav-item');\r\n                if (m) {\r\n                    var p = m.parentNode;\r\n                    if (p) forEach(toArray(p.children), function (c) { removeClass(c, 'active'); });\r\n                    addClass(m, 'active');\r\n                    var d = m.getAttribute('data');\r\n                    if (d) forEach(qa('.sidebar-fix-rigth .sub-nav'), function (n, i) { n.style.display = (i == d ? 'block' : 'none'); });\r\n                }\r\n            });\r\n        });\r\n    </script>\r\n</body>\r\n</html>\r\n\r\n\t            <script type=\"text/javascript\">\r\n                    var _hmt = _hmt || [];\r\n                    (function () {\r\n                        var hm = document.createElement('script');hm.async=1;\r\n                        hm.src = 'https://hm.baidu.com/hm.js?5ee048d7caaf0d439cf16309a1687754';\r\n                        var s = document.getElementsByTagName('script')[0];s.parentNode.insertBefore(hm, s);\r\n                    })();\r\n                </script><div id=\"translate\" class=\"ignore\" style=\"display: none\"></div><script src=\"/static/js/translate.js\"></script>"
﻿"<!DOCTYPE html>\r\n<html xmlns=\"http://www.w3.org/1999/xhtml\" lang=\"ja\">\r\n<head><link rel=\"canonical\" href=\"https://ocr.oldfish.cn/ja/Article.aspx?id=81&slug=rnn-sequence-modeling\" /><link rel=\"alternate\" href=\"https://ocr.oldfish.cn/Article.aspx?id=81&slug=rnn-sequence-modeling\" hreflang=\"x-default\" />\r\n<link rel=\"alternate\" href=\"https://ocr.oldfish.cn/Article.aspx?id=81&slug=rnn-sequence-modeling\" hreflang=\"zh\" />\r\n<link rel=\"alternate\" href=\"https://ocr.oldfish.cn/Article.aspx?id=81&slug=rnn-sequence-modeling\" hreflang=\"zh\" />\r\n<link rel=\"alternate\" href=\"https://ocr.oldfish.cn/zh-Hant/Article.aspx?id=81&slug=rnn-sequence-modeling\" hreflang=\"zh-tw\" />\r\n<link rel=\"alternate\" href=\"https://ocr.oldfish.cn/yue/Article.aspx?id=81&slug=rnn-sequence-modeling\" hreflang=\"zh-yue\" />\r\n<link rel=\"alternate\" href=\"https://ocr.oldfish.cn/en/Article.aspx?id=81&slug=rnn-sequence-modeling\" hreflang=\"en\" />\r\n<link rel=\"alternate\" href=\"https://ocr.oldfish.cn/es/Article.aspx?id=81&slug=rnn-sequence-modeling\" hreflang=\"es\" />\r\n<link rel=\"alternate\" href=\"https://ocr.oldfish.cn/hi/Article.aspx?id=81&slug=rnn-sequence-modeling\" hreflang=\"hi\" />\r\n<link rel=\"alternate\" href=\"https://ocr.oldfish.cn/ar/Article.aspx?id=81&slug=rnn-sequence-modeling\" hreflang=\"ar\" />\r\n<link rel=\"alternate\" href=\"https://ocr.oldfish.cn/pt/Article.aspx?id=81&slug=rnn-sequence-modeling\" hreflang=\"pt\" />\r\n<link rel=\"alternate\" href=\"https://ocr.oldfish.cn/bn/Article.aspx?id=81&slug=rnn-sequence-modeling\" hreflang=\"bn\" />\r\n<link rel=\"alternate\" href=\"https://ocr.oldfish.cn/ru/Article.aspx?id=81&slug=rnn-sequence-modeling\" hreflang=\"ru\" />\r\n<link rel=\"alternate\" href=\"https://ocr.oldfish.cn/ja/Article.aspx?id=81&slug=rnn-sequence-modeling\" hreflang=\"ja\" />\r\n<link rel=\"alternate\" href=\"https://ocr.oldfish.cn/pa/Article.aspx?id=81&slug=rnn-sequence-modeling\" hreflang=\"pa\" />\r\n<link rel=\"alternate\" href=\"https://ocr.oldfish.cn/de/Article.aspx?id=81&slug=rnn-sequence-modeling\" hreflang=\"de\" />\r\n<link rel=\"alternate\" href=\"https://ocr.oldfish.cn/fr/Article.aspx?id=81&slug=rnn-sequence-modeling\" hreflang=\"fr\" />\r\n<link rel=\"alternate\" href=\"https://ocr.oldfish.cn/mr/Article.aspx?id=81&slug=rnn-sequence-modeling\" hreflang=\"mr\" />\r\n<link rel=\"alternate\" href=\"https://ocr.oldfish.cn/te/Article.aspx?id=81&slug=rnn-sequence-modeling\" hreflang=\"te\" />\r\n<link rel=\"alternate\" href=\"https://ocr.oldfish.cn/vi/Article.aspx?id=81&slug=rnn-sequence-modeling\" hreflang=\"vi\" />\r\n<link rel=\"alternate\" href=\"https://ocr.oldfish.cn/ko/Article.aspx?id=81&slug=rnn-sequence-modeling\" hreflang=\"ko\" />\r\n<link rel=\"alternate\" href=\"https://ocr.oldfish.cn/ta/Article.aspx?id=81&slug=rnn-sequence-modeling\" hreflang=\"ta\" />\r\n<link rel=\"alternate\" href=\"https://ocr.oldfish.cn/ur/Article.aspx?id=81&slug=rnn-sequence-modeling\" hreflang=\"ur\" />\r\n<link rel=\"alternate\" href=\"https://ocr.oldfish.cn/tr/Article.aspx?id=81&slug=rnn-sequence-modeling\" hreflang=\"tr\" />\r\n<link rel=\"alternate\" href=\"https://ocr.oldfish.cn/it/Article.aspx?id=81&slug=rnn-sequence-modeling\" hreflang=\"it\" />\r\n<link rel=\"alternate\" href=\"https://ocr.oldfish.cn/gu/Article.aspx?id=81&slug=rnn-sequence-modeling\" hreflang=\"gu\" />\r\n<link rel=\"alternate\" href=\"https://ocr.oldfish.cn/pl/Article.aspx?id=81&slug=rnn-sequence-modeling\" hreflang=\"pl\" />\r\n<link rel=\"alternate\" href=\"https://ocr.oldfish.cn/uk/Article.aspx?id=81&slug=rnn-sequence-modeling\" hreflang=\"uk\" />\r\n<link rel=\"alternate\" href=\"https://ocr.oldfish.cn/ms/Article.aspx?id=81&slug=rnn-sequence-modeling\" hreflang=\"ms\" />\r\n<link rel=\"alternate\" href=\"https://ocr.oldfish.cn/id/Article.aspx?id=81&slug=rnn-sequence-modeling\" hreflang=\"id\" />\r\n<link rel=\"alternate\" href=\"https://ocr.oldfish.cn/ml/Article.aspx?id=81&slug=rnn-sequence-modeling\" hreflang=\"ml\" />\r\n<link rel=\"alternate\" href=\"https://ocr.oldfish.cn/kn/Article.aspx?id=81&slug=rnn-sequence-modeling\" hreflang=\"kn\" />\r\n<link rel=\"alternate\" href=\"https://ocr.oldfish.cn/fa/Article.aspx?id=81&slug=rnn-sequence-modeling\" hreflang=\"fa\" />\r\n<link rel=\"alternate\" href=\"https://ocr.oldfish.cn/nl/Article.aspx?id=81&slug=rnn-sequence-modeling\" hreflang=\"nl\" />\r\n<link rel=\"alternate\" href=\"https://ocr.oldfish.cn/th/Article.aspx?id=81&slug=rnn-sequence-modeling\" hreflang=\"th\" />\r\n<link rel=\"alternate\" href=\"https://ocr.oldfish.cn/sw/Article.aspx?id=81&slug=rnn-sequence-modeling\" hreflang=\"sw\" />\r\n<link rel=\"alternate\" href=\"https://ocr.oldfish.cn/ro/Article.aspx?id=81&slug=rnn-sequence-modeling\" hreflang=\"ro\" />\r\n<link rel=\"alternate\" href=\"https://ocr.oldfish.cn/my/Article.aspx?id=81&slug=rnn-sequence-modeling\" hreflang=\"my\" />\r\n<link rel=\"alternate\" href=\"https://ocr.oldfish.cn/or/Article.aspx?id=81&slug=rnn-sequence-modeling\" hreflang=\"or\" />\r\n<link rel=\"alternate\" href=\"https://ocr.oldfish.cn/he/Article.aspx?id=81&slug=rnn-sequence-modeling\" hreflang=\"he\" />\r\n<link rel=\"alternate\" href=\"https://ocr.oldfish.cn/am/Article.aspx?id=81&slug=rnn-sequence-modeling\" hreflang=\"am\" />\r\n<link rel=\"alternate\" href=\"https://ocr.oldfish.cn/fil/Article.aspx?id=81&slug=rnn-sequence-modeling\" hreflang=\"fil\" />\r\n<link rel=\"alternate\" href=\"https://ocr.oldfish.cn/sv/Article.aspx?id=81&slug=rnn-sequence-modeling\" hreflang=\"sv\" />\r\n<link rel=\"alternate\" href=\"https://ocr.oldfish.cn/el/Article.aspx?id=81&slug=rnn-sequence-modeling\" hreflang=\"el\" />\r\n<link rel=\"alternate\" href=\"https://ocr.oldfish.cn/cs/Article.aspx?id=81&slug=rnn-sequence-modeling\" hreflang=\"cs\" />\r\n<link rel=\"alternate\" href=\"https://ocr.oldfish.cn/hu/Article.aspx?id=81&slug=rnn-sequence-modeling\" hreflang=\"hu\" />\r\n<link rel=\"alternate\" href=\"https://ocr.oldfish.cn/be/Article.aspx?id=81&slug=rnn-sequence-modeling\" hreflang=\"be\" />\r\n<link rel=\"alternate\" href=\"https://ocr.oldfish.cn/si/Article.aspx?id=81&slug=rnn-sequence-modeling\" hreflang=\"si\" />\r\n<link rel=\"alternate\" href=\"https://ocr.oldfish.cn/ne/Article.aspx?id=81&slug=rnn-sequence-modeling\" hreflang=\"ne\" />\r\n<link rel=\"alternate\" href=\"https://ocr.oldfish.cn/km/Article.aspx?id=81&slug=rnn-sequence-modeling\" hreflang=\"km\" />\r\n<link rel=\"alternate\" href=\"https://ocr.oldfish.cn/sk/Article.aspx?id=81&slug=rnn-sequence-modeling\" hreflang=\"sk\" />\r\n<link rel=\"alternate\" href=\"https://ocr.oldfish.cn/bg/Article.aspx?id=81&slug=rnn-sequence-modeling\" hreflang=\"bg\" />\r\n<link rel=\"alternate\" href=\"https://ocr.oldfish.cn/fr-ca/Article.aspx?id=81&slug=rnn-sequence-modeling\" hreflang=\"fr-ca\" />\r\n<link rel=\"alternate\" href=\"https://ocr.oldfish.cn/ha/Article.aspx?id=81&slug=rnn-sequence-modeling\" hreflang=\"ha\" />\r\n<link rel=\"alternate\" href=\"https://ocr.oldfish.cn/yo/Article.aspx?id=81&slug=rnn-sequence-modeling\" hreflang=\"yo\" />\r\n<link rel=\"alternate\" href=\"https://ocr.oldfish.cn/ig/Article.aspx?id=81&slug=rnn-sequence-modeling\" hreflang=\"ig\" />\r\n<link rel=\"alternate\" href=\"https://ocr.oldfish.cn/ku/Article.aspx?id=81&slug=rnn-sequence-modeling\" hreflang=\"ku\" />\r\n<link rel=\"alternate\" href=\"https://ocr.oldfish.cn/rw/Article.aspx?id=81&slug=rnn-sequence-modeling\" hreflang=\"rw\" />\r\n<link rel=\"alternate\" href=\"https://ocr.oldfish.cn/ca/Article.aspx?id=81&slug=rnn-sequence-modeling\" hreflang=\"ca\" />\r\n<link rel=\"alternate\" href=\"https://ocr.oldfish.cn/da/Article.aspx?id=81&slug=rnn-sequence-modeling\" hreflang=\"da\" />\r\n<link rel=\"alternate\" href=\"https://ocr.oldfish.cn/fi/Article.aspx?id=81&slug=rnn-sequence-modeling\" hreflang=\"fi\" />\r\n<link rel=\"alternate\" href=\"https://ocr.oldfish.cn/nb/Article.aspx?id=81&slug=rnn-sequence-modeling\" hreflang=\"nb\" />\r\n<link rel=\"alternate\" href=\"https://ocr.oldfish.cn/hr/Article.aspx?id=81&slug=rnn-sequence-modeling\" hreflang=\"hr\" />\r\n<link rel=\"alternate\" href=\"https://ocr.oldfish.cn/sr-Cyrl/Article.aspx?id=81&slug=rnn-sequence-modeling\" hreflang=\"sr-cyrl\" />\r\n<link rel=\"alternate\" href=\"https://ocr.oldfish.cn/sr-Latn/Article.aspx?id=81&slug=rnn-sequence-modeling\" hreflang=\"sr-latn\" />\r\n<link rel=\"alternate\" href=\"https://ocr.oldfish.cn/sq/Article.aspx?id=81&slug=rnn-sequence-modeling\" hreflang=\"sq\" />\r\n<link rel=\"alternate\" href=\"https://ocr.oldfish.cn/so/Article.aspx?id=81&slug=rnn-sequence-modeling\" hreflang=\"so\" />\r\n<link rel=\"alternate\" href=\"https://ocr.oldfish.cn/zu/Article.aspx?id=81&slug=rnn-sequence-modeling\" hreflang=\"zu\" />\r\n<link rel=\"alternate\" href=\"https://ocr.oldfish.cn/ka/Article.aspx?id=81&slug=rnn-sequence-modeling\" hreflang=\"ka\" />\r\n<meta http-equiv=\"Content-Type\" content=\"text/html; charset=UTF-8\" /><meta http-equiv=\"X-UA-Compatible\" content=\"IE=Edge,chrome=1\" /><meta name=\"renderer\" content=\"webkit\" /><meta name=\"viewport\" content=\"width=device-width, initial-scale=1.0, maximum-scale=5.0\" /><link rel=\"icon\" href=\"/favicon.ico\" type=\"image/x-icon\" /><meta name=\"robots\" content=\"all\" /><link rel=\"preconnect\" href=\"https://scm-file-new-1301864755.cos.ap-beijing.myqcloud.com\" /><link rel=\"preconnect\" href=\"https://cdn.oldfish.cn\" /><link rel=\"dns-prefetch\" href=\"https://hm.baidu.com\" /><link rel=\"dns-prefetch\" href=\"https://cdn.oldfish.cn\" /><link rel=\"preload\" href=\"/site/css/site.css\" as=\"style\" />\r\n<script>function toArray(l){var a=[];for(var i=0;i<(l?l.length:0);i++)a.push(l[i]);return a;}function forEach(l,f){for(var i=0,n=(l?l.length:0);i<n;i++)f(l[i],i);}function hasClass(e,c){if(!e||!c)return false;return(' '+e.className+' ').indexOf(' '+c+' ')>-1;}function addClass(e,c){if(!e||!c||hasClass(e,c))return;e.className=(e.className?e.className+' ':'')+c;}function removeClass(e,c){if(!e||!c)return;var u=' '+e.className+' ';while(u.indexOf(' '+c+' ')>-1){u=u.replace(' '+c+' ',' ');}e.className=u.replace(/^\\s+|\\s+$/g,'');}function toggleClass(e,c){if(!e||!c)return;if(hasClass(e,c))removeClass(e,c);else addClass(e,c);}function matchesSelector(e,s){if(!e||e.nodeType!==1)return false;if(e.matches)return e.matches(s);var p=e.parentNode||document;var m=toArray(p.querySelectorAll(s));for(var i=0;i<m.length;i++)if(m[i]===e)return true;return false;}function closest(e,s){var n=e;while(n&&n.nodeType===1){if(matchesSelector(n,s))return n;n=n.parentNode;}return null;}function safeRemove(e){if(e&&e.parentNode)e.parentNode.removeChild(e);}function qa(s,r){return toArray((r||document).querySelectorAll(s));}function q(s,r){return(r||document).querySelector(s);}function xhrGet(u,o,e){try{var x=new XMLHttpRequest();x.open('GET',u,true);x.onreadystatechange=function(){if(x.readyState===4){if(x.status>=200&&x.status<300)o&&o(x.responseText);else e&&e(x);}};x.send(null);}catch(r){e&&e(r);}}function getScrollY(){return window.pageYOffset||document.documentElement.scrollTop||document.body.scrollTop||0;}function getOrigin(){return window.location.protocol+'//'+window.location.host;}</script>\r\n<script>var isWebP=(function(){try{return document.createElement('canvas').toDataURL('image/webp').indexOf('data:image/webp')===0;}catch(e){return false;}})();document.addEventListener('DOMContentLoaded',function(){var c=document.getElementById('user-login-container');if(c){var u=c.getAttribute('data-url');if(u){xhrGet(u,function(h){c.innerHTML=h;forEach(qa('a',c),function(l){l.style.display='inline-block';l.style.marginLeft='10px';});});}}});</script>\r\n    \n    \n\n    <meta name=\"description\" content=\"OCRにおけるRNN、LSTM、GRUの応用について詳しく説明します。 シーケンスモデリングの原理、勾配問題の解、および双方向RNNの利点の詳細な分析。\" />\n    <meta name=\"keywords\" content=\"RNN、LSTM、GRU、シーケンスモデリング、勾配消失、双方向RNN、アテンションメカニズム、CRNN、OCR、OCRテキスト認識、画像からテキストへ、OCR技術\" />\n    <meta property=\"og:title\" content=\"【ディープラーニングOCRシリーズ・4】リカレントニューラルネットワークとシーケンスモデリング\" />\n    <meta property=\"og:description\" content=\"OCRにおけるRNN、LSTM、GRUの応用について詳しく説明します。 シーケンスモデリングの原理、勾配問題の解、および双方向RNNの利点の詳細な分析。\" />\n    <meta property=\"og:type\" content=\"article\" />\n    <meta property=\"og:url\" content=\"https://ocr.oldfish.cn/Article.aspx?id=81&slug=rnn-sequence-modeling&lang=zh-Hans&_translation_source=true\" />\n    <meta property=\"og:site_name\" content=\"OCRテキスト認識アシスタント\" />\n    <meta name=\"twitter:card\" content=\"summary\" />\n    <meta name=\"twitter:title\" content=\"【ディープラーニングOCRシリーズ・4】リカレントニューラルネットワークとシーケンスモデリング\" />\n    <meta name=\"twitter:description\" content=\"OCRにおけるRNN、LSTM、GRUの応用について詳しく説明します。 シーケンスモデリングの原理、勾配問題の解、および双方向RNNの利点の詳細な分析。\" />\n\n    \n    <script type=\"application/ld+json\">\n    {\n        \"@context\": \"https://schema.org\",\n        \"@type\": \"Article\",\n        \"headline\": \"【[ディープラーニングOCRシリーズ4]リカレントニューラルネットワークとシーケンスモデリング\",\n        \"description\": \"OCRにおけるRNN、LSTM、GRUの応用について詳しく説明します。 配列モデリングの原理、勾配問題の解、双方向RNNの利点の詳細な分析。\",\n        \"publisher\": {\n            \"@type\": \"Organization\",\n            \"name\": \"OCRテキスト認識アシスタントチーム\"\n        },\n        \"datePublished\": \"2025-08-19T06:32:05Z\",\n        \"dateModified\": \"2025-08-19T06:32:05Z\",\n        \"mainEntityOfPage\": {\n            \"@type\": \"WebPage\",\n            \"@id\": \"https://ocr.oldfish.cn/Article.aspx?id=81&slug=rnn-sequence-modeling&lang=zh-Hans&_translation_source=true\"\n        }\n    }\n    </script>\n    \n    <style>\n        .reading-progress { position: fixed; top: 0; left: 0; width: 0%; height: 3px; background: linear-gradient(90deg, #1764ff, #0d47a1); z-index: 9999; transition: width 0.2s ease; box-shadow: 0 1px 3px rgba(23, 100, 255, 0.3); }\n        .home-container.root { overflow: visible !important; }\n        .article-detail-container { background: #f5f7fc; min-height: calc(100vh - 60px); padding: 80px 0 60px; position: relative; overflow: visible; }\n        .article-detail-container .row { margin: 0 -12px; align-items: flex-start !important; min-height: 100vh !important; }\n        .article-detail-container .row > [class*=\"col-\"] { padding: 0 12px; box-sizing: border-box; min-width: 0; }\n        .article-detail-container .row > .col-lg-8, .article-detail-container .row > .col-xl-9 { overflow: hidden; }\n        .article-detail-container .row > .col-lg-4, .article-detail-container .row > .col-xl-3 { overflow: visible !important; height: 100% !important; min-height: 100% !important; position: static !important; display: block !important; transform: none !important; will-change: auto !important; contain: none !important; perspective: none !important; filter: none !important; }\n        .article-detail { background: #fff; border-radius: 8px; padding: 1.5rem; margin-bottom: 2rem; box-shadow: 0 2px 10px rgba(0,0,0,0.1); width: 100%; box-sizing: border-box; }\n        .article-title { font-size: 1.7rem; color: #1a1c1f; line-height: 1.3; margin-bottom: 1.5rem; font-weight: 600; }\n        .article-meta { display: flex; align-items: center; flex-wrap: nowrap; gap: 1.2rem; color: #6c757d; font-size: 0.88rem; margin-bottom: 2rem; padding: 1.2rem 1.4rem; background: linear-gradient(135deg, #f8f9fa 0%, #ffffff 100%); border-radius: 12px; border-left: 4px solid #1764ff; box-shadow: 0 2px 8px rgba(0,0,0,0.06); overflow-x: hidden; transition: all 0.3s ease; }\n        .article-meta:hover { box-shadow: 0 4px 16px rgba(0,0,0,0.1); transform: translateY(-1px); }\n        .article-meta .meta-item { display: flex; align-items: center; gap: 0.25rem; font-weight: 500; color: #495057; white-space: nowrap; flex-shrink: 1; transition: color 0.2s ease; cursor: default; }\n        .article-meta .meta-item:hover { color: #1764ff; }\n        .article-meta .meta-item:hover .meta-icon { transform: scale(1.1); filter: drop-shadow(0 2px 6px rgba(13, 71, 161, 0.25)); }\n        .meta-icon { display: inline-block; font-size: 16px; flex-shrink: 0; vertical-align: middle; transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1); margin-right: 4px; opacity: 0.9; }\n        .meta-item:hover .meta-icon { opacity: 1; }\n        .meta-item:hover { color: #1764ff; }\n        .meta-item { position: relative; }\n        .meta-item::after { content: ''; position: absolute; bottom: -2px; left: 0; width: 0; height: 2px; background: linear-gradient(90deg, #1764ff, #00d4ff); transition: width 0.3s ease; }\n        .meta-item:hover::after { width: 100%; }\n        .article-category, .read-time { display: inline-block; padding: 6px 12px; border-radius: 16px; font-size: 0.8rem; font-weight: 600; }\n        .article-category { background: linear-gradient(135deg, #1764ff, #0d47a1); color: white; text-decoration: none; text-transform: uppercase; letter-spacing: 0.5px; transition: all 0.3s ease; }\n        .article-category:hover { background: linear-gradient(135deg, #0d47a1, #1764ff); color: white; text-decoration: none; transform: translateY(-1px); box-shadow: 0 4px 12px rgba(23, 100, 255, 0.3); }\n        .read-time { background: #e3f2fd; color: #1565c0; border: 1px solid #90caf9; }\n        .article-tags { margin: 20px 0 1.5rem; }\n        .article-tags .tag { display: inline-block; background: #1764ff; color: #fff; padding: 4px 12px; border-radius: 12px; font-size: 0.8rem; margin: 0 8px 4px 0; }\n        .article-summary { background: #e3f2fd; border-left: 4px solid #1764ff; padding: 1rem 1.5rem; margin-bottom: 2rem; border-radius: 0 4px 4px 0; }\n        .article-content { line-height: 1.8; font-size: 16px; color: #333; word-wrap: break-word; }\n        .article-content h1, .article-content h2, .article-content h3, .article-content h4, .article-content h5, .article-content h6 { margin: 2rem 0 1rem; font-weight: 600; line-height: 1.25; color: #1a1c1f; }\n        .article-content h1 { font-size: 2rem; border-bottom: 2px solid #e9ecef; padding-bottom: 0.5rem; }\n        .article-content h2 { font-size: 1.5rem; border-bottom: 1px solid #e9ecef; padding-bottom: 0.3rem; }\n        .article-content h3 { font-size: 1.25rem; }\n        .article-content h4 { font-size: 1.1rem; }\n        .article-content h5, .article-content h6 { font-size: 1rem; }\n        .article-content ul, .article-content ol { margin: 1rem 0; padding-left: 2rem; }\n        .article-content ul ul, .article-content ol ol, .article-content ul ol, .article-content ol ul { margin: 0.5rem 0; }\n        .article-content blockquote { margin: 1rem 0; padding: 0.5rem 1rem; border-left: 4px solid #1764ff; background-color: #f8f9fa; color: #6c757d; font-style: italic; }\n        .article-content pre { background-color: #f8f9fa; border: 1px solid #e9ecef; border-radius: 6px; padding: 1rem; margin: 1rem 0; overflow-x: auto; font-family: 'Courier New', Courier, monospace; font-size: 0.9em; line-height: 1.4; }\n        .article-content pre code { background: none; border: none; padding: 0; color: inherit; }\n        .article-content p { margin: 1rem 0; }\n        .article-content p:first-child { margin-top: 0; }\n        .article-content p:last-child { margin-bottom: 0; }\n        .article-footer { margin-top: 4rem; padding: 2.5rem; background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%); border-radius: 12px; border: 1px solid #e9ecef; position: relative; overflow: hidden; }\n        .article-footer::before { content: ''; position: absolute; top: 0; left: 0; right: 0; height: 3px; background: linear-gradient(90deg, #1764ff, #17a2b8, #28a745); }\n        .article-share h6 { color: #495057; font-weight: 600; margin-bottom: 1.5rem; font-size: 16px; }\n        .share-buttons { display: flex; gap: 12px; flex-wrap: wrap; }\n        .share-buttons .btn { padding: 10px 20px; border-radius: 25px; font-weight: 500; font-size: 14px; border: none; transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1); box-shadow: 0 2px 8px rgba(0,0,0,0.1); display: inline-flex; align-items: center; gap: 8px; text-decoration: none; }\n        .btn-weibo { background: linear-gradient(135deg, #e6162d, #c41230); color: white; }\n        .btn-copy { background: #1764ff; color: white; }\n        .btn-print { background: linear-gradient(135deg, #17a2b8, #138496); color: white; }\n        .btn-weibo:hover { background: linear-gradient(135deg, #c41230, #a00e26); box-shadow: 0 4px 16px rgba(230, 22, 45, 0.3); }\n        .btn-copy:hover { background: #0d47a1; box-shadow: 0 4px 16px rgba(23, 100, 255, 0.3); }\n        .btn-print:hover { background: linear-gradient(135deg, #138496, #117a8b); box-shadow: 0 4px 16px rgba(23, 162, 184, 0.3); }\n        .btn-weibo:hover, .btn-copy:hover, .btn-print:hover { transform: translateY(-2px); color: white; }\n        .article-actions { display: flex; justify-content: flex-end; align-items: center; }\n        .related-articles { margin-top: 3rem; }\n        .related-article-item { background: #fff; border-radius: 8px; padding: 1.5rem; margin-bottom: 1rem; box-shadow: 0 2px 8px rgba(0,0,0,0.1); transition: transform 0.3s ease; }\n        .related-article-item:hover { transform: translateY(-2px); }\n        .sidebar-card { background: linear-gradient(135deg, rgba(255,255,255,0.9) 0%, rgba(255,255,255,0.8) 100%); backdrop-filter: blur(20px); border: 1px solid rgba(255,255,255,0.3); border-radius: 16px; padding: 1rem; margin-bottom: 0.5rem; box-shadow: 0 4px 20px rgba(0,0,0,0.08), inset 0 1px 0 rgba(255,255,255,0.2); overflow: visible; box-sizing: border-box; width: 100%; z-index: 10; }\n        .sidebar-card.fixed { box-shadow: 0 8px 32px rgba(0,0,0,0.12); }\n        .sidebar-card::before { content: ''; position: absolute; top: 0; left: 0; right: 0; height: 3px; background: linear-gradient(90deg, #00d4ff, #1764ff, #9c27b0); border-radius: 20px 20px 0 0; }\n        .sidebar-card h5 { color: #1a202c; padding-bottom: 0.8rem; border-bottom: 2px solid rgba(23, 100, 255, 0.2); font-weight: 700; font-size: 1.1rem; }\n        .sidebar { min-height: 100vh; position: relative !important; display: block !important; flex-direction: column !important; gap: 1rem; }\n        .toc-card { flex: 0 1 auto; max-height: 60vh; overflow-y: auto; }\n        .sidebar-card:not(.toc-card) { flex: 1 1 auto; overflow: visible; }\n        .recommended-item { padding: 0.75rem 0; border-bottom: 1px solid rgba(0,0,0,0.05); }\n        .recommended-item:last-child { border-bottom: none; }\n        .recommended-item h6 { margin: 0 0 0.5rem 0; font-size: 0.9rem; line-height: 1.4; }\n        .recommended-item h6 a { color: #1a1c1f; text-decoration: none; transition: color 0.3s ease; }\n        .recommended-item h6 a:hover { color: #1764ff; }\n        .recommended-item .text-muted { font-size: 0.75rem; margin: 0; }\n        .toc-list { list-style: none; padding: 0; margin: 0; max-height: none; overflow-y: visible; overflow-x: hidden; }\n        .toc-list::-webkit-scrollbar { width: 4px; }\n        .toc-list::-webkit-scrollbar-track { background: rgba(0,0,0,0.1); border-radius: 2px; }\n        .toc-list::-webkit-scrollbar-thumb { background: linear-gradient(135deg, #00d4ff, #1764ff); border-radius: 2px; }\n        .toc-list::-webkit-scrollbar-thumb:hover { background: linear-gradient(135deg, #1764ff, #0d47a1); }\n        .toc-list a { color: #4a5568; text-decoration: none; font-size: 13px; font-weight: 500; display: block; padding: 8px 12px; border-radius: 8px; transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1); position: relative; margin-bottom: 4px; border: 1px solid transparent; }\n        .toc-list a::before { content: ''; position: absolute; left: 0; top: 0; bottom: 0; width: 3px; background: linear-gradient(135deg, #00d4ff, #1764ff); border-radius: 0 12px 12px 0; transform: scaleY(0); transition: transform 0.3s ease; }\n        .toc-list a:hover { color: #1764ff; background: linear-gradient(135deg, rgba(0, 212, 255, 0.1), rgba(23, 100, 255, 0.1)); border-color: rgba(23, 100, 255, 0.2); transform: translateX(8px); box-shadow: 0 4px 12px rgba(0, 212, 255, 0.2); }\n        .toc-list a:hover::before, .toc-list a.active::before { transform: scaleY(1); }\n        .toc-list a.active { color: #1764ff; background: linear-gradient(135deg, rgba(0, 212, 255, 0.15), rgba(23, 100, 255, 0.15)); font-weight: 700; border-color: rgba(23, 100, 255, 0.3); transform: translateX(4px); box-shadow: 0 6px 16px rgba(0, 212, 255, 0.25); }\n        .toc-number { color: #1764ff; font-weight: 600; margin-right: 0.5rem; font-size: 0.9em; }\n        .toc-text { flex: 1; }\n        .toc-level-1 { font-weight: 700; font-size: 15px; }\n        .toc-level-2 { font-size: 14px; }\n        .toc-level-3 { font-size: 13px; opacity: 0.9; }\n        .toc-level-4, .toc-level-5, .toc-level-6 { font-size: 12px; opacity: 0.8; }\n        .article-content .formatted-list { margin: 1.5rem 0; padding-left: 0; list-style: none; }\n        .article-content .formatted-list.ordered { counter-reset: list-counter; }\n        .article-content .formatted-list.ordered li { counter-increment: list-counter; position: relative; margin: 1rem 0; padding: 1rem 1rem 1rem 3rem; background: linear-gradient(135deg, rgba(23, 100, 255, 0.03), rgba(0, 212, 255, 0.03)); border-left: 4px solid #1764ff; border-radius: 0 8px 8px 0; transition: all 0.3s ease; }\n        .article-content .formatted-list.ordered li::before { content: counter(list-counter); position: absolute; left: 0.8rem; top: 1rem; background: linear-gradient(135deg, #1764ff, #00d4ff); color: white; width: 1.5rem; height: 1.5rem; border-radius: 50%; display: flex; align-items: center; justify-content: center; font-weight: 600; font-size: 0.9rem; }\n        .article-content .formatted-list.ordered li:hover { background: linear-gradient(135deg, rgba(23, 100, 255, 0.06), rgba(0, 212, 255, 0.06)); transform: translateX(4px); box-shadow: 0 2px 8px rgba(23, 100, 255, 0.15); }\n        .article-content .formatted-list.unordered li { position: relative; margin: 0.8rem 0; padding: 0.8rem 1rem 0.8rem 2.5rem; background: linear-gradient(135deg, rgba(40, 167, 69, 0.03), rgba(25, 135, 84, 0.03)); border-left: 4px solid #28a745; border-radius: 0 6px 6px 0; transition: all 0.3s ease; }\n        .article-content .formatted-list.unordered li::before { content: \"•\"; position: absolute; left: 1rem; top: 0.8rem; color: #28a745; font-size: 1.2rem; font-weight: bold; }\n        .article-content .formatted-list.unordered li:hover { background: linear-gradient(135deg, rgba(40, 167, 69, 0.06), rgba(25, 135, 84, 0.06)); transform: translateX(4px); box-shadow: 0 2px 8px rgba(40, 167, 69, 0.15); }\n        .article-content .list-title { color: #1764ff; font-weight: 600; display: inline-block; margin-bottom: 0.3rem; }\n        .article-content .list-content { color: #2c3e50; line-height: 1.6; display: block; }\n        .article-content .inline-code { background: #f8f9fa; border: 1px solid #e9ecef; border-radius: 4px; padding: 2px 6px; font-family: 'Consolas', 'Monaco', 'Courier New', monospace; font-size: 0.9em; color: #e83e8c; }\n        .article-content .label-item { margin: 0.8rem 0; }\n        .article-content .label { color: #1764ff; font-weight: 600; }\n        .article-content .section-label { color: #2c3e50; font-weight: 600; margin: 1rem 0 0.5rem 0; font-size: 1.1em; border-left: 4px solid #1764ff; padding-left: 0.8rem; }\n        .article-content p, .article-content .content-paragraph { margin: 1.2rem 0; line-height: 1.8; text-align: justify; color: #2c3e50; font-size: 1rem; }\n        .article-content .content-paragraph { text-indent: 0; margin: 1rem 0; }\n        .article-content .indent-paragraph { text-indent: 2em; margin: 1.2rem 0; }\n        .article-content .text-indent-1 { text-indent: 2em; display: block; }\n        .article-content .text-indent-2 { text-indent: 4em; display: block; }\n        .article-content .text-indent-3 { text-indent: 6em; display: block; }\n        .article-content .network-config { background: linear-gradient(135deg, #f8f9fa, #e9ecef); border: 1px solid #dee2e6; border-radius: 6px; padding: 0.8rem 1rem; margin: 0.5rem 0; font-family: 'Consolas', 'Monaco', 'Courier New', monospace; font-size: 0.9rem; box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05); }\n        .article-content .config-label { color: #1764ff; font-weight: 600; }\n        .article-content .config-desc { color: #495057; }\n        .article-content .algorithm-step { background: linear-gradient(135deg, rgba(23, 100, 255, 0.05), rgba(0, 212, 255, 0.05)); border-left: 4px solid #1764ff; padding: 0.8rem 1rem; margin: 0.5rem 0; border-radius: 0 6px 6px 0; transition: all 0.3s ease; }\n        .article-content .step-number { color: #1764ff; font-weight: 700; margin-right: 0.5rem; }\n        .article-content .step-desc { color: #2c3e50; line-height: 1.5; }\n        .article-content table { width: 100%; border-collapse: collapse; margin: 1.5rem 0; background: white; border-radius: 8px; overflow: hidden; box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1); }\n        .article-content table th { background: linear-gradient(135deg, #1764ff, #00d4ff); color: white; padding: 1rem; text-align: left; font-weight: 600; }\n        .article-content table td { padding: 0.8rem 1rem; border-bottom: 1px solid #e9ecef; }\n        .article-content table tr:hover { background: rgba(23, 100, 255, 0.05); }\n        .article-content strong { color: #1764ff; font-weight: 600; }\n        .article-content em { color: #6c757d; font-style: italic; }\n        .article-content a { color: #1764ff; text-decoration: none; border-bottom: 1px solid transparent; transition: all 0.3s ease; }\n        .article-content a:hover { border-bottom-color: #1764ff; color: #0056b3; }\n        .article-content .definition-item { background: linear-gradient(135deg, rgba(40, 167, 69, 0.05), rgba(25, 135, 84, 0.05)); border-left: 4px solid #28a745; padding: 1rem; margin: 1rem 0; border-radius: 0 8px 8px 0; }\n        .article-content .definition-label { color: #28a745; font-weight: 600; display: block; margin-bottom: 0.5rem; }\n        .article-content .definition-content { color: #2c3e50; line-height: 1.6; }\n        .article-content .note-item { background: linear-gradient(135deg, rgba(255, 193, 7, 0.1), rgba(255, 193, 7, 0.05)); border: 1px solid rgba(255, 193, 7, 0.3); border-radius: 8px; padding: 1rem; margin: 1rem 0; position: relative; }\n        .article-content .note-item::before { content: \"💡\"; position: absolute; left: 1rem; top: 1rem; font-size: 1.2em; }\n        .article-content .note-label { color: #856404; font-weight: 600; margin-left: 2rem; }\n        .article-content .note-content { color: #2c3e50; line-height: 1.6; margin-left: 2rem; display: block; margin-top: 0.3rem; }\n        .article-content .math-formula { background: #f8f9fa; border: 1px solid #e9ecef; border-radius: 6px; padding: 1rem; margin: 1rem 0; text-align: center; font-family: 'Times New Roman', serif; font-size: 1.1em; color: #2c3e50; }\n        @media (min-width: 1400px) { .article-content { font-size: 17px; line-height: 1.8; } .article-detail { padding: 2.5rem; } }\n        @media (max-width: 1199px) and (min-width: 992px) { .article-detail { padding: 1.5rem; } .sidebar-card { padding: 1rem; } .toc-list a { font-size: 12px; padding: 6px 8px; } }\n        @media (max-width: 768px) {\n            .article-detail-container { padding: 60px 15px; }\n            .article-content .network-config, .article-content .algorithm-step, .article-content .definition-item, .article-content .note-item { margin: 0.5rem -1rem; border-radius: 0; }\n            .article-content .note-item::before { left: 0.5rem; }\n            .article-content .note-label, .article-content .note-content { margin-left: 1.5rem; }\n            .article-detail { padding: 1.5rem; }\n            .article-title { font-size: 1.6rem; line-height: 1.3; }\n            .article-meta { flex-wrap: wrap; gap: 1rem; padding: 1rem; font-size: 0.8rem; }\n            .article-meta .meta-item { flex-shrink: 1; }\n            .meta-icon { font-size: 14px; margin-right: 3px; }\n            .article-content { font-size: 15px; }\n            .article-content h1 { font-size: 1.6rem; }\n            .article-content h2 { font-size: 1.4rem; }\n            .article-content h3 { font-size: 1.2rem; }\n            .article-content h4 { font-size: 1.1rem; }\n            .article-content pre { padding: 0.75rem; font-size: 0.85em; overflow-x: auto; }\n            .article-content code { font-size: 0.85em; padding: 1px 4px; }\n            .sidebar { margin-top: 2rem; }\n            .sidebar-card { position: static; }\n            .toc-card { max-height: none; overflow-y: visible; }\n            .share-buttons { flex-direction: column; gap: 0.5rem; }\n            .share-buttons .btn { width: 100%; justify-content: center; }\n        }\n        @media print { body * { visibility: hidden; } .article-detail, .article-detail * { visibility: visible; } .article-detail { position: absolute; left: 0; top: 0; width: 100%; background: white; box-shadow: none; border: none; margin: 0; padding: 20px; } .article-meta { border: none; background: none; padding: 0; margin-bottom: 20px; } .article-footer, .reading-progress, .back-to-top { display: none !important; } .article-content { font-size: 12pt; line-height: 1.6; } .article-content h1, .article-content h2, .article-content h3, .article-content h4, .article-content h5, .article-content h6 { page-break-after: avoid; margin-top: 20pt; margin-bottom: 10pt; } .article-content pre, .article-content blockquote { page-break-inside: avoid; } }\n    </style>\n\n<script type=\"application/ld+json\">{\"@context\":\"https://schema.org\",\"@type\":\"BreadcrumbList\",\"itemListElement\":[{\"@type\":\"ListItem\",\"position\":1,\"name\":\"家\",\"item\":\"https://ocr.oldfish.cn/default.aspx\"},{\"@type\":\"ListItem\",\"position\":2,\"name\":\"技術記事\",\"item\":\"https://ocr.oldfish.cn/Articles.aspx\"},{\"@type\":\"ListItem\",\"position\":3,\"name\":\"記事の詳細\",\"item\":\"https://ocr.oldfish.cn/Article.aspx?id=81&slug=rnn-sequence-modeling&lang=zh-Hans&_translation_source=true\"}]}</script>\n<link rel=\"stylesheet\" href=\"/site/css/site.css\" type=\"text/css\" /><title>【ディープラーニングOCRシリーズ・4】リカレントニューラルネットワークとシーケンスモデリング</title><meta http-equiv=\"Content-Language\" content=\"ja\" />\n</head>\r\n<body>\r\n    <div class=\"home-container root\">\r\n        <header class=\"v4_header_pc\">\r\n            <div class=\"header_pc_left\">\r\n                <a href=\"Default.aspx\" title=\"ホーム | AIインテリジェントテキスト認識\" class=\"pc-logo-wrap ml-4 mr-5\">\r\n                    <picture>\r\n                        <source srcset=\"site/image/logo-s.png.webp\" type=\"image/webp\" />\r\n                        <img src=\"site/image/logo-s.png\" class=\"pc-logo\" style=\"height: 100%\" alt=\"OCR テキスト認識アシスタント公式ウェブサイトロゴ - AI インテリジェント テキスト認識プラットフォーム\" />\r\n                    </picture>\r\n                    <span style=\"font-size: 18px; font-weight: bold; color: #333;\">OCRテキスト認識アシスタント</span>\r\n                </a>\r\n                <ul class=\"top-nav\" role=\"navigation\" aria-label=\"メインナビゲーション\">\r\n                    <li><a class=\"color-default\" href=\"Default.aspx\" title=\"OCRテキスト認識アシスタントホームページ\">家</a></li>\r\n                    <li><a class=\"color-default\" data-sub=\"product-sub-nav\" href=\"Detail.aspx\" title=\"OCR製品機能紹介\">製品の特徴:</a><div class=\"triangle\"></div>\r\n                    </li>\r\n                    <li><a class=\"color-default\" data-sub=\"experience-sub-nav\" href=\"Ocr.aspx\" title=\"OCR機能をオンラインで体験\">オンライン体験</a><div class=\"triangle\"></div>\r\n                    </li>\r\n                    <li><a class=\"color-default\" data-sub=\"vip-sub-nav\" href=\"Version.aspx\" title=\"OCR会員アップグレードサービス\">メンバーシップのアップグレード</a><div class=\"triangle\"></div>\r\n                    </li>\r\n                    <li><a class=\"color-default\" href=\"Download.aspx\" title=\"OCR Text Recognition Assistantを無料でダウンロード\">無料ダウンロード</a></li>\r\n                    <li><a class=\"color-default\" data-sub=\"articles-sub-nav\" href=\"Articles.aspx\" title=\"OCRの技術記事と知識の共有\">テクノロジーの共有</a><div class=\"triangle\"></div>\r\n                    </li>\r\n                    <li><a class=\"color-default\" data-sub=\"support-sub-nav\" href=\"FAQ.aspx\" title=\"OCR の使用に関するヘルプとテクニカル サポート\">ヘルプセンター</a><div class=\"triangle\"></div>\r\n                    </li>\r\n                </ul>\r\n                <div class=\"product-con\">\r\n                    <section id=\"product-sub-nav\" class=\"pls-nav-dropdown\" style=\"visibility: hidden; height: 0px; display: none;\">\r\n                        <div class=\"d-flex nav-drown-con\" style=\"height: 0px; transition: transform 0.2s ease-in-out 0s,opacity 0.2s ease-in-out 0s; opacity: 0; transform: translate(0px, -100%);\">\r\n                            <div class=\"prdocu-sub-left\">\r\n                                <div class=\"mr-auto\">\r\n                                    <picture>\r\n                                        <source class=\"p-icon\" srcset=\"site/image/icon/p_1.png.webp\" type=\"image/webp\" alt=\"\" aria-hidden=\"true\" />\r\n                                        <img class=\"p-icon\" src=\"site/image/icon/p_1.png\" alt=\"OCR製品機能アイコン\" aria-hidden=\"true\" loading=\"lazy\" />\r\n                                    </picture>\r\n                                    <div class=\"mt-4\">\r\n                                        <h3 class=\"h6\">OCRテキスト認識アシスタント</h3>\r\n                                        <p class=\"color-gray fn14 mt-3 sub-left-desc\">効率の向上、コストの削減、価値の創造</p>\r\n                                        <p class=\"color-gray fn14 mt-3 sub-left-desc\">&nbsp;</p>\r\n                                        <p class=\"color-gray fn14 mt-3 sub-left-desc\">インテリジェントな認識、高速処理、正確な出力</p>\r\n                                        <p class=\"color-gray fn14 mt-3 sub-left-desc\">テキストから表へ、数式から翻訳へ</p>\r\n                                        <p class=\"color-gray fn14 mt-3 sub-left-desc\">すべてのワープロをとても簡単に</p>\r\n                                        <a class=\"link block disa fn14 contact-business shake\" href=\"Detail.aspx\">機能について学ぶ<span class=\"iconfont4 icon-xiangzuo gt\"></span></a>\r\n                                    </div>\r\n                                </div>\r\n                            </div>\r\n                            <div class=\"bg-white prdocu-sub-right\">\r\n                                <div>\r\n                                    <h2 class=\"text-base-color h6\">製品の特徴:</h2>\r\n                                    <div class=\"mt-4 d-flex flex-wrap\">\r\n                                        <a href=\"Detail.aspx\" class=\"pro-item color-default\" title=\"OCRアシスタントのコア機能の詳細を確認する\">\r\n                                            <div class=\"letter-wrap\">\r\n                                                <h3 class=\"h6\">コア機能:</h3>\r\n                                                <span class=\"color-gray fn14\">98%+ の認識率を持つ OCR Assistant のコア機能と技術的利点の詳細をご覧ください</span>\r\n                                            </div>\r\n                                        </a>\r\n                                        <a href=\"Version.aspx\" class=\"pro-item color-default\" title=\"OCR アシスタントのバージョンの違いを比較する\">\r\n                                            <div class=\"letter-wrap\">\r\n                                                <h3 class=\"h6\">バージョン比較</h3>\r\n                                                <span class=\"color-gray fn14\">無料版、個人版、プロフェッショナル版、アルティメット版の機能の違いを詳しく比較</span>\r\n                                            </div>\r\n                                        </a>\r\n                                        <a href=\"FAQ.aspx\" class=\"pro-item color-default\" title=\"OCRアシスタントのFAQを確認する\">\r\n                                            <div class=\"letter-wrap\">\r\n                                                <h3 class=\"h6\">製品Q&A</h3>\r\n                                                <span class=\"color-gray fn14\">製品の機能や使用方法、よくある質問への詳細な回答をすばやく知ることができます</span>\r\n                                            </div>\r\n                                        </a>\r\n                                        <a href=\"Download.aspx\" class=\"pro-item color-default\" title=\"OCR Text Recognition Assistantを無料でダウンロード\">\r\n                                            <div class=\"letter-wrap\">\r\n                                                <h3 class=\"h6\">無料で試す</h3>\r\n                                                <span class=\"color-gray fn14\">今すぐOCRアシスタントをダウンロードしてインストールし、強力なテキスト認識機能を無料で体験してください</span>\r\n                                            </div>\r\n                                        </a>\r\n                                        <div style=\"width: 100%; margin: 15px 0;\">\r\n                                            <h4 class=\"h6\" style=\"color: #007cfa; margin-bottom: 10px;\">オンラインOCR認識</h4>\r\n                                        </div>\r\n                                        <a href=\"Ocr.aspx?type=text_recognize\" class=\"pro-item color-default\" title=\"ユニバーサルテキスト認識をオンラインで体験\">\r\n                                            <div class=\"letter-wrap\">\r\n                                                <h3 class=\"h6\">ユニバーサル文字認識</h3>\r\n                                                <span class=\"color-gray fn14\">多言語の高精度テキストをインテリジェントに抽出し、印刷およびマルチシーンの複雑な画像認識をサポート</span>\r\n                                            </div>\r\n                                        </a>\r\n                                        <a href=\"Ocr.aspx?type=table\" class=\"pro-item color-default\">\r\n                                            <div class=\"letter-wrap\">\r\n                                                <h3 class=\"h6\">ユニバーサルテーブル識別</h3>\r\n                                                <span class=\"color-gray fn14\">表の画像からExcelファイルへのインテリジェントな変換、複雑な表構造と結合されたセルの自動処理</span>\r\n                                            </div>\r\n                                        </a>\r\n                                        <a href=\"Ocr.aspx?type=handwritten_ocr\" class=\"pro-item color-default\">\r\n                                            <div class=\"letter-wrap\">\r\n                                                <h3 class=\"h6\">手書き認識</h3>\r\n                                                <span class=\"color-gray fn14\">中国語と英語の手書きコンテンツをインテリジェントに認識し、教室のメモ、医療記録、その他のシナリオをサポートします</span>\r\n                                            </div>\r\n                                        </a>\r\n                                        <a href=\"Ocr.aspx?type=pdf2word\" class=\"pro-item color-default\">\r\n                                            <div class=\"letter-wrap\">\r\n                                                <h3 class=\"h6\">PDFからWordへ</h3>\r\n                                                <span class=\"color-gray fn14\">PDFドキュメントはWord形式にすばやく変換され、元のレイアウトとグラフィックレイアウトを完全に保持します</span>\r\n                                            </div>\r\n                                        </a>\r\n                                    </div>\r\n                                </div>\r\n                            </div>\r\n                        </div>\r\n                    </section>\r\n                    <section id=\"experience-sub-nav\" class=\"pls-nav-dropdown solution-part\" style=\"visibility: hidden; height: 0px; display: none;\">\r\n                        <div class=\"d-flex nav-drown-con\" style=\"height: 0px; transition: transform 0.2s ease-in-out 0s,opacity 0.2s ease-in-out 0s; opacity: 0; transform: translate(0px, -100%);\">\r\n                            <div class=\"prdocu-sub-left\">\r\n                                <div class=\"mr-auto\">\r\n                                    <picture>\r\n                                        <source srcset=\"site/image/icon/p_4.png.webp\" type=\"image/webp\" />\r\n                                        <img class=\"p-icon\" src=\"site/image/icon/p_4.png\" alt=\"オンライン OCR エクスペリエンス センター アイコン\" aria-hidden=\"true\" loading=\"lazy\" />\r\n                                    </picture>\r\n                                    <div class=\"mt-4\">\r\n                                        <h3 class=\"h6\">OCRテキスト認識アシスタント</h3>\r\n                                        <p class=\"color-gray fn14 mt-3 sub-left-desc\">テキスト、表、数式、ドキュメント、翻訳</p>\r\n                                        <p class=\"color-gray fn14 mt-3 sub-left-desc\">&nbsp;</p>\r\n                                        <p class=\"color-gray fn14 mt-3 sub-left-desc\">ワープロのすべてのニーズを3つのステップで完了します</p>\r\n                                        <p class=\"color-gray fn14 mt-3 sub-left-desc\">スクリーンショット → → アプリを特定する</p>\r\n                                        <p class=\"color-gray fn14 mt-3 sub-left-desc\">作業効率を300%向上</p>\r\n                                        <a class=\"link block disa fn14 contact-business shake\" href=\"Ocr.aspx\">今すぐ試す<span class=\"iconfont4 icon-xiangzuo gt\"></span></a>\r\n                                    </div>\r\n                                </div>\r\n                            </div>\r\n                            <div class=\"bg-white prdocu-sub-right\">\r\n                                <div>\r\n                                    <div class=\"mt-4 d-flex flex-wrap\">\r\n                                        <div style=\"width: 100%; margin-bottom: 15px;\">\r\n                                            <h4 class=\"h6\" style=\"color: #007cfa; margin-bottom: 10px;\">OCR機能体験</h4>\r\n                                        </div>\r\n                                        <a href=\"Ocr.aspx?type=index\" class=\"pro-item color-default\">\r\n                                            <div class=\"letter-wrap\">\r\n                                                <h3 class=\"h6\">フル機能</h3>\r\n                                                <span class=\"color-gray fn14\">すべてのOCRスマート機能を1か所で体験して、ニーズに最適なソリューションをすばやく見つけます</span>\r\n                                            </div>\r\n                                        </a>\r\n                                        <a href=\"Ocr.aspx?type=text_recognize\" class=\"pro-item color-default\">\r\n                                            <div class=\"letter-wrap\">\r\n                                                <h3 class=\"h6\">ユニバーサル文字認識</h3>\r\n                                                <span class=\"color-gray fn14\">多言語の高精度テキストをインテリジェントに抽出し、印刷およびマルチシーンの複雑な画像認識をサポート</span>\r\n                                            </div>\r\n                                        </a>\r\n                                        <a href=\"Ocr.aspx?type=table\" class=\"pro-item color-default\">\r\n                                            <div class=\"letter-wrap\">\r\n                                                <h3 class=\"h6\">ユニバーサルテーブル識別</h3>\r\n                                                <span class=\"color-gray fn14\">表の画像からExcelファイルへのインテリジェントな変換、複雑な表構造と結合されたセルの自動処理</span>\r\n                                            </div>\r\n                                        </a>\r\n                                        <a href=\"Ocr.aspx?type=handwritten_ocr\" class=\"pro-item color-default\">\r\n                                            <div class=\"letter-wrap\">\r\n                                                <h3 class=\"h6\">手書き認識</h3>\r\n                                                <span class=\"color-gray fn14\">中国語と英語の手書きコンテンツをインテリジェントに認識し、教室のメモ、医療記録、その他のシナリオをサポートします</span>\r\n                                            </div>\r\n                                        </a>\r\n                                        <a href=\"Ocr.aspx?type=pdf2word\" class=\"pro-item color-default\">\r\n                                            <div class=\"letter-wrap\">\r\n                                                <h3 class=\"h6\">PDFからWordへ</h3>\r\n                                                <span class=\"color-gray fn14\">PDFドキュメントはWord形式にすばやく変換され、元のレイアウトとグラフィックレイアウトを完全に保持します</span>\r\n                                            </div>\r\n                                        </a>\r\n                                        <a href=\"Ocr.aspx?type=pdf2markdown\" class=\"pro-item color-default\">\r\n                                            <div class=\"letter-wrap\">\r\n                                                <h3 class=\"h6\">PDFからマークダウンへ</h3>\r\n                                                <span class=\"color-gray fn14\">PDFドキュメントはインテリジェントにMD形式に変換され、コードブロックとテキスト構造は処理のために自動的に最適化されます</span>\r\n                                            </div>\r\n                                        </a>\r\n                                        <div style=\"width: 100%; margin: 15px 0;\">\r\n                                            <h4 class=\"h6\" style=\"color: #007cfa; margin-bottom: 10px;\">文書処理ツール</h4>\r\n                                        </div>\r\n                                        <a href=\"Ocr.aspx?type=word2pdf\" class=\"pro-item color-default\">\r\n                                            <div class=\"letter-wrap\">\r\n                                                <h3 class=\"h6\">Word から PDF へ</h3>\r\n                                                <span class=\"color-gray fn14\">Word 文書はワンクリックで PDF に変換され、元の形式を完全に保持し、アーカイブや公式文書の共有に適しています</span>\r\n                                            </div>\r\n                                        </a>\r\n                                        <a href=\"Ocr.aspx?type=word2jpg\" class=\"pro-item color-default\">\r\n                                            <div class=\"letter-wrap\">\r\n                                                <h3 class=\"h6\">Word から画像へ</h3>\r\n                                                <span class=\"color-gray fn14\">Word 文書の JPG 画像へのインテリジェントな変換、複数ページの処理のサポート、ソーシャル メディアでの共有が簡単</span>\r\n                                            </div>\r\n                                        </a>\r\n                                        <a href=\"Ocr.aspx?type=pdf2jpg\" class=\"pro-item color-default\">\r\n                                            <div class=\"letter-wrap\">\r\n                                                <h3 class=\"h6\">PDFから画像へ</h3>\r\n                                                <span class=\"color-gray fn14\">PDFドキュメントを高解像度のJPG画像に変換し、バッチ処理とカスタム解像度をサポートします</span>\r\n                                            </div>\r\n                                        </a>\r\n                                        <a href=\"Ocr.aspx?type=image2pdf\" class=\"pro-item color-default\">\r\n                                            <div class=\"letter-wrap\">\r\n                                                <h3 class=\"h6\">画像からPDFへ</h3>\r\n                                                <span class=\"color-gray fn14\">複数の画像をPDFドキュメントに結合し、並べ替えとページ設定をサポート</span>\r\n                                            </div>\r\n                                        </a>\r\n                                        <div style=\"width: 100%; margin: 15px 0;\">\r\n                                            <h4 class=\"h6\" style=\"color: #007cfa; margin-bottom: 10px;\">開発者ツール</h4>\r\n                                        </div>\r\n                                        <a href=\"Tool.aspx?type=Json\" class=\"pro-item color-default\">\r\n                                            <div class=\"letter-wrap\">\r\n                                                <h3 class=\"h6\">JSON フォーマット</h3>\r\n                                                <span class=\"color-gray fn14\">JSON コード構造をインテリジェントに美化し、圧縮と拡張をサポートし、開発とデバッグを容易にします。</span>\r\n                                            </div>\r\n                                        </a>\r\n                                        <a href=\"Tool.aspx?type=Regex\" class=\"pro-item color-default\">\r\n                                            <div class=\"letter-wrap\">\r\n                                                <h3 class=\"h6\">正規表現</h3>\r\n                                                <span class=\"color-gray fn14\">一般的なパターンの組み込みライブラリを使用して、正規表現の一致効果をリアルタイムで検証します</span>\r\n                                            </div>\r\n                                        </a>\r\n                                        <a href=\"Tool.aspx?type=Encode\" class=\"pro-item color-default\">\r\n                                            <div class=\"letter-wrap\">\r\n                                                <h3 class=\"h6\">テキストエンコーディング変換</h3>\r\n                                                <span class=\"color-gray fn14\">Base64、URL、Unicode などの複数のエンコード形式の変換をサポートします</span>\r\n                                            </div>\r\n                                        </a>\r\n                                        <a href=\"Tool.aspx?type=Diff\" class=\"pro-item color-default\">\r\n                                            <div class=\"letter-wrap\">\r\n                                                <h3 class=\"h6\">テキストの一致と結合</h3>\r\n                                                <span class=\"color-gray fn14\">テキストの違いを強調表示し、行ごとの比較とインテリジェントな結合をサポートします</span>\r\n                                            </div>\r\n                                        </a>\r\n                                        <a href=\"Tool.aspx?type=Color\" class=\"pro-item color-default\">\r\n                                            <div class=\"letter-wrap\">\r\n                                                <h3 class=\"h6\">カラーツール</h3>\r\n                                                <span class=\"color-gray fn14\">RGB/HEX カラー変換、オンライン カラー ピッカー、フロントエンド開発に必須のツール</span>\r\n                                            </div>\r\n                                        </a>\r\n                                        <a href=\"Tool.aspx?type=Count\" class=\"pro-item color-default\">\r\n                                            <div class=\"letter-wrap\">\r\n                                                <h3 class=\"h6\">単語数</h3>\r\n                                                <span class=\"color-gray fn14\">文字、語彙、段落をインテリジェントにカウントし、テキストレイアウトを自動的に最適化します</span>\r\n                                            </div>\r\n                                        </a>\r\n                                        <a href=\"Tool.aspx?type=Timespan\" class=\"pro-item color-default\">\r\n                                            <div class=\"letter-wrap\">\r\n                                                <h3 class=\"h6\">タイムスタンプ変換</h3>\r\n                                                <span class=\"color-gray fn14\">時刻はUnixタイムスタンプとの間で変換され、複数の形式とタイムゾーン設定がサポートされます</span>\r\n                                            </div>\r\n                                        </a>\r\n                                        <a href=\"Tool.aspx?type=Calc\" class=\"pro-item color-default\">\r\n                                            <div class=\"letter-wrap\">\r\n                                                <h3 class=\"h6\">電卓ツール</h3>\r\n                                                <span class=\"color-gray fn14\">基本操作と高度な数学関数計算をサポートするオンライン関数電卓</span>\r\n                                            </div>\r\n                                        </a>\r\n                                    </div>\r\n                                </div>\r\n                            </div>\r\n                        </div>\r\n                    </section>\r\n                    <section id=\"articles-sub-nav\" class=\"pls-nav-dropdown solution-part\" style=\"visibility: hidden; height: 0px; display: none;\">\r\n                        <div class=\"d-flex nav-drown-con\" style=\"height: 0px; transition: transform 0.2s ease-in-out 0s,opacity 0.2s ease-in-out 0s; opacity: 0; transform: translate(0px, -100%);\">\r\n                            <div class=\"prdocu-sub-left\">\r\n                                <div class=\"mr-auto\">\r\n                                    <picture>\r\n                                        <source srcset=\"site/image/icon/p_4.png.webp\" type=\"image/webp\" />\r\n                                        <img class=\"p-icon\" src=\"site/image/icon/p_4.png\" alt=\"Tech Sharing Center アイコン\" aria-hidden=\"true\" loading=\"lazy\" />\r\n                                    </picture>\r\n                                    <div class=\"mt-4\">\r\n                                        <h3 class=\"h6\">OCR技術の共有</h3>\r\n                                        <p class=\"color-gray fn14 mt-3 sub-left-desc\">技術チュートリアル、アプリケーションケース、ツールの推奨事項</p>\r\n                                        <p class=\"color-gray fn14 mt-3 sub-left-desc\">&nbsp;</p>\r\n                                        <p class=\"color-gray fn14 mt-3 sub-left-desc\">初心者から習得までの完全な学習パス</p>\r\n                                        <p class=\"color-gray fn14 mt-3 sub-left-desc\">テクニカル分析→ツール応用→実践例</p>\r\n                                        <p class=\"color-gray fn14 mt-3 sub-left-desc\">OCRテクノロジーの向上への道を強化</p>\r\n                                        <a class=\"link block disa fn14 contact-business shake\" href=\"Articles.aspx\">記事を参照<span class=\"iconfont4 icon-xiangzuo gt\"></span></a>\r\n                                    </div>\r\n                                </div>\r\n                            </div>\r\n                            <div class=\"bg-white prdocu-sub-right\">\r\n                                <div>\r\n                                    <h2 class=\"text-base-color h6\">テクノロジーの共有</h2>\r\n                                    <div class=\"mt-4 d-flex flex-wrap\">\r\n                                        <a href=\"Articles.aspx\" class=\"pro-item color-default\" title=\"OCRの技術記事をすべて表示\">\r\n                                            <div class=\"letter-wrap\">\r\n                                                <h3 class=\"h6\">すべての記事</h3>\r\n                                                <span class=\"color-gray fn14\">基礎から上級まで、完全な知識体系をカバーするすべてのOCR技術記事を参照</span>\r\n                                            </div>\r\n                                        </a>\r\n                                        \r\n                                        <a href=\"Articles.aspx?category=2\" class=\"pro-item color-default\" title=\"OCR の技術チュートリアルと入門ガイド\">\r\n                                            <div class=\"letter-wrap\">\r\n                                                <h3 class=\"h6\">アドバンスガイド</h3>\r\n                                                <span class=\"color-gray fn14\">入門から熟練した OCR 技術チュートリアル、詳細なハウツー ガイド、実践的なウォークスルーまで</span>\r\n                                            </div>\r\n                                        </a>\r\n                                        \r\n                                        <a href=\"Articles.aspx?category=3\" class=\"pro-item color-default\" title=\"OCR技術の原理、アルゴリズム、応用\">\r\n                                            <div class=\"letter-wrap\">\r\n                                                <h3 class=\"h6\">技術探求</h3>\r\n                                                <span class=\"color-gray fn14\">原理から応用まで、OCR技術のフロンティアを探求し、コアアルゴリズムを深く分析します</span>\r\n                                            </div>\r\n                                        </a>\r\n                                        \r\n                                        <a href=\"Articles.aspx?category=4\" class=\"pro-item color-default\" title=\"OCR業界の最新動向と開発動向\">\r\n                                            <div class=\"letter-wrap\">\r\n                                                <h3 class=\"h6\">業界動向</h3>\r\n                                                <span class=\"color-gray fn14\">OCR テクノロジー開発の傾向、市場分析、業界のダイナミクス、将来の見通しに関する詳細な洞察</span>\r\n                                            </div>\r\n                                        </a>\r\n                                        \r\n                                        <a href=\"Articles.aspx?category=5\" class=\"pro-item color-default\" title=\"さまざまな業界におけるOCR技術の応用事例\">\r\n                                            <div class=\"letter-wrap\">\r\n                                                <h3 class=\"h6\">使用例:</h3>\r\n                                                <span class=\"color-gray fn14\">さまざまな業界における OCR テクノロジーの実際の応用例、ソリューション、ベスト プラクティスが共有されます</span>\r\n                                            </div>\r\n                                        </a>\r\n                                        \r\n                                        <a href=\"Articles.aspx?category=6\" class=\"pro-item color-default\" title=\"OCR ソフトウェア ツールを使用するための専門的なレビュー、比較分析、推奨ガイドライン\">\r\n                                            <div class=\"letter-wrap\">\r\n                                                <h3 class=\"h6\">ツールレビュー</h3>\r\n                                                <span class=\"color-gray fn14\">さまざまなOCRテキスト認識ソフトウェアとツールを評価し、詳細な機能比較と選択の提案を提供します</span>\r\n                                            </div>\r\n                                        </a>\r\n                                        \r\n                                    </div>\r\n                                </div>\r\n                            </div>\r\n                        </div>\r\n                    </section>\r\n                    <section id=\"vip-sub-nav\" class=\"pls-nav-dropdown solution-part\" style=\"visibility: hidden; height: 0px; display: none;\">\r\n                        <div class=\"d-flex nav-drown-con\" style=\"height: 0px; transition: transform 0.2s ease-in-out 0s,opacity 0.2s ease-in-out 0s; opacity: 0; transform: translate(0px, -100%);\">\r\n                            <div class=\"prdocu-sub-left\">\r\n                                <div class=\"mr-auto\">\r\n                                    <picture>\r\n                                        <source class=\"p-icon\" srcset=\"site/image/icon/p_1.png.webp\" type=\"image/webp\" alt=\"\" aria-hidden=\"true\" />\r\n                                        <img class=\"p-icon\" src=\"site/image/icon/p_1.png\" alt=\"メンバーシップアップグレードサービスアイコン\" aria-hidden=\"true\" loading=\"lazy\" />\r\n                                    </picture>\r\n                                    <div class=\"mt-4\">\r\n                                        <h3 class=\"h6\">会員アップグレードサービス</h3>\r\n                                        <p class=\"color-gray fn14 mt-3 sub-left-desc\">すべてのプレミアム機能のロックを解除し、限定サービスをお楽しみください</p>\r\n                                        <p class=\"color-gray fn14 mt-3 sub-left-desc\">&nbsp;</p>\r\n                                        <p class=\"color-gray fn14 mt-3 sub-left-desc\">オフライン認識、バッチ処理、使い放題</p>\r\n                                        <p class=\"color-gray fn14 mt-3 sub-left-desc\">プロ→アルティメット→エンタープライズ</p>\r\n                                        <p class=\"color-gray fn14 mt-3 sub-left-desc\">あなたのニーズに合ったものがあります</p>\r\n                                        <a class=\"link block disa fn14 contact-business shake\" href=\"Version.aspx\">詳細を見る<span class=\"iconfont4 icon-xiangzuo gt\"></span></a>\r\n                                    </div>\r\n                                </div>\r\n                            </div>\r\n                            <div class=\"bg-white prdocu-sub-right\">\r\n                                <div>\r\n                                    <h2 class=\"text-base-color h6\" style=\"margin: inherit;\">メンバーシップのアップグレード</h2>\r\n                                    <div class=\"mt-4 d-flex flex-wrap\">\r\n                                        <a href=\"Version.aspx\" class=\"pro-item color-default\">\r\n                                            <div class=\"letter-wrap\">\r\n                                                <h3 class=\"h6\">会員特典</h3>\r\n                                                <span class=\"color-gray fn14\">エディション間の違いの詳細を確認し、最適なメンバーシップレベルを選択してください</span>\r\n                                            </div>\r\n                                        </a>\r\n                                        <a href=\"Upgrade.aspx\" class=\"pro-item color-default\">\r\n                                            <div class=\"letter-wrap\">\r\n                                                <h3 class=\"h6\">今すぐアップグレード</h3>\r\n                                                <span class=\"color-gray fn14\">VIPメンバーシップをすばやくアップグレードして、より多くのプレミアム機能と限定サービスのロックを解除します</span>\r\n                                            </div>\r\n                                        </a>\r\n                                        <a href=\"User.aspx\" class=\"pro-item color-default\">\r\n                                            <div class=\"letter-wrap\">\r\n                                                <h3 class=\"h6\">マイアカウント</h3>\r\n                                                <span class=\"color-gray fn14\">アカウント情報、サブスクリプションステータス、使用履歴を管理して設定をパーソナライズします</span>\r\n                                            </div>\r\n                                        </a>\r\n                                    </div>\r\n                                </div>\r\n                            </div>\r\n                        </div>\r\n                    </section>\r\n                    <section id=\"support-sub-nav\" class=\"pls-nav-dropdown solution-part\" style=\"visibility: hidden; height: 0px; display: none;\">\r\n                        <div class=\"d-flex nav-drown-con\" style=\"height: 0px; transition: transform 0.2s ease-in-out 0s,opacity 0.2s ease-in-out 0s; opacity: 0; transform: translate(0px, -100%);\">\r\n                            <div class=\"prdocu-sub-left\">\r\n                                <div class=\"mr-auto\">\r\n                                    <picture>\r\n                                        <source class=\"p-icon\" srcset=\"site/image/icon/p_4.png.webp\" type=\"image/webp\" alt=\"\" aria-hidden=\"true\" />\r\n                                        <img class=\"p-icon\" src=\"site/image/icon/p_4.png\" alt=\"ヘルプセンターのサポートアイコン\" aria-hidden=\"true\" loading=\"lazy\" />\r\n                                    </picture>\r\n                                    <div class=\"mt-4\">\r\n                                        <h3 class=\"h6\">ヘルプセンター</h3>\r\n                                        <p class=\"color-gray fn14 mt-3 sub-left-desc\">プロフェッショナルなカスタマーサービス、詳細な文書化、迅速な対応</p>\r\n                                        <p class=\"color-gray fn14 mt-3 sub-left-desc\">&nbsp;</p>\r\n                                        <p class=\"color-gray fn14 mt-3 sub-left-desc\">問題に遭遇してもパニックにならないでください</p>\r\n                                        <p class=\"color-gray fn14 mt-3 sub-left-desc\">問題を見つける→解決→</p>\r\n                                        <p class=\"color-gray fn14 mt-3 sub-left-desc\">エクスペリエンスをよりスムーズに</p>\r\n                                        <a class=\"link block disa fn14 contact-business shake\" href=\"FAQ.aspx\">ヘルプ<span class=\"iconfont4 icon-xiangzuo gt\"></span></a>\r\n                                    </div>\r\n                                </div>\r\n                            </div>\r\n                            <div class=\"bg-white prdocu-sub-right\">\r\n                                <div>\r\n                                    <h2 class=\"text-base-color h6\" style=\"margin: inherit;\">ヘルプセンター</h2>\r\n                                    <div class=\"mt-4 d-flex flex-wrap\">\r\n                                        <a href=\"FAQ.aspx\" class=\"pro-item color-default\">\r\n                                            <div class=\"letter-wrap\">\r\n                                                <h3 class=\"h6\">よくある質問</h3>\r\n                                                <span class=\"color-gray fn14\">ユーザーからよくある質問に迅速に回答し、詳細な使用ガイドと技術サポートを提供します</span>\r\n                                            </div>\r\n                                        </a>\r\n                                        <a href=\"AboutUs.aspx\" class=\"pro-item color-default\">\r\n                                            <div class=\"letter-wrap\">\r\n                                                <h3 class=\"h6\">我々について</h3>\r\n                                                <span class=\"color-gray fn14\">OCRテキスト認識アシスタントの開発の歴史、コア機能、サービスコンセプトについて学ぶ</span>\r\n                                            </div>\r\n                                        </a>\r\n                                        <a href=\"UserAgreement.aspx\" class=\"pro-item color-default\">\r\n                                            <div class=\"letter-wrap\">\r\n                                                <h3 class=\"h6\">ユーザー契約</h3>\r\n                                                <span class=\"color-gray fn14\">詳細な利用規約とユーザーの権利と義務</span>\r\n                                            </div>\r\n                                        </a>\r\n                                        <a href=\"PrivacyPolicy.aspx\" class=\"pro-item color-default\">\r\n                                            <div class=\"letter-wrap\">\r\n                                                <h3 class=\"h6\">プライバシー契約</h3>\r\n                                                <span class=\"color-gray fn14\">個人情報保護方針とデータセキュリティ対策</span>\r\n                                            </div>\r\n                                        </a>\r\n                                        <a href=\"Status.aspx\" class=\"pro-item color-default\">\r\n                                            <div class=\"letter-wrap\">\r\n                                                <h3 class=\"h6\">システムステータス</h3>\r\n                                                <span class=\"color-gray fn14\">グローバル識別ノードの動作状況をリアルタイムで監視し、システムパフォーマンスデータを表示</span>\r\n                                            </div>\r\n                                        </a>\r\n                                        <a href=\"javascript:void(0);\" class=\"pro-item color-default\" onclick=\"if(window.openCustomerService) window.openCustomerService(); else alert('右側のフローティングウィンドウアイコンをクリックして、カスタマーサービスにお問い合わせください');\">\r\n                                            <div class=\"letter-wrap\">\r\n                                                <h3 class=\"h6\">カスタマーサービスに連絡する</h3>\r\n                                                <span class=\"color-gray fn14\">質問やニーズに迅速に対応するオンラインカスタマーサービスサポート</span>\r\n                                            </div>\r\n                                        </a>\r\n                                    </div>\r\n                                </div>\r\n                            </div>\r\n                        </div>\r\n                    </section>\r\n                </div>\r\n            </div>\r\n            <div class=\"header_pc_right\" style=\"display: flex; align-items: center; justify-content: flex-end;\">\r\n                <div id=\"translate\"></div>\r\n                <div id=\"user-login-container\" data-url=\"User.ashx?op=userinfo\" style=\"white-space: nowrap; padding-right: 20px;\"></div>\r\n            </div>\r\n        </header>\r\n        <header class=\"v4_header_mob\">\r\n            <div class=\"container\">\r\n                <div class=\"row no-gutters justify-content-between align-items-center\">\r\n                    <a href=\"Default.aspx\" title=\"ホーム | AIインテリジェントテキスト認識\" class=\"mob-logo-wrap\">\r\n                        <picture>\r\n                            <source srcset=\"site/image/logo-s.png.webp\" type=\"image/webp\" />\r\n                            <img src=\"site/image/logo-s.png\" class=\"pc-logo\" style=\"height: 32px; width: 32px\" alt=\"OCRテキスト認識アシスタントモバイルロゴ\" />\r\n                        </picture>\r\n                        <span style=\"font-size: 18px; font-weight: bold; color: #333;\">OCRテキスト認識アシスタント</span>\r\n                    </a>\r\n                    <div class=\"right-menu\" role=\"button\" tabindex=\"0\" aria-label=\"ナビゲーションメニューを開く\" data-toggle=\"collapse\" data-target=\"#mob-header-collapse\" aria-expanded=\"false\" aria-controls=\"mob-header-collapse\">\r\n                        <nav-products id=\"nav_products\">\r\n                            <nav-button class=\"submail-mdi-nav-btn\" style=\"width: 60px\">\r\n                                <label id=\"mdi-submail-nav-btn\">\r\n                                    <span class=\"mdi-nav-products-1\">\r\n                                        <span class=\"nav-products-icon\"></span>\r\n                                    </span>\r\n                                    <span class=\"mdi-nav-products-2\">\r\n                                        <span class=\"nav-products-icon\"></span>\r\n                                    </span>\r\n                                    <span class=\"mdi-nav-products-3\">\r\n                                        <span class=\"nav-products-icon\"></span>\r\n                                    </span>\r\n                                </label>\r\n                            </nav-button>\r\n                        </nav-products>\r\n                    </div>\r\n                </div>\r\n            </div>\r\n            <div class=\"mob-nav-content\">\r\n                <div class=\"sidebar-fix\">\r\n                    <div class=\"container\">\r\n                        <div class=\"row\">\r\n                            <div class=\"col-4 bg-white sidebar-fix-left\" style=\"border-right: 1px solid #e1e6ed\">\r\n                                <div class=\"mob-nav-item active\" data=\"0\">\r\n                                    <div class=\"nav-header\"><a href=\"Default.aspx\" class=\"nav-header-letter px-3\"><span>家</span></a></div>\r\n                                </div>\r\n                                <div class=\"mob-nav-item\" data=\"1\">\r\n                                    <div class=\"nav-header\"><a href=\"Detail.aspx\" class=\"nav-header-letter px-3\"><span>機能</span></a></div>\r\n                                </div>\r\n                                <div class=\"mob-nav-item\" data=\"2\">\r\n                                    <div class=\"nav-header\"><a href=\"Ocr.aspx\" class=\"nav-header-letter px-3\"><span>経験</span></a></div>\r\n                                </div>\r\n                                <div class=\"mob-nav-item\" data=\"3\">\r\n                                    <div class=\"nav-header\"><a href=\"Version.aspx\" class=\"nav-header-letter px-3\"><span>メンバー</span></a></div>\r\n                                </div>\r\n                                <div class=\"mob-nav-item\" data=\"4\">\r\n                                    <div class=\"nav-header\"><a href=\"Download.aspx\" class=\"nav-header-letter px-3\"><span>ダウンロード</span></a></div>\r\n                                </div>\r\n                                <div class=\"mob-nav-item\" data=\"5\">\r\n                                    <div class=\"nav-header\"><a href=\"Articles.aspx\" class=\"nav-header-letter px-3\"><span>共有</span></a></div>\r\n                                </div>\r\n                                <div class=\"mob-nav-item\" data=\"6\">\r\n                                    <div class=\"nav-header\"><a href=\"FAQ.aspx\" class=\"nav-header-letter px-3\"><span>ヘルプ</span></a></div>\r\n                                </div>\r\n                            </div>\r\n                            <div class=\"col-8 bg-white sidebar-fix-rigth\">\r\n                                <div class=\"sub-nav\">\r\n                                    <div class=\"sub-nav-item py-2 pb-3 px-4\">\r\n                                        <h2 class=\"h5\" style=\"margin: inherit;\">効率的な生産性ツール</h2>\r\n                                        <p class=\"color-gray fn14 mt-3 sub-left-desc\">インテリジェントな認識、高速処理、正確な出力</p>\r\n                                        <p class=\"color-gray fn14 mt-3 sub-left-desc\">&nbsp;</p>\r\n                                        <p class=\"color-gray fn14 mt-3 sub-left-desc\">ドキュメントのページ全体を 3 秒で認識</p>\r\n                                        <p class=\"color-gray fn14 mt-3 sub-left-desc\">98%+ 認識精度</p>\r\n                                        <p class=\"color-gray fn14 mt-3 sub-left-desc\">遅延のない多言語リアルタイム処理</p>\r\n                                        <a class=\"link block disa fn14 contact-business shake\" href=\"Download.aspx\">今すぐ体験をダウンロード<span class=\"iconfont4 icon-xiangzuo gt\"></span></a>\r\n                                    </div>\r\n                                </div>\r\n                                <div class=\"sub-nav\">\r\n                                    <div class=\"sub-nav-item py-3 px-4\">\r\n                                        <h2 class=\"h5\" style=\"margin: inherit;\">製品の特徴:</h2>\r\n                                        <p class=\"color-gray fn14 mt-3 sub-left-desc\">AIインテリジェント識別、ワンストップソリューション</p>\r\n                                    </div>\r\n                                    <div class=\"sub-nav-item py-3 px-4\"><a href=\"Detail.aspx\">機能紹介</a></div>\r\n                                    <div class=\"sub-nav-item py-3 px-4\"><a href=\"Download.aspx\">ソフトウェアのダウンロード</a></div>\r\n                                    <div class=\"sub-nav-item py-3 px-4\"><a href=\"Version.aspx\">バージョン比較</a></div>\r\n                                    <div class=\"sub-nav-item py-3 px-4\"><a href=\"Ocr.aspx\">オンライン体験</a></div>\r\n                                    <div class=\"sub-nav-item py-3 px-4\"><a href=\"Status.aspx\">システムステータス</a></div>\r\n                                </div>\r\n                                <div class=\"sub-nav\">\r\n                                    <div class=\"sub-nav-item py-3 px-4\">\r\n                                        <h2 class=\"h5\" style=\"margin: inherit;\">オンライン体験</h2>\r\n                                        <p class=\"color-gray fn14 mt-3 sub-left-desc\">無料のオンラインOCR機能体験</p>\r\n                                    </div>\r\n                                    <div class=\"sub-nav-item py-3 px-4\"><a href=\"Ocr.aspx\">フル機能</a></div>\r\n                                    <div class=\"sub-nav-item py-3 px-4\"><a href=\"Ocr.aspx?type=text_recognize\">単語認識</a></div>\r\n                                    <div class=\"sub-nav-item py-3 px-4\"><a href=\"Ocr.aspx?type=table\">テーブルの識別</a></div>\r\n                                    <div class=\"sub-nav-item py-3 px-4\"><a href=\"Ocr.aspx?type=pdf2word\">PDFからWordへ</a></div>\r\n                                </div>\r\n                                <div class=\"sub-nav\">\r\n                                    <div class=\"sub-nav-item py-3 px-4\">\r\n                                        <h2 class=\"h5\" style=\"margin: inherit;\">メンバーシップのアップグレード</h2>\r\n                                        <p class=\"color-gray fn14 mt-3 sub-left-desc\">すべての機能のロックを解除し、限定サービスをお楽しみください</p>\r\n                                    </div>\r\n                                    <div class=\"sub-nav-item py-3 px-4\"><a href=\"Version.aspx\">会員特典</a></div>\r\n                                    <div class=\"sub-nav-item py-3 px-4\"><a href=\"Upgrade.aspx\">すぐにアクティブ化</a></div>\r\n                                </div>\r\n                                <div class=\"sub-nav\">\r\n                                    <div class=\"sub-nav-item py-3 px-4\">\r\n                                        <h2 class=\"h5\" style=\"margin: inherit;\">ソフトウェアのダウンロード</h2>\r\n                                        <p class=\"color-gray fn14 mt-3 sub-left-desc\">プロフェッショナルなOCRソフトウェアを無料でダウンロード</p>\r\n                                    </div>\r\n                                    <div class=\"sub-nav-item py-3 px-4\"><a href=\"Download.aspx\">今すぐダウンロード</a></div>\r\n                                    <div class=\"sub-nav-item py-3 px-4\"><a href=\"Version.aspx\">バージョン比較</a></div>\r\n                                </div>\r\n                                <div class=\"sub-nav\">\r\n                                    <div class=\"sub-nav-item py-3 px-4\">\r\n                                        <h2 class=\"h5\" style=\"margin: inherit;\">テクノロジーの共有</h2>\r\n                                        <p class=\"color-gray fn14 mt-3 sub-left-desc\">OCRの技術記事と知識の共有</p>\r\n                                    </div>\r\n                                    <div class=\"sub-nav-item py-3 px-4\"><a href=\"Articles.aspx\">すべての記事</a></div>\r\n                                    \r\n                                    <div class=\"sub-nav-item py-3 px-4\"><a href=\"Articles.aspx?category=2\">アドバンスガイド</a></div>\r\n                                    \r\n                                    <div class=\"sub-nav-item py-3 px-4\"><a href=\"Articles.aspx?category=3\">技術探求</a></div>\r\n                                    \r\n                                    <div class=\"sub-nav-item py-3 px-4\"><a href=\"Articles.aspx?category=4\">業界動向</a></div>\r\n                                    \r\n                                    <div class=\"sub-nav-item py-3 px-4\"><a href=\"Articles.aspx?category=5\">使用例:</a></div>\r\n                                    \r\n                                    <div class=\"sub-nav-item py-3 px-4\"><a href=\"Articles.aspx?category=6\">ツールレビュー</a></div>\r\n                                    \r\n                                </div>\r\n                                <div class=\"sub-nav\">\r\n                                    <div class=\"sub-nav-item py-3 px-4\">\r\n                                        <h2 class=\"h5\" style=\"margin: inherit;\">ヘルプセンター</h2>\r\n                                        <p class=\"color-gray fn14 mt-3 sub-left-desc\">プロフェッショナルな顧客サービス、親密なサービス</p>\r\n                                    </div>\r\n                                    <div class=\"sub-nav-item py-3 px-4\"><a href=\"FAQ.aspx\">ヘルプを使用する</a></div>\r\n                                    <div class=\"sub-nav-item py-3 px-4\"><a href=\"AboutUs.aspx\">我々について</a></div>\r\n                                    <div class=\"sub-nav-item py-3 px-4\"><a href=\"mailto:<EMAIL>\">カスタマーサービスに連絡する</a></div>\r\n                                    <div class=\"sub-nav-item py-3 px-4\"><a href=\"UserAgreement.aspx\">利用規約</a></div>\r\n                                </div>\r\n                            </div>\r\n                        </div>\r\n                    </div>\r\n                </div>\r\n            </div>\r\n        </header>\r\n        <form method=\"post\" action=\"./Article.aspx?id=81&amp;slug=rnn-sequence-modeling&amp;lang=zh-Hans&amp;_translation_source=true\" id=\"aspnetForm\">\r\n<div class=\"aspNetHidden\">\r\n<input type=\"hidden\" name=\"__VIEWSTATE\" id=\"__VIEWSTATE\" value=\"Q9uUeUlUJtaRG68B9ZsvqxMA0nWMrG97wrg6XH6pF/PALpTNvdWvXETFldXabtYfiX/3jguNbdKAEx4O02Rz9ETQIoNHodxI3/t/Hx/oGKM=\" />\r\n</div>\r\n\r\n<div class=\"aspNetHidden\">\r\n\r\n\t<input type=\"hidden\" name=\"__VIEWSTATEGENERATOR\" id=\"__VIEWSTATEGENERATOR\" value=\"2173C2F0\" />\r\n</div>\r\n            <div>\r\n                \n\n<div class=\"reading-progress\" id=\"readingProgress\"></div>\n<div class=\"article-detail-container\">\n<div class=\"container\">\n<div class=\"row\">\n<div class=\"col-lg-8 col-xl-9 col-md-12\">\n                    \n                    <article class=\"article-detail\" id=\"articleDetail\" data-article-id=\"81\">\n                        <header class=\"article-header\">\n                            <h1 class=\"article-title\">【ディープラーニングOCRシリーズ・4】リカレントニューラルネットワークとシーケンスモデリング</h1>\n                            <div class=\"article-meta\">\n                                <div class=\"meta-item\">\n                                    <span class=\"meta-icon\">📅</span>\n                                    <span>投稿時間: 2025-08-19</span>\n                                </div>\n                                <div class=\"meta-item\">\n                                    <span class=\"meta-icon\">👁️</span>\n                                    <span>読書：<span class=\"view-count\">1200</span></span>\n                                </div>\n                                <div class=\"meta-item\">\n                                    <span class=\"meta-icon\">⏱️</span>\n                                    <span>約50分(9819ワード)</span>\n                                </div>\n                                \n                                <div class=\"meta-item\">\n                                    <span class=\"meta-icon\">📁</span>\n                                    <span>カテゴリー: 上級ガイド</span>\n                                </div>\n                                \n                            </div>\n                            <div class=\"article-summary\"><p>OCRにおけるRNN、LSTM、GRUの応用について詳しく説明します。 シーケンスモデリングの原理、勾配問題の解、および双方向RNNの利点の詳細な分析。</p></div>\n                        </header>\n                        <div class=\"article-content\" id=\"articleContent\">## はじめに\r\n\r\nリカレント ニューラル ネットワーク (RNN) は、シーケンス データの処理に特化したディープ ラーニングのニューラル ネットワーク アーキテクチャです。 OCR タスクでは、テキスト認識は基本的にシーケンス間の変換問題であり、一連の画像特徴をテキスト文字シーケンスに変換します。 この記事では、RNN の仕組み、その主なバリアント、OCR における具体的なアプリケーションについて詳しく説明し、読者に包括的な理論的基礎と実践的なガイダンスを提供します。\r\n\r\n## RNNの基礎\r\n\r\n### 従来のニューラルネットワークの限界\r\n\r\n従来のフィードフォワードニューラルネットワークには、シーケンスデータの処理に根本的な制限があります。 これらのネットワークは、入力データが独立して均一に分布していることを前提としており、シーケンス内の要素間の時間的依存関係をキャプチャできません。\r\n\r\n**フィードフォワードネットワークの問題**:\r\n- 固定入力長と出力長:可変長シーケンスは処理できません\r\n- 記憶能力の不足:歴史情報を活用できない\r\n- パラメータ共有の難しさ: 同じパターンを異なる場所で繰り返し学習する必要があります\r\n- 位置感度: 入力の順序を変更すると、まったく異なる出力が得られる可能性があります\r\n\r\nこれらの制限は、OCR タスクで特に顕著です。 テキスト シーケンスはコンテキストに大きく依存し、前の文字の認識結果は、多くの場合、後続の文字の可能性を判断するのに役立ちます。 例えば、英単語「the」を識別する際、「th」がすでに認識されている場合、次の文字は「e」である可能性が高いです。\r\n\r\n### RNN の核となる考え方\r\n\r\nRNN は、ループ結合を導入することでシーケンス モデリングの問題を解決します。 中心的なアイデアは、ネットワークに「メモリ」メカニズムを追加して、ネットワークが前の瞬間の情報を保存して利用できるようにすることです。\r\n\r\n**RNNの数学的表現**:\r\nモーメントtでは、RNNの隠れ状態h_t現在の入力x_tと前のモーメントの隠れ状態によって決定されますh_{t-1}。\r\n\r\nh_t = f(W_hh * h_{t-1} + W_xh * x_t + b_h)\r\n\r\nその中には:\r\n- W_hh は、隠れ状態から隠れ状態までの重み行列です。\r\n- W_xhは、隠れ状態に入力された重み行列です。  \r\n- b_hはバイアスベクトルです\r\n- f は活性化関数 (通常は tanh または ReLU) です。\r\n\r\n出力y_tは、現在の非表示状態から計算されます。\r\ny_t = W_hy * h_t + b_y\r\n\r\n**RNN の利点**:\r\n- パラメータの共有: すべてのタイムステップで同じ重みが共有されます\r\n- 可変長シーケンス処理: 任意の長さの入力シーケンスを処理できます\r\n- 記憶能力: 隠し状態はネットワークの「記憶」として機能します\r\n- 柔軟な入出力: 1 対 1、1 対多、多対 1、多対多モードなどをサポート\r\n\r\n### RNNの拡張ビュー\r\n\r\nRNN がどのように機能するかをよりよく理解するために、時間的次元で RNN を拡張できます。 拡張された RNN はディープ フィードフォワード ネットワークのように見えますが、すべてのタイムステップは同じパラメーターを共有します。\r\n\r\n**展開する時間の重要性**:\r\n- わかりやすい情報の流れ:時間ステップ間で情報がどのように受け継がれているかを明確に確認できます\r\n- 勾配計算: 勾配は、時間バックプロパゲーション (BPTT) アルゴリズムを通じて計算されます\r\n- 並列化に関する考慮事項: RNN は本質的にシーケンシャルですが、特定の操作は並列化できます\r\n\r\n**展開プロセスの数学的説明**:\r\n長さTの配列の場合、RNNは次のように拡張します。\r\nh_1 = f(W_xh * x_1 + b_h)\r\nh_2 = f(W_hh * h_1 + W_xh * x_2 + b_h)\r\nh_3 = f(W_hh * h_2 + W_xh * x_3 + b_h)\r\n...\r\nh_T = f(W_hh * h_{T-1} + W_xh * x_T + b_h)\r\n\r\nこの展開されたフォームは、タイム ステップ間で情報がどのように受け渡されるか、およびすべてのタイム ステップでパラメーターがどのように共有されるかを明確に示します。\r\n\r\n## 勾配消失と爆発の問題\r\n\r\n### 問題の根本\r\n\r\nRNN をトレーニングするときは、Backpropagation Through Time (BPTT) アルゴリズムを使用します。 アルゴリズムは、各タイムステップ パラメーターの損失関数の勾配を計算する必要があります。\r\n\r\n**勾配計算の連鎖法則**:\r\nシーケンスが長い場合、勾配を複数の時間ステップで逆伝播する必要があります。 チェーンルールによれば、勾配には重み行列の複数の乗算が含まれます。\r\n\r\n∂L/∂W = σ_t (∂L/∂y_t) * (∂y_t/∂h_t) * (∂h_t/∂W)\r\n\r\nここで、∂h_t/∂W は、モーメント t からモーメント 1 までのすべての中間状態の積を含みます。\r\n\r\n**勾配消失の数学的分析**:\r\n時間ステップ間の勾配の伝播について考えてみましょう。\r\n∂h_t/∂h_{t-1} = diag(f_prime(W_hh * h_{t-1} + W_xh * x_t + b_h)) * W_hh\r\n\r\n配列長がTの場合、勾配にはT-1のような積項が含まれます。 W_hhの最大固有値が1未満の場合、連続行列の乗算により勾配指数関数的減衰が発生します。\r\n\r\n**勾配爆発の数学的解析**:\r\n逆に、W_hhの最大固有値が 1 より大きい場合、勾配は指数関数的に増加します。\r\n||∂h_t/∂h_1|| ≈ ||W_hh||^{t-1}\r\n\r\nこれにより、トレーニングが不安定になり、パラメータの更新が過剰になります。\r\n\r\n### 解決策の詳細な説明\r\n\r\nグラデーションクリッピング:\r\nグラデーション クリッピングは、グラデーションの爆発を解決する最も直接的な方法です。 勾配ノルムが設定されたしきい値を超えると、勾配はしきい値サイズにスケーリングされます。 この方法はシンプルで効果的ですが、しきい値を慎重に選択する必要があります。 しきい値が小さすぎると学習能力が制限され、しきい値が大きすぎると勾配爆発を効果的に防ぐことはできません。\r\n\r\n**重み初期化戦略**:\r\n適切な重みの初期化により、勾配の問題を軽減できます。\r\n- Xavierの初期化:重みの分散は1/nで、nは入力次元です\r\n- 初期化:重み分散は2/nで、ReLU活性化関数に適しています\r\n- 直交初期化: 重み行列を直交行列として初期化します\r\n\r\n**アクティベーション関数の選択**:\r\n活性化関数が異なれば、勾配伝播に及ぼす影響も異なります。\r\n- tanh:出力範囲[-1,1]、勾配最大値1\r\n- ReLU: 勾配消失を緩和できますが、神経細胞死を引き起こす可能性があります\r\n- Leaky ReLU: ReLU のニューロン死問題を解決します。\r\n\r\n**アーキテクチャの改善**:\r\n最も基本的な解決策は RNN アーキテクチャを改善することであり、それが LSTM と GRU の出現につながりました。 これらのアーキテクチャは、ゲート メカニズムと特殊な情報フロー設計を通じて勾配に対処します。\r\n\r\n## LSTM: 長短期記憶ネットワーク\r\n\r\n### LSTM の設計動機\r\n\r\nLSTM (Long Short-Term Memory) は、1997 年に Hochreiter と Schmidhuber によって提案された RNN バリアントで、勾配消失と長距離依存学習障害の問題を解決するために特別に設計されました。\r\n\r\n**LSTM のコア イノベーション**:\r\n- セル状態: 情報の「高速道路」として機能し、情報が時間ステップ間に直接流れるようにします\r\n- ゲーティング機構:情報の流入、保持、出力を正確に制御\r\n- 解離記憶メカニズム:短期記憶(隠れ状態)と長期記憶(細胞状態)を区別する\r\n\r\n**LSTM が勾配の問題を解決する方法**:\r\nLSTM は、乗算演算ではなく加算演算によってセル状態を更新するため、勾配がより前の時間ステップに流れやすくなります。 セル状態の数式を更新しました。\r\n\r\nC_t = f_t ⊙ C_{t-1} + i_t ⊙ C_tilde_t\r\n\r\nここでは元素レベルの加算が使用され、従来のRNNでの連続行列の増殖を回避します。\r\n\r\n### LSTMアーキテクチャの詳細な説明\r\n\r\nLSTM には、3 つのゲーティング ユニットとセル状態が含まれています。\r\n\r\n**1. ゲートを忘れる**:\r\n忘却の門は、セル状態からどの情報を破棄するかを決定します。\r\nf_t = σ(W_f · [h_{t-1}、x_t] + b_f)\r\n\r\n忘却ゲートの出力は0から1までの値で、0は「完全に忘れられた」、1は「完全に保持」されます。 このゲートにより、LSTM は重要でない履歴情報を選択的に忘れることができます。\r\n\r\n**2. 入力ゲート**:\r\n入力ゲートは、セル状態に格納される新しい情報を決定します。\r\ni_t = σ(W_i · [h_{t-1}、x_t] + b_i)\r\nC_tilde_t = tanh(W_C · [h_{t-1}, x_t] + b_C)\r\n\r\n入力ゲートは、シグモイド層が更新する値を決定し、tanh層が候補値ベクトルを作成するという2つの部分で構成されています。\r\n\r\n**3. セルステータスの更新**:\r\n忘却ゲートと入力ゲートの出力を組み合わせて、セルの状態を更新します。\r\nC_t = f_t ⊙ C_{t-1} + i_t ⊙ C_tilde_t\r\n\r\nこの式は、LSTM の中心であり、要素レベルの乗算と加算演算による情報の選択的保持と更新です。\r\n\r\n**4. 出力ゲート**:\r\n出力ゲートは、セルのどの部分が出力されるかを決定します。\r\no_t = σ(W_o · [h_{t-1}, x_t] + b_o)\r\nh_t = o_t ⊙タン(C_t)\r\n\r\n出力ゲートは、セルの状態のどの部分が現在の出力に影響を与えるかを制御します。\r\n\r\n### LSTM のバリアント\r\n\r\n**のぞき穴LSTM**:\r\n標準のLSTMをベースに構築されたPeephole LSTMは、ゲーティングユニットがセルの状態を表示できるようにします。\r\nf_t = σ(W_f · [C_{t-1}, h_{t-1}, x_t] + b_f)\r\ni_t = σ(W_i · [C_{t-1}、h_{t-1}、x_t] + b_i)\r\no_t = σ(W_o · [C_t、h_{t-1}、x_t] + b_o)\r\n\r\n**結合LSTM**:\r\n忘れゲートと入力ゲートを結合して、忘れられた情報の量が入力された情報の量と等しくなるようにします。\r\nf_t = σ(W_f · [h_{t-1}、x_t] + b_f)\r\ni_t = 1 - f_t\r\n\r\nこの設計により、LSTMのコア機能を維持しながら、パラメータの数が削減されます。\r\n\r\n## GRU: ゲートループユニット\r\n\r\n### GRUの設計の簡素化\r\n\r\nGRU(Gated Recurrent Unit)は、2014年にChoらによって提案されたLSTMの簡略版です。 GRUは、LSTMの3つのゲートを2つのゲートに簡略化し、セルラー状態と隠れ状態をマージします。\r\n\r\n**GRU の設計哲学**:\r\n- 簡素化された構造: ドアの数を減らし、計算の複雑さを軽減します\r\n- パフォーマンスの維持: LSTM に匹敵するパフォーマンスを維持しながら簡素化します\r\n- 実装が簡単:構造がシンプルなため、実装と試運転が容易\r\n\r\n### GRUのゲーティング機構\r\n\r\n**1. ゲートのリセット**:\r\nr_t = σ(W_r · [h_{t-1}、x_t] + b_r)\r\n\r\nリセットゲートは、新しい入力と以前のメモリを組み合わせる方法を決定します。 リセットゲートが0に近づくと、モデルは以前の非表示状態を無視します。\r\n\r\n**2. 更新ゲート**:\r\nz_t = σ(W_z · [h_{t-1}、x_t] + b_z)\r\n\r\n更新ゲートは、保持する過去の情報の量と、追加する新しい情報の量を決定します。 LSTMの忘却ゲートと入力ゲートの組み合わせと同様に、忘却と入力の両方を制御します。\r\n\r\n**3. 候補者の非表示ステータス**:\r\nh_tilde_t = tanh(W_h · [r_t ⊙ h_{t-1}、x_t] + b_h)\r\n\r\n候補の隠れ状態は、リセット ゲートを使用して、前の隠れ状態の効果を制御します。\r\n\r\n**4. 最終的な隠し状態**:\r\nh_t = (1 - z_t) ⊙ h_{t-1} + z_t ⊙ h_tilde_t\r\n\r\n最終的な隠れ状態は、前の隠れ状態と候補の隠れ状態の加重平均です。\r\n\r\n### GRU と LSTM の詳細な比較\r\n\r\n**パラメータ数の比較**:\r\n- LSTM:4つの重み行列(忘却ゲート、入力ゲート、候補値、出力ゲート)\r\n- GRU:3つの重み行列(リセットゲート、更新ゲート、候補値)\r\n- GRUのパラメータ数はLSTMの約75%です。\r\n\r\n**計算量の比較**:\r\n- LSTM:4つのゲート出力の計算とセル状態の更新が必要\r\n- GRU:2つのゲートと非表示のステータス更新の出力を計算するだけです\r\n- GRUは通常、LSTMよりも20〜30%高速です。\r\n\r\n**パフォーマンスの比較**:\r\n- ほとんどのタスクで、GRU と LSTM のパフォーマンスは同等です\r\n- LSTMは、一部の長シーケンスタスクではGRUよりもわずかに優れている可能性があります\r\n- GRU は、コンピューティング リソースが限られている場合に適しています。\r\n\r\n## 双方向 RNN\r\n\r\n### 双方向処理の必要性\r\n\r\n多くのシーケンスモデリングタスクでは、現在の瞬間の出力は過去だけでなく未来の情報にも依存します。 これは、文字認識で単語や文全体の文脈を考慮する必要があることが多い OCR タスクでは特に重要です。\r\n\r\n**一方向RNNの制限**:\r\n- 履歴情報のみを使用でき、将来のコンテキストは取得できません\r\n- 特定のタスク、特にグローバル情報を必要とするタスクではのパフォーマンスが制限されています\r\n- あいまいな文字の認識が制限されている\r\n\r\n**双方向処理の利点**:\r\n- 完全なコンテキスト情報: 過去と将来の情報の両方を活用する\r\n- より良い曖昧さ回避: 文脈情報による曖昧さ回避\r\n- 認識精度の向上: ほとんどのシーケンス注釈タスクでパフォーマンスが向上しました\r\n\r\n### 双方向 LSTM アーキテクチャ\r\n\r\n双方向 LSTM は、次の 2 つの LSTM レイヤーで構成されます。\r\n- LSTM の転送: シーケンスを左から右に処理します\r\n- 逆方向 LSTM: シーケンスを右から左に処理します\r\n\r\n**数学的表現**:\r\nh_forward_t = LSTM_forward(x_t, h_forward_{t-1})\r\nh_backward_t = LSTM_backward(x_t, h_backward_{t+1})\r\nh_t = [h_forward_t; h_backward_t] # 非表示状態を前後にステッチする\r\n\r\n**トレーニングプロセス**:\r\n1. LSTM転送はシーケンスを通常の順序で処理します\r\n2. 逆方向の LSTM は、シーケンスを逆の順序で処理します\r\n3. 各タイムステップで、隠れ状態を両方向に接続します\r\n4. スプライス状態を予測に使用する\r\n\r\n**利点と欠点**:\r\n利：\r\n- 完全なコンテキスト情報\r\n- パフォーマンスの向上\r\n- 対称性処理\r\n\r\n劣った位置:\r\n- 計算の複雑さを2倍に\r\n- リアルタイムで処理できない(フルシーケンスが必要)\r\n- メモリ要件の増加\r\n\r\n## OCRでのシーケンスモデリングアプリケーション\r\n\r\n### テキスト行認識の詳細な説明\r\n\r\nOCR システムでは、テキスト行認識はシーケンス モデリングの典型的なアプリケーションです。 このプロセスには、一連の画像特徴を一連の文字に変換することが含まれます。\r\n\r\n**問題モデリング**:\r\n- 入力: 画像特徴シーケンス X = {x_1, x_2, ..., x_T}\r\n- 出力: 文字シーケンス Y = {y_1, y_2, ..., y_S}\r\n- 課題:入力シーケンス長Tと出力シーケンス長Sが等しくないことがよくあります\r\n\r\n**テキスト行認識におけるCRNNアーキテクチャの応用**:\r\nCRNN(畳み込みリカレントニューラルネットワーク)は、OCRで最も成功したアーキテクチャの1つです。\r\n\r\n1. **CNN 特徴抽出レイヤー**:\r\n   - 畳み込みニューラルネットワークを使用した画像特徴の抽出\r\n   - 2D 画像特徴を 1D 特徴シーケンスに変換する\r\n   - タイミング情報の連続性の維持\r\n\r\n2. **RNN配列モデリング層**:\r\n   - 双方向 LSTM を使用したモデル化特徴シーケンス\r\n   - 文字間のコンテキスト依存関係をキャプチャする\r\n   - 時間ステップごとの出力文字確率分布\r\n\r\n3. **CTC アライメント レイヤー**:\r\n   - 入出力シーケンス長の不一致に対処\r\n   - 文字レベルの配置寸法は必要ありません\r\n   - エンドツーエンドのトレーニング\r\n\r\n**特徴抽出のシーケンスへの変換**:\r\nCNNによって抽出された特徴マップは、RNNが処理できる配列形式に変換する必要があります。\r\n- 特徴マップを列にセグメント化し、各列をタイムステップとして\r\n- 空間情報の年表を維持する\r\n- 特徴シーケンスの長さが画像の幅に比例することを確認します\r\n\r\n### OCRにおけるアテンションメカニズムの応用\r\n\r\n従来のRNNでは、長い配列を扱う際に依然として情報のボトルネックがあります。 アテンションメカニズムの導入により、シーケンスモデリングの機能がさらに強化されます。\r\n\r\n**注意メカニズムの原理**:\r\nアテンションメカニズムにより、モデルは各出力を生成するときに入力シーケンスのさまざまな部分に焦点を当てることができます。\r\n- 固定長エンコードベクトルの情報ボトルネックを解決\r\n- モデルの決定を説明可能にする\r\n- 長いシーケンスの処理の改善\r\n\r\n**OCR の特定のアプリケーション**:\r\n\r\n1. **キャラクターレベルの注意**:\r\n   - 各キャラクターを識別するときは、関連する画像領域に焦点を当てます\r\n   - 注意の重みをその場で調整する\r\n   - 複雑な背景に対する堅牢性の向上\r\n\r\n2. **単語レベルの注意**:\r\n   - 語彙レベルで文脈情報を考慮する\r\n   - 言語モデルの知識を活用する\r\n   - 単語全体の認識の精度を向上させる\r\n\r\n3. **マルチスケールの注意**:\r\n   - さまざまな解像度での注意メカニズムの適用\r\n   - さまざまなサイズのテキストを処理する\r\n   - 規模の変更への適応性の向上\r\n\r\n**注意メカニズムの数学的表現**:\r\nエンコーダー出力シーケンスH = {h_1, h_2, ..., h_T} およびデコーダーの状態 s_t の場合:\r\n\r\ne_{t,i} = a(s_t, h_i) # 注意スコア\r\nα_{t,i} = softmax(e_{t,i}) # アテンションウェイト\r\nc_t = Σ_i α_{t,i} * h_i # コンテキストベクトル\r\n\r\n## トレーニング戦略と最適化\r\n\r\n### シーケンス間のトレーニング戦略\r\n\r\n**教師の強制**:\r\nトレーニングフェーズでは、実際のターゲットシーケンスをデコーダーの入力として使用します。\r\n- 長所:トレーニング速度が速く、収束が安定している\r\n- 短所: トレーニングと推論のフェーズに一貫性がなく、エラーが蓄積する\r\n\r\n**定期サンプリング**:\r\nトレーニング中に教師の強制からモデル独自の予測の使用に徐々に移行します。\r\n- 初期段階では実際のラベルを使用し、後の段階では予測をモデル化します\r\n- トレーニングと推論の違いを減らす\r\n- モデルの堅牢性の向上\r\n\r\n**カリキュラム学習**:\r\n単純なサンプルから始めて、サンプルの複雑さを徐々に増やします。\r\n- 短いシーケンスから長いシーケンス: 最初に短いテキストをトレーニングし、次に長いテキストをトレーニングします\r\n- 鮮明な画像からぼやけた画像まで: 画像の複雑さを徐々に増やします\r\n- 単純なフォントから複雑なフォントまで: 印刷物から手書きまで\r\n\r\n### 正則化テクニック\r\n\r\n**RNNにおけるドロップアウトの適用**:\r\nRNN にドロップアウトを適用するには、特別な注意が必要です。\r\n- ループ接続にドロップアウトを適用しない\r\n- ドロップアウトは入力層と出力層に適用できます\r\n- 変分ドロップアウト: すべてのタイムステップで同じドロップアウトマスクを使用します\r\n\r\n**重量減衰**:\r\nL2 正則化は、過学習を防ぎます。\r\n損失 = クロスエントロピー + λ * ||W||²\r\n\r\nここで、λは正則化係数であり、検証セットによって最適化する必要があります。\r\n\r\n**グラデーショントリミング**:\r\n勾配爆発を防ぐ効果的な方法。 グラデーションノルムがしきい値を超えると、グラデーションの方向を変更せずにグラデーションを比例的にスケールします。\r\n\r\n**早期停止**:\r\n検証セットのパフォーマンスを監視し、パフォーマンスが向上しなくなったらトレーニングを停止します。\r\n- オーバーフィッティングの防止\r\n- コンピューティングリソースの節約\r\n- 最適なモデルを選択する\r\n\r\n### ハイパーパラメータのチューニング\r\n\r\n**学習率のスケジューリング**:\r\n- 初期学習率: 通常は 0.001-0.01 に設定されます\r\n- 学習率の減衰:指数関数的減衰またはラダー減衰\r\n- 適応学習率: Adam、RMSprop などのオプティマイザーを使用します\r\n\r\n**バッチサイズの選択**:\r\n- 小バッチ: 汎化パフォーマンスは向上しますが、トレーニング時間は長くなります\r\n- 大量: トレーニングは高速ですが、一般化に影響を与える可能性があります\r\n- 通常、16〜128のバッチサイズが選択されます\r\n\r\n**シーケンス長処理**:\r\n- 固定長: シーケンスを固定長に切り捨てたり塗りつぶしたりします\r\n- 動的長さ: パディングとマスキングを使用して、可変長のシーケンスを処理します\r\n- バギング戦略: 同様の長さのシーケンスをグループ化します\r\n\r\n## パフォーマンス評価と分析\r\n\r\n### メトリクスを評価する\r\n\r\n**文字レベルの精度**:\r\nAccuracy_char = (正しく認識された文字数) / (合計文字数)\r\n\r\nこれは最も基本的な評価指標であり、モデルの文字認識能力を直接反映します。\r\n\r\n**シリアルレベルの精度**:\r\nAccuracy_seq =(正しく認識されたシーケンスの数)/(シーケンスの総数)\r\n\r\nこの指標はより厳密であり、完全に正しいシーケンスのみが正しいと見なされます。\r\n\r\n**編集距離(レーベンシュタイン距離)**:\r\n予測系列と真系列の差を測定します。\r\n- 挿入、除去、および置換操作の最小数\r\n- 標準化された編集距離:編集距離/シーケンスの長さ\r\n- BLEU スコア: 機械翻訳で一般的に使用され、OCR 評価にも使用できます\r\n\r\n### エラー分析\r\n\r\n**一般的なエラーの種類**:\r\n1. **文字の混乱**: 類似文字の誤認\r\n   - 数字の0と文字Oは\r\n   - 数字の1と文字l。\r\n   - 文字MとN\r\n\r\n2. **シーケンスエラー**:文字順のエラー\r\n   - キャラクターの位置が逆になります\r\n   - 文字の重複または省略\r\n\r\n3. **長さ誤差**: シーケンス長の予測エラー\r\n   - 長すぎる:存在しない文字を挿入\r\n   - 短すぎる: 存在する文字が欠落しています\r\n\r\n**分析方法**:\r\n1. **混同行列**: 文字レベルのエラーパターンを分析します\r\n2. **アテンションの視覚化**: モデルの懸念事項を理解する\r\n3. **勾配解析**: 勾配フローの確認\r\n4. **アクティベーション分析**: ネットワークの層全体のアクティベーション パターンを観察します\r\n\r\n### モデル診断\r\n\r\n**オーバーフィット検出**:\r\n- トレーニング損失は減少し続け、検証損失は増加\r\n- トレーニングの精度が検証の精度よりもはるかに高い\r\n- 解決策: 規則性を高め、モデルの複雑さを軽減する\r\n\r\n**アンダーフィット検出**:\r\n- トレーニングと検証の両方の損失が高い\r\n- モデルがトレーニングセットでうまく機能しない\r\n- 解決策: モデルの複雑さを増やし、学習率を調整する\r\n\r\n**勾配問題の診断**:\r\n- 勾配損失: 勾配値が小さすぎて学習が遅い\r\n- 勾配爆発: 勾配値が大きすぎると、トレーニングが不安定になります\r\n- 解決策:LSTM / GRUの使用、グラデーショントリミング\r\n\r\n## 実際の応用例\r\n\r\n### 手書き文字認識システム\r\n\r\n**アプリケーションシナリオ**:\r\n- 手書きのメモをデジタル化する: 紙のメモを電子文書に変換する\r\n- フォームの自動入力: 手書きのフォーム内容を自動的に認識します\r\n- 歴史的文書の識別: 古代の書籍や歴史的文書をデジタル化します\r\n\r\n**技術的特徴**:\r\n- 大きな文字バリエーション: 手書きのテキストは高度なパーソナライゼーションを備えています\r\n- 連続ペン処理:文字間の接続を処理する必要があります\r\n- コンテキスト重要: 言語モデルを利用して認識を向上させる\r\n\r\n**システムアーキテクチャ**:\r\n1. **前処理モジュール**:\r\n   - 画像のノイズ除去と強調\r\n   - 傾き補正\r\n   - テキスト行分割\r\n\r\n2. **特徴抽出モジュール**:\r\n   - CNNは視覚的特徴を抽出します\r\n   - マルチスケール機能融合\r\n   - 機能のシリアル化\r\n\r\n3. **シーケンスモデリングモジュール**:\r\n   - 双方向LSTMモデリング\r\n   - 注意のメカニズム\r\n   - コンテキストエンコーディング\r\n\r\n4. **デコードモジュール**:\r\n   - CTCデコードまたはアテンションデコード\r\n   - 言語モデルの後処理\r\n   - 信頼度評価\r\n\r\n### 印刷文書認識システム\r\n\r\n**アプリケーションシナリオ**:\r\n- 文書のデジタル化: 紙の文書を編集可能な形式に変換する\r\n- 請求書認識: 請求書、領収書、その他の請求書を自動的に処理します\r\n- 標識認識: 道路標識、店舗標識などを識別します\r\n\r\n**技術的特徴**:\r\n- 通常のフォント: 手書きのテキストよりも規則的なフォント\r\n- タイポグラフィルール:レイアウト情報を活用できます\r\n- 高精度要件: 商用アプリケーションには厳しい精度要件があります\r\n\r\n**最適化戦略**:\r\n1. **マルチフォントトレーニング**: 複数のフォントからのトレーニングデータを使用\r\n2. **データ強化**: 回転、拡大縮小、ノイズの追加\r\n3. **後処理の最適化**: スペルチェック、文法修正\r\n4. **信頼度評価**: 認識結果の信頼性スコアを提供します\r\n\r\n### シーンテキスト認識システム\r\n\r\n**アプリケーションシナリオ**:\r\n- ストリートビューのテキスト認識: Google ストリートビューでのテキスト認識\r\n- 製品ラベル認識:スーパーマーケット製品の自動識別\r\n- 交通標識認識: 高度道路交通システムの応用\r\n\r\n**技術的な課題**:\r\n- 複雑な背景: テキストは複雑な自然シーンに埋め込まれています\r\n- 激しい変形:遠近法変形、曲げ変形\r\n- リアルタイム要件: モバイル アプリは応答性が高い必要があります\r\n\r\n**解決**：\r\n1. **堅牢な特徴抽出**: より深いCNNネットワークを使用します\r\n2. **マルチスケール処理**: さまざまなサイズのテキストを処理します\r\n3. **ジオメトリ補正**: 幾何学的変形を自動的に補正します\r\n4. **モデル圧縮**: モデルをモバイル向けに最適化します\r\n\r\n## 概要\r\n\r\nリカレントニューラルネットワークは、OCRにおけるシーケンスモデリングのための強力なツールを提供します。 基本的な RNN から改良された LSTM や GRU、双方向処理やアテンション メカニズムに至るまで、これらの技術の開発により OCR システムのパフォーマンスが大幅に向上しました。\r\n\r\n**重要なポイント**:\r\n- RNNはループ結合によるシーケンスモデリングを実装するが、勾配消失の問題がある\r\n- LSTMとGRUは、ゲートメカニズムを介して長距離依存学習問題を解決します\r\n- 双方向RNNは、完全なコンテキスト情報を活用できます\r\n- アテンションメカニズムにより、シーケンスモデリングの能力がさらに向上\r\n- 適切なトレーニング戦略と正則化手法は、モデルのパフォーマンスにとって重要です。\r\n\r\n**今後の開発の方向性**:\r\n- Transformerアーキテクチャとの統合\r\n- シーケンスモデリングへのより効率的なアプローチ\r\n- エンドツーエンドのマルチモーダル学習\r\n- リアルタイム性と精度のバランス\r\n\r\nテクノロジーが進化し続けるにつれて、シーケンス モデリング技術も進化しています。 OCR の分野で RNN とそのバリアントによって蓄積された経験と技術は、より高度な配列モデリング手法を理解し、設計するための強固な基盤を築きました。</div>\n                        \n                        <div class=\"article-tags\">\n                            <h6>ラベル：</h6>\n                            <div class=\"tags-list\">\n                                \n                                <span class=\"tag\">RNN</span>\n                                \n                                <span class=\"tag\">LSTM</span>\n                                \n                                <span class=\"tag\">GRU</span>\n                                \n                                <span class=\"tag\">シーケンスモデリング</span>\n                                \n                                <span class=\"tag\">グラデーションが消えます</span>\n                                \n                                <span class=\"tag\">双方向RNN</span>\n                                \n                                <span class=\"tag\">アテンションメカニズム</span>\n                                \n                                <span class=\"tag\">CRNN</span>\n                                \n                                <span class=\"tag\">OCR</span>\n                                \n                            </div>\n                        </div>\n                        \n                    </article>\n                    \n                    <footer class=\"article-footer\" id=\"articleFooter\">\n                        <div class=\"article-share\">\n                            <h6 style=\"color: #1a1c1f; margin-bottom: 1rem;\">共有と運用:</h6>\n                            <div class=\"share-buttons\">\n                                <a href=\"javascript:void(0)\" onclick=\"shareToWeibo()\" class=\"btn btn-weibo\">📱 Weiboが共有しました</a>\n                                <a href=\"javascript:void(0)\" onclick=\"copyUrl()\" class=\"btn btn-copy\">🔗 リンクをコピー</a>\n                                <button type=\"button\" class=\"btn btn-print\" onclick=\"printArticle()\">🖨️ 記事を印刷する</button>\n                            </div>\n                        </div>\n                    </footer>\n                    \n                </div>\n                <div class=\"col-lg-4 col-xl-3 col-md-12\">\n                    <div class=\"sidebar\">\n                        <div class=\"sidebar-card toc-card\" id=\"tocCard\" style=\"display: none;\"><h5>目次</h5><div id=\"tableOfContents\" class=\"toc-list\"></div></div>\n                        <div class=\"sidebar-card\">\n                            <h5>推奨読書</h5>\n                            <div id=\"recommendedArticles\">\n                                \n                                <div class=\"recommended-item\"><h6><a href=\"Article.aspx?slug=document-intelligence-future-outlook\">【文書インテリジェント処理シリーズ・20】文書インテリジェント処理技術の発展展望</a></h6><p class=\"text-muted small\">2025-08-19 • 1203 次の読書</p></div>\n                                \n                                <div class=\"recommended-item\"><h6><a href=\"Article.aspx?slug=document-intelligence-quality-assurance\">【文書インテリジェント処理シリーズ・19】文書インテリジェント処理品質保証システム</a></h6><p class=\"text-muted small\">2025-08-19 • 1200 次の読書</p></div>\n                                \n                                <div class=\"recommended-item\"><h6><a href=\"Article.aspx?slug=large-scale-document-processing-optimization\">【文書インテリジェント処理シリーズ・18】大規模文書処理性能の最適化</a></h6><p class=\"text-muted small\">2025-08-19 • 1201 次の読書</p></div>\n                                \n                            </div>\n                        </div>\n                    </div>\n</div>\n</div>\n</div>\n</div>\n<script>\nvar articleId,viewCountKey,now=new Date().getTime();\ndocument.addEventListener('DOMContentLoaded',function(){var articleDetail=document.getElementById('articleDetail');if(articleDetail&&articleDetail.querySelector('.article-content')){formatContent();initReadingProgress();optimizeImages();updateViewCount();}});\nfunction updateViewCount(){articleId=getArticleId();if(!articleId)return;viewCountKey='article_view_'+articleId;var lastViewTime=localStorage.getItem(viewCountKey);if(lastViewTime&&(now-parseInt(lastViewTime))<3600000)return;var xhr=new XMLHttpRequest();xhr.open('POST','/Article.ashx',true);xhr.setRequestHeader('Content-Type','application/x-www-form-urlencoded');xhr.onreadystatechange=function(){if(xhr.readyState===4&&xhr.status===200){try{var response=JSON.parse(xhr.responseText);if(response.success){updateViewCountDisplay(response.viewCount);localStorage.setItem(viewCountKey,now.toString());}}catch(e){}}};xhr.send('action=updateviewcount&articleId='+encodeURIComponent(articleId));}\nfunction getArticleId(){var articleDetail=document.getElementById('articleDetail');if(articleDetail){var id=articleDetail.getAttribute('data-article-id');if(id)return id;}var urlParams=new URLSearchParams(window.location.search);return urlParams.get('id');}\nfunction updateViewCountDisplay(newCount){document.querySelectorAll('.view-count').forEach(function(element){element.textContent=newCount;});}\nfunction decodeHtmlEntities(text){var textArea=document.createElement('textarea');textArea.innerHTML=text;return textArea.value;}\nfunction formatContent(){var contentDiv=document.getElementById('articleContent');if(!contentDiv)return;var content=contentDiv.textContent||contentDiv.innerText;if(!content)return;content=decodeHtmlEntities(content);content=formatMarkdownContent(content);content=formatIndentation(content);content=formatLists(content);content=formatParagraphs(content);content=formatLinks(content);contentDiv.innerHTML=content;initTableOfContents();}\nfunction formatIndentation(content){var lines=content.split('\\n'),result=[];for(var i=0;i<lines.length;i++){var line=lines[i];if(line.trim().match(/^<[^>]+>/)){result.push(line);continue;}line=line.replace(/^\\t+/g,function(match){return '    '.repeat(match.length);});if(line.match(/^  +[^\\s]/)){var indentMatch=line.match(/^( +)(.*)$/);if(indentMatch){var indentLevel=Math.floor(indentMatch[1].length/2);line='<span class=\"text-indent-'+indentLevel+'\">'+indentMatch[2]+'</span>';}}result.push(line);}return result.join('\\n');}\nfunction formatMarkdownContent(content){content=content.replace(/^\\s*##### (.+)$/gm,'<h5>$1</h5>').replace(/^\\s*#### (.+)$/gm,'<h4>$1</h4>').replace(/^\\s*### (.+)$/gm,'<h3>$1</h3>').replace(/^\\s*## (.+)$/gm,'<h2>$1</h2>').replace(/^\\s*# (.+)$/gm,'<h1>$1</h1>').replace(/^> (.+)$/gm,'<blockquote><p>$1</p></blockquote>').replace(/\\*\\*([^*]+?)\\*\\*/g,'<strong>$1</strong>').replace(/\\*([^*\\n]+?)\\*/g,'<em>$1</em>').replace(/`([^`]+?)`/g,'<code class=\"inline-code\">$1</code>').replace(/^(\\*\\*[^*]+?\\*\\*)：/gm,'<div class=\"label-item\"><span class=\"label\">$1</span></div>').replace(/^([^：\\n]+?)：$/gm,'<div class=\"section-label\">$1：</div>').replace(/^(\\*\\*[^*]+?\\*\\*)：(.+)$/gm,'<div class=\"definition-item\"><span class=\"definition-label\">$1：</span><span class=\"definition-content\">$2</span></div>').replace(/^(note|note|note):(.+)$/gm,'<div class=\"note-item\"><span class=\"note-label\">$1：</span><span class=\"note-content\">$2</span></div>');content=formatLists(content);content=formatMathSymbols(content);content=formatParagraphs(content);return content;}\nfunction formatLists(content){var lines=content.split('\\n'),result=[],inOrderedList=false,inUnorderedList=false;for(var i=0;i<lines.length;i++){var line=lines[i],trimmed=line.trim();var orderedMatch=line.match(/^(\\d+)\\.\\s*(.+)$/);var unorderedMatch=line.match(/^[-*+]\\s*(.+)$/);if(orderedMatch){if(!inOrderedList){if(inUnorderedList){result.push('</ul>');inUnorderedList=false;}result.push('<ol class=\"formatted-list ordered\">');inOrderedList=true;}var listContent=orderedMatch[2].replace(/^\\*\\*([^*]+?)\\*\\*：(.+)$/,'<strong class=\"list-title\">$1：</strong><span class=\"list-content\">$2</span>');result.push('<li>'+listContent+'</li>');}else if(unorderedMatch){if(!inUnorderedList){if(inOrderedList){result.push('</ol>');inOrderedList=false;}result.push('<ul class=\"formatted-list unordered\">');inUnorderedList=true;}var listContent=unorderedMatch[1].replace(/^\\*\\*([^*]+?)\\*\\*：(.+)$/,'<strong class=\"list-title\">$1：</strong><span class=\"list-content\">$2</span>');result.push('<li>'+listContent+'</li>');}else{if(trimmed===''){if(i+1<lines.length){var nextLine=lines[i+1].trim();if(!nextLine.match(/^\\d+\\.\\s/)&&!nextLine.match(/^[-*+]\\s/)){if(inOrderedList){result.push('</ol>');inOrderedList=false;}if(inUnorderedList){result.push('</ul>');inUnorderedList=false;}}}result.push(line);}else{if(inOrderedList){result.push('</ol>');inOrderedList=false;}if(inUnorderedList){result.push('</ul>');inUnorderedList=false;}result.push(line);}}}if(inOrderedList)result.push('</ol>');if(inUnorderedList)result.push('</ul>');return result.join('\\n');}\nfunction formatMathSymbols(content){var mathSymbols={'α':'&alpha;','β':'&beta;','γ':'&gamma;','δ':'&delta;','ε':'&epsilon;','ζ':'&zeta;','η':'&eta;','θ':'&theta;','λ':'&lambda;','μ':'&mu;','π':'&pi;','σ':'&sigma;','∑':'&sum;','∏':'&prod;','∫':'&int;','∂':'&part;','∇':'&nabla;','∞':'&infin;','≤':'&le;','≥':'&ge;','≠':'&ne;','≈':'&asymp;','±':'&plusmn;','×':'&times;','÷':'&divide;','→':'&rarr;','←':'&larr;','↑':'&uarr;','↓':'&darr;','⇒':'&rArr;','⇐':'&lArr;'};var subMap={'₀':'0','₁':'1','₂':'2','₃':'3','₄':'4','₅':'5','₆':'6','₇':'7','₈':'8','₉':'9','ₖ':'k','ₜ':'t','ₛ':'s','₊':'+','₋':'-'};var supMap={'⁰':'0','¹':'1','²':'2','³':'3','⁴':'4','⁵':'5','⁶':'6','⁷':'7','⁸':'8','⁹':'9','ᵏ':'k','ᵗ':'t','ˢ':'s','⁺':'+','⁻':'-'};for(var symbol in mathSymbols){content=content.replace(new RegExp(escapeRegExp(symbol),'g'),mathSymbols[symbol]);}content=content.replace(/([a-zA-Z])₍([0-9]+)₎/g,'$1<sub>$2</sub>').replace(/([a-zA-Z])⁽([0-9]+)⁾/g,'$1<sup>$2</sup>').replace(/([a-zA-Z])([₀₁₂₃₄₅₆₇₈₉ₖₜₛ₊₋]+)/g,function(match,base,subscript){var converted=subscript.replace(/[₀₁₂₃₄₅₆₇₈₉ₖₜₛ₊₋]/g,function(char){return subMap[char]||char;});return base+'<sub>'+converted+'</sub>';}).replace(/([a-zA-Z])([⁰¹²³⁴⁵⁶⁷⁸⁹ᵏᵗˢ⁺⁻]+)/g,function(match,base,superscript){var converted=superscript.replace(/[⁰¹²³⁴⁵⁶⁷⁸⁹ᵏᵗˢ⁺⁻]/g,function(char){return supMap[char]||char;});return base+'<sup>'+converted+'</sup>';}).replace(/\\b([A-Za-z])_\\{([^}]+)\\}/g,'$1<sub>$2</sub>').replace(/\\b([A-Za-z])\\^\\{([^}]+)\\}/g,'$1<sup>$2</sup>');return content;}\nfunction formatParagraphs(content){var lines=content.split('\\n'),result=[],inParagraph=false,currentParagraph=[];for(var i=0;i<lines.length;i++){var originalLine=lines[i],line=originalLine.trim();if(line.match(/^<[^>]+>/)||line===''){if(inParagraph&&currentParagraph.length>0){var paragraphContent=currentParagraph.join(' ').trim();if(paragraphContent)result.push('<p class=\"content-paragraph indent-paragraph\">'+paragraphContent+'</p>');currentParagraph=[];inParagraph=false;}result.push(originalLine);continue;}if(line.match(/^#{1,6}\\s/)||line.match(/^\\d+\\.\\s/)||line.match(/^[-*+]\\s/)||line.match(/^>/)){if(inParagraph&&currentParagraph.length>0){var paragraphContent=currentParagraph.join(' ').trim();if(paragraphContent)result.push('<p class=\"content-paragraph indent-paragraph\">'+paragraphContent+'</p>');currentParagraph=[];inParagraph=false;}result.push(originalLine);continue;}if(line){currentParagraph.push(line);inParagraph=true;}}if(inParagraph&&currentParagraph.length>0){var paragraphContent=currentParagraph.join(' ').trim();if(paragraphContent)result.push('<p class=\"content-paragraph indent-paragraph\">'+paragraphContent+'</p>');}return result.join('\\n');}\nfunction escapeRegExp(string){return string.replace(/[.*+?^${}()|[\\]\\\\]/g,'\\\\$&');}\nfunction formatLinks(content){return content.replace(/<a\\s+href=\"(https?:\\/\\/[^\"]+)\"([^>]*)>/gi,function(match,url,attrs){return !attrs.includes('target=')?'<a href=\"'+url+'\"'+attrs+' target=\"_blank\" rel=\"noopener noreferrer\">':match;});}\nfunction escapeHtml(text){var div=document.createElement('div');div.textContent=text;return div.innerHTML;}\nfunction addTocClickEvents(){document.querySelectorAll('#tableOfContents a').forEach(function(link){link.addEventListener('click',function(e){e.preventDefault();var targetElement=document.getElementById(this.getAttribute('data-target'));if(targetElement){targetElement.scrollIntoView({behavior:'smooth',block:'start'});updateActiveTocItem(this);}});});}\nfunction addScrollHighlight(headings){var throttleTimer=null;function updateActiveHeading(){var windowHeight=window.innerHeight,activeHeading=null;for(var i=headings.length-1;i>=0;i--){var rect=headings[i].getBoundingClientRect();if(rect.top<=windowHeight*0.3){activeHeading=headings[i];break;}}if(activeHeading){var activeLink=document.querySelector('#tableOfContents a[data-target=\"'+activeHeading.id+'\"]');if(activeLink)updateActiveTocItem(activeLink);}}window.addEventListener('scroll',function(){if(throttleTimer)clearTimeout(throttleTimer);throttleTimer=setTimeout(updateActiveHeading,100);});updateActiveHeading();}\nfunction updateActiveTocItem(activeLink){document.querySelectorAll('#tableOfContents a').forEach(function(link){link.classList.remove('active');});if(activeLink){activeLink.classList.add('active');scrollTocToActiveItem(activeLink);}}\nfunction scrollTocToActiveItem(activeLink){var tocCard=document.getElementById('tocCard');if(!activeLink||!tocCard)return;var cardRect=tocCard.getBoundingClientRect();var activeRect=activeLink.getBoundingClientRect();var relativeTop=activeRect.top-cardRect.top;var relativeBottom=activeRect.bottom-cardRect.bottom;var cardStyle=window.getComputedStyle(tocCard);var paddingTop=parseFloat(cardStyle.paddingTop)||0;var titleHeight=tocCard.querySelector('h5')?tocCard.querySelector('h5').offsetHeight:0;var contentTop=paddingTop+titleHeight;if(relativeTop<contentTop){tocCard.scrollTop+=(relativeTop-contentTop-10);}else if(relativeBottom>0){tocCard.scrollTop+=(relativeBottom+10);}}\nfunction optimizeImages(){document.querySelectorAll('.article-content img').forEach(function(img){img.loading='lazy';img.onerror=function(){this.style.display='none';};if(!img.alt)img.alt='写真付きの記事';});}\nfunction initReadingProgress(){var progressBar=document.getElementById('readingProgress');function updateProgress(){var scrollTop=window.pageYOffset||document.documentElement.scrollTop;var docHeight=document.documentElement.scrollHeight-window.innerHeight;progressBar.style.width=Math.min((scrollTop/docHeight)*100,100)+'%';}window.addEventListener('scroll',updateProgress);updateProgress();}\nfunction initTableOfContents(){var content=document.querySelector('.article-content');if(!content)return;var headings=content.querySelectorAll('h1, h2, h3, h4, h5, h6');if(headings.length<=1)return;var tocCard=document.getElementById('tocCard');var tocList=document.getElementById('tableOfContents');var html='',counters=[0,0,0,0,0,0];headings.forEach(function(heading,index){var id='heading-'+index;heading.id=id;var level=parseInt(heading.tagName.substring(1));var indent=(level-1)*15;counters[level-1]++;for(var i=level;i<counters.length;i++)counters[i]=0;var numberText='';for(var i=0;i<level;i++){if(counters[i]>0)numberText+=(numberText?'.':'')+counters[i];}numberText+='.';html+='<a href=\"#'+id+'\" class=\"toc-item toc-level-'+level+'\" style=\"padding-left: '+indent+'px;\" data-target=\"'+id+'\"><span class=\"toc-number\">'+numberText+'</span> <span class=\"toc-text\">'+heading.textContent+'</span></a>';});tocList.innerHTML=html;tocCard.style.display='block';addTocClickEvents();addScrollHighlight(headings);tocList.addEventListener('click',function(e){if(e.target.tagName==='A'){e.preventDefault();var targetElement=document.getElementById(e.target.getAttribute('data-target'));if(targetElement){targetElement.scrollIntoView({behavior:'smooth',block:'start'});targetElement.style.transition='all 0.3s ease';targetElement.style.backgroundColor='#e3f2fd';targetElement.style.padding='10px';targetElement.style.borderRadius='6px';setTimeout(function(){targetElement.style.backgroundColor='';targetElement.style.padding='';targetElement.style.borderRadius='';},2000);}}});}\nfunction shareToWeibo(){window.open('https://service.weibo.com/share/share.php?url='+encodeURIComponent(window.location.href)+'&title='+encodeURIComponent(document.title),'_blank');}\nfunction copyUrl(){navigator.clipboard.writeText(window.location.href).then(function(){alert('リンクがクリップボードにコピーされました');}).catch(function(){var textArea=document.createElement('textarea');textArea.value=window.location.href;textArea.style.cssText='position:fixed;left:-999999px;top:-999999px';document.body.appendChild(textArea);textArea.focus();textArea.select();try{alert(document.execCommand('copy')?'リンクがクリップボードにコピーされました':'コピーに失敗した場合は、リンクを手動でコピーしてください');}catch(err){alert('コピーに失敗した場合は、リンクを手動でコピーしてください');}document.body.removeChild(textArea);});}\nfunction printArticle(){var printWindow=window.open('','_blank');var articleContent=document.querySelector('.article-detail').innerHTML;var articleTitle=document.querySelector('.article-title').textContent;printWindow.document.write('<!DOCTYPE html><html lang=\"ja\"><title>'+articleTitle+'</title><style>body{font-family:-apple-system,BlinkMacSystemFont,\"Segoe UI\",Roboto,\"Helvetica Neue\",Arial,sans-serif;line-height:1.6;color:#333;max-width:800px;margin:0 auto;padding:20px}h1,h2,h3,h4,h5,h6{color:#1a1c1f;margin-top:1.5em;margin-bottom:0.5em}h1{font-size:2em;border-bottom:2px solid #e9ecef;padding-bottom:0.3em}h2{font-size:1.5em;border-bottom:1px solid #e9ecef;padding-bottom:0.3em}h3{font-size:1.25em}p{margin-bottom:1em}pre{background:#f8f9fa;border:1px solid #e9ecef;border-radius:4px;padding:1em;overflow-x:auto;font-family:Consolas,Monaco,\"Courier New\",monospace}code{background:#f1f3f4;padding:2px 4px;border-radius:3px;font-family:Consolas,Monaco,\"Courier New\",monospace}blockquote{border-left:4px solid #1764ff;background:#f8f9fa;padding:1em;margin:1em 0;border-radius:0 4px 4px 0}img{max-width:100%;height:auto}table{width:100%;border-collapse:collapse;margin:1em 0}th,td{border:1px solid #ddd;padding:8px;text-align:left}th{background-color:#f2f2f2}.article-meta,.article-footer,.share-buttons{display:none}</style><body>'+articleContent+'</body></html>');printWindow.document.close();printWindow.focus();setTimeout(function(){printWindow.print();printWindow.close();},250);}\nfunction improvedStickyPosition(){var sidebarCards=document.querySelectorAll('.sidebar-card');var originalPositions=[],isFixed=[];sidebarCards.forEach(function(card,index){var rect=card.getBoundingClientRect();originalPositions[index]=rect.top+window.pageYOffset;isFixed[index]=false;var placeholder=document.createElement('div');placeholder.style.cssText='height:'+card.offsetHeight+'px;display:none';placeholder.className='sticky-placeholder';card.parentNode.insertBefore(placeholder,card.nextSibling);});function updateStickyPosition(){var scrollTop=window.pageYOffset||document.documentElement.scrollTop;sidebarCards.forEach(function(card,index){var originalTop=originalPositions[index];var stickyTop=index>0?(document.querySelector('.toc-card').style.position==='fixed'?60+document.querySelector('.toc-card').offsetHeight+8:80):60;var placeholder=card.nextSibling;if(scrollTop>originalTop-stickyTop){if(!isFixed[index]){var colContainer=card.closest('[class*=\"col-\"]');var colRect=colContainer.getBoundingClientRect();var colStyle=window.getComputedStyle(colContainer);var paddingLeft=parseFloat(colStyle.paddingLeft)||0;var paddingRight=parseFloat(colStyle.paddingRight)||0;card.style.cssText='position:fixed;top:'+stickyTop+'px;left:'+(colRect.left+paddingLeft)+'px;width:'+(colRect.width-paddingLeft-paddingRight)+'px;z-index:9999;box-sizing:border-box';card.classList.add('fixed');placeholder.style.display='block';isFixed[index]=true;}}else if(isFixed[index]){card.style.cssText='';card.classList.remove('fixed');placeholder.style.display='none';isFixed[index]=false;}});}window.addEventListener('scroll',updateStickyPosition);window.addEventListener('resize',function(){setTimeout(function(){sidebarCards.forEach(function(card,index){if(isFixed[index]){var colContainer=card.closest('[class*=\"col-\"]');var colRect=colContainer.getBoundingClientRect();var colStyle=window.getComputedStyle(colContainer);var paddingLeft=parseFloat(colStyle.paddingLeft)||0;var paddingRight=parseFloat(colStyle.paddingRight)||0;card.style.left=(colRect.left+paddingLeft)+'px';card.style.width=(colRect.width-paddingLeft-paddingRight)+'px';}else{originalPositions[index]=card.getBoundingClientRect().top+window.pageYOffset;}});},100);});updateStickyPosition();}\ndocument.addEventListener('DOMContentLoaded',function(){setTimeout(improvedStickyPosition,1000);});\n    </script>\n\r\n            </div>\r\n        </form>\r\n\r\n        <div class=\"fixed-icon\">\r\n            <div style=\"width: 120px; position: relative; left: 1rem; top: -12px;\">\r\n            </div>\r\n            <a id=\"webchat\" class=\"wx-con disa\" href=\"http://wpa.qq.com/msgrd?v=3&uin=365833440&site=qq&menu=yes\" target=\"_blank\">\r\n                <img class=\"wechat-ico\" src=\"site/image/icon/qq.svg\" alt=\"OCRアシスタントQQオンラインカスタマーサービス\" />\r\n                <div class=\"wx-text\">QQカスタマーサービス(365833440)</div>\r\n                <label class=\"badge badge-danger\" style=\"background: rgb(255, 34, 122); display: inline-block;\">1</label>\r\n            </a>\r\n\r\n            <a class=\"wx-con\" href=\"https://qm.qq.com/cgi-bin/qm/qr?k=bohNy9GMdgD5bsYrB-fcIjy-s6Ot3zOg&jump_from=webapi&qr=1\" target=\"_blank\">\r\n                <img class=\"wechat-ico\" src=\"site/image/icon/qq.svg\" alt=\"OCRアシスタントQQユーザーコミュニケーショングループ\" />\r\n                <div class=\"wx-text\">QQグループ(100029010)</div>\r\n                <label class=\"badge badge-danger\" style=\"background: rgb(255, 34, 122); display: inline-block;\">2</label>\r\n            </a>\r\n            <a href=\"mailto:<EMAIL>\" class=\"wx-con\">\r\n                <img class=\"wechat-ico\" src=\"site/image/icon/im.svg\" alt=\"OCRアシスタントカスタマーサービスにメールで連絡\" />\r\n                <div class=\"wx-text\" style=\"width: 240px; text-align: left; left: -15.3rem; padding: 10px 20px;\">\r\n                    <div style=\"font-size: 15px\">Eメール:<EMAIL></div>\r\n                    <hr style=\"background: #c1c1c1; margin: 10px 10px 6px 0;\" />\r\n                    <span style=\"font-size: 13px; color: #c1c1c1\">ご意見やご提案ありがとうございます!</span>\r\n                </div>\r\n                <label class=\"badge badge-danger\" style=\"background: rgb(255, 34, 122); display: inline-block;\">3</label>\r\n            </a>\r\n        </div>\r\n\r\n        <style>\r\n            .content {\r\n                width: 100%;\r\n                padding: 0 !important;\r\n                margin: 0 auto\r\n            }\r\n\r\n            h3.h6 {\r\n                font-size: 1rem !important;\r\n                font-weight: bold !important;\r\n                line-height: 1.2 !important;\r\n                margin-bottom: .5rem;\r\n                font-family: inherit\r\n            }\r\n        </style>\r\n        <footer class=\"page-footer\">\r\n            <div class=\"container\">\r\n                <div class=\"row foot-cont pt-4 no-gutters\" style=\"border-top: 1px solid #e1e6ed; max-width: 100%\">\r\n                    <div class=\"col-lg-12 hu-an p-0 mb-3 mt-3 text-l color-gray\" style=\"text-align: center;\">\r\n                        OCRテキスト認識アシスタント&nbsp;©️ 2025 ALL RIGHTS RESERVED. 全著作権所有&nbsp;&nbsp;&nbsp;|&nbsp;&nbsp;&nbsp;\r\n                        <a href=\"PrivacyPolicy.aspx\" style=\"text-decoration: underline;\">プライバシー契約</a>&nbsp;&nbsp;&nbsp;|&nbsp;&nbsp;&nbsp;\r\n                        <a href=\"UserAgreement.aspx\" style=\"text-decoration: underline;\">ユーザー契約</a>&nbsp;&nbsp;&nbsp;|&nbsp;&nbsp;&nbsp;\r\n                        <a href=\"Status.aspx\" style=\"text-decoration: underline;\">サービス状況</a>&nbsp;&nbsp;&nbsp;|&nbsp;&nbsp;&nbsp;\r\n                        <a class=\"mb-2 color-gray\" href=\"https://beian.miit.gov.cn/\" target=\"_blank\" style=\"text-decoration: underline;\">E ICP作成番号2021012692</a>\r\n                    </div>\r\n                </div>\r\n            </div>\r\n        </footer>\r\n        <style>\r\n            #translate > .translateSelectLanguage {\r\n                right: 2rem;\r\n                font-size: 1rem;\r\n                width: 150px;\r\n                padding: .3rem;\r\n                margin-right: 20px;\r\n                border: 1px solid #C9C9C9;\r\n                background: #fff;\r\n                color: #555\r\n            }\r\n        </style>\r\n    </div>\r\n    <script>\r\n        document.addEventListener('DOMContentLoaded', function () {\r\n            var setScroll = function (active) {\r\n                forEach(qa('.v4_header_pc,.v4_header_mob'), function (e) {\r\n                    if (active) addClass(e, 'scrollActive'); else removeClass(e, 'scrollActive');\r\n                });\r\n            };\r\n            if (getScrollY() > 60) setScroll(1);\r\n            window.addEventListener('scroll', function () { setScroll(getScrollY() > 60); });\r\n\r\n            forEach(qa('.v4_header_pc'), function (h) {\r\n                h.addEventListener('mouseenter', function () { addClass(h, 'active'); });\r\n                h.addEventListener('mouseleave', function () { removeClass(h, 'active'); });\r\n            });\r\n\r\n            var menuTimer;\r\n            var hideMenu = function (menuId) {\r\n                var m = document.getElementById(menuId);\r\n                if (m) {\r\n                    m.style.cssText = 'visibility:hidden;height:0;display:none';\r\n                    var n = q('.nav-drown-con', m);\r\n                    if (n) { n.style.cssText = 'height:0;opacity:0;transform:translate(0,-100%)'; }\r\n                }\r\n            };\r\n\r\n            forEach(qa('ul.top-nav>li'), function (i) {\r\n                i.addEventListener('mouseenter', function () {\r\n                    clearTimeout(menuTimer);\r\n                    var l = this.querySelector('a'), t = this.querySelector('.triangle'), s = l ? l.getAttribute('data-sub') : null;\r\n                    if (l) addClass(l, 'active'); if (t) addClass(t, 'active');\r\n                    if (s) {\r\n                        var m = document.getElementById(s);\r\n                        if (m) {\r\n                            m.style.cssText = 'display:block;visibility:visible';\r\n                            var siblings = m.parentNode ? toArray(m.parentNode.children) : [];\r\n                            forEach(siblings, function (c) { if (c !== m) c.style.display = 'none'; });\r\n                            var n = q('.nav-drown-con', m);\r\n                            if (n) { n.style.cssText = 'height:100%;opacity:1;transform:translate(0,0)'; }\r\n\r\n                            m.addEventListener('mouseenter', function () { clearTimeout(menuTimer); });\r\n                            m.addEventListener('mouseleave', function () { menuTimer = setTimeout(function () { hideMenu(s); }, 100); });\r\n                        }\r\n                    }\r\n                });\r\n                i.addEventListener('mouseleave', function () {\r\n                    var l = this.querySelector('a'), t = this.querySelector('.triangle'), s = l ? l.getAttribute('data-sub') : null;\r\n                    if (l) removeClass(l, 'active'); if (t) removeClass(t, 'active');\r\n                    if (s) { menuTimer = setTimeout(function () { hideMenu(s); }, 100); }\r\n                });\r\n            });\r\n\r\n            var r = q('.v4_header_mob .right-menu');\r\n            if (r) r.addEventListener('click', function () {\r\n                var arr = [r, q('.v4_header_mob'), q('#nav_products'), q('.mob-nav-content .sidebar-fix')];\r\n                for (var i = 0; i < arr.length; i++) {\r\n                    var e = arr[i];\r\n                    if (e) {\r\n                        var name = (i === 0 ? 'active' : (i === 1 ? 'active' : (i === 2 ? 'active1' : 'fixedActiveBg')));\r\n                        toggleClass(e, name);\r\n                    }\r\n                }\r\n                var f = q('.mob-nav-content .sidebar-fix');\r\n                if (f) {\r\n                    toggleClass(f, 'show');\r\n                    document.body.style.overflow = hasClass(f, 'show') ? 'hidden' : 'scroll';\r\n                }\r\n            });\r\n\r\n            document.addEventListener('click', function (e) {\r\n                e = e || window.event;\r\n                var target = e.target || e.srcElement;\r\n                var m = closest(target, '.sidebar-fix-left .mob-nav-item');\r\n                if (m) {\r\n                    var p = m.parentNode;\r\n                    if (p) forEach(toArray(p.children), function (c) { removeClass(c, 'active'); });\r\n                    addClass(m, 'active');\r\n                    var d = m.getAttribute('data');\r\n                    if (d) forEach(qa('.sidebar-fix-rigth .sub-nav'), function (n, i) { n.style.display = (i == d ? 'block' : 'none'); });\r\n                }\r\n            });\r\n        });\r\n    </script>\r\n</body>\r\n</html>\r\n\r\n\t            <script type=\"text/javascript\">\r\n                    var _hmt = _hmt || [];\r\n                    (function () {\r\n                        var hm = document.createElement('script');hm.async=1;\r\n                        hm.src = 'https://hm.baidu.com/hm.js?5ee048d7caaf0d439cf16309a1687754';\r\n                        var s = document.getElementsByTagName('script')[0];s.parentNode.insertBefore(hm, s);\r\n                    })();\r\n                </script><div id=\"translate\" class=\"ignore\" style=\"display: none\"></div><script src=\"/static/js/translate.js\"></script>"
﻿"<!DOCTYPE html>\r\n<html xmlns=\"http://www.w3.org/1999/xhtml\" lang=\"ur\">\r\n<head><link rel=\"canonical\" href=\"https://ocr.oldfish.cn/ur/Article.aspx?id=81&slug=rnn-sequence-modeling\" /><link rel=\"alternate\" href=\"https://ocr.oldfish.cn/Article.aspx?id=81&slug=rnn-sequence-modeling\" hreflang=\"x-default\" />\r\n<link rel=\"alternate\" href=\"https://ocr.oldfish.cn/Article.aspx?id=81&slug=rnn-sequence-modeling\" hreflang=\"zh\" />\r\n<link rel=\"alternate\" href=\"https://ocr.oldfish.cn/Article.aspx?id=81&slug=rnn-sequence-modeling\" hreflang=\"zh\" />\r\n<link rel=\"alternate\" href=\"https://ocr.oldfish.cn/zh-Hant/Article.aspx?id=81&slug=rnn-sequence-modeling\" hreflang=\"zh-tw\" />\r\n<link rel=\"alternate\" href=\"https://ocr.oldfish.cn/yue/Article.aspx?id=81&slug=rnn-sequence-modeling\" hreflang=\"zh-yue\" />\r\n<link rel=\"alternate\" href=\"https://ocr.oldfish.cn/en/Article.aspx?id=81&slug=rnn-sequence-modeling\" hreflang=\"en\" />\r\n<link rel=\"alternate\" href=\"https://ocr.oldfish.cn/es/Article.aspx?id=81&slug=rnn-sequence-modeling\" hreflang=\"es\" />\r\n<link rel=\"alternate\" href=\"https://ocr.oldfish.cn/hi/Article.aspx?id=81&slug=rnn-sequence-modeling\" hreflang=\"hi\" />\r\n<link rel=\"alternate\" href=\"https://ocr.oldfish.cn/ar/Article.aspx?id=81&slug=rnn-sequence-modeling\" hreflang=\"ar\" />\r\n<link rel=\"alternate\" href=\"https://ocr.oldfish.cn/pt/Article.aspx?id=81&slug=rnn-sequence-modeling\" hreflang=\"pt\" />\r\n<link rel=\"alternate\" href=\"https://ocr.oldfish.cn/bn/Article.aspx?id=81&slug=rnn-sequence-modeling\" hreflang=\"bn\" />\r\n<link rel=\"alternate\" href=\"https://ocr.oldfish.cn/ru/Article.aspx?id=81&slug=rnn-sequence-modeling\" hreflang=\"ru\" />\r\n<link rel=\"alternate\" href=\"https://ocr.oldfish.cn/ja/Article.aspx?id=81&slug=rnn-sequence-modeling\" hreflang=\"ja\" />\r\n<link rel=\"alternate\" href=\"https://ocr.oldfish.cn/pa/Article.aspx?id=81&slug=rnn-sequence-modeling\" hreflang=\"pa\" />\r\n<link rel=\"alternate\" href=\"https://ocr.oldfish.cn/de/Article.aspx?id=81&slug=rnn-sequence-modeling\" hreflang=\"de\" />\r\n<link rel=\"alternate\" href=\"https://ocr.oldfish.cn/fr/Article.aspx?id=81&slug=rnn-sequence-modeling\" hreflang=\"fr\" />\r\n<link rel=\"alternate\" href=\"https://ocr.oldfish.cn/mr/Article.aspx?id=81&slug=rnn-sequence-modeling\" hreflang=\"mr\" />\r\n<link rel=\"alternate\" href=\"https://ocr.oldfish.cn/te/Article.aspx?id=81&slug=rnn-sequence-modeling\" hreflang=\"te\" />\r\n<link rel=\"alternate\" href=\"https://ocr.oldfish.cn/vi/Article.aspx?id=81&slug=rnn-sequence-modeling\" hreflang=\"vi\" />\r\n<link rel=\"alternate\" href=\"https://ocr.oldfish.cn/ko/Article.aspx?id=81&slug=rnn-sequence-modeling\" hreflang=\"ko\" />\r\n<link rel=\"alternate\" href=\"https://ocr.oldfish.cn/ta/Article.aspx?id=81&slug=rnn-sequence-modeling\" hreflang=\"ta\" />\r\n<link rel=\"alternate\" href=\"https://ocr.oldfish.cn/ur/Article.aspx?id=81&slug=rnn-sequence-modeling\" hreflang=\"ur\" />\r\n<link rel=\"alternate\" href=\"https://ocr.oldfish.cn/tr/Article.aspx?id=81&slug=rnn-sequence-modeling\" hreflang=\"tr\" />\r\n<link rel=\"alternate\" href=\"https://ocr.oldfish.cn/it/Article.aspx?id=81&slug=rnn-sequence-modeling\" hreflang=\"it\" />\r\n<link rel=\"alternate\" href=\"https://ocr.oldfish.cn/gu/Article.aspx?id=81&slug=rnn-sequence-modeling\" hreflang=\"gu\" />\r\n<link rel=\"alternate\" href=\"https://ocr.oldfish.cn/pl/Article.aspx?id=81&slug=rnn-sequence-modeling\" hreflang=\"pl\" />\r\n<link rel=\"alternate\" href=\"https://ocr.oldfish.cn/uk/Article.aspx?id=81&slug=rnn-sequence-modeling\" hreflang=\"uk\" />\r\n<link rel=\"alternate\" href=\"https://ocr.oldfish.cn/ms/Article.aspx?id=81&slug=rnn-sequence-modeling\" hreflang=\"ms\" />\r\n<link rel=\"alternate\" href=\"https://ocr.oldfish.cn/id/Article.aspx?id=81&slug=rnn-sequence-modeling\" hreflang=\"id\" />\r\n<link rel=\"alternate\" href=\"https://ocr.oldfish.cn/ml/Article.aspx?id=81&slug=rnn-sequence-modeling\" hreflang=\"ml\" />\r\n<link rel=\"alternate\" href=\"https://ocr.oldfish.cn/kn/Article.aspx?id=81&slug=rnn-sequence-modeling\" hreflang=\"kn\" />\r\n<link rel=\"alternate\" href=\"https://ocr.oldfish.cn/fa/Article.aspx?id=81&slug=rnn-sequence-modeling\" hreflang=\"fa\" />\r\n<link rel=\"alternate\" href=\"https://ocr.oldfish.cn/nl/Article.aspx?id=81&slug=rnn-sequence-modeling\" hreflang=\"nl\" />\r\n<link rel=\"alternate\" href=\"https://ocr.oldfish.cn/th/Article.aspx?id=81&slug=rnn-sequence-modeling\" hreflang=\"th\" />\r\n<link rel=\"alternate\" href=\"https://ocr.oldfish.cn/sw/Article.aspx?id=81&slug=rnn-sequence-modeling\" hreflang=\"sw\" />\r\n<link rel=\"alternate\" href=\"https://ocr.oldfish.cn/ro/Article.aspx?id=81&slug=rnn-sequence-modeling\" hreflang=\"ro\" />\r\n<link rel=\"alternate\" href=\"https://ocr.oldfish.cn/my/Article.aspx?id=81&slug=rnn-sequence-modeling\" hreflang=\"my\" />\r\n<link rel=\"alternate\" href=\"https://ocr.oldfish.cn/or/Article.aspx?id=81&slug=rnn-sequence-modeling\" hreflang=\"or\" />\r\n<link rel=\"alternate\" href=\"https://ocr.oldfish.cn/he/Article.aspx?id=81&slug=rnn-sequence-modeling\" hreflang=\"he\" />\r\n<link rel=\"alternate\" href=\"https://ocr.oldfish.cn/am/Article.aspx?id=81&slug=rnn-sequence-modeling\" hreflang=\"am\" />\r\n<link rel=\"alternate\" href=\"https://ocr.oldfish.cn/fil/Article.aspx?id=81&slug=rnn-sequence-modeling\" hreflang=\"fil\" />\r\n<link rel=\"alternate\" href=\"https://ocr.oldfish.cn/sv/Article.aspx?id=81&slug=rnn-sequence-modeling\" hreflang=\"sv\" />\r\n<link rel=\"alternate\" href=\"https://ocr.oldfish.cn/el/Article.aspx?id=81&slug=rnn-sequence-modeling\" hreflang=\"el\" />\r\n<link rel=\"alternate\" href=\"https://ocr.oldfish.cn/cs/Article.aspx?id=81&slug=rnn-sequence-modeling\" hreflang=\"cs\" />\r\n<link rel=\"alternate\" href=\"https://ocr.oldfish.cn/hu/Article.aspx?id=81&slug=rnn-sequence-modeling\" hreflang=\"hu\" />\r\n<link rel=\"alternate\" href=\"https://ocr.oldfish.cn/be/Article.aspx?id=81&slug=rnn-sequence-modeling\" hreflang=\"be\" />\r\n<link rel=\"alternate\" href=\"https://ocr.oldfish.cn/si/Article.aspx?id=81&slug=rnn-sequence-modeling\" hreflang=\"si\" />\r\n<link rel=\"alternate\" href=\"https://ocr.oldfish.cn/ne/Article.aspx?id=81&slug=rnn-sequence-modeling\" hreflang=\"ne\" />\r\n<link rel=\"alternate\" href=\"https://ocr.oldfish.cn/km/Article.aspx?id=81&slug=rnn-sequence-modeling\" hreflang=\"km\" />\r\n<link rel=\"alternate\" href=\"https://ocr.oldfish.cn/sk/Article.aspx?id=81&slug=rnn-sequence-modeling\" hreflang=\"sk\" />\r\n<link rel=\"alternate\" href=\"https://ocr.oldfish.cn/bg/Article.aspx?id=81&slug=rnn-sequence-modeling\" hreflang=\"bg\" />\r\n<link rel=\"alternate\" href=\"https://ocr.oldfish.cn/fr-ca/Article.aspx?id=81&slug=rnn-sequence-modeling\" hreflang=\"fr-ca\" />\r\n<link rel=\"alternate\" href=\"https://ocr.oldfish.cn/ha/Article.aspx?id=81&slug=rnn-sequence-modeling\" hreflang=\"ha\" />\r\n<link rel=\"alternate\" href=\"https://ocr.oldfish.cn/yo/Article.aspx?id=81&slug=rnn-sequence-modeling\" hreflang=\"yo\" />\r\n<link rel=\"alternate\" href=\"https://ocr.oldfish.cn/ig/Article.aspx?id=81&slug=rnn-sequence-modeling\" hreflang=\"ig\" />\r\n<link rel=\"alternate\" href=\"https://ocr.oldfish.cn/ku/Article.aspx?id=81&slug=rnn-sequence-modeling\" hreflang=\"ku\" />\r\n<link rel=\"alternate\" href=\"https://ocr.oldfish.cn/rw/Article.aspx?id=81&slug=rnn-sequence-modeling\" hreflang=\"rw\" />\r\n<link rel=\"alternate\" href=\"https://ocr.oldfish.cn/ca/Article.aspx?id=81&slug=rnn-sequence-modeling\" hreflang=\"ca\" />\r\n<link rel=\"alternate\" href=\"https://ocr.oldfish.cn/da/Article.aspx?id=81&slug=rnn-sequence-modeling\" hreflang=\"da\" />\r\n<link rel=\"alternate\" href=\"https://ocr.oldfish.cn/fi/Article.aspx?id=81&slug=rnn-sequence-modeling\" hreflang=\"fi\" />\r\n<link rel=\"alternate\" href=\"https://ocr.oldfish.cn/nb/Article.aspx?id=81&slug=rnn-sequence-modeling\" hreflang=\"nb\" />\r\n<link rel=\"alternate\" href=\"https://ocr.oldfish.cn/hr/Article.aspx?id=81&slug=rnn-sequence-modeling\" hreflang=\"hr\" />\r\n<link rel=\"alternate\" href=\"https://ocr.oldfish.cn/sr-Cyrl/Article.aspx?id=81&slug=rnn-sequence-modeling\" hreflang=\"sr-cyrl\" />\r\n<link rel=\"alternate\" href=\"https://ocr.oldfish.cn/sr-Latn/Article.aspx?id=81&slug=rnn-sequence-modeling\" hreflang=\"sr-latn\" />\r\n<link rel=\"alternate\" href=\"https://ocr.oldfish.cn/sq/Article.aspx?id=81&slug=rnn-sequence-modeling\" hreflang=\"sq\" />\r\n<link rel=\"alternate\" href=\"https://ocr.oldfish.cn/so/Article.aspx?id=81&slug=rnn-sequence-modeling\" hreflang=\"so\" />\r\n<link rel=\"alternate\" href=\"https://ocr.oldfish.cn/zu/Article.aspx?id=81&slug=rnn-sequence-modeling\" hreflang=\"zu\" />\r\n<link rel=\"alternate\" href=\"https://ocr.oldfish.cn/ka/Article.aspx?id=81&slug=rnn-sequence-modeling\" hreflang=\"ka\" />\r\n<meta http-equiv=\"Content-Type\" content=\"text/html; charset=UTF-8\" /><meta http-equiv=\"X-UA-Compatible\" content=\"IE=Edge,chrome=1\" /><meta name=\"renderer\" content=\"webkit\" /><meta name=\"viewport\" content=\"width=device-width, initial-scale=1.0, maximum-scale=5.0\" /><link rel=\"icon\" href=\"/favicon.ico\" type=\"image/x-icon\" /><meta name=\"robots\" content=\"all\" /><link rel=\"preconnect\" href=\"https://scm-file-new-1301864755.cos.ap-beijing.myqcloud.com\" /><link rel=\"preconnect\" href=\"https://cdn.oldfish.cn\" /><link rel=\"dns-prefetch\" href=\"https://hm.baidu.com\" /><link rel=\"dns-prefetch\" href=\"https://cdn.oldfish.cn\" /><link rel=\"preload\" href=\"/site/css/site.css\" as=\"style\" />\r\n<script>function toArray(l){var a=[];for(var i=0;i<(l?l.length:0);i++)a.push(l[i]);return a;}function forEach(l,f){for(var i=0,n=(l?l.length:0);i<n;i++)f(l[i],i);}function hasClass(e,c){if(!e||!c)return false;return(' '+e.className+' ').indexOf(' '+c+' ')>-1;}function addClass(e,c){if(!e||!c||hasClass(e,c))return;e.className=(e.className?e.className+' ':'')+c;}function removeClass(e,c){if(!e||!c)return;var u=' '+e.className+' ';while(u.indexOf(' '+c+' ')>-1){u=u.replace(' '+c+' ',' ');}e.className=u.replace(/^\\s+|\\s+$/g,'');}function toggleClass(e,c){if(!e||!c)return;if(hasClass(e,c))removeClass(e,c);else addClass(e,c);}function matchesSelector(e,s){if(!e||e.nodeType!==1)return false;if(e.matches)return e.matches(s);var p=e.parentNode||document;var m=toArray(p.querySelectorAll(s));for(var i=0;i<m.length;i++)if(m[i]===e)return true;return false;}function closest(e,s){var n=e;while(n&&n.nodeType===1){if(matchesSelector(n,s))return n;n=n.parentNode;}return null;}function safeRemove(e){if(e&&e.parentNode)e.parentNode.removeChild(e);}function qa(s,r){return toArray((r||document).querySelectorAll(s));}function q(s,r){return(r||document).querySelector(s);}function xhrGet(u,o,e){try{var x=new XMLHttpRequest();x.open('GET',u,true);x.onreadystatechange=function(){if(x.readyState===4){if(x.status>=200&&x.status<300)o&&o(x.responseText);else e&&e(x);}};x.send(null);}catch(r){e&&e(r);}}function getScrollY(){return window.pageYOffset||document.documentElement.scrollTop||document.body.scrollTop||0;}function getOrigin(){return window.location.protocol+'//'+window.location.host;}</script>\r\n<script>var isWebP=(function(){try{return document.createElement('canvas').toDataURL('image/webp').indexOf('data:image/webp')===0;}catch(e){return false;}})();document.addEventListener('DOMContentLoaded',function(){var c=document.getElementById('user-login-container');if(c){var u=c.getAttribute('data-url');if(u){xhrGet(u,function(h){c.innerHTML=h;forEach(qa('a',c),function(l){l.style.display='inline-block';l.style.marginLeft='10px';});});}}});</script>\r\n    \n    \n\n    <meta name=\"description\" content=\"او سی آر میں آر این این ، ایل ایس ٹی ایم ، جی آر یو کی درخواست میں غوطہ لگائیں۔ ترتیب ماڈلنگ کے اصولوں کا تفصیلی تجزیہ، گریڈینٹ کے مسائل کے حل، اور دو طرفہ آر این این کے فوائد.\" />\n    <meta name=\"keywords\" content=\"آر این این ، ایل ایس ٹی ایم ، جی آر یو ، سیکوئنس ماڈلنگ ، گریڈینٹ غائب ہونا ، دو طرفہ آر این این ، توجہ میکانزم ، سی آر این این ، او سی آر ، او سی آر ٹیکسٹ کی شناخت ، امیج ٹو ٹیکسٹ ، او سی آر ٹکنالوجی\" />\n    <meta property=\"og:title\" content=\"ڈیپ لرننگ او سی آر سیریز ·4】 ریکرنٹ نیورل نیٹ ورکس اور سیکوئنس ماڈلنگ\" />\n    <meta property=\"og:description\" content=\"او سی آر میں آر این این ، ایل ایس ٹی ایم ، جی آر یو کی درخواست میں غوطہ لگائیں۔ ترتیب ماڈلنگ کے اصولوں کا تفصیلی تجزیہ، گریڈینٹ کے مسائل کے حل، اور دو طرفہ آر این این کے فوائد.\" />\n    <meta property=\"og:type\" content=\"article\" />\n    <meta property=\"og:url\" content=\"https://ocr.oldfish.cn/Article.aspx?id=81&slug=rnn-sequence-modeling&lang=zh-Hans&_translation_source=true\" />\n    <meta property=\"og:site_name\" content=\"OCR ٹیکسٹ ریکوگنیشن اسسٹنٹ\" />\n    <meta name=\"twitter:card\" content=\"summary\" />\n    <meta name=\"twitter:title\" content=\"ڈیپ لرننگ او سی آر سیریز ·4】 ریکرنٹ نیورل نیٹ ورکس اور سیکوئنس ماڈلنگ\" />\n    <meta name=\"twitter:description\" content=\"او سی آر میں آر این این ، ایل ایس ٹی ایم ، جی آر یو کی درخواست میں غوطہ لگائیں۔ ترتیب ماڈلنگ کے اصولوں کا تفصیلی تجزیہ، گریڈینٹ کے مسائل کے حل، اور دو طرفہ آر این این کے فوائد.\" />\n\n    \n    <script type=\"application/ld+json\">\n    {\n        \"@context\": \"https://schema.org\",\n        \"@type\": \"Article\",\n        \"headline\": \"【ڈیپ لرننگ او سی آر سیریز 4] ریکرنٹ نیورل نیٹ ورک اور سیکوئنس ماڈلنگ\",\n        \"description\": \"او سی آر میں آر این این ، ایل ایس ٹی ایم ، جی آر یو کی درخواست میں غوطہ لگائیں۔ ترتیب ماڈلنگ کے اصولوں کا تفصیلی تجزیہ، گریڈینٹ کے مسائل کے حل، اور دو طرفہ آر این این کے فوائد。\",\n        \"publisher\": {\n            \"@type\": \"Organization\",\n            \"name\": \"OCR ٹیکسٹ ریکوگنیشن اسسٹنٹ ٹیم\"\n        },\n        \"datePublished\": \"2025-08-19T06:32:05Z\",\n        \"dateModified\": \"2025-08-19T06:32:05Z\",\n        \"mainEntityOfPage\": {\n            \"@type\": \"WebPage\",\n            \"@id\": \"https://ocr.oldfish.cn/Article.aspx?id=81&slug=rnn-sequence-modeling&lang=zh-Hans&_translation_source=true\"\n        }\n    }\n    </script>\n    \n    <style>\n        .reading-progress { position: fixed; top: 0; left: 0; width: 0%; height: 3px; background: linear-gradient(90deg, #1764ff, #0d47a1); z-index: 9999; transition: width 0.2s ease; box-shadow: 0 1px 3px rgba(23, 100, 255, 0.3); }\n        .home-container.root { overflow: visible !important; }\n        .article-detail-container { background: #f5f7fc; min-height: calc(100vh - 60px); padding: 80px 0 60px; position: relative; overflow: visible; }\n        .article-detail-container .row { margin: 0 -12px; align-items: flex-start !important; min-height: 100vh !important; }\n        .article-detail-container .row > [class*=\"col-\"] { padding: 0 12px; box-sizing: border-box; min-width: 0; }\n        .article-detail-container .row > .col-lg-8, .article-detail-container .row > .col-xl-9 { overflow: hidden; }\n        .article-detail-container .row > .col-lg-4, .article-detail-container .row > .col-xl-3 { overflow: visible !important; height: 100% !important; min-height: 100% !important; position: static !important; display: block !important; transform: none !important; will-change: auto !important; contain: none !important; perspective: none !important; filter: none !important; }\n        .article-detail { background: #fff; border-radius: 8px; padding: 1.5rem; margin-bottom: 2rem; box-shadow: 0 2px 10px rgba(0,0,0,0.1); width: 100%; box-sizing: border-box; }\n        .article-title { font-size: 1.7rem; color: #1a1c1f; line-height: 1.3; margin-bottom: 1.5rem; font-weight: 600; }\n        .article-meta { display: flex; align-items: center; flex-wrap: nowrap; gap: 1.2rem; color: #6c757d; font-size: 0.88rem; margin-bottom: 2rem; padding: 1.2rem 1.4rem; background: linear-gradient(135deg, #f8f9fa 0%, #ffffff 100%); border-radius: 12px; border-left: 4px solid #1764ff; box-shadow: 0 2px 8px rgba(0,0,0,0.06); overflow-x: hidden; transition: all 0.3s ease; }\n        .article-meta:hover { box-shadow: 0 4px 16px rgba(0,0,0,0.1); transform: translateY(-1px); }\n        .article-meta .meta-item { display: flex; align-items: center; gap: 0.25rem; font-weight: 500; color: #495057; white-space: nowrap; flex-shrink: 1; transition: color 0.2s ease; cursor: default; }\n        .article-meta .meta-item:hover { color: #1764ff; }\n        .article-meta .meta-item:hover .meta-icon { transform: scale(1.1); filter: drop-shadow(0 2px 6px rgba(13, 71, 161, 0.25)); }\n        .meta-icon { display: inline-block; font-size: 16px; flex-shrink: 0; vertical-align: middle; transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1); margin-right: 4px; opacity: 0.9; }\n        .meta-item:hover .meta-icon { opacity: 1; }\n        .meta-item:hover { color: #1764ff; }\n        .meta-item { position: relative; }\n        .meta-item::after { content: ''; position: absolute; bottom: -2px; left: 0; width: 0; height: 2px; background: linear-gradient(90deg, #1764ff, #00d4ff); transition: width 0.3s ease; }\n        .meta-item:hover::after { width: 100%; }\n        .article-category, .read-time { display: inline-block; padding: 6px 12px; border-radius: 16px; font-size: 0.8rem; font-weight: 600; }\n        .article-category { background: linear-gradient(135deg, #1764ff, #0d47a1); color: white; text-decoration: none; text-transform: uppercase; letter-spacing: 0.5px; transition: all 0.3s ease; }\n        .article-category:hover { background: linear-gradient(135deg, #0d47a1, #1764ff); color: white; text-decoration: none; transform: translateY(-1px); box-shadow: 0 4px 12px rgba(23, 100, 255, 0.3); }\n        .read-time { background: #e3f2fd; color: #1565c0; border: 1px solid #90caf9; }\n        .article-tags { margin: 20px 0 1.5rem; }\n        .article-tags .tag { display: inline-block; background: #1764ff; color: #fff; padding: 4px 12px; border-radius: 12px; font-size: 0.8rem; margin: 0 8px 4px 0; }\n        .article-summary { background: #e3f2fd; border-left: 4px solid #1764ff; padding: 1rem 1.5rem; margin-bottom: 2rem; border-radius: 0 4px 4px 0; }\n        .article-content { line-height: 1.8; font-size: 16px; color: #333; word-wrap: break-word; }\n        .article-content h1, .article-content h2, .article-content h3, .article-content h4, .article-content h5, .article-content h6 { margin: 2rem 0 1rem; font-weight: 600; line-height: 1.25; color: #1a1c1f; }\n        .article-content h1 { font-size: 2rem; border-bottom: 2px solid #e9ecef; padding-bottom: 0.5rem; }\n        .article-content h2 { font-size: 1.5rem; border-bottom: 1px solid #e9ecef; padding-bottom: 0.3rem; }\n        .article-content h3 { font-size: 1.25rem; }\n        .article-content h4 { font-size: 1.1rem; }\n        .article-content h5, .article-content h6 { font-size: 1rem; }\n        .article-content ul, .article-content ol { margin: 1rem 0; padding-left: 2rem; }\n        .article-content ul ul, .article-content ol ol, .article-content ul ol, .article-content ol ul { margin: 0.5rem 0; }\n        .article-content blockquote { margin: 1rem 0; padding: 0.5rem 1rem; border-left: 4px solid #1764ff; background-color: #f8f9fa; color: #6c757d; font-style: italic; }\n        .article-content pre { background-color: #f8f9fa; border: 1px solid #e9ecef; border-radius: 6px; padding: 1rem; margin: 1rem 0; overflow-x: auto; font-family: 'Courier New', Courier, monospace; font-size: 0.9em; line-height: 1.4; }\n        .article-content pre code { background: none; border: none; padding: 0; color: inherit; }\n        .article-content p { margin: 1rem 0; }\n        .article-content p:first-child { margin-top: 0; }\n        .article-content p:last-child { margin-bottom: 0; }\n        .article-footer { margin-top: 4rem; padding: 2.5rem; background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%); border-radius: 12px; border: 1px solid #e9ecef; position: relative; overflow: hidden; }\n        .article-footer::before { content: ''; position: absolute; top: 0; left: 0; right: 0; height: 3px; background: linear-gradient(90deg, #1764ff, #17a2b8, #28a745); }\n        .article-share h6 { color: #495057; font-weight: 600; margin-bottom: 1.5rem; font-size: 16px; }\n        .share-buttons { display: flex; gap: 12px; flex-wrap: wrap; }\n        .share-buttons .btn { padding: 10px 20px; border-radius: 25px; font-weight: 500; font-size: 14px; border: none; transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1); box-shadow: 0 2px 8px rgba(0,0,0,0.1); display: inline-flex; align-items: center; gap: 8px; text-decoration: none; }\n        .btn-weibo { background: linear-gradient(135deg, #e6162d, #c41230); color: white; }\n        .btn-copy { background: #1764ff; color: white; }\n        .btn-print { background: linear-gradient(135deg, #17a2b8, #138496); color: white; }\n        .btn-weibo:hover { background: linear-gradient(135deg, #c41230, #a00e26); box-shadow: 0 4px 16px rgba(230, 22, 45, 0.3); }\n        .btn-copy:hover { background: #0d47a1; box-shadow: 0 4px 16px rgba(23, 100, 255, 0.3); }\n        .btn-print:hover { background: linear-gradient(135deg, #138496, #117a8b); box-shadow: 0 4px 16px rgba(23, 162, 184, 0.3); }\n        .btn-weibo:hover, .btn-copy:hover, .btn-print:hover { transform: translateY(-2px); color: white; }\n        .article-actions { display: flex; justify-content: flex-end; align-items: center; }\n        .related-articles { margin-top: 3rem; }\n        .related-article-item { background: #fff; border-radius: 8px; padding: 1.5rem; margin-bottom: 1rem; box-shadow: 0 2px 8px rgba(0,0,0,0.1); transition: transform 0.3s ease; }\n        .related-article-item:hover { transform: translateY(-2px); }\n        .sidebar-card { background: linear-gradient(135deg, rgba(255,255,255,0.9) 0%, rgba(255,255,255,0.8) 100%); backdrop-filter: blur(20px); border: 1px solid rgba(255,255,255,0.3); border-radius: 16px; padding: 1rem; margin-bottom: 0.5rem; box-shadow: 0 4px 20px rgba(0,0,0,0.08), inset 0 1px 0 rgba(255,255,255,0.2); overflow: visible; box-sizing: border-box; width: 100%; z-index: 10; }\n        .sidebar-card.fixed { box-shadow: 0 8px 32px rgba(0,0,0,0.12); }\n        .sidebar-card::before { content: ''; position: absolute; top: 0; left: 0; right: 0; height: 3px; background: linear-gradient(90deg, #00d4ff, #1764ff, #9c27b0); border-radius: 20px 20px 0 0; }\n        .sidebar-card h5 { color: #1a202c; padding-bottom: 0.8rem; border-bottom: 2px solid rgba(23, 100, 255, 0.2); font-weight: 700; font-size: 1.1rem; }\n        .sidebar { min-height: 100vh; position: relative !important; display: block !important; flex-direction: column !important; gap: 1rem; }\n        .toc-card { flex: 0 1 auto; max-height: 60vh; overflow-y: auto; }\n        .sidebar-card:not(.toc-card) { flex: 1 1 auto; overflow: visible; }\n        .recommended-item { padding: 0.75rem 0; border-bottom: 1px solid rgba(0,0,0,0.05); }\n        .recommended-item:last-child { border-bottom: none; }\n        .recommended-item h6 { margin: 0 0 0.5rem 0; font-size: 0.9rem; line-height: 1.4; }\n        .recommended-item h6 a { color: #1a1c1f; text-decoration: none; transition: color 0.3s ease; }\n        .recommended-item h6 a:hover { color: #1764ff; }\n        .recommended-item .text-muted { font-size: 0.75rem; margin: 0; }\n        .toc-list { list-style: none; padding: 0; margin: 0; max-height: none; overflow-y: visible; overflow-x: hidden; }\n        .toc-list::-webkit-scrollbar { width: 4px; }\n        .toc-list::-webkit-scrollbar-track { background: rgba(0,0,0,0.1); border-radius: 2px; }\n        .toc-list::-webkit-scrollbar-thumb { background: linear-gradient(135deg, #00d4ff, #1764ff); border-radius: 2px; }\n        .toc-list::-webkit-scrollbar-thumb:hover { background: linear-gradient(135deg, #1764ff, #0d47a1); }\n        .toc-list a { color: #4a5568; text-decoration: none; font-size: 13px; font-weight: 500; display: block; padding: 8px 12px; border-radius: 8px; transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1); position: relative; margin-bottom: 4px; border: 1px solid transparent; }\n        .toc-list a::before { content: ''; position: absolute; left: 0; top: 0; bottom: 0; width: 3px; background: linear-gradient(135deg, #00d4ff, #1764ff); border-radius: 0 12px 12px 0; transform: scaleY(0); transition: transform 0.3s ease; }\n        .toc-list a:hover { color: #1764ff; background: linear-gradient(135deg, rgba(0, 212, 255, 0.1), rgba(23, 100, 255, 0.1)); border-color: rgba(23, 100, 255, 0.2); transform: translateX(8px); box-shadow: 0 4px 12px rgba(0, 212, 255, 0.2); }\n        .toc-list a:hover::before, .toc-list a.active::before { transform: scaleY(1); }\n        .toc-list a.active { color: #1764ff; background: linear-gradient(135deg, rgba(0, 212, 255, 0.15), rgba(23, 100, 255, 0.15)); font-weight: 700; border-color: rgba(23, 100, 255, 0.3); transform: translateX(4px); box-shadow: 0 6px 16px rgba(0, 212, 255, 0.25); }\n        .toc-number { color: #1764ff; font-weight: 600; margin-right: 0.5rem; font-size: 0.9em; }\n        .toc-text { flex: 1; }\n        .toc-level-1 { font-weight: 700; font-size: 15px; }\n        .toc-level-2 { font-size: 14px; }\n        .toc-level-3 { font-size: 13px; opacity: 0.9; }\n        .toc-level-4, .toc-level-5, .toc-level-6 { font-size: 12px; opacity: 0.8; }\n        .article-content .formatted-list { margin: 1.5rem 0; padding-left: 0; list-style: none; }\n        .article-content .formatted-list.ordered { counter-reset: list-counter; }\n        .article-content .formatted-list.ordered li { counter-increment: list-counter; position: relative; margin: 1rem 0; padding: 1rem 1rem 1rem 3rem; background: linear-gradient(135deg, rgba(23, 100, 255, 0.03), rgba(0, 212, 255, 0.03)); border-left: 4px solid #1764ff; border-radius: 0 8px 8px 0; transition: all 0.3s ease; }\n        .article-content .formatted-list.ordered li::before { content: counter(list-counter); position: absolute; left: 0.8rem; top: 1rem; background: linear-gradient(135deg, #1764ff, #00d4ff); color: white; width: 1.5rem; height: 1.5rem; border-radius: 50%; display: flex; align-items: center; justify-content: center; font-weight: 600; font-size: 0.9rem; }\n        .article-content .formatted-list.ordered li:hover { background: linear-gradient(135deg, rgba(23, 100, 255, 0.06), rgba(0, 212, 255, 0.06)); transform: translateX(4px); box-shadow: 0 2px 8px rgba(23, 100, 255, 0.15); }\n        .article-content .formatted-list.unordered li { position: relative; margin: 0.8rem 0; padding: 0.8rem 1rem 0.8rem 2.5rem; background: linear-gradient(135deg, rgba(40, 167, 69, 0.03), rgba(25, 135, 84, 0.03)); border-left: 4px solid #28a745; border-radius: 0 6px 6px 0; transition: all 0.3s ease; }\n        .article-content .formatted-list.unordered li::before { content: \"•\"; position: absolute; left: 1rem; top: 0.8rem; color: #28a745; font-size: 1.2rem; font-weight: bold; }\n        .article-content .formatted-list.unordered li:hover { background: linear-gradient(135deg, rgba(40, 167, 69, 0.06), rgba(25, 135, 84, 0.06)); transform: translateX(4px); box-shadow: 0 2px 8px rgba(40, 167, 69, 0.15); }\n        .article-content .list-title { color: #1764ff; font-weight: 600; display: inline-block; margin-bottom: 0.3rem; }\n        .article-content .list-content { color: #2c3e50; line-height: 1.6; display: block; }\n        .article-content .inline-code { background: #f8f9fa; border: 1px solid #e9ecef; border-radius: 4px; padding: 2px 6px; font-family: 'Consolas', 'Monaco', 'Courier New', monospace; font-size: 0.9em; color: #e83e8c; }\n        .article-content .label-item { margin: 0.8rem 0; }\n        .article-content .label { color: #1764ff; font-weight: 600; }\n        .article-content .section-label { color: #2c3e50; font-weight: 600; margin: 1rem 0 0.5rem 0; font-size: 1.1em; border-left: 4px solid #1764ff; padding-left: 0.8rem; }\n        .article-content p, .article-content .content-paragraph { margin: 1.2rem 0; line-height: 1.8; text-align: justify; color: #2c3e50; font-size: 1rem; }\n        .article-content .content-paragraph { text-indent: 0; margin: 1rem 0; }\n        .article-content .indent-paragraph { text-indent: 2em; margin: 1.2rem 0; }\n        .article-content .text-indent-1 { text-indent: 2em; display: block; }\n        .article-content .text-indent-2 { text-indent: 4em; display: block; }\n        .article-content .text-indent-3 { text-indent: 6em; display: block; }\n        .article-content .network-config { background: linear-gradient(135deg, #f8f9fa, #e9ecef); border: 1px solid #dee2e6; border-radius: 6px; padding: 0.8rem 1rem; margin: 0.5rem 0; font-family: 'Consolas', 'Monaco', 'Courier New', monospace; font-size: 0.9rem; box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05); }\n        .article-content .config-label { color: #1764ff; font-weight: 600; }\n        .article-content .config-desc { color: #495057; }\n        .article-content .algorithm-step { background: linear-gradient(135deg, rgba(23, 100, 255, 0.05), rgba(0, 212, 255, 0.05)); border-left: 4px solid #1764ff; padding: 0.8rem 1rem; margin: 0.5rem 0; border-radius: 0 6px 6px 0; transition: all 0.3s ease; }\n        .article-content .step-number { color: #1764ff; font-weight: 700; margin-right: 0.5rem; }\n        .article-content .step-desc { color: #2c3e50; line-height: 1.5; }\n        .article-content table { width: 100%; border-collapse: collapse; margin: 1.5rem 0; background: white; border-radius: 8px; overflow: hidden; box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1); }\n        .article-content table th { background: linear-gradient(135deg, #1764ff, #00d4ff); color: white; padding: 1rem; text-align: left; font-weight: 600; }\n        .article-content table td { padding: 0.8rem 1rem; border-bottom: 1px solid #e9ecef; }\n        .article-content table tr:hover { background: rgba(23, 100, 255, 0.05); }\n        .article-content strong { color: #1764ff; font-weight: 600; }\n        .article-content em { color: #6c757d; font-style: italic; }\n        .article-content a { color: #1764ff; text-decoration: none; border-bottom: 1px solid transparent; transition: all 0.3s ease; }\n        .article-content a:hover { border-bottom-color: #1764ff; color: #0056b3; }\n        .article-content .definition-item { background: linear-gradient(135deg, rgba(40, 167, 69, 0.05), rgba(25, 135, 84, 0.05)); border-left: 4px solid #28a745; padding: 1rem; margin: 1rem 0; border-radius: 0 8px 8px 0; }\n        .article-content .definition-label { color: #28a745; font-weight: 600; display: block; margin-bottom: 0.5rem; }\n        .article-content .definition-content { color: #2c3e50; line-height: 1.6; }\n        .article-content .note-item { background: linear-gradient(135deg, rgba(255, 193, 7, 0.1), rgba(255, 193, 7, 0.05)); border: 1px solid rgba(255, 193, 7, 0.3); border-radius: 8px; padding: 1rem; margin: 1rem 0; position: relative; }\n        .article-content .note-item::before { content: \"💡\"; position: absolute; left: 1rem; top: 1rem; font-size: 1.2em; }\n        .article-content .note-label { color: #856404; font-weight: 600; margin-left: 2rem; }\n        .article-content .note-content { color: #2c3e50; line-height: 1.6; margin-left: 2rem; display: block; margin-top: 0.3rem; }\n        .article-content .math-formula { background: #f8f9fa; border: 1px solid #e9ecef; border-radius: 6px; padding: 1rem; margin: 1rem 0; text-align: center; font-family: 'Times New Roman', serif; font-size: 1.1em; color: #2c3e50; }\n        @media (min-width: 1400px) { .article-content { font-size: 17px; line-height: 1.8; } .article-detail { padding: 2.5rem; } }\n        @media (max-width: 1199px) and (min-width: 992px) { .article-detail { padding: 1.5rem; } .sidebar-card { padding: 1rem; } .toc-list a { font-size: 12px; padding: 6px 8px; } }\n        @media (max-width: 768px) {\n            .article-detail-container { padding: 60px 15px; }\n            .article-content .network-config, .article-content .algorithm-step, .article-content .definition-item, .article-content .note-item { margin: 0.5rem -1rem; border-radius: 0; }\n            .article-content .note-item::before { left: 0.5rem; }\n            .article-content .note-label, .article-content .note-content { margin-left: 1.5rem; }\n            .article-detail { padding: 1.5rem; }\n            .article-title { font-size: 1.6rem; line-height: 1.3; }\n            .article-meta { flex-wrap: wrap; gap: 1rem; padding: 1rem; font-size: 0.8rem; }\n            .article-meta .meta-item { flex-shrink: 1; }\n            .meta-icon { font-size: 14px; margin-right: 3px; }\n            .article-content { font-size: 15px; }\n            .article-content h1 { font-size: 1.6rem; }\n            .article-content h2 { font-size: 1.4rem; }\n            .article-content h3 { font-size: 1.2rem; }\n            .article-content h4 { font-size: 1.1rem; }\n            .article-content pre { padding: 0.75rem; font-size: 0.85em; overflow-x: auto; }\n            .article-content code { font-size: 0.85em; padding: 1px 4px; }\n            .sidebar { margin-top: 2rem; }\n            .sidebar-card { position: static; }\n            .toc-card { max-height: none; overflow-y: visible; }\n            .share-buttons { flex-direction: column; gap: 0.5rem; }\n            .share-buttons .btn { width: 100%; justify-content: center; }\n        }\n        @media print { body * { visibility: hidden; } .article-detail, .article-detail * { visibility: visible; } .article-detail { position: absolute; left: 0; top: 0; width: 100%; background: white; box-shadow: none; border: none; margin: 0; padding: 20px; } .article-meta { border: none; background: none; padding: 0; margin-bottom: 20px; } .article-footer, .reading-progress, .back-to-top { display: none !important; } .article-content { font-size: 12pt; line-height: 1.6; } .article-content h1, .article-content h2, .article-content h3, .article-content h4, .article-content h5, .article-content h6 { page-break-after: avoid; margin-top: 20pt; margin-bottom: 10pt; } .article-content pre, .article-content blockquote { page-break-inside: avoid; } }\n    </style>\n\n<script type=\"application/ld+json\">{\"@context\":\"https://schema.org\",\"@type\":\"BreadcrumbList\",\"itemListElement\":[{\"@type\":\"ListItem\",\"position\":1,\"name\":\"گھر\",\"item\":\"https://ocr.oldfish.cn/default.aspx\"},{\"@type\":\"ListItem\",\"position\":2,\"name\":\"تکنیکی مضامین\",\"item\":\"https://ocr.oldfish.cn/Articles.aspx\"},{\"@type\":\"ListItem\",\"position\":3,\"name\":\"مضمون کی تفصیلات\",\"item\":\"https://ocr.oldfish.cn/Article.aspx?id=81&slug=rnn-sequence-modeling&lang=zh-Hans&_translation_source=true\"}]}</script>\n<link rel=\"stylesheet\" href=\"/site/css/site.css\" type=\"text/css\" /><title>ڈیپ لرننگ او سی آر سیریز ·4】 ریکرنٹ نیورل نیٹ ورکس اور سیکوئنس ماڈلنگ</title><meta http-equiv=\"Content-Language\" content=\"ur\" />\n</head>\r\n<body>\r\n    <div class=\"home-container root\">\r\n        <header class=\"v4_header_pc\">\r\n            <div class=\"header_pc_left\">\r\n                <a href=\"Default.aspx\" title=\"گھر | AI ذہین متن کی شناخت\" class=\"pc-logo-wrap ml-4 mr-5\">\r\n                    <picture>\r\n                        <source srcset=\"site/image/logo-s.png.webp\" type=\"image/webp\" />\r\n                        <img src=\"site/image/logo-s.png\" class=\"pc-logo\" style=\"height: 100%\" alt=\"او سی آر ٹیکسٹ ریکوگنیشن اسسٹنٹ آفیشل ویب سائٹ لوگو - اے آئی انٹیلیجنٹ ٹیکسٹ ریکگنیشن پلیٹ فارم\" />\r\n                    </picture>\r\n                    <span style=\"font-size: 18px; font-weight: bold; color: #333;\">OCR ٹیکسٹ ریکوگنیشن اسسٹنٹ</span>\r\n                </a>\r\n                <ul class=\"top-nav\" role=\"navigation\" aria-label=\"مرکزی نیویگیشن\">\r\n                    <li><a class=\"color-default\" href=\"Default.aspx\" title=\"OCR ٹیکسٹ ریکوگنیشن اسسٹنٹ ہوم پیج\">گھر</a></li>\r\n                    <li><a class=\"color-default\" data-sub=\"product-sub-nav\" href=\"Detail.aspx\" title=\"او سی آر پروڈکٹ فنکشن کا تعارف\">مصنوعات کی خصوصیات:</a><div class=\"triangle\"></div>\r\n                    </li>\r\n                    <li><a class=\"color-default\" data-sub=\"experience-sub-nav\" href=\"Ocr.aspx\" title=\"تجربہ او سی آر خصوصیات آن لائن\">آن لائن تجربہ</a><div class=\"triangle\"></div>\r\n                    </li>\r\n                    <li><a class=\"color-default\" data-sub=\"vip-sub-nav\" href=\"Version.aspx\" title=\"OCR رکنیت upgrade سروس\">رکنیت اپ گریڈ</a><div class=\"triangle\"></div>\r\n                    </li>\r\n                    <li><a class=\"color-default\" href=\"Download.aspx\" title=\"مفت میں او سی آر ٹیکسٹ ریکوگنیشن اسسٹنٹ ڈاؤن لوڈ کریں\">مفت ڈاؤن لوڈ</a></li>\r\n                    <li><a class=\"color-default\" data-sub=\"articles-sub-nav\" href=\"Articles.aspx\" title=\"او سی آر تکنیکی مضامین اور معلومات کا اشتراک\">ٹیکنالوجی کا اشتراک</a><div class=\"triangle\"></div>\r\n                    </li>\r\n                    <li><a class=\"color-default\" data-sub=\"support-sub-nav\" href=\"FAQ.aspx\" title=\"او سی آر کے استعمال میں مدد اور تکنیکی مدد\">مدد مرکز</a><div class=\"triangle\"></div>\r\n                    </li>\r\n                </ul>\r\n                <div class=\"product-con\">\r\n                    <section id=\"product-sub-nav\" class=\"pls-nav-dropdown\" style=\"visibility: hidden; height: 0px; display: none;\">\r\n                        <div class=\"d-flex nav-drown-con\" style=\"height: 0px; transition: transform 0.2s ease-in-out 0s,opacity 0.2s ease-in-out 0s; opacity: 0; transform: translate(0px, -100%);\">\r\n                            <div class=\"prdocu-sub-left\">\r\n                                <div class=\"mr-auto\">\r\n                                    <picture>\r\n                                        <source class=\"p-icon\" srcset=\"site/image/icon/p_1.png.webp\" type=\"image/webp\" alt=\"\" aria-hidden=\"true\" />\r\n                                        <img class=\"p-icon\" src=\"site/image/icon/p_1.png\" alt=\"OCR پروڈکٹ فنکشن آئیکن\" aria-hidden=\"true\" loading=\"lazy\" />\r\n                                    </picture>\r\n                                    <div class=\"mt-4\">\r\n                                        <h3 class=\"h6\">OCR ٹیکسٹ ریکوگنیشن اسسٹنٹ</h3>\r\n                                        <p class=\"color-gray fn14 mt-3 sub-left-desc\">کارکردگی کو بہتر بنائیں، اخراجات کو کم کریں، اور قدر پیدا کریں</p>\r\n                                        <p class=\"color-gray fn14 mt-3 sub-left-desc\">&nbsp;</p>\r\n                                        <p class=\"color-gray fn14 mt-3 sub-left-desc\">ذہین شناخت، تیز رفتار پروسیسنگ، اور درست آؤٹ پٹ</p>\r\n                                        <p class=\"color-gray fn14 mt-3 sub-left-desc\">متن سے لے کر جدول تک، فارمولوں سے ترجمہ تک</p>\r\n                                        <p class=\"color-gray fn14 mt-3 sub-left-desc\">ہر لفظ کی پروسیسنگ کو اتنا آسان بنائیں</p>\r\n                                        <a class=\"link block disa fn14 contact-business shake\" href=\"Detail.aspx\">خصوصیات کے بارے میں جانیں<span class=\"iconfont4 icon-xiangzuo gt\"></span></a>\r\n                                    </div>\r\n                                </div>\r\n                            </div>\r\n                            <div class=\"bg-white prdocu-sub-right\">\r\n                                <div>\r\n                                    <h2 class=\"text-base-color h6\">مصنوعات کی خصوصیات:</h2>\r\n                                    <div class=\"mt-4 d-flex flex-wrap\">\r\n                                        <a href=\"Detail.aspx\" class=\"pro-item color-default\" title=\"او سی آر اسسٹنٹ کے بنیادی افعال کی تفصیلات دیکھیں\">\r\n                                            <div class=\"letter-wrap\">\r\n                                                <h3 class=\"h6\">بنیادی خصوصیات:</h3>\r\n                                                <span class=\"color-gray fn14\">او سی آر اسسٹنٹ کی بنیادی خصوصیات اور تکنیکی فوائد کے بارے میں مزید جانیں ، 98٪ + شناخت کی شرح کے ساتھ</span>\r\n                                            </div>\r\n                                        </a>\r\n                                        <a href=\"Version.aspx\" class=\"pro-item color-default\" title=\"او سی آر اسسٹنٹ ورژن کے درمیان اختلافات کا موازنہ کریں\">\r\n                                            <div class=\"letter-wrap\">\r\n                                                <h3 class=\"h6\">ورژن موازنہ</h3>\r\n                                                <span class=\"color-gray fn14\">مفت ورژن ، ذاتی ورژن ، پیشہ ورانہ ورژن ، اور حتمی ورژن کے عملی اختلافات کا تفصیل سے موازنہ کریں۔</span>\r\n                                            </div>\r\n                                        </a>\r\n                                        <a href=\"FAQ.aspx\" class=\"pro-item color-default\" title=\"او سی آر اسسٹنٹ ایف اے کیو چیک کریں\">\r\n                                            <div class=\"letter-wrap\">\r\n                                                <h3 class=\"h6\">پروڈکٹ سوال و جواب</h3>\r\n                                                <span class=\"color-gray fn14\">مصنوعات کی خصوصیات، استعمال کے طریقوں، اور اکثر پوچھے جانے والے سوالات کے تفصیلی جوابات کے بارے میں جلدی سے جانیں</span>\r\n                                            </div>\r\n                                        </a>\r\n                                        <a href=\"Download.aspx\" class=\"pro-item color-default\" title=\"مفت میں او سی آر ٹیکسٹ ریکوگنیشن اسسٹنٹ ڈاؤن لوڈ کریں\">\r\n                                            <div class=\"letter-wrap\">\r\n                                                <h3 class=\"h6\">مفت میں کوشش کریں</h3>\r\n                                                <span class=\"color-gray fn14\">مفت میں طاقتور ٹیکسٹ ریکگنیشن فنکشن کا تجربہ کرنے کے لئے اب او سی آر اسسٹنٹ ڈاؤن لوڈ اور انسٹال کریں۔</span>\r\n                                            </div>\r\n                                        </a>\r\n                                        <div style=\"width: 100%; margin: 15px 0;\">\r\n                                            <h4 class=\"h6\" style=\"color: #007cfa; margin-bottom: 10px;\">آن لائن او سی آر کی شناخت</h4>\r\n                                        </div>\r\n                                        <a href=\"Ocr.aspx?type=text_recognize\" class=\"pro-item color-default\" title=\"آن لائن آفاقی متن کی شناخت کا تجربہ کریں\">\r\n                                            <div class=\"letter-wrap\">\r\n                                                <h3 class=\"h6\">عالمگیر کردار کی پہچان</h3>\r\n                                                <span class=\"color-gray fn14\">کثیر لسانی اعلی درستگی والے متن کا ذہین اخراج، مطبوعہ اور کثیر منظر پیچیدہ امیج کی شناخت کی حمایت کرتا ہے</span>\r\n                                            </div>\r\n                                        </a>\r\n                                        <a href=\"Ocr.aspx?type=table\" class=\"pro-item color-default\">\r\n                                            <div class=\"letter-wrap\">\r\n                                                <h3 class=\"h6\">Universal جدول کی شناخت</h3>\r\n                                                <span class=\"color-gray fn14\">ٹیبل تصاویر کو ایکسل فائلوں میں ذہین تبدیلی، پیچیدہ ٹیبل ڈھانچوں اور ضم شدہ خلیوں کی خود کار طریقے سے پروسیسنگ</span>\r\n                                            </div>\r\n                                        </a>\r\n                                        <a href=\"Ocr.aspx?type=handwritten_ocr\" class=\"pro-item color-default\">\r\n                                            <div class=\"letter-wrap\">\r\n                                                <h3 class=\"h6\">ہاتھ کی لکھائی کی پہچان</h3>\r\n                                                <span class=\"color-gray fn14\">چینی اور انگریزی ہاتھ سے لکھے گئے مواد کی ذہین شناخت، سپورٹ کلاس روم نوٹ، میڈیکل ریکارڈ اور دیگر منظرنامے</span>\r\n                                            </div>\r\n                                        </a>\r\n                                        <a href=\"Ocr.aspx?type=pdf2word\" class=\"pro-item color-default\">\r\n                                            <div class=\"letter-wrap\">\r\n                                                <h3 class=\"h6\">پی ڈی ایف سے ورڈ تک</h3>\r\n                                                <span class=\"color-gray fn14\">پی ڈی ایف دستاویزات تیزی سے ورڈ فارمیٹ میں تبدیل ہوجاتی ہیں ، اصل ترتیب اور گرافک ترتیب کو مکمل طور پر محفوظ کرتی ہیں۔</span>\r\n                                            </div>\r\n                                        </a>\r\n                                    </div>\r\n                                </div>\r\n                            </div>\r\n                        </div>\r\n                    </section>\r\n                    <section id=\"experience-sub-nav\" class=\"pls-nav-dropdown solution-part\" style=\"visibility: hidden; height: 0px; display: none;\">\r\n                        <div class=\"d-flex nav-drown-con\" style=\"height: 0px; transition: transform 0.2s ease-in-out 0s,opacity 0.2s ease-in-out 0s; opacity: 0; transform: translate(0px, -100%);\">\r\n                            <div class=\"prdocu-sub-left\">\r\n                                <div class=\"mr-auto\">\r\n                                    <picture>\r\n                                        <source srcset=\"site/image/icon/p_4.png.webp\" type=\"image/webp\" />\r\n                                        <img class=\"p-icon\" src=\"site/image/icon/p_4.png\" alt=\"آن لائن او سی آر تجربہ مرکز کا آئیکن\" aria-hidden=\"true\" loading=\"lazy\" />\r\n                                    </picture>\r\n                                    <div class=\"mt-4\">\r\n                                        <h3 class=\"h6\">OCR ٹیکسٹ ریکوگنیشن اسسٹنٹ</h3>\r\n                                        <p class=\"color-gray fn14 mt-3 sub-left-desc\">متن، جدول، فارمولے، دستاویزات، ترجمے</p>\r\n                                        <p class=\"color-gray fn14 mt-3 sub-left-desc\">&nbsp;</p>\r\n                                        <p class=\"color-gray fn14 mt-3 sub-left-desc\">اپنی ورڈ پروسیسنگ کی تمام ضروریات کو تین مراحل میں مکمل کریں</p>\r\n                                        <p class=\"color-gray fn14 mt-3 sub-left-desc\">اسکرین شاٹ → → ایپس کی شناخت کریں</p>\r\n                                        <p class=\"color-gray fn14 mt-3 sub-left-desc\">کام کی کارکردگی میں 300٪ اضافہ</p>\r\n                                        <a class=\"link block disa fn14 contact-business shake\" href=\"Ocr.aspx\">اب کوشش کریں<span class=\"iconfont4 icon-xiangzuo gt\"></span></a>\r\n                                    </div>\r\n                                </div>\r\n                            </div>\r\n                            <div class=\"bg-white prdocu-sub-right\">\r\n                                <div>\r\n                                    <div class=\"mt-4 d-flex flex-wrap\">\r\n                                        <div style=\"width: 100%; margin-bottom: 15px;\">\r\n                                            <h4 class=\"h6\" style=\"color: #007cfa; margin-bottom: 10px;\">OCR فنکشن کا تجربہ</h4>\r\n                                        </div>\r\n                                        <a href=\"Ocr.aspx?type=index\" class=\"pro-item color-default\">\r\n                                            <div class=\"letter-wrap\">\r\n                                                <h3 class=\"h6\">مکمل فعالیت</h3>\r\n                                                <span class=\"color-gray fn14\">آپ کی ضروریات کے لئے بہترین حل تلاش کرنے کے لئے ایک ہی جگہ پر تمام او سی آر سمارٹ خصوصیات کا تجربہ کریں</span>\r\n                                            </div>\r\n                                        </a>\r\n                                        <a href=\"Ocr.aspx?type=text_recognize\" class=\"pro-item color-default\">\r\n                                            <div class=\"letter-wrap\">\r\n                                                <h3 class=\"h6\">عالمگیر کردار کی پہچان</h3>\r\n                                                <span class=\"color-gray fn14\">کثیر لسانی اعلی درستگی والے متن کا ذہین اخراج، مطبوعہ اور کثیر منظر پیچیدہ امیج کی شناخت کی حمایت کرتا ہے</span>\r\n                                            </div>\r\n                                        </a>\r\n                                        <a href=\"Ocr.aspx?type=table\" class=\"pro-item color-default\">\r\n                                            <div class=\"letter-wrap\">\r\n                                                <h3 class=\"h6\">Universal جدول کی شناخت</h3>\r\n                                                <span class=\"color-gray fn14\">ٹیبل تصاویر کو ایکسل فائلوں میں ذہین تبدیلی، پیچیدہ ٹیبل ڈھانچوں اور ضم شدہ خلیوں کی خود کار طریقے سے پروسیسنگ</span>\r\n                                            </div>\r\n                                        </a>\r\n                                        <a href=\"Ocr.aspx?type=handwritten_ocr\" class=\"pro-item color-default\">\r\n                                            <div class=\"letter-wrap\">\r\n                                                <h3 class=\"h6\">ہاتھ کی لکھائی کی پہچان</h3>\r\n                                                <span class=\"color-gray fn14\">چینی اور انگریزی ہاتھ سے لکھے گئے مواد کی ذہین شناخت، سپورٹ کلاس روم نوٹ، میڈیکل ریکارڈ اور دیگر منظرنامے</span>\r\n                                            </div>\r\n                                        </a>\r\n                                        <a href=\"Ocr.aspx?type=pdf2word\" class=\"pro-item color-default\">\r\n                                            <div class=\"letter-wrap\">\r\n                                                <h3 class=\"h6\">پی ڈی ایف سے ورڈ تک</h3>\r\n                                                <span class=\"color-gray fn14\">پی ڈی ایف دستاویزات تیزی سے ورڈ فارمیٹ میں تبدیل ہوجاتی ہیں ، اصل ترتیب اور گرافک ترتیب کو مکمل طور پر محفوظ کرتی ہیں۔</span>\r\n                                            </div>\r\n                                        </a>\r\n                                        <a href=\"Ocr.aspx?type=pdf2markdown\" class=\"pro-item color-default\">\r\n                                            <div class=\"letter-wrap\">\r\n                                                <h3 class=\"h6\">پی ڈی ایف سے مارک ڈاؤن</h3>\r\n                                                <span class=\"color-gray fn14\">پی ڈی ایف دستاویزات کو ذہین طریقے سے ایم ڈی فارمیٹ میں تبدیل کیا جاتا ہے ، اور کوڈ بلاکس اور ٹیکسٹ اسٹرکچرز کو پروسیسنگ کے لئے خود بخود بہتر بنایا جاتا ہے۔</span>\r\n                                            </div>\r\n                                        </a>\r\n                                        <div style=\"width: 100%; margin: 15px 0;\">\r\n                                            <h4 class=\"h6\" style=\"color: #007cfa; margin-bottom: 10px;\">دستاویز پروسیسنگ ٹولز</h4>\r\n                                        </div>\r\n                                        <a href=\"Ocr.aspx?type=word2pdf\" class=\"pro-item color-default\">\r\n                                            <div class=\"letter-wrap\">\r\n                                                <h3 class=\"h6\">لفظ سے پی ڈی ایف تک</h3>\r\n                                                <span class=\"color-gray fn14\">ورڈ دستاویزات کو ایک کلک کے ساتھ پی ڈی ایف میں تبدیل کیا جاتا ہے ، اصل شکل کو مکمل طور پر برقرار رکھتے ہوئے ، آرکائیونگ اور سرکاری دستاویز شیئرنگ کے لئے موزوں ہے۔</span>\r\n                                            </div>\r\n                                        </a>\r\n                                        <a href=\"Ocr.aspx?type=word2jpg\" class=\"pro-item color-default\">\r\n                                            <div class=\"letter-wrap\">\r\n                                                <h3 class=\"h6\">لفظ سے تصویر تک</h3>\r\n                                                <span class=\"color-gray fn14\">ورڈ دستاویز کو جے پی جی امیج میں ذہین تبدیلی، ملٹی پیج پروسیسنگ کی حمایت، سوشل میڈیا پر شیئر کرنا آسان</span>\r\n                                            </div>\r\n                                        </a>\r\n                                        <a href=\"Ocr.aspx?type=pdf2jpg\" class=\"pro-item color-default\">\r\n                                            <div class=\"letter-wrap\">\r\n                                                <h3 class=\"h6\">تصویر میں پی ڈی ایف</h3>\r\n                                                <span class=\"color-gray fn14\">پی ڈی ایف دستاویزات کو ہائی ڈیفینیشن میں جے پی جی تصاویر میں تبدیل کریں ، بیچ پروسیسنگ اور اپنی مرضی کے مطابق ریزولوشن کی حمایت کریں۔</span>\r\n                                            </div>\r\n                                        </a>\r\n                                        <a href=\"Ocr.aspx?type=image2pdf\" class=\"pro-item color-default\">\r\n                                            <div class=\"letter-wrap\">\r\n                                                <h3 class=\"h6\">تصویر سے پی ڈی ایف تک</h3>\r\n                                                <span class=\"color-gray fn14\">متعدد تصاویر کو پی ڈی ایف دستاویزات میں ضم کریں ، ترتیب اور صفحے کے سیٹ اپ کی حمایت کریں۔</span>\r\n                                            </div>\r\n                                        </a>\r\n                                        <div style=\"width: 100%; margin: 15px 0;\">\r\n                                            <h4 class=\"h6\" style=\"color: #007cfa; margin-bottom: 10px;\">Developer tools</h4>\r\n                                        </div>\r\n                                        <a href=\"Tool.aspx?type=Json\" class=\"pro-item color-default\">\r\n                                            <div class=\"letter-wrap\">\r\n                                                <h3 class=\"h6\">JSON formatting</h3>\r\n                                                <span class=\"color-gray fn14\">ذہین طریقے سے جے ایس او این کوڈ ڈھانچے کو خوبصورت بنائیں ، کمپریشن اور توسیع کی حمایت کریں ، اور ترقی اور ڈیبگنگ کی سہولت فراہم کریں۔</span>\r\n                                            </div>\r\n                                        </a>\r\n                                        <a href=\"Tool.aspx?type=Regex\" class=\"pro-item color-default\">\r\n                                            <div class=\"letter-wrap\">\r\n                                                <h3 class=\"h6\">باقاعدگی سے اظہار</h3>\r\n                                                <span class=\"color-gray fn14\">عام نمونوں کی بلٹ ان لائبریری کے ساتھ حقیقی وقت میں باقاعدگی سے اظہار میچنگ اثرات کی تصدیق کریں</span>\r\n                                            </div>\r\n                                        </a>\r\n                                        <a href=\"Tool.aspx?type=Encode\" class=\"pro-item color-default\">\r\n                                            <div class=\"letter-wrap\">\r\n                                                <h3 class=\"h6\">ٹیکسٹ انکوڈنگ کی تبدیلی</h3>\r\n                                                <span class=\"color-gray fn14\">یہ بیس 64 ، یو آر ایل ، اور یونیکوڈ جیسے متعدد انکوڈنگ فارمیٹس کی تبدیلی کی حمایت کرتا ہے۔</span>\r\n                                            </div>\r\n                                        </a>\r\n                                        <a href=\"Tool.aspx?type=Diff\" class=\"pro-item color-default\">\r\n                                            <div class=\"letter-wrap\">\r\n                                                <h3 class=\"h6\">متن کا ملاپ اور انضمام</h3>\r\n                                                <span class=\"color-gray fn14\">متن کے اختلافات کو اجاگر کریں اور لائن بہ لائن موازنہ اور ذہین انضمام کی حمایت کریں</span>\r\n                                            </div>\r\n                                        </a>\r\n                                        <a href=\"Tool.aspx?type=Color\" class=\"pro-item color-default\">\r\n                                            <div class=\"letter-wrap\">\r\n                                                <h3 class=\"h6\">رنگ کا ٹول</h3>\r\n                                                <span class=\"color-gray fn14\">آر جی بی / ایچ ای ایکس رنگ کی تبدیلی ، آن لائن رنگ چننے والا ، فرنٹ اینڈ ڈویلپمنٹ کے لئے لازمی ٹول</span>\r\n                                            </div>\r\n                                        </a>\r\n                                        <a href=\"Tool.aspx?type=Count\" class=\"pro-item color-default\">\r\n                                            <div class=\"letter-wrap\">\r\n                                                <h3 class=\"h6\">الفاظ کی گنتی</h3>\r\n                                                <span class=\"color-gray fn14\">حروف، الفاظ اور پیراگراف کی ذہین گنتی، اور متن کی ترتیب کو خود بخود بہتر بنانا</span>\r\n                                            </div>\r\n                                        </a>\r\n                                        <a href=\"Tool.aspx?type=Timespan\" class=\"pro-item color-default\">\r\n                                            <div class=\"letter-wrap\">\r\n                                                <h3 class=\"h6\">Timestamp conversion</h3>\r\n                                                <span class=\"color-gray fn14\">وقت کو یونیکس ٹائم اسٹیمپس میں تبدیل کیا جاتا ہے ، اور متعدد فارمیٹس اور ٹائم زون کی ترتیبات کی حمایت کی جاتی ہے۔</span>\r\n                                            </div>\r\n                                        </a>\r\n                                        <a href=\"Tool.aspx?type=Calc\" class=\"pro-item color-default\">\r\n                                            <div class=\"letter-wrap\">\r\n                                                <h3 class=\"h6\">کیلکولیٹر ٹول</h3>\r\n                                                <span class=\"color-gray fn14\">بنیادی آپریشنز اور جدید ریاضیاتی فنکشن حسابات کی حمایت کے ساتھ آن لائن سائنسی کیلکولیٹر</span>\r\n                                            </div>\r\n                                        </a>\r\n                                    </div>\r\n                                </div>\r\n                            </div>\r\n                        </div>\r\n                    </section>\r\n                    <section id=\"articles-sub-nav\" class=\"pls-nav-dropdown solution-part\" style=\"visibility: hidden; height: 0px; display: none;\">\r\n                        <div class=\"d-flex nav-drown-con\" style=\"height: 0px; transition: transform 0.2s ease-in-out 0s,opacity 0.2s ease-in-out 0s; opacity: 0; transform: translate(0px, -100%);\">\r\n                            <div class=\"prdocu-sub-left\">\r\n                                <div class=\"mr-auto\">\r\n                                    <picture>\r\n                                        <source srcset=\"site/image/icon/p_4.png.webp\" type=\"image/webp\" />\r\n                                        <img class=\"p-icon\" src=\"site/image/icon/p_4.png\" alt=\"ٹیک شیئرنگ سینٹر آئیکن\" aria-hidden=\"true\" loading=\"lazy\" />\r\n                                    </picture>\r\n                                    <div class=\"mt-4\">\r\n                                        <h3 class=\"h6\">او سی آر ٹیکنالوجی شیئرنگ</h3>\r\n                                        <p class=\"color-gray fn14 mt-3 sub-left-desc\">تکنیکی سبق، درخواست کے معاملات، آلے کی سفارشات</p>\r\n                                        <p class=\"color-gray fn14 mt-3 sub-left-desc\">&nbsp;</p>\r\n                                        <p class=\"color-gray fn14 mt-3 sub-left-desc\">ابتدائی سے مہارت تک سیکھنے کا ایک مکمل راستہ</p>\r\n                                        <p class=\"color-gray fn14 mt-3 sub-left-desc\">عملی معاملات → تکنیکی تجزیہ → ٹول ایپلی کیشنز</p>\r\n                                        <p class=\"color-gray fn14 mt-3 sub-left-desc\">او سی آر ٹکنالوجی کی بہتری کے لئے اپنے راستے کو بااختیار بنائیں</p>\r\n                                        <a class=\"link block disa fn14 contact-business shake\" href=\"Articles.aspx\">مضامین براؤز کریں<span class=\"iconfont4 icon-xiangzuo gt\"></span></a>\r\n                                    </div>\r\n                                </div>\r\n                            </div>\r\n                            <div class=\"bg-white prdocu-sub-right\">\r\n                                <div>\r\n                                    <h2 class=\"text-base-color h6\">ٹیکنالوجی کا اشتراک</h2>\r\n                                    <div class=\"mt-4 d-flex flex-wrap\">\r\n                                        <a href=\"Articles.aspx\" class=\"pro-item color-default\" title=\"تمام OCR تکنیکی مضامین دیکھیں\">\r\n                                            <div class=\"letter-wrap\">\r\n                                                <h3 class=\"h6\">تمام مضامین</h3>\r\n                                                <span class=\"color-gray fn14\">او سی آر کے تمام تکنیکی مضامین کو براؤز کریں جو بنیادی سے لے کر اعلی درجے تک کے علم کے مکمل جسم کا احاطہ کرتے ہیں۔</span>\r\n                                            </div>\r\n                                        </a>\r\n                                        \r\n                                        <a href=\"Articles.aspx?category=2\" class=\"pro-item color-default\" title=\"او سی آر تکنیکی ٹیوٹوریل اور شروع گائیڈ حاصل کرنا\">\r\n                                            <div class=\"letter-wrap\">\r\n                                                <h3 class=\"h6\">Advanced Guide</h3>\r\n                                                <span class=\"color-gray fn14\">تعارفی سے لے کر ماہر او سی آر تکنیکی ٹیوٹوریل، تفصیلی گائیڈز اور عملی واک تھرو</span>\r\n                                            </div>\r\n                                        </a>\r\n                                        \r\n                                        <a href=\"Articles.aspx?category=3\" class=\"pro-item color-default\" title=\"او سی آر ٹیکنالوجی کے اصول، الگورتھم اور ایپلی کیشنز\">\r\n                                            <div class=\"letter-wrap\">\r\n                                                <h3 class=\"h6\">تکنیکی تلاش</h3>\r\n                                                <span class=\"color-gray fn14\">اصولوں سے ایپلی کیشنز تک او سی آر ٹکنالوجی کی سرحدوں کو تلاش کریں ، اور بنیادی الگورتھم کا گہرائی سے تجزیہ کریں۔</span>\r\n                                            </div>\r\n                                        </a>\r\n                                        \r\n                                        <a href=\"Articles.aspx?category=4\" class=\"pro-item color-default\" title=\"او سی آر صنعت میں تازہ ترین پیش رفت اور ترقی کے رجحانات\">\r\n                                            <div class=\"letter-wrap\">\r\n                                                <h3 class=\"h6\">صنعت کے رجحانات</h3>\r\n                                                <span class=\"color-gray fn14\">او سی آر ٹیکنالوجی کی ترقی کے رجحانات، مارکیٹ تجزیہ، صنعت کی حرکیات، اور مستقبل کے امکانات میں گہری بصیرت</span>\r\n                                            </div>\r\n                                        </a>\r\n                                        \r\n                                        <a href=\"Articles.aspx?category=5\" class=\"pro-item color-default\" title=\"مختلف صنعتوں میں او سی آر ٹکنالوجی کے اطلاق کے معاملات\">\r\n                                            <div class=\"letter-wrap\">\r\n                                                <h3 class=\"h6\">کیسز کا استعمال کریں:</h3>\r\n                                                <span class=\"color-gray fn14\">مختلف صنعتوں میں او سی آر ٹکنالوجی کے حقیقی دنیا کے ایپلی کیشن کیسز ، حل اور بہترین طریقوں کا اشتراک کیا جاتا ہے۔</span>\r\n                                            </div>\r\n                                        </a>\r\n                                        \r\n                                        <a href=\"Articles.aspx?category=6\" class=\"pro-item color-default\" title=\"پیشہ ورانہ جائزے، تقابلی تجزیہ، اور او سی آر سافٹ ویئر ٹولز کے استعمال کے لئے تجویز کردہ رہنما خطوط\">\r\n                                            <div class=\"letter-wrap\">\r\n                                                <h3 class=\"h6\">آلے کا جائزہ</h3>\r\n                                                <span class=\"color-gray fn14\">مختلف او سی آر ٹیکسٹ ریکوگنیشن سافٹ ویئر اور ٹولز کا جائزہ لیں ، اور تفصیلی فنکشن موازنہ اور انتخاب کی تجاویز فراہم کریں۔</span>\r\n                                            </div>\r\n                                        </a>\r\n                                        \r\n                                    </div>\r\n                                </div>\r\n                            </div>\r\n                        </div>\r\n                    </section>\r\n                    <section id=\"vip-sub-nav\" class=\"pls-nav-dropdown solution-part\" style=\"visibility: hidden; height: 0px; display: none;\">\r\n                        <div class=\"d-flex nav-drown-con\" style=\"height: 0px; transition: transform 0.2s ease-in-out 0s,opacity 0.2s ease-in-out 0s; opacity: 0; transform: translate(0px, -100%);\">\r\n                            <div class=\"prdocu-sub-left\">\r\n                                <div class=\"mr-auto\">\r\n                                    <picture>\r\n                                        <source class=\"p-icon\" srcset=\"site/image/icon/p_1.png.webp\" type=\"image/webp\" alt=\"\" aria-hidden=\"true\" />\r\n                                        <img class=\"p-icon\" src=\"site/image/icon/p_1.png\" alt=\"رکنیت اپ گریڈ سروس icon\" aria-hidden=\"true\" loading=\"lazy\" />\r\n                                    </picture>\r\n                                    <div class=\"mt-4\">\r\n                                        <h3 class=\"h6\">رکنیت upgrade سروس</h3>\r\n                                        <p class=\"color-gray fn14 mt-3 sub-left-desc\">تمام پریمیم خصوصیات کو ان لاک کریں اور خصوصی خدمات سے لطف اٹھائیں</p>\r\n                                        <p class=\"color-gray fn14 mt-3 sub-left-desc\">&nbsp;</p>\r\n                                        <p class=\"color-gray fn14 mt-3 sub-left-desc\">آف لائن شناخت، بیچ پروسیسنگ، لامحدود استعمال</p>\r\n                                        <p class=\"color-gray fn14 mt-3 sub-left-desc\">پرو → الٹیمیٹ → انٹرپرائز</p>\r\n                                        <p class=\"color-gray fn14 mt-3 sub-left-desc\">آپ کی ضروریات کے مطابق کچھ ہے</p>\r\n                                        <a class=\"link block disa fn14 contact-business shake\" href=\"Version.aspx\">تفصیلات دیکھیں<span class=\"iconfont4 icon-xiangzuo gt\"></span></a>\r\n                                    </div>\r\n                                </div>\r\n                            </div>\r\n                            <div class=\"bg-white prdocu-sub-right\">\r\n                                <div>\r\n                                    <h2 class=\"text-base-color h6\" style=\"margin: inherit;\">رکنیت اپ گریڈ</h2>\r\n                                    <div class=\"mt-4 d-flex flex-wrap\">\r\n                                        <a href=\"Version.aspx\" class=\"pro-item color-default\">\r\n                                            <div class=\"letter-wrap\">\r\n                                                <h3 class=\"h6\">رکنیت کے استحقاق</h3>\r\n                                                <span class=\"color-gray fn14\">ایڈیشنز کے درمیان اختلافات کے بارے میں مزید جانیں اور رکنیت کی سطح کا انتخاب کریں جو آپ کے لئے بہترین ہے۔</span>\r\n                                            </div>\r\n                                        </a>\r\n                                        <a href=\"Upgrade.aspx\" class=\"pro-item color-default\">\r\n                                            <div class=\"letter-wrap\">\r\n                                                <h3 class=\"h6\">اب اپ گریڈ کریں</h3>\r\n                                                <span class=\"color-gray fn14\">مزید پریمیم خصوصیات اور خصوصی خدمات کو ان لاک کرنے کے لئے اپنی وی آئی پی رکنیت کو تیزی سے اپ گریڈ کریں</span>\r\n                                            </div>\r\n                                        </a>\r\n                                        <a href=\"User.aspx\" class=\"pro-item color-default\">\r\n                                            <div class=\"letter-wrap\">\r\n                                                <h3 class=\"h6\">میرا اکاؤنٹ</h3>\r\n                                                <span class=\"color-gray fn14\">ترتیبات کو ذاتی بنانے کے لئے اکاؤنٹ کی معلومات، سبسکرپشن کی حیثیت، اور استعمال کی تاریخ کا انتظام کریں</span>\r\n                                            </div>\r\n                                        </a>\r\n                                    </div>\r\n                                </div>\r\n                            </div>\r\n                        </div>\r\n                    </section>\r\n                    <section id=\"support-sub-nav\" class=\"pls-nav-dropdown solution-part\" style=\"visibility: hidden; height: 0px; display: none;\">\r\n                        <div class=\"d-flex nav-drown-con\" style=\"height: 0px; transition: transform 0.2s ease-in-out 0s,opacity 0.2s ease-in-out 0s; opacity: 0; transform: translate(0px, -100%);\">\r\n                            <div class=\"prdocu-sub-left\">\r\n                                <div class=\"mr-auto\">\r\n                                    <picture>\r\n                                        <source class=\"p-icon\" srcset=\"site/image/icon/p_4.png.webp\" type=\"image/webp\" alt=\"\" aria-hidden=\"true\" />\r\n                                        <img class=\"p-icon\" src=\"site/image/icon/p_4.png\" alt=\"مدد مرکز کی حمایت آئیکن\" aria-hidden=\"true\" loading=\"lazy\" />\r\n                                    </picture>\r\n                                    <div class=\"mt-4\">\r\n                                        <h3 class=\"h6\">مدد مرکز</h3>\r\n                                        <p class=\"color-gray fn14 mt-3 sub-left-desc\">پیشہ ورانہ کسٹمر سروس، تفصیلی دستاویزات، اور فوری جواب</p>\r\n                                        <p class=\"color-gray fn14 mt-3 sub-left-desc\">&nbsp;</p>\r\n                                        <p class=\"color-gray fn14 mt-3 sub-left-desc\">جب آپ کو مسائل کا سامنا ہو تو گھبرائیں نہیں</p>\r\n                                        <p class=\"color-gray fn14 mt-3 sub-left-desc\">مسئلہ → → حل تلاش کرتے ہیں</p>\r\n                                        <p class=\"color-gray fn14 mt-3 sub-left-desc\">اپنے تجربے کو ہموار بنائیں</p>\r\n                                        <a class=\"link block disa fn14 contact-business shake\" href=\"FAQ.aspx\">مدد حاصل کریں<span class=\"iconfont4 icon-xiangzuo gt\"></span></a>\r\n                                    </div>\r\n                                </div>\r\n                            </div>\r\n                            <div class=\"bg-white prdocu-sub-right\">\r\n                                <div>\r\n                                    <h2 class=\"text-base-color h6\" style=\"margin: inherit;\">مدد مرکز</h2>\r\n                                    <div class=\"mt-4 d-flex flex-wrap\">\r\n                                        <a href=\"FAQ.aspx\" class=\"pro-item color-default\">\r\n                                            <div class=\"letter-wrap\">\r\n                                                <h3 class=\"h6\">اکثر پوچھے جانے والے سوالات</h3>\r\n                                                <span class=\"color-gray fn14\">فوری طور پر عام صارف کے سوالات کے جوابات دیں اور تفصیلی استعمال گائیڈز اور تکنیکی مدد فراہم کریں</span>\r\n                                            </div>\r\n                                        </a>\r\n                                        <a href=\"AboutUs.aspx\" class=\"pro-item color-default\">\r\n                                            <div class=\"letter-wrap\">\r\n                                                <h3 class=\"h6\">ہماری بارے ميں</h3>\r\n                                                <span class=\"color-gray fn14\">او سی آر ٹیکسٹ ریکوگنیشن اسسٹنٹ کی ترقی کی تاریخ ، بنیادی افعال اور خدمت کے تصورات کے بارے میں جانیں۔</span>\r\n                                            </div>\r\n                                        </a>\r\n                                        <a href=\"UserAgreement.aspx\" class=\"pro-item color-default\">\r\n                                            <div class=\"letter-wrap\">\r\n                                                <h3 class=\"h6\">صارف کا معاہدہ</h3>\r\n                                                <span class=\"color-gray fn14\">سروس کی تفصیلی شرائط اور صارف کے حقوق اور ذمہ داریاں</span>\r\n                                            </div>\r\n                                        </a>\r\n                                        <a href=\"PrivacyPolicy.aspx\" class=\"pro-item color-default\">\r\n                                            <div class=\"letter-wrap\">\r\n                                                <h3 class=\"h6\">رازداری کا معاہدہ</h3>\r\n                                                <span class=\"color-gray fn14\">ذاتی معلومات کے تحفظ کی پالیسی اور ڈیٹا سیکورٹی کے اقدامات</span>\r\n                                            </div>\r\n                                        </a>\r\n                                        <a href=\"Status.aspx\" class=\"pro-item color-default\">\r\n                                            <div class=\"letter-wrap\">\r\n                                                <h3 class=\"h6\">نظام کی حیثیت</h3>\r\n                                                <span class=\"color-gray fn14\">ریئل ٹائم میں عالمی شناختی نوڈز کی آپریشن کی حیثیت کی نگرانی کریں اور سسٹم کی کارکردگی کے اعداد و شمار دیکھیں</span>\r\n                                            </div>\r\n                                        </a>\r\n                                        <a href=\"javascript:void(0);\" class=\"pro-item color-default\" onclick=\"if(window.openCustomerService) window.openCustomerService(); else alert('براہ کرم کسٹمر سروس سے رابطہ کرنے کے لئے دائیں طرف فلوٹنگ ونڈو آئیکن پر کلک کریں۔');\">\r\n                                            <div class=\"letter-wrap\">\r\n                                                <h3 class=\"h6\">کسٹمر سروس سے رابطہ کریں</h3>\r\n                                                <span class=\"color-gray fn14\">آپ کے سوالات اور ضروریات کا فوری جواب دینے کے لئے آن لائن کسٹمر سروس کی مدد</span>\r\n                                            </div>\r\n                                        </a>\r\n                                    </div>\r\n                                </div>\r\n                            </div>\r\n                        </div>\r\n                    </section>\r\n                </div>\r\n            </div>\r\n            <div class=\"header_pc_right\" style=\"display: flex; align-items: center; justify-content: flex-end;\">\r\n                <div id=\"translate\"></div>\r\n                <div id=\"user-login-container\" data-url=\"User.ashx?op=userinfo\" style=\"white-space: nowrap; padding-right: 20px;\"></div>\r\n            </div>\r\n        </header>\r\n        <header class=\"v4_header_mob\">\r\n            <div class=\"container\">\r\n                <div class=\"row no-gutters justify-content-between align-items-center\">\r\n                    <a href=\"Default.aspx\" title=\"گھر | AI ذہین متن کی شناخت\" class=\"mob-logo-wrap\">\r\n                        <picture>\r\n                            <source srcset=\"site/image/logo-s.png.webp\" type=\"image/webp\" />\r\n                            <img src=\"site/image/logo-s.png\" class=\"pc-logo\" style=\"height: 32px; width: 32px\" alt=\"او سی آر ٹیکسٹ ریکوگنیشن اسسٹنٹ موبائل لوگو\" />\r\n                        </picture>\r\n                        <span style=\"font-size: 18px; font-weight: bold; color: #333;\">OCR ٹیکسٹ ریکوگنیشن اسسٹنٹ</span>\r\n                    </a>\r\n                    <div class=\"right-menu\" role=\"button\" tabindex=\"0\" aria-label=\"نیویگیشن مینو کھولیں\" data-toggle=\"collapse\" data-target=\"#mob-header-collapse\" aria-expanded=\"false\" aria-controls=\"mob-header-collapse\">\r\n                        <nav-products id=\"nav_products\">\r\n                            <nav-button class=\"submail-mdi-nav-btn\" style=\"width: 60px\">\r\n                                <label id=\"mdi-submail-nav-btn\">\r\n                                    <span class=\"mdi-nav-products-1\">\r\n                                        <span class=\"nav-products-icon\"></span>\r\n                                    </span>\r\n                                    <span class=\"mdi-nav-products-2\">\r\n                                        <span class=\"nav-products-icon\"></span>\r\n                                    </span>\r\n                                    <span class=\"mdi-nav-products-3\">\r\n                                        <span class=\"nav-products-icon\"></span>\r\n                                    </span>\r\n                                </label>\r\n                            </nav-button>\r\n                        </nav-products>\r\n                    </div>\r\n                </div>\r\n            </div>\r\n            <div class=\"mob-nav-content\">\r\n                <div class=\"sidebar-fix\">\r\n                    <div class=\"container\">\r\n                        <div class=\"row\">\r\n                            <div class=\"col-4 bg-white sidebar-fix-left\" style=\"border-right: 1px solid #e1e6ed\">\r\n                                <div class=\"mob-nav-item active\" data=\"0\">\r\n                                    <div class=\"nav-header\"><a href=\"Default.aspx\" class=\"nav-header-letter px-3\"><span>گھر</span></a></div>\r\n                                </div>\r\n                                <div class=\"mob-nav-item\" data=\"1\">\r\n                                    <div class=\"nav-header\"><a href=\"Detail.aspx\" class=\"nav-header-letter px-3\"><span>کار</span></a></div>\r\n                                </div>\r\n                                <div class=\"mob-nav-item\" data=\"2\">\r\n                                    <div class=\"nav-header\"><a href=\"Ocr.aspx\" class=\"nav-header-letter px-3\"><span>تجربہ</span></a></div>\r\n                                </div>\r\n                                <div class=\"mob-nav-item\" data=\"3\">\r\n                                    <div class=\"nav-header\"><a href=\"Version.aspx\" class=\"nav-header-letter px-3\"><span>رکن</span></a></div>\r\n                                </div>\r\n                                <div class=\"mob-nav-item\" data=\"4\">\r\n                                    <div class=\"nav-header\"><a href=\"Download.aspx\" class=\"nav-header-letter px-3\"><span>ڈاؤن لوڈ</span></a></div>\r\n                                </div>\r\n                                <div class=\"mob-nav-item\" data=\"5\">\r\n                                    <div class=\"nav-header\"><a href=\"Articles.aspx\" class=\"nav-header-letter px-3\"><span>حصہ</span></a></div>\r\n                                </div>\r\n                                <div class=\"mob-nav-item\" data=\"6\">\r\n                                    <div class=\"nav-header\"><a href=\"FAQ.aspx\" class=\"nav-header-letter px-3\"><span>مدد</span></a></div>\r\n                                </div>\r\n                            </div>\r\n                            <div class=\"col-8 bg-white sidebar-fix-rigth\">\r\n                                <div class=\"sub-nav\">\r\n                                    <div class=\"sub-nav-item py-2 pb-3 px-4\">\r\n                                        <h2 class=\"h5\" style=\"margin: inherit;\">موثر پیداواری اوزار</h2>\r\n                                        <p class=\"color-gray fn14 mt-3 sub-left-desc\">ذہین شناخت، تیز رفتار پروسیسنگ، اور درست آؤٹ پٹ</p>\r\n                                        <p class=\"color-gray fn14 mt-3 sub-left-desc\">&nbsp;</p>\r\n                                        <p class=\"color-gray fn14 mt-3 sub-left-desc\">3 سیکنڈ میں دستاویزات کے مکمل صفحے کی شناخت کریں</p>\r\n                                        <p class=\"color-gray fn14 mt-3 sub-left-desc\">98٪ + شناخت کی درستگی</p>\r\n                                        <p class=\"color-gray fn14 mt-3 sub-left-desc\">تاخیر کے بغیر کثیر لسانی ریئل ٹائم پروسیسنگ</p>\r\n                                        <a class=\"link block disa fn14 contact-business shake\" href=\"Download.aspx\">اب تجربہ ڈاؤن لوڈ کریں<span class=\"iconfont4 icon-xiangzuo gt\"></span></a>\r\n                                    </div>\r\n                                </div>\r\n                                <div class=\"sub-nav\">\r\n                                    <div class=\"sub-nav-item py-3 px-4\">\r\n                                        <h2 class=\"h5\" style=\"margin: inherit;\">مصنوعات کی خصوصیات:</h2>\r\n                                        <p class=\"color-gray fn14 mt-3 sub-left-desc\">AI ذہین شناخت, ون اسٹاپ حل</p>\r\n                                    </div>\r\n                                    <div class=\"sub-nav-item py-3 px-4\"><a href=\"Detail.aspx\">فنکشن کا تعارف</a></div>\r\n                                    <div class=\"sub-nav-item py-3 px-4\"><a href=\"Download.aspx\">سافٹ ویئر ڈاؤن لوڈ</a></div>\r\n                                    <div class=\"sub-nav-item py-3 px-4\"><a href=\"Version.aspx\">ورژن موازنہ</a></div>\r\n                                    <div class=\"sub-nav-item py-3 px-4\"><a href=\"Ocr.aspx\">آن لائن تجربہ</a></div>\r\n                                    <div class=\"sub-nav-item py-3 px-4\"><a href=\"Status.aspx\">نظام کی حیثیت</a></div>\r\n                                </div>\r\n                                <div class=\"sub-nav\">\r\n                                    <div class=\"sub-nav-item py-3 px-4\">\r\n                                        <h2 class=\"h5\" style=\"margin: inherit;\">آن لائن تجربہ</h2>\r\n                                        <p class=\"color-gray fn14 mt-3 sub-left-desc\">مفت آن لائن او سی آر فنکشن کا تجربہ</p>\r\n                                    </div>\r\n                                    <div class=\"sub-nav-item py-3 px-4\"><a href=\"Ocr.aspx\">مکمل فعالیت</a></div>\r\n                                    <div class=\"sub-nav-item py-3 px-4\"><a href=\"Ocr.aspx?type=text_recognize\">الفاظ کی پہچان</a></div>\r\n                                    <div class=\"sub-nav-item py-3 px-4\"><a href=\"Ocr.aspx?type=table\">جدول کی شناخت</a></div>\r\n                                    <div class=\"sub-nav-item py-3 px-4\"><a href=\"Ocr.aspx?type=pdf2word\">پی ڈی ایف سے ورڈ تک</a></div>\r\n                                </div>\r\n                                <div class=\"sub-nav\">\r\n                                    <div class=\"sub-nav-item py-3 px-4\">\r\n                                        <h2 class=\"h5\" style=\"margin: inherit;\">رکنیت اپ گریڈ</h2>\r\n                                        <p class=\"color-gray fn14 mt-3 sub-left-desc\">تمام خصوصیات کو ان لاک کریں اور خصوصی خدمات سے لطف اٹھائیں</p>\r\n                                    </div>\r\n                                    <div class=\"sub-nav-item py-3 px-4\"><a href=\"Version.aspx\">رکنیت کے فوائد</a></div>\r\n                                    <div class=\"sub-nav-item py-3 px-4\"><a href=\"Upgrade.aspx\">فوری طور پر چالو کریں</a></div>\r\n                                </div>\r\n                                <div class=\"sub-nav\">\r\n                                    <div class=\"sub-nav-item py-3 px-4\">\r\n                                        <h2 class=\"h5\" style=\"margin: inherit;\">سافٹ ویئر ڈاؤن لوڈ</h2>\r\n                                        <p class=\"color-gray fn14 mt-3 sub-left-desc\">مفت میں پیشہ ورانہ او سی آر سافٹ ویئر ڈاؤن لوڈ کریں</p>\r\n                                    </div>\r\n                                    <div class=\"sub-nav-item py-3 px-4\"><a href=\"Download.aspx\">ابھی ڈاؤن لوڈ کریں</a></div>\r\n                                    <div class=\"sub-nav-item py-3 px-4\"><a href=\"Version.aspx\">ورژن موازنہ</a></div>\r\n                                </div>\r\n                                <div class=\"sub-nav\">\r\n                                    <div class=\"sub-nav-item py-3 px-4\">\r\n                                        <h2 class=\"h5\" style=\"margin: inherit;\">ٹیکنالوجی کا اشتراک</h2>\r\n                                        <p class=\"color-gray fn14 mt-3 sub-left-desc\">او سی آر تکنیکی مضامین اور معلومات کا اشتراک</p>\r\n                                    </div>\r\n                                    <div class=\"sub-nav-item py-3 px-4\"><a href=\"Articles.aspx\">تمام مضامین</a></div>\r\n                                    \r\n                                    <div class=\"sub-nav-item py-3 px-4\"><a href=\"Articles.aspx?category=2\">Advanced Guide</a></div>\r\n                                    \r\n                                    <div class=\"sub-nav-item py-3 px-4\"><a href=\"Articles.aspx?category=3\">تکنیکی تلاش</a></div>\r\n                                    \r\n                                    <div class=\"sub-nav-item py-3 px-4\"><a href=\"Articles.aspx?category=4\">صنعت کے رجحانات</a></div>\r\n                                    \r\n                                    <div class=\"sub-nav-item py-3 px-4\"><a href=\"Articles.aspx?category=5\">کیسز کا استعمال کریں:</a></div>\r\n                                    \r\n                                    <div class=\"sub-nav-item py-3 px-4\"><a href=\"Articles.aspx?category=6\">آلے کا جائزہ</a></div>\r\n                                    \r\n                                </div>\r\n                                <div class=\"sub-nav\">\r\n                                    <div class=\"sub-nav-item py-3 px-4\">\r\n                                        <h2 class=\"h5\" style=\"margin: inherit;\">مدد مرکز</h2>\r\n                                        <p class=\"color-gray fn14 mt-3 sub-left-desc\">پیشہ ورانہ کسٹمر سروس، قریبی خدمت</p>\r\n                                    </div>\r\n                                    <div class=\"sub-nav-item py-3 px-4\"><a href=\"FAQ.aspx\">مدد کا استعمال کریں</a></div>\r\n                                    <div class=\"sub-nav-item py-3 px-4\"><a href=\"AboutUs.aspx\">ہماری بارے ميں</a></div>\r\n                                    <div class=\"sub-nav-item py-3 px-4\"><a href=\"mailto:<EMAIL>\">کسٹمر سروس سے رابطہ کریں</a></div>\r\n                                    <div class=\"sub-nav-item py-3 px-4\"><a href=\"UserAgreement.aspx\">سروس کی شرائط</a></div>\r\n                                </div>\r\n                            </div>\r\n                        </div>\r\n                    </div>\r\n                </div>\r\n            </div>\r\n        </header>\r\n        <form method=\"post\" action=\"./Article.aspx?id=81&amp;slug=rnn-sequence-modeling&amp;lang=zh-Hans&amp;_translation_source=true\" id=\"aspnetForm\">\r\n<div class=\"aspNetHidden\">\r\n<input type=\"hidden\" name=\"__VIEWSTATE\" id=\"__VIEWSTATE\" value=\"Q9uUeUlUJtaRG68B9ZsvqxMA0nWMrG97wrg6XH6pF/PALpTNvdWvXETFldXabtYfiX/3jguNbdKAEx4O02Rz9ETQIoNHodxI3/t/Hx/oGKM=\" />\r\n</div>\r\n\r\n<div class=\"aspNetHidden\">\r\n\r\n\t<input type=\"hidden\" name=\"__VIEWSTATEGENERATOR\" id=\"__VIEWSTATEGENERATOR\" value=\"2173C2F0\" />\r\n</div>\r\n            <div>\r\n                \n\n<div class=\"reading-progress\" id=\"readingProgress\"></div>\n<div class=\"article-detail-container\">\n<div class=\"container\">\n<div class=\"row\">\n<div class=\"col-lg-8 col-xl-9 col-md-12\">\n                    \n                    <article class=\"article-detail\" id=\"articleDetail\" data-article-id=\"81\">\n                        <header class=\"article-header\">\n                            <h1 class=\"article-title\">ڈیپ لرننگ او سی آر سیریز ·4】 ریکرنٹ نیورل نیٹ ورکس اور سیکوئنس ماڈلنگ</h1>\n                            <div class=\"article-meta\">\n                                <div class=\"meta-item\">\n                                    <span class=\"meta-icon\">📅</span>\n                                    <span>پوسٹ ٹائم: 2025-08-19</span>\n                                </div>\n                                <div class=\"meta-item\">\n                                    <span class=\"meta-icon\">👁️</span>\n                                    <span>پڑھنے:<span class=\"view-count\">1200</span></span>\n                                </div>\n                                <div class=\"meta-item\">\n                                    <span class=\"meta-icon\">⏱️</span>\n                                    <span>تقریبا 50 منٹ (9819 الفاظ)</span>\n                                </div>\n                                \n                                <div class=\"meta-item\">\n                                    <span class=\"meta-icon\">📁</span>\n                                    <span>زمرہ:اعلی درجے کے گائیڈز</span>\n                                </div>\n                                \n                            </div>\n                            <div class=\"article-summary\"><p>او سی آر میں آر این این ، ایل ایس ٹی ایم ، جی آر یو کی درخواست میں غوطہ لگائیں۔ ترتیب ماڈلنگ کے اصولوں کا تفصیلی تجزیہ، گریڈینٹ کے مسائل کے حل، اور دو طرفہ آر این این کے فوائد.</p></div>\n                        </header>\n                        <div class=\"article-content\" id=\"articleContent\">## تعارف\r\n\r\nریکرنٹ نیورل نیٹ ورک (آر این این) گہری تعلیم میں ایک اعصابی نیٹ ورک آرکیٹیکچر ہے جو ترتیب ڈیٹا کی پروسیسنگ میں مہارت رکھتا ہے۔ او سی آر کاموں میں ، متن کی شناخت بنیادی طور پر ترتیب سے ترتیب میں تبدیلی کا مسئلہ ہے: تصویری خصوصیات کی ترتیب کو متن کے کردار کی ترتیب میں تبدیل کرنا۔ یہ مضمون آر این این کے کام کرنے، اس کی اہم اقسام، اور او سی آر میں اس کی مخصوص ایپلی کیشنز پر غور کرے گا، قارئین کو ایک جامع نظریاتی بنیاد اور عملی رہنمائی فراہم کرے گا.\r\n\r\n## آر این این بنیادی باتیں\r\n\r\n### روایتی اعصابی نیٹ ورکس کی حدود\r\n\r\nروایتی فیڈ فارورڈ نیورل نیٹ ورکس میں ترتیب کے اعداد و شمار کی پروسیسنگ میں بنیادی حدود ہیں۔ یہ نیٹ ورکس فرض کرتے ہیں کہ ان پٹ ڈیٹا آزاد اور ہم آہنگ ہے ، اور ترتیب میں عناصر کے مابین عارضی انحصار کو پکڑ نہیں سکتا ہے۔\r\n\r\n**فیڈ فارورڈ نیٹ ورک کے مسائل**:\r\n- فکسڈ ان پٹ اور آؤٹ پٹ لمبائی: متغیر لمبائی کے ترتیبات کو سنبھالا نہیں جا سکتا\r\n- یادداشت کی صلاحیت کی کمی: تاریخی معلومات کو استعمال کرنے میں ناکامی\r\n- پیرامیٹر شیئرنگ میں دشواری: ایک ہی پیٹرن کو مختلف مقامات پر بار بار سیکھنے کی ضرورت ہے\r\n- پوزیشنل حساسیت: ان پٹ کی ترتیب کو تبدیل کرنے سے مکمل طور پر مختلف آؤٹ پٹ پیدا ہوسکتے ہیں\r\n\r\nاو سی آر کے کاموں میں یہ حدود خاص طور پر قابل ذکر ہیں۔ متن کے سلسلے انتہائی سیاق و سباق پر منحصر ہیں ، اور پچھلے کردار کی شناخت کے نتائج اکثر بعد کے کرداروں کے امکانات کا تعین کرنے میں مدد کرتے ہیں۔ مثال کے طور پر ، انگریزی لفظ \"دی\" کی شناخت کرتے وقت ، اگر \"تھ\" کو پہلے ہی تسلیم کیا گیا ہے تو ، اگلا کردار \"ای\" ہونے کا امکان ہے۔\r\n\r\n### آر این این کا بنیادی خیال\r\n\r\nآر این این لوپ جوائنز متعارف کروا کر سیکوئنس ماڈلنگ کا مسئلہ حل کرتا ہے۔ بنیادی خیال نیٹ ورک میں \"میموری\" میکانزم شامل کرنا ہے ، تاکہ نیٹ ورک پچھلے لمحات سے معلومات کو اسٹور اور استعمال کرسکے۔\r\n\r\n** آر این این کی ریاضیاتی نمائندگی**:\r\nفی الحال ، آر این این کی پوشیدہ حالت کا تعین موجودہ ان پٹ x_t اور پچھلے لمحے کی پوشیدہ حالت سے کیا h_t h_{t-1}:\r\n\r\nh_t = f(W_hh * h_{t-1} + W_xh * x_t + b_h)\r\n\r\nاس میں:\r\n- W_hh پوشیدہ حالت سے پوشیدہ حالت تک وزن میٹرکس ہے\r\n- W_xh پوشیدہ حالت میں داخل ہونے والا وزن میٹرکس ہے  \r\n- b_h ایک تعصب ویکٹر ہے\r\n- ایف ایکٹیویشن فنکشن ہے (عام طور پر ٹین یا ریلو)\r\n\r\ny_t آؤٹ پٹ کا حساب موجودہ پوشیدہ حالت سے لگایا جاتا ہے:\r\ny_t = W_hy * h_t + b_y\r\n\r\n** آر این این کے فوائد**:\r\n- پیرامیٹر شیئرنگ: تمام ٹائم اسٹیپس میں ایک ہی وزن کا اشتراک کیا جاتا ہے\r\n- متغیر لمبائی ترتیب پروسیسنگ: من مانی لمبائی کے ان پٹ سیکونس کو سنبھال سکتے ہیں\r\n- میموری کی صلاحیت: پوشیدہ حالتیں نیٹ ورک کی \"یادوں\" کے طور پر کام کرتی ہیں\r\n- لچکدار ان پٹ اور آؤٹ پٹ: ون ٹو ون، ون ٹو ملٹی، ملٹی ٹو ون، کئی ٹو کئی موڈز اور بہت کچھ کی حمایت کرتا ہے۔\r\n\r\n### آر این این کے وسیع نقطہ نظر\r\n\r\nبہتر طور پر سمجھنے کے لئے کہ آر این این کس طرح کام کرتے ہیں ، ہم انہیں عارضی جہت میں توسیع دے سکتے ہیں۔ توسیع شدہ آر این این ایک گہرے فیڈ فارورڈ نیٹ ورک کی طرح نظر آتا ہے ، لیکن تمام ٹائم اسٹیپس ایک ہی پیرامیٹرز کا اشتراک کرتے ہیں۔\r\n\r\n** وقت کی اہمیت سامنے آنے والی**:\r\n- معلومات کے بہاؤ کو سمجھنے میں آسان: یہ واضح طور پر دیکھنا ممکن ہے کہ وقت کے مراحل کے درمیان معلومات کس طرح منتقل کی جاتی ہیں\r\nگریڈینٹ کی گنتی: گریڈیئنٹس کا حساب ٹائم بیک پروپیگیشن (بی پی ٹی ٹی) الگورتھم کے ذریعے کیا جاتا ہے۔\r\n- متوازی غور: اگرچہ آر این این فطری طور پر ترتیب وار ہیں ، لیکن کچھ کارروائیوں کو متوازی بنایا جاسکتا ہے۔\r\n\r\n** کھلنے والے عمل کی ریاضیاتی وضاحت**:\r\nلمبائی ٹی کے ترتیب کے لئے ، آر این این مندرجہ ذیل طور پر پھیلتا ہے:\r\nh_1 = f(W_xh * x_1 + b_h)\r\nh_2 = f(W_hh * h_1 + W_xh * x_2 + b_h)\r\nh_3 = f(W_hh * h_2 + W_xh * x_3 + b_h)\r\n...\r\nh_T = f(W_hh * h_{T-1} + W_xh * x_T + b_h)\r\n\r\nیہ افشا شدہ فارم واضح طور پر دکھاتا ہے کہ وقت کے مراحل کے درمیان معلومات کیسے پاس کی جاتی ہیں اور ہر وقت کے مراحل میں پیرامیٹرز کا اشتراک کیسے کیا جاتا ہے۔\r\n\r\n## گریڈینٹ غائب ہونا اور دھماکے کا مسئلہ\r\n\r\n### مسئلے کی جڑ\r\n\r\nآر این این کی تربیت کرتے وقت ، ہم بیک پروپیگیشن تھرو ٹائم (بی پی ٹی ٹی) الگورتھم کا استعمال کرتے ہیں۔ الگورتھم کو ہر ٹائم اسٹیپ پیرامیٹر کے لئے نقصان کے فنکشن کے گریڈینٹ کا حساب لگانے کی ضرورت ہے۔\r\n\r\n**گریڈینٹ حساب کے لئے زنجیر قانون **:\r\nجب ترتیب طویل ہوتی ہے تو ، گریڈینٹ کو متعدد وقت کے مراحل کے ذریعہ واپس پیش کرنے کی ضرورت ہوتی ہے۔ زنجیر کے اصول کے مطابق ، ایک گریڈینٹ میں وزن میٹرکس کے متعدد گنا شامل ہوں گے:\r\n\r\n∂ایل / ∂ ڈبلیو = π_t (∂ایل / ∂y_t) * (∂y_t / ∂h_t) * (∂h_t / ∂ ڈبلیو)\r\n\r\nجہاں ∂h_t / ∂ ڈبلیو میں لمحہ ٹی سے لمحہ 1 تک تمام درمیانی ریاستوں کی پیداوار شامل ہے۔\r\n\r\n**گریڈینٹ گمشدگی کا ریاضیاتی تجزیہ **:\r\nوقت کے مراحل کے درمیان گریڈیئنٹس کے پھیلاؤ پر غور کریں:\r\n∂h_t/∂h_{t-1} = diag(f_prime(W_hh * h_{t-1} + W_xh * x_t + b_h)) * W_hh\r\n\r\nجب ترتیب کی لمبائی ٹی ہوتی ہے تو ، گراڈینٹ میں ٹی -1 ایسی مصنوعات کی اصطلاح شامل ہوتی ہے۔ اگر W_hh کی زیادہ سے زیادہ مقدار 1 سے کم ہے تو ، مسلسل میٹرکس گنا گریڈینٹ ایکسپونیشل سڑنے کا سبب بنے گا۔\r\n\r\n**گریڈینٹ دھماکوں کا ریاضیاتی تجزیہ**:\r\nاس کے برعکس ، جب W_hh کی زیادہ سے زیادہ مقدار 1 سے زیادہ ہوتی ہے تو ، گراڈینٹ تیزی سے بڑھ جاتا ہے:\r\n|| ∂h_t/∂h_1 | ≈ || W_hh || ^{t-1}\r\n\r\nیہ غیر مستحکم تربیت اور حد سے زیادہ پیرامیٹر اپ ڈیٹس کا باعث بنتا ہے۔\r\n\r\n### حل کی تفصیلی وضاحت\r\n\r\nگریڈینٹ کلپنگ:\r\nگریڈینٹ کلپنگ گریڈینٹ دھماکوں کو حل کرنے کا سب سے براہ راست طریقہ ہے۔ جب گریڈینٹ کا معیار ایک مقررہ حد سے تجاوز کرتا ہے تو ، گریڈینٹ کو حد کے سائز تک اسکیل کیا جاتا ہے۔ یہ طریقہ آسان اور مؤثر ہے ، لیکن حد کے محتاط انتخاب کی ضرورت ہے۔ ایک حد جو بہت چھوٹی ہے وہ سیکھنے کی صلاحیت کو محدود کردے گی ، اور ایک حد جو بہت بڑی ہے وہ گریڈینٹ دھماکے کو مؤثر طریقے سے روک نہیں پائے گی۔\r\n\r\n** وزن شروع کرنے کی حکمت عملی**:\r\nمناسب وزن شروع کرنے سے گریڈینٹ کے مسائل کو کم کیا جاسکتا ہے:\r\n- زیویئر کا آغاز: وزن کا تغیر 1 / این ہے ، جہاں این ان پٹ طول و عرض ہے۔\r\n- وہ آغاز: وزن کا تغیر 2 / این ہے ، جو ریلو ایکٹیویشن افعال کے لئے موزوں ہے۔\r\n- آرتھوگونیل شروعات: وزن میٹرکس کو آرتھوگونیل میٹرکس کے طور پر شروع کرتا ہے\r\n\r\n** ایکٹیویشن افعال کا انتخاب**:\r\nمختلف ایکٹیویشن افعال گریڈینٹ پروپیگیشن پر مختلف اثرات مرتب کرتے ہیں:\r\n- ٹان: آؤٹ پٹ رینج [-1,1]، گریڈینٹ زیادہ سے زیادہ قیمت 1\r\n- ریلو: گریڈینٹ گمشدگی کو کم کر سکتا ہے لیکن اعصابی موت کا سبب بن سکتا ہے\r\n- لیکی ریلو: ریلو کے اعصابی موت کے مسئلے کو حل کرتا ہے\r\n\r\n** آرکیٹیکچرل بہتری**:\r\nسب سے بنیادی حل آر این این آرکیٹیکچر کو بہتر بنانا تھا ، جس کی وجہ سے ایل ایس ٹی ایم اور جی آر یو کا ظہور ہوا۔ یہ آرکیٹیکچر سیٹنگ میکانزم اور خصوصی معلومات کے بہاؤ کے ڈیزائن کے ذریعے گریڈیئنٹس کو حل کرتے ہیں۔\r\n\r\n# # ایل ایس ٹی ایم: طویل قلیل مدتی میموری نیٹ ورک\r\n\r\n### ایل ایس ٹی ایم کے لئے ڈیزائن محرک\r\n\r\nایل ایس ٹی ایم (لانگ شارٹ ٹرم میموری) ایک آر این این ورژن ہے جسے 1997 میں ہوچریٹر اور شمڈ ہوبر نے تجویز کیا تھا ، جسے خاص طور پر گریڈینٹ غائب ہونے اور طویل فاصلے پر منحصر سیکھنے کی مشکلات کے مسئلے کو حل کرنے کے لئے ڈیزائن کیا گیا ہے۔\r\n\r\n** ایل ایس ٹی ایم کی بنیادی اختراعات**:\r\n- سیل اسٹیٹ: معلومات کے لئے \"شاہراہ\" کے طور پر کام کرتا ہے، معلومات کو وقت کے مراحل کے درمیان براہ راست بہنے کی اجازت دیتا ہے\r\n- گیٹنگ میکانزم: معلومات کی آمد، برقرار رکھنے اور آؤٹ پٹ پر درست کنٹرول\r\n- الگ شدہ میموری میکانزم: قلیل مدتی میموری (پوشیدہ حالت) اور طویل مدتی میموری (سیلولر حالت) کے درمیان فرق کریں\r\n\r\n** ایل ایس ٹی ایم گریڈینٹ کے مسائل کو کیسے حل کرتا ہے**:\r\nایل ایس ٹی ایم خلیوں کی حالت کو اپ ڈیٹ کرتا ہے نہ کہ متعدد آپریشنز کے بجائے ایڈیٹیو کے ذریعے ، جس سے گریڈیئنٹس کو پہلے کے وقت کے مراحل میں زیادہ آسانی سے بہنے کی اجازت ملتی ہے۔ سیل اسٹیٹ کے لئے تازہ ترین فارمولا:\r\n\r\nC_t = f_t ⊙ C_{t-1} + i_t ⊙ C_tilde_t\r\n\r\nعنصر کی سطح کا اضافہ یہاں استعمال کیا جاتا ہے ، روایتی آر این این میں مسلسل میٹرکس گنا سے گریز کرتا ہے۔\r\n\r\n### ایل ایس ٹی ایم آرکیٹیکچر کی تفصیلی وضاحت\r\n\r\nایل ایس ٹی ایم میں تین گیٹنگ یونٹس اور ایک سیل اسٹیٹ شامل ہے:\r\n\r\n**1. گیٹ بھول جاؤ**:\r\nگمنامی کا دروازہ فیصلہ کرتا ہے کہ سیل کی حالت سے کون سی معلومات خارج کی جائیں:\r\nf_t = σ (W_f · [h_{t-1}, x_t] + b_f)\r\n\r\nگمنام دروازے کی آؤٹ پٹ 0 اور 1 کے درمیان ایک قدر ہے ، جس میں 0 کو \"مکمل طور پر فراموش\" کیا جاتا ہے اور 1 کو \"مکمل طور پر برقرار\" رکھا جاتا ہے۔ یہ گیٹ ایل ایس ٹی ایم کو غیر اہم تاریخی معلومات کو منتخب طور پر بھولنے کی اجازت دیتا ہے۔\r\n\r\n**2. ان پٹ گیٹ**:\r\nان پٹ گیٹ اس بات کا تعین کرتا ہے کہ سیل کی حالت میں کون سی نئی معلومات ذخیرہ کی جاتی ہیں:\r\ni_t = σ (W_i · [h_{t-1}, x_t] + b_i)\r\nC_tilde_t = تنح (W_C · [h_{t-1}, x_t] + b_C)\r\n\r\nان پٹ گیٹ دو حصوں پر مشتمل ہوتا ہے: سگموئیڈ پرت اس بات کا تعین کرتی ہے کہ کون سی اقدار کو اپ ڈیٹ کرنا ہے ، اور ٹینہ پرت امیدوار کی قدر ویکٹر بناتی ہے۔\r\n\r\n**3. سیل اسٹیٹس اپ ڈیٹ**:\r\nسیل کی حالت کو اپ ڈیٹ کرنے کے لئے بھولنے والے گیٹ اور ان پٹ گیٹ کے آؤٹ پٹ کو یکجا کریں:\r\nC_t = f_t ⊙ C_{t-1} + i_t ⊙ C_tilde_t\r\n\r\nیہ فارمولا ایل ایس ٹی ایم کے مرکز میں ہے: عنصر کی سطح پر گنا اور اضافی کارروائیوں کے ذریعے معلومات کو منتخب طور پر برقرار رکھنا اور اپ ڈیٹ کرنا۔\r\n\r\n**4. آؤٹ پٹ گیٹ**:\r\nآؤٹ پٹ گیٹ اس بات کا تعین کرتا ہے کہ سیل کے کون سے حصے آؤٹ پٹ ہیں:\r\no_t = σ (W_o · [h_{t-1}, x_t] + b_o)\r\nh_t = o_t ⊙ (C_t)\r\n\r\nآؤٹ پٹ گیٹ کنٹرول کرتا ہے کہ سیل کی حالت کے کون سے حصے موجودہ آؤٹ پٹ کو متاثر کرتے ہیں۔\r\n\r\n### ایل ایس ٹی ایم ورژن\r\n\r\n** پیپ ہول ایل ایس ٹی ایم **:\r\nمعیاری ایل ایس ٹی ایم کی بنیاد پر ، پیپ ہول ایل ایس ٹی ایم گیٹنگ یونٹ کو سیل کی حالت دیکھنے کی اجازت دیتا ہے:\r\nf_t = σ (W_f · [C_{t-1}, h_{t-1}, x_t] + b_f)\r\ni_t = σ (W_i · [C_{t-1}, h_{t-1}, x_t] + b_i)\r\no_t = σ (W_o · [C_t، h_{t-1}, x_t] + b_o)\r\n\r\n** مل کر ایل ایس ٹی ایم**:\r\nبھولنے والے دروازے کو ان پٹ گیٹ کے ساتھ جوڑیں تاکہ اس بات کو یقینی بنایا جاسکے کہ بھولی ہوئی معلومات کی مقدار درج کردہ معلومات کی مقدار کے برابر ہے:\r\nf_t = σ (W_f · [h_{t-1}, x_t] + b_f)\r\ni_t = 1 - f_t\r\n\r\nیہ ڈیزائن ایل ایس ٹی ایم کی بنیادی فعالیت کو برقرار رکھتے ہوئے پیرامیٹرز کی تعداد کو کم کرتا ہے۔\r\n\r\n## جی آر یو: گیٹڈ لوپ یونٹ\r\n\r\n### جی آر یو کا آسان ڈیزائن\r\n\r\nجی آر یو (گیٹڈ ریکرنٹ یونٹ) ایل ایس ٹی ایم کا ایک آسان ورژن ہے جسے چو ایٹ ال نے 2014 میں تجویز کیا تھا۔ جی آر یو ایل ایس ٹی ایم کے تین دروازوں کو دو دروازوں میں آسان بناتا ہے اور سیلولر ریاست اور پوشیدہ حالت کو ضم کرتا ہے۔\r\n\r\n** جی آر یو کا ڈیزائن فلسفہ**:\r\n- سادہ ساخت: دروازوں کی تعداد کو کم کرتا ہے اور حساب کی پیچیدگی کو کم کرتا ہے\r\n- کارکردگی کو برقرار رکھیں: ایل ایس ٹی ایم-موازنہ کارکردگی کو برقرار رکھتے ہوئے آسان بنائیں\r\n- نافذ کرنے میں آسان: آسان تعمیر آسان نفاذ اور کمیشننگ کی اجازت دیتا ہے\r\n\r\n### جی آر یو کا گیٹنگ میکانزم\r\n\r\n**1. گیٹ کو ری سیٹ کریں**:\r\nr_t = σ (W_r · [h_{t-1}, x_t] + b_r)\r\n\r\nری سیٹ گیٹ اس بات کا تعین کرتا ہے کہ پچھلی میموری کے ساتھ نئے ان پٹ کو کیسے جوڑنا ہے۔ جب ری سیٹ گیٹ 0 کے قریب پہنچتا ہے تو ، ماڈل پچھلی پوشیدہ حالت کو نظر انداز کرتا ہے۔\r\n\r\n**2. گیٹ کو اپ ڈیٹ کریں**:\r\nz_t = σ (W_z · [h_{t-1}, x_t] + b_z)\r\n\r\nاپ ڈیٹ گیٹ اس بات کا تعین کرتا ہے کہ ماضی کی کتنی معلومات رکھنا ہے اور کتنی نئی معلومات شامل کرنا ہے۔ یہ بھولنے اور ان پٹ دونوں کو کنٹرول کرتا ہے ، جیسا کہ ایل ایس ٹی ایم میں بھولنے اور ان پٹ گیٹس کے امتزاج کی طرح ہے۔\r\n\r\n**3. امیدوار کی پوشیدہ حیثیت**:\r\nh_tilde_t = تنح (W_h · [r_t ⊙ h_{t-1}, x_t] + b_h)\r\n\r\nامیدوار کی پوشیدہ ریاستیں پچھلی پوشیدہ حالت کے اثرات کو کنٹرول کرنے کے لئے ری سیٹ گیٹ کا استعمال کرتی ہیں۔\r\n\r\n**4. آخری پوشیدہ حالت**:\r\nh_t = (1 - z_t) ⊙ h_{t-1} + z_t ⊙ h_tilde_t\r\n\r\nآخری پوشیدہ حالت پچھلی پوشیدہ ریاست اور امیدوار کی پوشیدہ ریاست کا وزنی اوسط ہے۔\r\n\r\n### جی آر یو بمقابلہ ایل ایس ٹی ایم کا گہرائی سے موازنہ\r\n\r\n** پیرامیٹرز کی تعداد کا موازنہ **:\r\n- ایل ایس ٹی ایم: 4 وزن میٹرکس (گیٹ، ان پٹ گیٹ، امیدوار کی قیمت، آؤٹ پٹ گیٹ بھول جانا)\r\n- جی آر یو: 3 وزن میٹرکس (ری سیٹ گیٹ، اپ ڈیٹ گیٹ، امیدوار کی قیمت)\r\n- جی آر یو کے پیرامیٹرز کی تعداد ایل ایس ٹی ایم کا تقریبا 75٪ ہے\r\n\r\n** کمپیوٹیشنل پیچیدگی کا موازنہ**:\r\n- ایل ایس ٹی ایم: 4 گیٹ آؤٹ پٹ اور سیل اسٹیٹ اپ ڈیٹس کے حساب کی ضرورت ہے\r\n- جی آر یو: صرف 2 گیٹس اور پوشیدہ اسٹیٹس اپ ڈیٹس کی آؤٹ پٹ کا حساب لگائیں\r\n- جی آر یو عام طور پر ایل ایس ٹی ایم سے 20-30٪ تیز ہے\r\n\r\nکارکردگی کا موازنہ**:\r\n- زیادہ تر کاموں پر ، جی آر یو اور ایل ایس ٹی ایم موازنہ کرتے ہیں۔\r\n- ایل ایس ٹی ایم کچھ طویل ترتیب کے کاموں پر جی آر یو سے قدرے بہتر ہوسکتا ہے۔\r\n- جی آر یو ان معاملات میں ایک بہتر انتخاب ہے جہاں کمپیوٹنگ وسائل محدود ہیں\r\n\r\n## دو طرفہ آر این این\r\n\r\n### دو طرفہ پروسیسنگ کی ضرورت\r\n\r\nبہت سے ترتیب ماڈلنگ کاموں میں ، موجودہ لمحے کی آؤٹ پٹ نہ صرف ماضی پر بلکہ مستقبل کی معلومات پر بھی منحصر ہے۔ یہ او سی آر کاموں میں خاص طور پر اہم ہے ، جہاں کردار کی شناخت کے لئے اکثر پورے لفظ یا جملے کے سیاق و سباق پر غور کرنے کی ضرورت ہوتی ہے۔\r\n\r\n** یک طرفہ آر این این کی حدود**:\r\n- صرف تاریخی معلومات استعمال کی جا سکتی ہیں، مستقبل کا کوئی سیاق و سباق حاصل نہیں کیا جا سکتا\r\n- کچھ کاموں میں محدود کارکردگی، خاص طور پر وہ جن کے لئے عالمی معلومات کی ضرورت ہوتی ہے\r\n- مبہم کرداروں کی محدود شناخت\r\n\r\n**دو طرفہ پروسیسنگ کے فوائد**:\r\n- مکمل سیاق و سباق کی معلومات: ماضی اور مستقبل کی معلومات دونوں سے فائدہ اٹھائیں\r\n- بہتر تفہیم: سیاق و سباق سے متعلق معلومات کے ساتھ اختلاف\r\n- بہتر شناخت کی درستگی: زیادہ تر ترتیب تبصرہ کے کاموں پر بہتر کارکردگی کا مظاہرہ کیا\r\n\r\n### دو طرفہ ایل ایس ٹی ایم آرکیٹیکچر\r\n\r\nدو طرفہ ایل ایس ٹی ایم دو ایل ایس ٹی ایم پرتوں پر مشتمل ہے:\r\n- فارورڈ ایل ایس ٹی ایم: بائیں سے دائیں طرف پروسیس سیکونس\r\n- بیک ورڈ ایل ایس ٹی ایم: دائیں سے بائیں تک پروسیس سیکونس\r\n\r\n** ریاضیاتی نمائندگی**:\r\nh_forward_t = LSTM_forward (x_t، h_forward_{t-1})\r\nh_backward_t = LSTM_backward (x_t، h_backward_{t+1})\r\nh_t = [h_forward_t; h_backward_t] # آگے اور پیچھے چھپی ہوئی ریاستوں کی تشکیل\r\n\r\n** تربیتی عمل**:\r\n1. فارورڈ ایل ایس ٹی ایم معمول کی ترتیب میں ترتیب پر عمل کرتا ہے\r\n2. پس ماندہ ایل ایس ٹی ایم ترتیب کو الٹا ترتیب میں پروسیس کرتا ہے\r\n3. ہر وقت کے قدم پر، پوشیدہ حالتوں کو دونوں سمتوں میں مربوط کریں\r\n4. پیشن گوئی کے لئے تقسیم شدہ حالت کا استعمال کریں\r\n\r\n** فوائد اور نقصانات**:\r\nفائدہ:\r\n- مکمل سیاق و سباق کی معلومات\r\n- بہتر کارکردگی\r\n- ہم آہنگی کا علاج\r\n\r\nپست پوزیشن:\r\n- حساب کتاب کی پیچیدگی کو دوگنا کریں\r\n- حقیقی وقت میں پروسیس نہیں کیا جا سکتا (مکمل ترتیب کی ضرورت ہے)\r\n- میموری کی ضروریات میں اضافہ\r\n\r\nاو سی آر میں ## ترتیب ماڈلنگ ایپلی کیشنز\r\n\r\n### ٹیکسٹ لائن کی شناخت کی تفصیلی وضاحت\r\n\r\nاو سی آر سسٹم میں ، ٹیکسٹ لائن کی شناخت ترتیب ماڈلنگ کی ایک عام ایپلی کیشن ہے۔ اس عمل میں تصویری خصوصیات کی ترتیب کو کرداروں کی ترتیب میں تبدیل کرنا شامل ہے۔\r\n\r\n** مسئلہ ماڈلنگ**:\r\n- ان پٹ: امیج فیچر سیکوئنس ایکس = {x_1، x_2، ...، x_T}\r\n- آؤٹ پٹ: حروف کی ترتیب Y = {y_1، y_2، ..., y_S}\r\n- چیلنج: ان پٹ ترتیب کی لمبائی ٹی اور آؤٹ پٹ ترتیب کی لمبائی ایس اکثر برابر نہیں ہوتی ہے۔\r\n\r\n** ٹیکسٹ لائن کی شناخت میں سی آر این این آرکیٹیکچر کا اطلاق**:\r\nسی آر این این (کنوولوشنل ریکرنٹ نیورل نیٹ ورک) او سی آر میں سب سے زیادہ کامیاب آرکیٹیکچر میں سے ایک ہے:\r\n\r\n1. ** سی این این فیچر ایکسٹریکشن پرت**:\r\n   - کنوولوشنل نیورل نیٹ ورکس کا استعمال کرتے ہوئے تصویری خصوصیات نکالیں\r\n   - 2 ڈی امیج خصوصیات کو 1 ڈی فیچر سیکونس میں تبدیل کریں\r\n   - وقت کی معلومات کا تسلسل برقرار رکھیں\r\n\r\n2. ** آر این این سیکوئنس ماڈلنگ پرت**:\r\n   - دو طرفہ ایل ایس ٹی ایم کا استعمال کرتے ہوئے ماڈل فیچر سیکونس\r\n   - کرداروں کے درمیان سیاق و سباق پر انحصار کو پکڑنا\r\n   - ہر وقت مرحلے کے لئے آؤٹ پٹ کردار امکان کی تقسیم\r\n\r\n3. ** سی ٹی سی الائنمنٹ پرت**:\r\n   - ان پٹ / آؤٹ پٹ ترتیب کی لمبائی میں عدم مطابقت کو دور کرتا ہے\r\n   - کردار کی سطح کی صف بندی کے طول و عرض کی ضرورت نہیں ہے\r\n   - اینڈ ٹو اینڈ ٹریننگ\r\n\r\n** فیچر نکالنے کو ترتیب میں تبدیل کرنا **:\r\nسی این این کے ذریعہ نکالے گئے فیچر نقشے کو ایک ترتیب فارم میں تبدیل کرنے کی ضرورت ہے جس پر آر این این عمل کرسکتا ہے:\r\n- فیچر نقشے کو کالموں میں تقسیم کریں ، ہر کالم کو وقت کے مرحلے کے طور پر رکھیں۔\r\n- مقامی معلومات کی تاریخ کو برقرار رکھنا\r\n- اس بات کو یقینی بنائیں کہ فیچر ترتیب کی لمبائی تصویر کی چوڑائی کے متناسب ہے\r\n\r\n### او سی آر میں توجہ کے میکانزم کا اطلاق\r\n\r\nروایتی آر این این میں اب بھی طویل ترتیبوں سے نمٹنے کے دوران معلومات کی رکاوٹیں ہوتی ہیں۔ توجہ کے میکانزم کا تعارف ترتیب ماڈلنگ کی صلاحیتوں کو مزید بڑھاتا ہے۔\r\n\r\n** توجہ کے میکانزم کے اصول **:\r\nتوجہ کا میکانزم ماڈل کو ہر آؤٹ پٹ پیدا کرتے وقت ان پٹ ترتیب کے مختلف حصوں پر توجہ مرکوز کرنے کی اجازت دیتا ہے:\r\n- مقررہ لمبائی انکوڈڈ ویکٹرز کی معلومات کی رکاوٹ کو حل کیا\r\n- ماڈل کے فیصلوں کی وضاحت فراہم کرتا ہے\r\n- طویل ترتیبات کی بہتر پروسیسنگ\r\n\r\n** او سی آر میں مخصوص ایپلی کیشنز**\r\n\r\n1. ** کردار کی سطح کی توجہ**:\r\n   - ہر کردار کی شناخت کرتے وقت متعلقہ تصویری علاقوں پر توجہ مرکوز کریں\r\n   - مکھی پر توجہ کے وزن کو ایڈجسٹ کریں\r\n   - پیچیدہ پس منظر کے لئے مضبوطی کو بہتر بنائیں\r\n\r\n2. ** الفاظ کی سطح کی توجہ**:\r\n   - الفاظ کی سطح پر سیاق و سباق کی معلومات پر غور کریں\r\n   - زبان کے ماڈل کے علم سے فائدہ اٹھائیں\r\n   - پورے لفظ کی شناخت کی درستگی کو بہتر بنائیں\r\n\r\n3. **ملٹی اسکیل توجہ**:\r\n   - مختلف قراردادوں پر توجہ کے میکانزم کا اطلاق\r\n   - مختلف سائز کے متن کو سنبھالیں\r\n   - پیمانے کی تبدیلیوں کے لئے مطابقت پذیری کو بہتر بنائیں\r\n\r\n** توجہ کے میکانزم کی ریاضیاتی نمائندگی **:\r\nانکوڈر آؤٹ پٹ ترتیب کے لئے H = {h_1، h_2، ..., h_T} اور ڈیکوڈر اسٹیٹ s_t:\r\n\r\ne_{t,i} = a(s_t, h_i) # توجہ اسکور\r\nα_{t,i} = سافٹ میکس (e_{t,i}) # توجہ کا وزن\r\nc_t = π_i α_{t,i} * h_i # سیاق و سباق ویکٹر\r\n\r\n# # تربیتی حکمت عملی اور اصلاح\r\n\r\n### ترتیب سے ترتیب کی تربیتی حکمت عملی\r\n\r\n** استاد کی زبردستی**:\r\nتربیت کے مرحلے کے دوران ، ڈیکوڈر کے ان پٹ کے طور پر حقیقی ہدف کی ترتیب کا استعمال کریں:\r\n- پیشہ: تیز تربیت کی رفتار، مستحکم ہم آہنگی\r\n- نقصانات: غیر متوازن تربیت اور تخمینے کے مراحل، جس کی وجہ سے غلطیاں جمع ہوتی ہیں\r\n\r\n** شیڈول نمونے**:\r\nآہستہ آہستہ تربیت کے دوران ماڈل کی اپنی پیشگوئیوں کو استعمال کرنے کے لئے استاد کی مجبوری سے منتقلی:\r\n- ابتدائی مرحلے میں حقیقی لیبل استعمال کریں اور بعد کے مراحل میں ماڈل کی پیشگوئیاں کریں\r\n- تربیت اور استدلال میں فرق کو کم کریں\r\n- ماڈل کی مضبوطی کو بہتر بنائیں\r\n\r\n** نصاب سیکھنا**:\r\nسادہ نمونوں کے ساتھ شروع کریں اور آہستہ آہستہ نمونوں کی پیچیدگی میں اضافہ کریں:\r\n- مختصر سے طویل ترتیب: پہلے مختصر متن کی تربیت کریں، پھر طویل متن\r\n- واضح سے دھندلی تصاویر: آہستہ آہستہ تصویر کی پیچیدگی میں اضافہ\r\n- آسان سے پیچیدہ فونٹ: پرنٹ سے ہینڈ رائٹنگ تک\r\n\r\n### ریگولرائزیشن تکنیک\r\n\r\n** آر این این میں ڈراپ آؤٹ کا اطلاق**:\r\nآر این این میں ڈراپ آؤٹ کا اطلاق کرنے کے لئے خصوصی توجہ کی ضرورت ہے:\r\n- لوپ کنکشن پر ڈراپ آؤٹ کا اطلاق نہ کریں\r\n- ڈراپ آؤٹ کو ان پٹ اور آؤٹ پٹ پرتوں پر لاگو کیا جاسکتا ہے\r\n- مختلف ڈراپ آؤٹ: ہر وقت ایک ہی ڈراپ آؤٹ ماسک کا استعمال کریں\r\n\r\n** وزن میں کمی**:\r\nایل 2 ریگولرائزیشن اوور فٹنگ کو روکتا ہے:\r\nنقصان = کراس اینٹروپی + λ * || ڈبلیو || ²\r\n\r\nجہاں ریگولرائزیشن کوائنسٹ ہے ، جسے توثیق کے سیٹ کے ذریعہ بہتر بنانے کی ضرورت ہے۔\r\n\r\n**گریڈینٹ کرپنگ**:\r\nگریڈینٹ دھماکوں کو روکنے کا ایک مؤثر طریقہ. جب گریڈینٹ کا معیار حد سے تجاوز کرتا ہے تو ، گریڈینٹ کی سمت کو برقرار رکھنے کے لئے گریڈینٹ کو متناسب طور پر اسکیل کریں۔\r\n\r\n** جلدی رکنا**:\r\nجب کارکردگی اب بہتر نہیں ہو رہی ہے تو توثیق سیٹ کارکردگی اور تربیت کو روکیں:\r\n- ضرورت سے زیادہ فٹ ہونے سے روکیں\r\n- کمپیوٹنگ کے وسائل کو محفوظ کریں\r\n- بہترین ماڈل منتخب کریں\r\n\r\n### ہائپر پیرامیٹر ٹیوننگ\r\n\r\n** سیکھنے کی شرح شیڈولنگ **:\r\n- ابتدائی سیکھنے کی شرح: عام طور پر 0.001-0.01 پر مقرر\r\n- سیکھنے کی شرح میں کمی: تیزی سے زوال یا سیڑھی کا زوال\r\n- مطابقت پذیر سیکھنے کی شرح: ایڈم ، آر ایم ایس پروپ ، وغیرہ جیسے آپٹمائزرز کا استعمال کریں۔\r\n\r\n** بیچ سائز کا انتخاب**:\r\n- چھوٹے بیچ: بہتر عمومی کارکردگی لیکن طویل تربیتی وقت\r\n- اعلی حجم: تربیت تیز ہے لیکن عمومیت کو متاثر کر سکتی ہے\r\n- 16-128 کے درمیان بیچ سائز عام طور پر منتخب کیا جاتا ہے\r\n\r\n** ترتیب کی لمبائی پروسیسنگ**:\r\n- طے شدہ لمبائی: ترتیب کو مقررہ لمبائی میں کاٹنا یا بھرنا\r\n- متحرک لمبائی: متغیر لمبائی کے ترتیب کو سنبھالنے کے لئے پیڈنگ اور ماسکنگ کا استعمال کریں\r\n- بیگنگ حکمت عملی: یکساں لمبائی کے گروپ سیکونس\r\n\r\n## کارکردگی کی تشخیص اور تجزیہ\r\n\r\n### میٹرکس کا جائزہ لیں\r\n\r\n** کردار کی سطح کی درستگی**:\r\nAccuracy_char = (صحیح طور پر پہچانے جانے والے حروف کی تعداد) / (کل حروف)\r\n\r\nیہ سب سے بنیادی تشخیص اشارے ہے اور براہ راست ماڈل کی کردار کی شناخت کی صلاحیتوں کی عکاسی کرتا ہے.\r\n\r\n** سیریل سطح کی درستگی**:\r\nAccuracy_seq = (درست طور پر پہچانے جانے والے ترتیبات کی تعداد) / (ترتیبوں کی کل تعداد)\r\n\r\nیہ انڈیکیٹر زیادہ سخت ہے ، اور صرف ایک مکمل طور پر درست ترتیب کو صحیح سمجھا جاتا ہے۔\r\n\r\n** ایڈیٹنگ فاصلہ (لیونشٹین فاصلہ)**:\r\nپیش گوئی اور سچی سیریز کے درمیان فرق کی پیمائش کریں:\r\n- داخل کرنے، ہٹانے اور تبدیل کرنے کے آپریشنز کی کم از کم تعداد\r\n- معیاری ترمیم کا فاصلہ: فاصلہ / ترتیب کی لمبائی میں ترمیم\r\n- بی ایل ای یو اسکور: عام طور پر مشین ترجمہ میں استعمال ہوتا ہے اور او سی آر تشخیص کے لئے بھی استعمال کیا جاسکتا ہے\r\n\r\n### غلطی کا تجزیہ\r\n\r\n** عام غلطی کی اقسام**:\r\n1. **کردار کی الجھن**: اسی طرح کے کرداروں کی غلط شناخت\r\n   - نمبر 0 اور حرف او\r\n   - نمبر 1 اور حرف ایل\r\n   - حروف M اور N\r\n\r\n2. **ترتیب کی غلطی**: حروف کی ترتیب میں غلطی\r\n   - کردار کی پوزیشنیں الٹ جاتی ہیں\r\n   - کرداروں کی نقل یا کوتاہی\r\n\r\n3. ** لمبائی کی غلطی**: ترتیب کی لمبائی کی پیش گوئی کرنے میں غلطی\r\n   - بہت لمبا: غیر موجود حروف شامل کیے گئے\r\n   - بہت مختصر: موجود کردار غائب ہیں\r\n\r\n** تجزیہ کا طریقہ**:\r\n1. **کنفیوژن میٹرکس**: کردار کی سطح کی غلطی کے نمونوں کا تجزیہ کرتا ہے\r\n2. ** توجہ کا نظارہ**: ماڈل کے خدشات کو سمجھیں\r\n3. **گریڈینٹ تجزیہ**: گریڈینٹ بہاؤ کی جانچ کریں\r\n4. **ایکٹیویشن تجزیہ**: نیٹ ورک کی پرتوں میں فعالیت کے نمونوں کا مشاہدہ کریں\r\n\r\n### ماڈل تشخیص\r\n\r\n**اوورفٹ کا پتہ لگانا**:\r\n- تربیتی نقصانات میں کمی جاری ہے، توثیق کے نقصانات میں اضافہ\r\n- تربیت کی درستگی توثیق کی درستگی سے کہیں زیادہ ہے\r\n- حل: باقاعدگی میں اضافہ کریں اور ماڈل کی پیچیدگی کو کم کریں\r\n\r\n**انڈرفٹ ڈیٹیکشن**:\r\n- تربیت اور توثیق دونوں کے نقصانات زیادہ ہیں\r\n- ماڈل تربیتی سیٹ پر اچھی کارکردگی کا مظاہرہ نہیں کرتا ہے\r\n- حل: ماڈل کی پیچیدگی میں اضافہ اور سیکھنے کی شرح کو ایڈجسٹ کریں\r\n\r\n**گریڈینٹ مسئلے کی تشخیص**:\r\n- گریڈینٹ نقصان: گریڈینٹ ویلیو بہت چھوٹا ہے، آہستہ سیکھنا\r\nگریڈینٹ دھماکہ: حد سے زیادہ گریڈینٹ کی قدریں غیر مستحکم تربیت کا باعث بنتی ہیں\r\n- حل: ایل ایس ٹی ایم / جی آر یو کا استعمال کرتے ہوئے، گریڈینٹ کرپنگ\r\n\r\n## حقیقی دنیا کی ایپلی کیشن کیسز\r\n\r\n### ہاتھ سے لکھا ہوا کردار کی شناخت کا نظام\r\n\r\n** ایپلی کیشن منظرنامے**:\r\n- ہاتھ سے لکھے گئے نوٹوں کو ڈیجیٹائز کریں: کاغذی نوٹوں کو الیکٹرانک دستاویزات میں تبدیل کریں\r\n- فارم آٹو فل: ہاتھ سے لکھے گئے فارم کے مواد کو خود بخود پہچان تا ہے\r\n- تاریخی دستاویزات کی شناخت: قدیم کتابوں اور تاریخی دستاویزات کو ڈیجیٹلائز کریں\r\n\r\n** تکنیکی خصوصیات**:\r\n- بڑے کردار کی تغیرات: ہاتھ سے لکھے گئے متن میں اعلی درجے کی ذاتیت ہوتی ہے\r\n- مسلسل قلم کی پروسیسنگ: کرداروں کے مابین رابطوں کو سنبھالنے کی ضرورت ہے\r\n- سیاق و سباق اہم: شناخت کو بہتر بنانے کے لئے زبان کے ماڈل کا استعمال کریں\r\n\r\n** سسٹم آرکیٹیکچر**:\r\n1. **پریپریٹمنٹ ماڈیول**:\r\n   - امیج ڈینوائزنگ اور اضافہ\r\n   - جھکاؤ کی اصلاح\r\n   - ٹیکسٹ لائن کی تقسیم\r\n\r\n2. ** فیچر ایکسٹریکشن ماڈیول**:\r\n   - سی این این بصری خصوصیات نکالتا ہے\r\n   - ملٹی اسکیل فیچر فیوژن\r\n   - فیچر سیریلائزیشن\r\n\r\n3. ** سیکوئنس ماڈلنگ ماڈیول**:\r\n   - دو طرفہ ایل ایس ٹی ایم ماڈلنگ\r\n   - توجہ کے طریقہ کار\r\n   - سیاق و سباق انکوڈنگ\r\n\r\n4. **ڈی کوڈنگ ماڈیول**:\r\n   - سی ٹی سی ڈی کوڈنگ یا توجہ ڈی کوڈنگ\r\n   - پروسیسنگ کے بعد زبان کا ماڈل\r\n   - اعتماد کا جائزہ\r\n\r\n### مطبوعہ دستاویز کی شناخت کا نظام\r\n\r\n** ایپلی کیشن منظرنامے**:\r\n- دستاویزات کی ڈیجیٹلائزیشن: کاغذی دستاویزات کو قابل تدوین فارمیٹس میں تبدیل کرنا\r\n- بل کی شناخت: خود بخود انوائسز، رسیدوں اور دیگر بلوں پر عمل کریں\r\n- سائن ایج کی شناخت: سڑک کے نشانات، اسٹور کے نشانات، اور بہت کچھ کی شناخت کریں\r\n\r\n** تکنیکی خصوصیات**:\r\n- باقاعدہ فونٹ: ہاتھ سے لکھے گئے متن سے زیادہ باقاعدگی سے\r\n- ٹائپوگرافی کے قواعد: ترتیب کی معلومات استعمال کی جا سکتی ہے\r\n- اعلی درستگی کی ضروریات: تجارتی ایپلی کیشنز میں سخت درستگی کی ضروریات ہیں\r\n\r\n**آپٹمائزیشن حکمت عملی**:\r\n1. **ملٹی فونٹ ٹریننگ**: متعدد فونٹس سے تربیتی ڈیٹا استعمال کرتا ہے\r\n2. ** ڈیٹا میں اضافہ**: گھمائیں، پیمانہ، شور کا اضافہ\r\n3. ** پوسٹ پروسیسنگ آپٹیمائزیشن**: اسپیل چیک، گرامر کی اصلاح\r\n4. ** اعتماد کی تشخیص**: شناخت کے نتائج کے لئے قابل اعتماد اسکور فراہم کرتا ہے\r\n\r\n### منظر متن کی شناخت کا نظام\r\n\r\n** ایپلی کیشن منظرنامے**:\r\n- اسٹریٹ ویو ٹیکسٹ کی شناخت: گوگل اسٹریٹ ویو میں ٹیکسٹ کی شناخت\r\n- پروڈکٹ لیبل کی شناخت: سپر مارکیٹ کی مصنوعات کی خود کار شناخت\r\n- ٹریفک سائن کی شناخت: ذہین نقل و حمل کے نظام کی ایپلی کیشنز\r\n\r\n** تکنیکی چیلنجز**:\r\n- پیچیدہ پس منظر: متن پیچیدہ قدرتی مناظر میں سرایت کرتا ہے\r\n- شدید خرابی: نقطہ نظر کی خرابی، جھکنے کی خرابی\r\n- ریئل ٹائم ضروریات: موبائل ایپس کو جوابدہ ہونے کی ضرورت ہے\r\n\r\n**حل**:\r\n1. **مضبوط فیچر نکالنے**: گہرے سی این این نیٹ ورکس کا استعمال کرتا ہے\r\n2. **ملٹی اسکیل پروسیسنگ**: مختلف سائز کے متن کو سنبھالیں\r\n3. ** جیومیٹری کی اصلاح**: خود بخود جیومیٹرک خرابیوں کو درست کرتا ہے\r\n4. ** ماڈل کمپریشن**: موبائل کے لئے ماڈل کو بہتر بنائیں\r\n\r\n## خلاصہ\r\n\r\nبار بار اعصابی نیٹ ورکس او سی آر میں ترتیب ماڈلنگ کے لئے ایک طاقتور آلہ فراہم کرتے ہیں۔ بنیادی آر این این سے لے کر بہتر ایل ایس ٹی ایم اور جی آر یو سے لے کر دو طرفہ پروسیسنگ اور توجہ کے میکانزم تک ، ان ٹکنالوجیوں کی ترقی نے او سی آر سسٹم کی کارکردگی کو بہت بہتر بنایا ہے۔\r\n\r\n**اہم نکات**:\r\n- آر این این لوپ جوائنز کے ذریعے ترتیب ماڈلنگ کو نافذ کرتے ہیں ، لیکن ایک گریڈینٹ غائب ہونے کا مسئلہ ہے۔\r\n- ایل ایس ٹی ایم اور جی آر یو سیٹنگ میکانزم کے ذریعے طویل فاصلے پر منحصر سیکھنے کے مسئلے کو حل کرتے ہیں\r\n- دو طرفہ آر این این مکمل سیاق و سباق کی معلومات سے فائدہ اٹھانے کے قابل ہیں\r\n- توجہ کے میکانزم ترتیب ماڈلنگ کی صلاحیت کو مزید بڑھاتے ہیں\r\n- ماڈل کی کارکردگی کے لئے مناسب تربیتی حکمت عملی اور ریگولرائزیشن تکنیک اہم ہیں\r\n\r\nمستقبل کی ترقیاتی سمتیں**:\r\n- ٹرانسفارمر آرکیٹیکچر کے ساتھ انضمام\r\n- ترتیب ماڈلنگ کے لئے زیادہ موثر نقطہ نظر\r\n- اینڈ ٹو اینڈ ملٹی ماڈل لرننگ\r\n- حقیقی وقت اور درستگی کا توازن\r\n\r\nجیسے جیسے ٹکنالوجی ترقی کر رہی ہے ، ترتیب ماڈلنگ کی تکنیک اب بھی ترقی کر رہی ہے۔ او سی آر کے میدان میں آر این این اور ان کی اقسام کے ذریعہ جمع کردہ تجربے اور ٹکنالوجی نے زیادہ جدید ترتیب ماڈلنگ کے طریقوں کو سمجھنے اور ڈیزائن کرنے کے لئے ایک ٹھوس بنیاد رکھی ہے۔</div>\n                        \n                        <div class=\"article-tags\">\n                            <h6>لیبل:</h6>\n                            <div class=\"tags-list\">\n                                \n                                <span class=\"tag\">RNN</span>\n                                \n                                <span class=\"tag\">LSTM</span>\n                                \n                                <span class=\"tag\">GRU</span>\n                                \n                                <span class=\"tag\">ترتیب ماڈلنگ</span>\n                                \n                                <span class=\"tag\">ڈھال غائب ہو جاتا ہے</span>\n                                \n                                <span class=\"tag\">Bidirectional RNN</span>\n                                \n                                <span class=\"tag\">توجہ کا طریقہ کار</span>\n                                \n                                <span class=\"tag\">CRNN</span>\n                                \n                                <span class=\"tag\">OCR</span>\n                                \n                            </div>\n                        </div>\n                        \n                    </article>\n                    \n                    <footer class=\"article-footer\" id=\"articleFooter\">\n                        <div class=\"article-share\">\n                            <h6 style=\"color: #1a1c1f; margin-bottom: 1rem;\">اشتراک کریں اور کام کریں:</h6>\n                            <div class=\"share-buttons\">\n                                <a href=\"javascript:void(0)\" onclick=\"shareToWeibo()\" class=\"btn btn-weibo\">📱 Weibo نے شیئر کیا</a>\n                                <a href=\"javascript:void(0)\" onclick=\"copyUrl()\" class=\"btn btn-copy\">🔗 کاپی لنک</a>\n                                <button type=\"button\" class=\"btn btn-print\" onclick=\"printArticle()\">🖨️ مضمون پرنٹ کریں</button>\n                            </div>\n                        </div>\n                    </footer>\n                    \n                </div>\n                <div class=\"col-lg-4 col-xl-3 col-md-12\">\n                    <div class=\"sidebar\">\n                        <div class=\"sidebar-card toc-card\" id=\"tocCard\" style=\"display: none;\"><h5>مواد کا جدول</h5><div id=\"tableOfContents\" class=\"toc-list\"></div></div>\n                        <div class=\"sidebar-card\">\n                            <h5>پڑھنے کی سفارش کی</h5>\n                            <div id=\"recommendedArticles\">\n                                \n                                <div class=\"recommended-item\"><h6><a href=\"Article.aspx?slug=document-intelligence-future-outlook\">دستاویز ذہین پروسیسنگ سیریز ·20】 دستاویز ذہین پروسیسنگ ٹیکنالوجی کے ترقیاتی امکانات</a></h6><p class=\"text-muted small\">2025-08-19 • 1203 اگلا مطالعہ</p></div>\n                                \n                                <div class=\"recommended-item\"><h6><a href=\"Article.aspx?slug=document-intelligence-quality-assurance\">【دستاویز ذہین پروسیسنگ سیریز·19】دستاویز ذہین پروسیسنگ کوالٹی ایشورنس سسٹم</a></h6><p class=\"text-muted small\">2025-08-19 • 1200 اگلا مطالعہ</p></div>\n                                \n                                <div class=\"recommended-item\"><h6><a href=\"Article.aspx?slug=large-scale-document-processing-optimization\">دستاویز انٹیلی جنس پروسیسنگ سیریز ·18】بڑے پیمانے پر دستاویز پروسیسنگ کارکردگی کی اصلاح</a></h6><p class=\"text-muted small\">2025-08-19 • 1201 اگلا مطالعہ</p></div>\n                                \n                            </div>\n                        </div>\n                    </div>\n</div>\n</div>\n</div>\n</div>\n<script>\nvar articleId,viewCountKey,now=new Date().getTime();\ndocument.addEventListener('DOMContentLoaded',function(){var articleDetail=document.getElementById('articleDetail');if(articleDetail&&articleDetail.querySelector('.article-content')){formatContent();initReadingProgress();optimizeImages();updateViewCount();}});\nfunction updateViewCount(){articleId=getArticleId();if(!articleId)return;viewCountKey='article_view_'+articleId;var lastViewTime=localStorage.getItem(viewCountKey);if(lastViewTime&&(now-parseInt(lastViewTime))<3600000)return;var xhr=new XMLHttpRequest();xhr.open('POST','/Article.ashx',true);xhr.setRequestHeader('Content-Type','application/x-www-form-urlencoded');xhr.onreadystatechange=function(){if(xhr.readyState===4&&xhr.status===200){try{var response=JSON.parse(xhr.responseText);if(response.success){updateViewCountDisplay(response.viewCount);localStorage.setItem(viewCountKey,now.toString());}}catch(e){}}};xhr.send('action=updateviewcount&articleId='+encodeURIComponent(articleId));}\nfunction getArticleId(){var articleDetail=document.getElementById('articleDetail');if(articleDetail){var id=articleDetail.getAttribute('data-article-id');if(id)return id;}var urlParams=new URLSearchParams(window.location.search);return urlParams.get('id');}\nfunction updateViewCountDisplay(newCount){document.querySelectorAll('.view-count').forEach(function(element){element.textContent=newCount;});}\nfunction decodeHtmlEntities(text){var textArea=document.createElement('textarea');textArea.innerHTML=text;return textArea.value;}\nfunction formatContent(){var contentDiv=document.getElementById('articleContent');if(!contentDiv)return;var content=contentDiv.textContent||contentDiv.innerText;if(!content)return;content=decodeHtmlEntities(content);content=formatMarkdownContent(content);content=formatIndentation(content);content=formatLists(content);content=formatParagraphs(content);content=formatLinks(content);contentDiv.innerHTML=content;initTableOfContents();}\nfunction formatIndentation(content){var lines=content.split('\\n'),result=[];for(var i=0;i<lines.length;i++){var line=lines[i];if(line.trim().match(/^<[^>]+>/)){result.push(line);continue;}line=line.replace(/^\\t+/g,function(match){return '    '.repeat(match.length);});if(line.match(/^  +[^\\s]/)){var indentMatch=line.match(/^( +)(.*)$/);if(indentMatch){var indentLevel=Math.floor(indentMatch[1].length/2);line='<span class=\"text-indent-'+indentLevel+'\">'+indentMatch[2]+'</span>';}}result.push(line);}return result.join('\\n');}\nfunction formatMarkdownContent(content){content=content.replace(/^\\s*##### (.+)$/gm,'<h5>$1</h5>').replace(/^\\s*#### (.+)$/gm,'<h4>$1</h4>').replace(/^\\s*### (.+)$/gm,'<h3>$1</h3>').replace(/^\\s*## (.+)$/gm,'<h2>$1</h2>').replace(/^\\s*# (.+)$/gm,'<h1>$1</h1>').replace(/^> (.+)$/gm,'<blockquote><p>$1</p></blockquote>').replace(/\\*\\*([^*]+?)\\*\\*/g,'<strong>$1</strong>').replace(/\\*([^*\\n]+?)\\*/g,'<em>$1</em>').replace(/`([^`]+?)`/g,'<code class=\"inline-code\">$1</code>').replace(/^(\\*\\*[^*]+?\\*\\*)：/gm,'<div class=\"label-item\"><span class=\"label\">$1</span></div>').replace(/^([^：\\n]+?)：$/gm,'<div class=\"section-label\">$1：</div>').replace(/^(\\*\\*[^*]+?\\*\\*)：(.+)$/gm,'<div class=\"definition-item\"><span class=\"definition-label\">$1：</span><span class=\"definition-content\">$2</span></div>(/^(نوٹ|نوٹ|نوٹ):(.+)$/گرام کو تبدیل کریں۔<div class=\"note-item\"><span class=\"note-label\">$1：</span><span class=\"note-content\">$2</span></div>');content=formatLists(content);content=formatMathSymbols(content);content=formatParagraphs(content);return content;}\nfunction formatLists(content){var lines=content.split('\\n'),result=[],inOrderedList=false,inUnorderedList=false;for(var i=0;i<lines.length;i++){var line=lines[i],trimmed=line.trim();var orderedMatch=line.match(/^(\\d+)\\.\\s*(.+)$/);var unorderedMatch=line.match(/^[-*+]\\s*(.+)$/);if(orderedMatch){if(!inOrderedList){if(inUnorderedList){result.push('</ul>');inUnorderedList=false;}result.push('<ol class=\"formatted-list ordered\">');inOrderedList=true;}var listContent=orderedMatch[2].replace(/^\\*\\*([^*]+?)\\*\\*：(.+)$/,'<strong class=\"list-title\">$1：</strong><span class=\"list-content\">$2</span>');result.push('<li>'+listContent+'</li>');}else if(unorderedMatch){if(!inUnorderedList){if(inOrderedList){result.push('</ol>');inOrderedList=false;}result.push('<ul class=\"formatted-list unordered\">');inUnorderedList=true;}var listContent=unorderedMatch[1].replace(/^\\*\\*([^*]+?)\\*\\*：(.+)$/,'<strong class=\"list-title\">$1：</strong><span class=\"list-content\">$2</span>');result.push('<li>'+listContent+'</li>');}else{if(trimmed===''){if(i+1<lines.length){var nextLine=lines[i+1].trim();if(!nextLine.match(/^\\d+\\.\\s/)&&!nextLine.match(/^[-*+]\\s/)){if(inOrderedList){result.push('</ol>');inOrderedList=false;}if(inUnorderedList){result.push('</ul>');inUnorderedList=false;}}}result.push(line);}else{if(inOrderedList){result.push('</ol>');inOrderedList=false;}if(inUnorderedList){result.push('</ul>');inUnorderedList=false;}result.push(line);}}}if(inOrderedList)result.push('</ol>');if(inUnorderedList)result.push('</ul>');return result.join('\\n');}\nfunction formatMathSymbols(content){var mathSymbols={'α':'&alpha;','β':'&beta;','γ':'&gamma;','δ':'&delta;','ε':'&epsilon;','ζ':'&zeta;','η':'&eta;','θ':'&theta;','λ':'&lambda;','μ':'&mu;','π':'&pi;','σ':'&sigma;','∑':'&sum;','∏':'&prod;','∫':'&int;','∂':'&part;','∇':'&nabla;','∞':'&infin;','≤':'&le;','≥':'&ge;','≠':'&ne;','≈':'&asymp;','±':'&plusmn;','×':'&times;','÷':'&divide;','→':'&rarr;','←':'&larr;','↑':'&uarr;','↓':'&darr;','⇒':'&rArr;','⇐':'&lArr;'};var subMap={'₀':'0','₁':'1','₂':'2','₃':'3','₄':'4','₅':'5','₆':'6','₇':'7','₈':'8','₉':'9','ₖ':'k','ₜ':'t','ₛ':'s','₊':'+','₋':'-'};var supMap={'⁰':'0','¹':'1','²':'2','³':'3','⁴':'4','⁵':'5','⁶':'6','⁷':'7','⁸':'8','⁹':'9','ᵏ':'k','ᵗ':'t','ˢ':'s','⁺':'+','⁻':'-'};for(var symbol in mathSymbols){content=content.replace(new RegExp(escapeRegExp(symbol),'g'),mathSymbols[symbol]);}content=content.replace(/([a-zA-Z])₍([0-9]+)₎/g,'$1<sub>$2</sub>').replace(/([a-zA-Z])⁽([0-9]+)⁾/g,'$1<sup>$2</sup>').replace(/([a-zA-Z])([₀₁₂₃₄₅₆₇₈₉ₖₜₛ₊₋]+)/g,function(match,base,subscript){var converted=subscript.replace(/[₀₁₂₃₄₅₆₇₈₉ₖₜₛ₊₋]/g,function(char){return subMap[char]||char;});return base+'<sub>'+converted+'</sub>';}).replace(/([a-zA-Z])([⁰¹²³⁴⁵⁶⁷⁸⁹ᵏᵗˢ⁺⁻]+)/g,function(match,base,superscript){var converted=superscript.replace(/[⁰¹²³⁴⁵⁶⁷⁸⁹ᵏᵗˢ⁺⁻]/g,function(char){return supMap[char]||char;});return base+'<sup>'+converted+'</sup>';}).replace(/\\b([A-Za-z])_\\{([^}]+)\\}/g,'$1<sub>$2</sub>').replace(/\\b([A-Za-z])\\^\\{([^}]+)\\}/g,'$1<sup>$2</sup>');return content;}\nfunction formatParagraphs(content){var lines=content.split('\\n'),result=[],inParagraph=false,currentParagraph=[];for(var i=0;i<lines.length;i++){var originalLine=lines[i],line=originalLine.trim();if(line.match(/^<[^>]+>/)||line===''){if(inParagraph&&currentParagraph.length>0){var paragraphContent=currentParagraph.join(' ').trim();if(paragraphContent)result.push('<p class=\"content-paragraph indent-paragraph\">'+paragraphContent+'</p>');currentParagraph=[];inParagraph=false;}result.push(originalLine);continue;}if(line.match(/^#{1,6}\\s/)||line.match(/^\\d+\\.\\s/)||line.match(/^[-*+]\\s/)||line.match(/^>/)){if(inParagraph&&currentParagraph.length>0){var paragraphContent=currentParagraph.join(' ').trim();if(paragraphContent)result.push('<p class=\"content-paragraph indent-paragraph\">'+paragraphContent+'</p>');currentParagraph=[];inParagraph=false;}result.push(originalLine);continue;}if(line){currentParagraph.push(line);inParagraph=true;}}if(inParagraph&&currentParagraph.length>0){var paragraphContent=currentParagraph.join(' ').trim();if(paragraphContent)result.push('<p class=\"content-paragraph indent-paragraph\">'+paragraphContent+'</p>');}return result.join('\\n');}\nfunction escapeRegExp(string){return string.replace(/[.*+?^${}()|[\\]\\\\]/g,'\\\\$&');}\nfunction formatLinks(content){return content.replace(/<a\\s+href=\"(https?:\\/\\/[^\"]+)\"([^>]*)>/gi,function(match,url,attrs){return !attrs.includes('target=')?'<a href=\"'+url+'\"'+attrs+' target=\"_blank\" rel=\"noopener noreferrer\">':match;});}\nfunction escapeHtml(text){var div=document.createElement('div');div.textContent=text;return div.innerHTML;}\nfunction addTocClickEvents(){document.querySelectorAll('#tableOfContents a').forEach(function(link){link.addEventListener('click',function(e){e.preventDefault();var targetElement=document.getElementById(this.getAttribute('data-target'));if(targetElement){targetElement.scrollIntoView({behavior:'smooth',block:'start'});updateActiveTocItem(this);}});});}\nfunction addScrollHighlight(headings){var throttleTimer=null;function updateActiveHeading(){var windowHeight=window.innerHeight,activeHeading=null;for(var i=headings.length-1;i>=0;i--){var rect=headings[i].getBoundingClientRect();if(rect.top<=windowHeight*0.3){activeHeading=headings[i];break;}}if(activeHeading){var activeLink=document.querySelector('#tableOfContents a[data-target=\"'+activeHeading.id+'\"]');if(activeLink)updateActiveTocItem(activeLink);}}window.addEventListener('scroll',function(){if(throttleTimer)clearTimeout(throttleTimer);throttleTimer=setTimeout(updateActiveHeading,100);});updateActiveHeading();}\nfunction updateActiveTocItem(activeLink){document.querySelectorAll('#tableOfContents a').forEach(function(link){link.classList.remove('active');});if(activeLink){activeLink.classList.add('active');scrollTocToActiveItem(activeLink);}}\nfunction scrollTocToActiveItem(activeLink){var tocCard=document.getElementById('tocCard');if(!activeLink||!tocCard)return;var cardRect=tocCard.getBoundingClientRect();var activeRect=activeLink.getBoundingClientRect();var relativeTop=activeRect.top-cardRect.top;var relativeBottom=activeRect.bottom-cardRect.bottom;var cardStyle=window.getComputedStyle(tocCard);var paddingTop=parseFloat(cardStyle.paddingTop)||0;var titleHeight=tocCard.querySelector('h5')?tocCard.querySelector('h5').offsetHeight:0;var contentTop=paddingTop+titleHeight;if(relativeTop<contentTop){tocCard.scrollTop+=(relativeTop-contentTop-10);}else if(relativeBottom>0){tocCard.scrollTop+=(relativeBottom+10);}}\nfunction optimizeImages(){document.querySelectorAll('.article-content img').forEach(function(img){img.loading='lazy';img.onerror=function(){this.style.display='none';};if(!img.alt)img.alt='تصاویر کے ساتھ مضمون';});}\nfunction initReadingProgress(){var progressBar=document.getElementById('readingProgress');function updateProgress(){var scrollTop=window.pageYOffset||document.documentElement.scrollTop;var docHeight=document.documentElement.scrollHeight-window.innerHeight;progressBar.style.width=Math.min((scrollTop/docHeight)*100,100)+'%';}window.addEventListener('scroll',updateProgress);updateProgress();}\nfunction initTableOfContents(){var content=document.querySelector('.article-content');if(!content)return;var headings=content.querySelectorAll('h1, h2, h3, h4, h5, h6');if(headings.length<=1)return;var tocCard=document.getElementById('tocCard');var tocList=document.getElementById('tableOfContents');var html='',counters=[0,0,0,0,0,0];headings.forEach(function(heading,index){var id='heading-'+index;heading.id=id;var level=parseInt(heading.tagName.substring(1));var indent=(level-1)*15;counters[level-1]++;for(var i=level;i<counters.length;i++)counters[i]=0;var numberText='';for(var i=0;i<level;i++){if(counters[i]>0)numberText+=(numberText?'.':'')+counters[i];}numberText+='.';html+='<a href=\"#'+id+'\" class=\"toc-item toc-level-'+level+'\" style=\"padding-left: '+indent+'px;\" data-target=\"'+id+'\"><span class=\"toc-number\">'+numberText+'</span> <span class=\"toc-text\">'+heading.textContent+'</span></a>';});tocList.innerHTML=html;tocCard.style.display='block';addTocClickEvents();addScrollHighlight(headings);tocList.addEventListener('click',function(e){if(e.target.tagName==='A'){e.preventDefault();var targetElement=document.getElementById(e.target.getAttribute('data-target'));if(targetElement){targetElement.scrollIntoView({behavior:'smooth',block:'start'});targetElement.style.transition='all 0.3s ease';targetElement.style.backgroundColor='#e3f2fd';targetElement.style.padding='10px';targetElement.style.borderRadius='6px';setTimeout(function(){targetElement.style.backgroundColor='';targetElement.style.padding='';targetElement.style.borderRadius='';},2000);}}});}\nfunction shareToWeibo(){window.open('https://service.weibo.com/share/share.php?url='+encodeURIComponent(window.location.href)+'&title='+encodeURIComponent(document.title),'_blank');}\nfunction copyUrl(){navigator.clipboard.writeText(window.location.href).then(function(){alert('لنک کلپ بورڈ پر کاپی کیا گیا ہے');}).catch(function(){var textArea=document.createElement('textarea');textArea.value=window.location.href;textArea.style.cssText='position:fixed;left:-999999px;top:-999999px';document.body.appendChild(textArea);textArea.focus();textArea.select();try{alert(document.execCommand('copy')?'لنک کلپ بورڈ پر کاپی کیا گیا ہے':'اگر کاپی ناکام ہوجاتی ہے تو، براہ مہربانی دستی طور پر لنک کاپی کریں');}catch(err){alert('اگر کاپی ناکام ہوجاتی ہے تو، براہ مہربانی دستی طور پر لنک کاپی کریں');}document.body.removeChild(textArea);});}\nfunction printArticle(){var printWindow=window.open('','_blank');var articleContent=document.querySelector('.article-detail').innerHTML;var articleTitle=document.querySelector('.article-title').textContent;printWindow.document.write('<!DOCTYPE html><html lang=\"ur\"><title>'+articleTitle+'</title><style>body{font-family:-apple-system,BlinkMacSystemFont,\"Segoe UI\",Roboto,\"Helvetica Neue\",Arial,sans-serif;line-height:1.6;color:#333;max-width:800px;margin:0 auto;padding:20px}h1,h2,h3,h4,h5,h6{color:#1a1c1f;margin-top:1.5em;margin-bottom:0.5em}h1{font-size:2em;border-bottom:2px solid #e9ecef;padding-bottom:0.3em}h2{font-size:1.5em;border-bottom:1px solid #e9ecef;padding-bottom:0.3em}h3{font-size:1.25em}p{margin-bottom:1em}pre{background:#f8f9fa;border:1px solid #e9ecef;border-radius:4px;padding:1em;overflow-x:auto;font-family:Consolas,Monaco,\"Courier New\",monospace}code{background:#f1f3f4;padding:2px 4px;border-radius:3px;font-family:Consolas,Monaco,\"Courier New\",monospace}blockquote{border-left:4px solid #1764ff;background:#f8f9fa;padding:1em;margin:1em 0;border-radius:0 4px 4px 0}img{max-width:100%;height:auto}table{width:100%;border-collapse:collapse;margin:1em 0}th,td{border:1px solid #ddd;padding:8px;text-align:left}th{background-color:#f2f2f2}.article-meta,.article-footer,.share-buttons{display:none}</style><body>'+articleContent+'</body></html>');printWindow.document.close();printWindow.focus();setTimeout(function(){printWindow.print();printWindow.close();},250);}\nfunction improvedStickyPosition(){var sidebarCards=document.querySelectorAll('.sidebar-card');var originalPositions=[],isFixed=[];sidebarCards.forEach(function(card,index){var rect=card.getBoundingClientRect();originalPositions[index]=rect.top+window.pageYOffset;isFixed[index]=false;var placeholder=document.createElement('div');placeholder.style.cssText='height:'+card.offsetHeight+'px;display:none';placeholder.className='sticky-placeholder';card.parentNode.insertBefore(placeholder,card.nextSibling);});function updateStickyPosition(){var scrollTop=window.pageYOffset||document.documentElement.scrollTop;sidebarCards.forEach(function(card,index){var originalTop=originalPositions[index];var stickyTop=index>0?(document.querySelector('.toc-card').style.position==='fixed'?60+document.querySelector('.toc-card').offsetHeight+8:80):60;var placeholder=card.nextSibling;if(scrollTop>originalTop-stickyTop){if(!isFixed[index]){var colContainer=card.closest('[class*=\"col-\"]');var colRect=colContainer.getBoundingClientRect();var colStyle=window.getComputedStyle(colContainer);var paddingLeft=parseFloat(colStyle.paddingLeft)||0;var paddingRight=parseFloat(colStyle.paddingRight)||0;card.style.cssText='position:fixed;top:'+stickyTop+'px;left:'+(colRect.left+paddingLeft)+'px;width:'+(colRect.width-paddingLeft-paddingRight)+'px;z-index:9999;box-sizing:border-box';card.classList.add('fixed');placeholder.style.display='block';isFixed[index]=true;}}else if(isFixed[index]){card.style.cssText='';card.classList.remove('fixed');placeholder.style.display='none';isFixed[index]=false;}});}window.addEventListener('scroll',updateStickyPosition);window.addEventListener('resize',function(){setTimeout(function(){sidebarCards.forEach(function(card,index){if(isFixed[index]){var colContainer=card.closest('[class*=\"col-\"]');var colRect=colContainer.getBoundingClientRect();var colStyle=window.getComputedStyle(colContainer);var paddingLeft=parseFloat(colStyle.paddingLeft)||0;var paddingRight=parseFloat(colStyle.paddingRight)||0;card.style.left=(colRect.left+paddingLeft)+'px';card.style.width=(colRect.width-paddingLeft-paddingRight)+'px';}else{originalPositions[index]=card.getBoundingClientRect().top+window.pageYOffset;}});},100);});updateStickyPosition();}\ndocument.addEventListener('DOMContentLoaded',function(){setTimeout(improvedStickyPosition,1000);});\n    </script>\n\r\n            </div>\r\n        </form>\r\n\r\n        <div class=\"fixed-icon\">\r\n            <div style=\"width: 120px; position: relative; left: 1rem; top: -12px;\">\r\n            </div>\r\n            <a id=\"webchat\" class=\"wx-con disa\" href=\"http://wpa.qq.com/msgrd?v=3&uin=365833440&site=qq&menu=yes\" target=\"_blank\">\r\n                <img class=\"wechat-ico\" src=\"site/image/icon/qq.svg\" alt=\"OCR اسسٹنٹ QQ آن لائن کسٹمر سروس\" />\r\n                <div class=\"wx-text\">کیو کیو کسٹمر سروس (365833440)</div>\r\n                <label class=\"badge badge-danger\" style=\"background: rgb(255, 34, 122); display: inline-block;\">1</label>\r\n            </a>\r\n\r\n            <a class=\"wx-con\" href=\"https://qm.qq.com/cgi-bin/qm/qr?k=bohNy9GMdgD5bsYrB-fcIjy-s6Ot3zOg&jump_from=webapi&qr=1\" target=\"_blank\">\r\n                <img class=\"wechat-ico\" src=\"site/image/icon/qq.svg\" alt=\"OCR Assistant QQ user communication گروپ\" />\r\n                <div class=\"wx-text\">کیو کیو گروپ (100029010)</div>\r\n                <label class=\"badge badge-danger\" style=\"background: rgb(255, 34, 122); display: inline-block;\">2</label>\r\n            </a>\r\n            <a href=\"mailto:<EMAIL>\" class=\"wx-con\">\r\n                <img class=\"wechat-ico\" src=\"site/image/icon/im.svg\" alt=\"او سی آر اسسٹنٹ بذریعہ ای میل کسٹمر سروس سے رابطہ کریں\" />\r\n                <div class=\"wx-text\" style=\"width: 240px; text-align: left; left: -15.3rem; padding: 10px 20px;\">\r\n                    <div style=\"font-size: 15px\">ای میل: <EMAIL></div>\r\n                    <hr style=\"background: #c1c1c1; margin: 10px 10px 6px 0;\" />\r\n                    <span style=\"font-size: 13px; color: #c1c1c1\">آپ کے تبصرے اور تجاویز کے لئے آپ کا شکریہ!</span>\r\n                </div>\r\n                <label class=\"badge badge-danger\" style=\"background: rgb(255, 34, 122); display: inline-block;\">3</label>\r\n            </a>\r\n        </div>\r\n\r\n        <style>\r\n            .content {\r\n                width: 100%;\r\n                padding: 0 !important;\r\n                margin: 0 auto\r\n            }\r\n\r\n            h3.h6 {\r\n                font-size: 1rem !important;\r\n                font-weight: bold !important;\r\n                line-height: 1.2 !important;\r\n                margin-bottom: .5rem;\r\n                font-family: inherit\r\n            }\r\n        </style>\r\n        <footer class=\"page-footer\">\r\n            <div class=\"container\">\r\n                <div class=\"row foot-cont pt-4 no-gutters\" style=\"border-top: 1px solid #e1e6ed; max-width: 100%\">\r\n                    <div class=\"col-lg-12 hu-an p-0 mb-3 mt-3 text-l color-gray\" style=\"text-align: center;\">\r\n                        OCR ٹیکسٹ ریکوگنیشن اسسٹنٹ&nbsp;©️ 2025 ALL RIGHTS RESERVED. تمام حقوق محفوظ ہیں&nbsp;&nbsp;&nbsp;|&nbsp;&nbsp;&nbsp;\r\n                        <a href=\"PrivacyPolicy.aspx\" style=\"text-decoration: underline;\">رازداری کا معاہدہ</a>&nbsp;&nbsp;&nbsp;|&nbsp;&nbsp;&nbsp;\r\n                        <a href=\"UserAgreement.aspx\" style=\"text-decoration: underline;\">صارف کا معاہدہ</a>&nbsp;&nbsp;&nbsp;|&nbsp;&nbsp;&nbsp;\r\n                        <a href=\"Status.aspx\" style=\"text-decoration: underline;\">Service کی حیثیت</a>&nbsp;&nbsp;&nbsp;|&nbsp;&nbsp;&nbsp;\r\n                        <a class=\"mb-2 color-gray\" href=\"https://beian.miit.gov.cn/\" target=\"_blank\" style=\"text-decoration: underline;\">ای آئی سی پی تیاری نمبر 2021012692</a>\r\n                    </div>\r\n                </div>\r\n            </div>\r\n        </footer>\r\n        <style>\r\n            #translate > .translateSelectLanguage {\r\n                right: 2rem;\r\n                font-size: 1rem;\r\n                width: 150px;\r\n                padding: .3rem;\r\n                margin-right: 20px;\r\n                border: 1px solid #C9C9C9;\r\n                background: #fff;\r\n                color: #555\r\n            }\r\n        </style>\r\n    </div>\r\n    <script>\r\n        document.addEventListener('DOMContentLoaded', function () {\r\n            var setScroll = function (active) {\r\n                forEach(qa('.v4_header_pc,.v4_header_mob'), function (e) {\r\n                    if (active) addClass(e, 'scrollActive'); else removeClass(e, 'scrollActive');\r\n                });\r\n            };\r\n            if (getScrollY() > 60) setScroll(1);\r\n            window.addEventListener('scroll', function () { setScroll(getScrollY() > 60); });\r\n\r\n            forEach(qa('.v4_header_pc'), function (h) {\r\n                h.addEventListener('mouseenter', function () { addClass(h, 'active'); });\r\n                h.addEventListener('mouseleave', function () { removeClass(h, 'active'); });\r\n            });\r\n\r\n            var menuTimer;\r\n            var hideMenu = function (menuId) {\r\n                var m = document.getElementById(menuId);\r\n                if (m) {\r\n                    m.style.cssText = 'visibility:hidden;height:0;display:none';\r\n                    var n = q('.nav-drown-con', m);\r\n                    if (n) { n.style.cssText = 'height:0;opacity:0;transform:translate(0,-100%)'; }\r\n                }\r\n            };\r\n\r\n            forEach(qa('ul.top-nav>li'), function (i) {\r\n                i.addEventListener('mouseenter', function () {\r\n                    clearTimeout(menuTimer);\r\n                    var l = this.querySelector('a'), t = this.querySelector('.triangle'), s = l ? l.getAttribute('data-sub') : null;\r\n                    if (l) addClass(l, 'active'); if (t) addClass(t, 'active');\r\n                    if (s) {\r\n                        var m = document.getElementById(s);\r\n                        if (m) {\r\n                            m.style.cssText = 'display:block;visibility:visible';\r\n                            var siblings = m.parentNode ? toArray(m.parentNode.children) : [];\r\n                            forEach(siblings, function (c) { if (c !== m) c.style.display = 'none'; });\r\n                            var n = q('.nav-drown-con', m);\r\n                            if (n) { n.style.cssText = 'height:100%;opacity:1;transform:translate(0,0)'; }\r\n\r\n                            m.addEventListener('mouseenter', function () { clearTimeout(menuTimer); });\r\n                            m.addEventListener('mouseleave', function () { menuTimer = setTimeout(function () { hideMenu(s); }, 100); });\r\n                        }\r\n                    }\r\n                });\r\n                i.addEventListener('mouseleave', function () {\r\n                    var l = this.querySelector('a'), t = this.querySelector('.triangle'), s = l ? l.getAttribute('data-sub') : null;\r\n                    if (l) removeClass(l, 'active'); if (t) removeClass(t, 'active');\r\n                    if (s) { menuTimer = setTimeout(function () { hideMenu(s); }, 100); }\r\n                });\r\n            });\r\n\r\n            var r = q('.v4_header_mob .right-menu');\r\n            if (r) r.addEventListener('click', function () {\r\n                var arr = [r, q('.v4_header_mob'), q('#nav_products'), q('.mob-nav-content .sidebar-fix')];\r\n                for (var i = 0; i < arr.length; i++) {\r\n                    var e = arr[i];\r\n                    if (e) {\r\n                        var name = (i === 0 ? 'active' : (i === 1 ? 'active' : (i === 2 ? 'active1' : 'fixedActiveBg')));\r\n                        toggleClass(e, name);\r\n                    }\r\n                }\r\n                var f = q('.mob-nav-content .sidebar-fix');\r\n                if (f) {\r\n                    toggleClass(f, 'show');\r\n                    document.body.style.overflow = hasClass(f, 'show') ? 'hidden' : 'scroll';\r\n                }\r\n            });\r\n\r\n            document.addEventListener('click', function (e) {\r\n                e = e || window.event;\r\n                var target = e.target || e.srcElement;\r\n                var m = closest(target, '.sidebar-fix-left .mob-nav-item');\r\n                if (m) {\r\n                    var p = m.parentNode;\r\n                    if (p) forEach(toArray(p.children), function (c) { removeClass(c, 'active'); });\r\n                    addClass(m, 'active');\r\n                    var d = m.getAttribute('data');\r\n                    if (d) forEach(qa('.sidebar-fix-rigth .sub-nav'), function (n, i) { n.style.display = (i == d ? 'block' : 'none'); });\r\n                }\r\n            });\r\n        });\r\n    </script>\r\n</body>\r\n</html>\r\n\r\n\t            <script type=\"text/javascript\">\r\n                    var _hmt = _hmt || [];\r\n                    (function () {\r\n                        var hm = document.createElement('script');hm.async=1;\r\n                        hm.src = 'https://hm.baidu.com/hm.js?5ee048d7caaf0d439cf16309a1687754';\r\n                        var s = document.getElementsByTagName('script')[0];s.parentNode.insertBefore(hm, s);\r\n                    })();\r\n                </script><div id=\"translate\" class=\"ignore\" style=\"display: none\"></div><script src=\"/static/js/translate.js\"></script>"